class ChatRoom {
  final String id;
  final String name;
  final String? description;
  final List<String> participants;
  final String? lastMessage;
  final DateTime? lastMessageTime;
  final String? lastMessageSender;
  final int unreadCount;
  final bool isActive;
  final bool isPremium;
  final String? profilePicture;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? matchId;
  final bool isBlocked;
  final String? blockedBy;

  const ChatRoom({
    required this.id,
    required this.name,
    this.description,
    required this.participants,
    this.lastMessage,
    this.lastMessageTime,
    this.lastMessageSender,
    required this.unreadCount,
    required this.isActive,
    required this.isPremium,
    this.profilePicture,
    required this.createdAt,
    required this.updatedAt,
    this.matchId,
    required this.isBlocked,
    this.blockedBy,
  });

  factory ChatRoom.fromJson(Map<String, dynamic> json) {
    return ChatRoom(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString(),
      participants: List<String>.from(json['participants'] ?? []),
      lastMessage: json['lastMessage']?.toString(),
      lastMessageTime: json['lastMessageTime'] != null 
          ? DateTime.tryParse(json['lastMessageTime'].toString())
          : null,
      lastMessageSender: json['lastMessageSender']?.toString(),
      unreadCount: json['unreadCount']?.toInt() ?? 0,
      isActive: json['isActive'] ?? true,
      isPremium: json['isPremium'] ?? false,
      profilePicture: json['profilePicture']?.toString(),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt']?.toString() ?? '') ?? DateTime.now(),
      matchId: json['matchId']?.toString(),
      isBlocked: json['isBlocked'] ?? false,
      blockedBy: json['blockedBy']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'participants': participants,
      'lastMessage': lastMessage,
      'lastMessageTime': lastMessageTime?.toIso8601String(),
      'lastMessageSender': lastMessageSender,
      'unreadCount': unreadCount,
      'isActive': isActive,
      'isPremium': isPremium,
      'profilePicture': profilePicture,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'matchId': matchId,
      'isBlocked': isBlocked,
      'blockedBy': blockedBy,
    };
  }
}

class ChatMessage {
  final String id;
  final String chatRoomId;
  final String senderId;
  final String senderName;
  final String content;
  final MessageType type;
  final MessageStatus status;
  final DateTime timestamp;
  final String? replyToId;
  final ChatMessage? replyToMessage;
  final List<String>? attachments;
  final Map<String, dynamic>? metadata;
  final bool isEdited;
  final DateTime? editedAt;
  final bool isDeleted;
  final DateTime? deletedAt;

  const ChatMessage({
    required this.id,
    required this.chatRoomId,
    required this.senderId,
    required this.senderName,
    required this.content,
    required this.type,
    required this.status,
    required this.timestamp,
    this.replyToId,
    this.replyToMessage,
    this.attachments,
    this.metadata,
    required this.isEdited,
    this.editedAt,
    required this.isDeleted,
    this.deletedAt,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id']?.toString() ?? '',
      chatRoomId: json['chatRoomId']?.toString() ?? '',
      senderId: json['senderId']?.toString() ?? '',
      senderName: json['senderName']?.toString() ?? '',
      content: json['content']?.toString() ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => MessageType.text,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => MessageStatus.sent,
      ),
      timestamp: DateTime.tryParse(json['timestamp']?.toString() ?? '') ?? DateTime.now(),
      replyToId: json['replyToId']?.toString(),
      replyToMessage: json['replyToMessage'] != null 
          ? ChatMessage.fromJson(json['replyToMessage'])
          : null,
      attachments: json['attachments'] != null 
          ? List<String>.from(json['attachments'])
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
      isEdited: json['isEdited'] ?? false,
      editedAt: json['editedAt'] != null 
          ? DateTime.tryParse(json['editedAt'].toString())
          : null,
      isDeleted: json['isDeleted'] ?? false,
      deletedAt: json['deletedAt'] != null 
          ? DateTime.tryParse(json['deletedAt'].toString())
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chatRoomId': chatRoomId,
      'senderId': senderId,
      'senderName': senderName,
      'content': content,
      'type': type.name,
      'status': status.name,
      'timestamp': timestamp.toIso8601String(),
      'replyToId': replyToId,
      'replyToMessage': replyToMessage?.toJson(),
      'attachments': attachments,
      'metadata': metadata,
      'isEdited': isEdited,
      'editedAt': editedAt?.toIso8601String(),
      'isDeleted': isDeleted,
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  ChatMessage copyWith({
    String? id,
    String? chatRoomId,
    String? senderId,
    String? senderName,
    String? content,
    MessageType? type,
    MessageStatus? status,
    DateTime? timestamp,
    String? replyToId,
    ChatMessage? replyToMessage,
    List<String>? attachments,
    Map<String, dynamic>? metadata,
    bool? isEdited,
    DateTime? editedAt,
    bool? isDeleted,
    DateTime? deletedAt,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      chatRoomId: chatRoomId ?? this.chatRoomId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      content: content ?? this.content,
      type: type ?? this.type,
      status: status ?? this.status,
      timestamp: timestamp ?? this.timestamp,
      replyToId: replyToId ?? this.replyToId,
      replyToMessage: replyToMessage ?? this.replyToMessage,
      attachments: attachments ?? this.attachments,
      metadata: metadata ?? this.metadata,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }
}

enum MessageType {
  text,
  image,
  video,
  audio,
  document,
  location,
  contact,
  sticker,
  gif,
  system
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed
}

class TypingIndicator {
  final String userId;
  final String userName;
  final String chatRoomId;
  final DateTime timestamp;

  const TypingIndicator({
    required this.userId,
    required this.userName,
    required this.chatRoomId,
    required this.timestamp,
  });

  factory TypingIndicator.fromJson(Map<String, dynamic> json) {
    return TypingIndicator(
      userId: json['userId']?.toString() ?? '',
      userName: json['userName']?.toString() ?? '',
      chatRoomId: json['chatRoomId']?.toString() ?? '',
      timestamp: DateTime.tryParse(json['timestamp']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'userName': userName,
      'chatRoomId': chatRoomId,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class ChatUser {
  final String id;
  final String name;
  final String? profilePicture;
  final bool isOnline;
  final DateTime? lastSeen;
  final bool isTyping;

  const ChatUser({
    required this.id,
    required this.name,
    this.profilePicture,
    required this.isOnline,
    this.lastSeen,
    required this.isTyping,
  });

  factory ChatUser.fromJson(Map<String, dynamic> json) {
    return ChatUser(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      profilePicture: json['profilePicture']?.toString(),
      isOnline: json['isOnline'] ?? false,
      lastSeen: json['lastSeen'] != null 
          ? DateTime.tryParse(json['lastSeen'].toString())
          : null,
      isTyping: json['isTyping'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'profilePicture': profilePicture,
      'isOnline': isOnline,
      'lastSeen': lastSeen?.toIso8601String(),
      'isTyping': isTyping,
    };
  }
}

class ChatSettings {
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final String? customRingtone;
  final bool readReceiptsEnabled;
  final bool typingIndicatorEnabled;
  final String? wallpaper;
  final double fontSize;
  final String theme;

  const ChatSettings({
    required this.notificationsEnabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    this.customRingtone,
    required this.readReceiptsEnabled,
    required this.typingIndicatorEnabled,
    this.wallpaper,
    required this.fontSize,
    required this.theme,
  });

  factory ChatSettings.fromJson(Map<String, dynamic> json) {
    return ChatSettings(
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      soundEnabled: json['soundEnabled'] ?? true,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      customRingtone: json['customRingtone']?.toString(),
      readReceiptsEnabled: json['readReceiptsEnabled'] ?? true,
      typingIndicatorEnabled: json['typingIndicatorEnabled'] ?? true,
      wallpaper: json['wallpaper']?.toString(),
      fontSize: json['fontSize']?.toDouble() ?? 14.0,
      theme: json['theme']?.toString() ?? 'light',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
      'customRingtone': customRingtone,
      'readReceiptsEnabled': readReceiptsEnabled,
      'typingIndicatorEnabled': typingIndicatorEnabled,
      'wallpaper': wallpaper,
      'fontSize': fontSize,
      'theme': theme,
    };
  }
}
