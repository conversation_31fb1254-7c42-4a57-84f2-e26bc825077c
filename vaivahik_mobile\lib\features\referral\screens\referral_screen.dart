import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../models/referral_model.dart';
import '../providers/referral_provider.dart';
import '../widgets/referral_card_widget.dart';
import '../widgets/referral_stats_widget.dart';
import '../widgets/share_options_widget.dart';
import 'referral_history_screen.dart';
import 'invite_friends_screen.dart';

/// 🎁 Main Referral Screen
/// Shows referral code, stats, sharing options, and history
/// Uses existing website backend APIs

class ReferralScreen extends ConsumerStatefulWidget {
  const ReferralScreen({super.key});

  @override
  ConsumerState<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends ConsumerState<ReferralScreen> {
  @override
  void initState() {
    super.initState();
    // Load referral data on screen init
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(referralDataProvider.notifier).loadReferralData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final referralDataAsync = ref.watch(referralDataProvider);

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Refer & Earn',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.read(referralDataProvider.notifier).refresh();
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await ref.read(referralDataProvider.notifier).refresh();
        },
        child: referralDataAsync.when(
          loading: () => const _LoadingView(),
          error: (error, stack) => _ErrorView(
            error: error.toString(),
            onRetry: () => ref.read(referralDataProvider.notifier).refresh(),
          ),
          data: (referralData) => _ReferralContent(referralData: referralData),
        ),
      ),
    );
  }
}

class _ReferralContent extends StatelessWidget {
  final ReferralData referralData;

  const _ReferralContent({
    required this.referralData,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        children: [
          const SizedBox(height: 8),
          
          // Main Referral Card
          ReferralCardWidget(referralData: referralData)
              .animate().fadeIn().slideY(),
          
          const SizedBox(height: 16),
          
          // Share Options
          ShareOptionsWidget(referralData: referralData)
              .animate().fadeIn(delay: const Duration(milliseconds: 200)),
          
          const SizedBox(height: 16),
          
          // Quick Actions
          _QuickActionsWidget(referralData: referralData)
              .animate().fadeIn(delay: const Duration(milliseconds: 400)),
          
          const SizedBox(height: 16),
          
          // Referral Stats
          ReferralStatsWidget(referralData: referralData)
              .animate().fadeIn(delay: const Duration(milliseconds: 600)),
          
          const SizedBox(height: 16),
          
          // Achievements (if any)
          ReferralAchievementsWidget(referralData: referralData)
              .animate().fadeIn(delay: const Duration(milliseconds: 800)),
          
          const SizedBox(height: 16),
          
          // How It Works
          _HowItWorksWidget()
              .animate().fadeIn(delay: const Duration(milliseconds: 1000)),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _QuickActionsWidget extends StatelessWidget {
  final ReferralData referralData;

  const _QuickActionsWidget({
    required this.referralData,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _ActionButton(
              icon: Icons.person_add,
              label: 'Invite Friends',
              color: AppTheme.primaryColor,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => InviteFriendsScreen(referralData: referralData),
                  ),
                );
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _ActionButton(
              icon: Icons.history,
              label: 'View History',
              color: Colors.orange,
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ReferralHistoryScreen(referralData: referralData),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _ActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _ActionButton({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: color,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _HowItWorksWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: Colors.amber[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'How It Works',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const _HowItWorksStep(
            step: '1',
            title: 'Share Your Code',
            description: 'Share your unique referral code with friends and family',
            icon: Icons.share,
          ),
          const SizedBox(height: 12),
          const _HowItWorksStep(
            step: '2',
            title: 'Friend Joins',
            description: 'Your friend signs up using your referral code',
            icon: Icons.person_add,
          ),
          const SizedBox(height: 12),
          const _HowItWorksStep(
            step: '3',
            title: 'Both Get Rewards',
            description: 'You and your friend both receive amazing rewards!',
            icon: Icons.card_giftcard,
          ),
        ],
      ),
    );
  }
}

class _HowItWorksStep extends StatelessWidget {
  final String step;
  final String title;
  final String description;
  final IconData icon;

  const _HowItWorksStep({
    required this.step,
    required this.title,
    required this.description,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Center(
            child: Text(
              step,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 20,
        ),
      ],
    );
  }
}

class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading your referral data...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }
}

class _ErrorView extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

  const _ErrorView({
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Failed to load referral data',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              ),
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }
}
