import 'dart:io';
import '../../../core/api/api_client.dart';
import '../models/chat_models.dart';

class ChatService {
  final ApiClient _apiClient;

  ChatService(this._apiClient);

  // Get all chat rooms for the current user
  Future<List<ChatRoom>> getChatRooms({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get('/chat/rooms', queryParams: {
        'page': page,
        'limit': limit,
      });

      if (response['success'] == true) {
        final List<dynamic> roomsData = response['rooms'] ?? response['data'] ?? [];
        return roomsData.map((room) => ChatRoom.fromJson(room)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch chat rooms');
      }
    } catch (e) {
      throw Exception('Error fetching chat rooms: $e');
    }
  }

  // Get or create a chat room with another user
  Future<ChatRoom> getOrCreateChatRoom(String otherUserId) async {
    try {
      final response = await _apiClient.post('/chat/rooms', {
        'otherUserId': otherUserId,
      });

      if (response['success'] == true) {
        return ChatRoom.fromJson(response['room'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create chat room');
      }
    } catch (e) {
      throw Exception('Error creating chat room: $e');
    }
  }

  // Get messages for a specific chat room
  Future<List<ChatMessage>> getMessages(
    String chatRoomId, {
    int page = 1,
    int limit = 50,
    String? before,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (before != null) 'before': before,
      };

      final response = await _apiClient.get('/chat/rooms/$chatRoomId/messages', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> messagesData = response['messages'] ?? response['data'] ?? [];
        return messagesData.map((message) => ChatMessage.fromJson(message)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch messages');
      }
    } catch (e) {
      throw Exception('Error fetching messages: $e');
    }
  }

  // Send a text message
  Future<ChatMessage> sendMessage(
    String chatRoomId,
    String content, {
    String? replyToId,
    MessageType type = MessageType.text,
  }) async {
    try {
      final response = await _apiClient.post('/chat/rooms/$chatRoomId/messages', {
        'content': content,
        'type': type.name,
        if (replyToId != null) 'replyToId': replyToId,
      });

      if (response['success'] == true) {
        return ChatMessage.fromJson(response['message'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to send message');
      }
    } catch (e) {
      throw Exception('Error sending message: $e');
    }
  }

  // Send media message (image, video, audio, document)
  Future<ChatMessage> sendMediaMessage(
    String chatRoomId,
    File file,
    MessageType type, {
    String? caption,
    String? replyToId,
  }) async {
    try {
      // First upload the file
      final uploadResponse = await _apiClient.uploadFile('/chat/upload', file.path);
      
      if (uploadResponse['success'] != true) {
        throw Exception('Failed to upload file');
      }

      final fileUrl = uploadResponse['url'];
      
      // Then send the message with the file URL
      final response = await _apiClient.post('/chat/rooms/$chatRoomId/messages', {
        'content': caption ?? '',
        'type': type.name,
        'attachments': [fileUrl],
        if (replyToId != null) 'replyToId': replyToId,
      });

      if (response['success'] == true) {
        return ChatMessage.fromJson(response['message'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to send media message');
      }
    } catch (e) {
      throw Exception('Error sending media message: $e');
    }
  }

  // Mark messages as read
  Future<bool> markMessagesAsRead(String chatRoomId, List<String> messageIds) async {
    try {
      final response = await _apiClient.post('/chat/rooms/$chatRoomId/read', {
        'messageIds': messageIds,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error marking messages as read: $e');
    }
  }

  // Delete a message
  Future<bool> deleteMessage(String chatRoomId, String messageId, {bool deleteForEveryone = false}) async {
    try {
      final response = await _apiClient.delete('/chat/rooms/$chatRoomId/messages/$messageId', queryParams: {
        'deleteForEveryone': deleteForEveryone.toString(),
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error deleting message: $e');
    }
  }

  // Edit a message
  Future<ChatMessage> editMessage(String chatRoomId, String messageId, String newContent) async {
    try {
      final response = await _apiClient.put('/chat/rooms/$chatRoomId/messages/$messageId', {
        'content': newContent,
      });

      if (response['success'] == true) {
        return ChatMessage.fromJson(response['message'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to edit message');
      }
    } catch (e) {
      throw Exception('Error editing message: $e');
    }
  }

  // Send typing indicator
  Future<void> sendTypingIndicator(String chatRoomId, bool isTyping) async {
    try {
      await _apiClient.post('/chat/rooms/$chatRoomId/typing', {
        'isTyping': isTyping,
      });
    } catch (e) {
      // Ignore typing indicator errors
    }
  }

  // Block a user in chat
  Future<bool> blockUser(String chatRoomId, String userId) async {
    try {
      final response = await _apiClient.post('/chat/rooms/$chatRoomId/block', {
        'userId': userId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error blocking user: $e');
    }
  }

  // Unblock a user in chat
  Future<bool> unblockUser(String chatRoomId, String userId) async {
    try {
      final response = await _apiClient.post('/chat/rooms/$chatRoomId/unblock', {
        'userId': userId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error unblocking user: $e');
    }
  }

  // Report a chat or message
  Future<bool> reportChat(String chatRoomId, String reason, {String? messageId, String? details}) async {
    try {
      final response = await _apiClient.post('/chat/rooms/$chatRoomId/report', {
        'reason': reason,
        if (messageId != null) 'messageId': messageId,
        if (details != null) 'details': details,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error reporting chat: $e');
    }
  }

  // Get chat settings
  Future<ChatSettings> getChatSettings() async {
    try {
      final response = await _apiClient.get('/chat/settings');

      if (response['success'] == true) {
        return ChatSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch chat settings');
      }
    } catch (e) {
      throw Exception('Error fetching chat settings: $e');
    }
  }

  // Update chat settings
  Future<ChatSettings> updateChatSettings(ChatSettings settings) async {
    try {
      final response = await _apiClient.put('/chat/settings', settings.toJson());

      if (response['success'] == true) {
        return ChatSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update chat settings');
      }
    } catch (e) {
      throw Exception('Error updating chat settings: $e');
    }
  }

  // Search messages in a chat room
  Future<List<ChatMessage>> searchMessages(String chatRoomId, String query, {
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get('/chat/rooms/$chatRoomId/search', queryParams: {
        'query': query,
        'page': page,
        'limit': limit,
      });

      if (response['success'] == true) {
        final List<dynamic> messagesData = response['messages'] ?? response['data'] ?? [];
        return messagesData.map((message) => ChatMessage.fromJson(message)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to search messages');
      }
    } catch (e) {
      throw Exception('Error searching messages: $e');
    }
  }

  // Get chat statistics
  Future<Map<String, dynamic>> getChatStatistics() async {
    try {
      final response = await _apiClient.get('/chat/statistics');

      if (response['success'] == true) {
        return response['statistics'] ?? response['data'] ?? {};
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch chat statistics');
      }
    } catch (e) {
      throw Exception('Error fetching chat statistics: $e');
    }
  }

  // Clear chat history
  Future<bool> clearChatHistory(String chatRoomId) async {
    try {
      final response = await _apiClient.delete('/chat/rooms/$chatRoomId/clear');

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error clearing chat history: $e');
    }
  }

  // Export chat history
  Future<String> exportChatHistory(String chatRoomId, {String format = 'txt'}) async {
    try {
      final response = await _apiClient.get('/chat/rooms/$chatRoomId/export', queryParams: {
        'format': format,
      });

      if (response['success'] == true) {
        return response['downloadUrl'] ?? '';
      } else {
        throw Exception(response['message'] ?? 'Failed to export chat history');
      }
    } catch (e) {
      throw Exception('Error exporting chat history: $e');
    }
  }
}
