import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../app/theme.dart';

/// 📸 PHOTO PRIVACY SYSTEM - Advanced Photo Access Control
/// Features: Private photos, access requests, selective sharing, privacy controls
/// Reuses existing website privacy logic with enhanced mobile-specific features

/// Photo Privacy State
class PhotoPrivacyState {
  final bool isPhotoPrivate;
  final List<String> photoAccessRequests;
  final List<String> grantedPhotoAccess;
  final Map<String, DateTime> requestTimestamps;
  final Map<String, String> requestMessages;

  const PhotoPrivacyState({
    this.isPhotoPrivate = false,
    this.photoAccessRequests = const [],
    this.grantedPhotoAccess = const [],
    this.requestTimestamps = const {},
    this.requestMessages = const {},
  });

  PhotoPrivacyState copyWith({
    bool? isPhotoPrivate,
    List<String>? photoAccessRequests,
    List<String>? grantedPhotoAccess,
    Map<String, DateTime>? requestTimestamps,
    Map<String, String>? requestMessages,
  }) {
    return PhotoPrivacyState(
      isPhotoPrivate: isPhotoPrivate ?? this.isPhotoPrivate,
      photoAccessRequests: photoAccessRequests ?? this.photoAccessRequests,
      grantedPhotoAccess: grantedPhotoAccess ?? this.grantedPhotoAccess,
      requestTimestamps: requestTimestamps ?? this.requestTimestamps,
      requestMessages: requestMessages ?? this.requestMessages,
    );
  }
}

/// Photo Privacy Provider
final photoPrivacyProvider = StateNotifierProvider<PhotoPrivacyNotifier, PhotoPrivacyState>((ref) {
  return PhotoPrivacyNotifier();
});

/// Photo Privacy Notifier
class PhotoPrivacyNotifier extends StateNotifier<PhotoPrivacyState> {
  PhotoPrivacyNotifier() : super(const PhotoPrivacyState()) {
    _loadPrivacySettings();
  }

  Future<void> _loadPrivacySettings() async {
    // Load from API - integrate with existing website privacy settings
    // This would call the same API endpoints used by the website
  }

  Future<void> togglePhotoPrivacy() async {
    state = state.copyWith(isPhotoPrivate: !state.isPhotoPrivate);
    // Save to API
  }

  Future<void> requestPhotoAccess(String userId, String message) async {
    final updatedRequests = [...state.photoAccessRequests, userId];
    final updatedTimestamps = {...state.requestTimestamps, userId: DateTime.now()};
    final updatedMessages = {...state.requestMessages, userId: message};

    state = state.copyWith(
      photoAccessRequests: updatedRequests,
      requestTimestamps: updatedTimestamps,
      requestMessages: updatedMessages,
    );

    // Send notification to photo owner via API
    // This would integrate with existing notification system
  }

  Future<void> grantPhotoAccess(String userId) async {
    final updatedRequests = state.photoAccessRequests.where((id) => id != userId).toList();
    final updatedGranted = [...state.grantedPhotoAccess, userId];

    state = state.copyWith(
      photoAccessRequests: updatedRequests,
      grantedPhotoAccess: updatedGranted,
    );

    // Update API and send notification to requester
  }

  Future<void> denyPhotoAccess(String userId) async {
    final updatedRequests = state.photoAccessRequests.where((id) => id != userId).toList();

    state = state.copyWith(photoAccessRequests: updatedRequests);

    // Update API and send notification to requester
  }

  Future<void> revokePhotoAccess(String userId) async {
    final updatedGranted = state.grantedPhotoAccess.where((id) => id != userId).toList();

    state = state.copyWith(grantedPhotoAccess: updatedGranted);

    // Update API
  }
}

/// Private Photo Widget
class PrivatePhotoWidget extends ConsumerWidget {
  final String userId;
  final String? photoUrl;
  final double width;
  final double height;
  final BorderRadius? borderRadius;

  const PrivatePhotoWidget({
    super.key,
    required this.userId,
    this.photoUrl,
    this.width = 120,
    this.height = 120,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final photoPrivacy = ref.watch(photoPrivacyProvider);
    const currentUserId = 'current_user_id'; // Get from auth provider

    // Check if current user has access to this photo
    final hasAccess = photoPrivacy.grantedPhotoAccess.contains(currentUserId);
    final isOwnPhoto = userId == currentUserId;

    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        color: Colors.grey[200],
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: Stack(
          children: [
            // Background image or placeholder
            if (photoUrl != null && (hasAccess || isOwnPhoto))
              Image.network(
                photoUrl!,
                width: width,
                height: height,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
              )
            else
              _buildDefaultAvatar(),

            // Privacy overlay
            if (photoUrl != null && !hasAccess && !isOwnPhoto)
              _buildPrivacyOverlay(context, ref),
          ],
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 60,
      ),
    );
  }

  Widget _buildPrivacyOverlay(BuildContext context, WidgetRef ref) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.8),
        borderRadius: borderRadius ?? BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            color: Colors.white,
            size: 32,
          ),
          const SizedBox(height: 8),
          const Text(
            'Photo Private',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          const Text(
            'This user keeps photos private',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: () => _showPhotoAccessRequest(context, ref),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
              minimumSize: const Size(0, 0),
            ),
            child: const Text(
              'Request Access',
              style: TextStyle(
                fontSize: 10,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms);
  }

  void _showPhotoAccessRequest(BuildContext context, WidgetRef ref) {
    final messageController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Request Photo Access'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Send a request to view this user\'s photos. Include a polite message:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: messageController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'Hi! I would like to view your photos. Thank you!',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final message = messageController.text.trim();
              if (message.isNotEmpty) {
                ref.read(photoPrivacyProvider.notifier).requestPhotoAccess(userId, message);
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Photo access request sent!'),
                    backgroundColor: AppColors.success,
                  ),
                );
              }
            },
            child: const Text('Send Request'),
          ),
        ],
      ),
    );
  }
}

/// Photo Access Requests Screen
class PhotoAccessRequestsScreen extends ConsumerWidget {
  const PhotoAccessRequestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final photoPrivacy = ref.watch(photoPrivacyProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Photo Access Requests'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: photoPrivacy.photoAccessRequests.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: photoPrivacy.photoAccessRequests.length,
              itemBuilder: (context, index) {
                final userId = photoPrivacy.photoAccessRequests[index];
                return _buildRequestItem(context, ref, userId, photoPrivacy);
              },
            ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 80,
            color: Colors.grey,
          ),
          SizedBox(height: 16),
          Text(
            'No Photo Access Requests',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'When someone requests to view your photos,\nthey will appear here.',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildRequestItem(
    BuildContext context,
    WidgetRef ref,
    String userId,
    PhotoPrivacyState photoPrivacy,
  ) {
    final timestamp = photoPrivacy.requestTimestamps[userId];
    final message = photoPrivacy.requestMessages[userId];

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 25,
                backgroundColor: AppColors.primary,
                child: Text(
                  userId.substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'User $userId', // Replace with actual user name
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (timestamp != null)
                      Text(
                        _formatTimestamp(timestamp),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          if (message != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                message,
                style: const TextStyle(fontSize: 14),
              ),
            ),
          ],
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    ref.read(photoPrivacyProvider.notifier).grantPhotoAccess(userId);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Photo access granted!'),
                        backgroundColor: AppColors.success,
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.success,
                  ),
                  child: const Text('Grant Access'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    ref.read(photoPrivacyProvider.notifier).denyPhotoAccess(userId);
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Photo access denied'),
                        backgroundColor: Colors.orange,
                      ),
                    );
                  },
                  child: const Text('Deny'),
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.2);
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
