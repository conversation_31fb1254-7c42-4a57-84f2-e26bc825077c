import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../api/api_client.dart';
import '../models/spotlight_models.dart';

class SpotlightService {
  static final SpotlightService _instance = SpotlightService._internal();
  factory SpotlightService() => _instance;
  SpotlightService._internal();

  final ApiClient _apiClient = ApiClient();

  // Get all available spotlight features
  Future<List<SpotlightFeature>> getSpotlightFeatures() async {
    try {
      final response = await _apiClient.get('/user/spotlight/features');
      
      if (response['success'] == true) {
        final featuresData = response['features'] as List<dynamic>;
        return featuresData
            .map((feature) => SpotlightFeature.fromJson(feature))
            .toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch spotlight features');
      }
    } catch (e) {
      throw Exception('Error fetching spotlight features: $e');
    }
  }

  // Get user's current active spotlights
  Future<List<UserSpotlight>> getActiveSpotlights() async {
    try {
      final response = await _apiClient.get('/user/spotlight/active');
      
      if (response['success'] == true) {
        final spotlightsData = response['spotlights'] as List<dynamic>;
        return spotlightsData
            .map((spotlight) => UserSpotlight.fromJson(spotlight))
            .toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch active spotlights');
      }
    } catch (e) {
      throw Exception('Error fetching active spotlights: $e');
    }
  }

  // Get user's spotlight history
  Future<List<UserSpotlight>> getSpotlightHistory({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get('/user/spotlight/history?page=$page&limit=$limit');
      
      if (response['success'] == true) {
        final spotlightsData = response['spotlights'] as List<dynamic>;
        return spotlightsData
            .map((spotlight) => UserSpotlight.fromJson(spotlight))
            .toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch spotlight history');
      }
    } catch (e) {
      throw Exception('Error fetching spotlight history: $e');
    }
  }

  // Purchase spotlight feature using Razorpay
  Future<void> purchaseSpotlight({
    required SpotlightFeature feature,
    required Map<String, dynamic> userDetails,
    required Function(UserSpotlight) onSuccess,
    required Function(String) onError,
  }) async {
    try {
      // Create spotlight purchase order
      final orderResponse = await _apiClient.post('/payments/create-spotlight-order', {
        'featureId': feature.id,
        'amount': feature.finalPrice,
        'currency': 'INR',
        'userDetails': userDetails,
      });

      if (orderResponse['success'] != true) {
        throw Exception(orderResponse['message'] ?? 'Failed to create order');
      }

      final orderId = orderResponse['orderId'];
      final amount = orderResponse['amount'];

      // Initialize Razorpay
      final razorpay = _initializeRazorpay(
        orderId: orderId,
        amount: amount,
        userDetails: userDetails,
        onPaymentSuccess: (response) async {
          try {
            // Verify payment with backend
            final verifyResponse = await _apiClient.post('/payments/verify-spotlight-payment', {
              'razorpay_order_id': response['razorpay_order_id'],
              'razorpay_payment_id': response['razorpay_payment_id'],
              'razorpay_signature': response['razorpay_signature'],
              'featureId': feature.id,
            });

            if (verifyResponse['success'] == true) {
              final spotlight = UserSpotlight.fromJson(verifyResponse['spotlight']);
              onSuccess(spotlight);
            } else {
              onError(verifyResponse['message'] ?? 'Payment verification failed');
            }
          } catch (e) {
            onError('Payment verification error: $e');
          }
        },
        onPaymentError: (response) {
          onError('Payment failed: ${response['description'] ?? 'Unknown error'}');
        },
      );

      // Open Razorpay checkout
      razorpay.open({
        'key': await _getRazorpayKey(),
        'amount': amount,
        'currency': 'INR',
        'name': 'Vaivahik',
        'description': 'Spotlight Feature - ${feature.name}',
        'order_id': orderId,
        'prefill': {
          'contact': userDetails['phone'],
          'email': userDetails['email'],
          'name': userDetails['name'],
        },
        'theme': {
          'color': '#E91E63',
        },
      });

    } catch (e) {
      onError('Purchase error: $e');
    }
  }

  // Get spotlight analytics
  Future<SpotlightAnalytics> getSpotlightAnalytics({
    String? spotlightId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (spotlightId != null) queryParams['spotlightId'] = spotlightId;
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      String url = '/user/spotlight/analytics';
      if (queryParams.isNotEmpty) {
        final queryString = queryParams.entries
            .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
            .join('&');
        url += '?$queryString';
      }
      final response = await _apiClient.get(url);
      
      if (response['success'] == true) {
        return SpotlightAnalytics.fromJson(response['analytics']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch analytics');
      }
    } catch (e) {
      throw Exception('Error fetching spotlight analytics: $e');
    }
  }

  // Cancel active spotlight (if allowed)
  Future<bool> cancelSpotlight(String spotlightId) async {
    try {
      final response = await _apiClient.post('/user/spotlight/cancel', {
        'spotlightId': spotlightId,
      });
      
      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to cancel spotlight');
      }
    } catch (e) {
      throw Exception('Error canceling spotlight: $e');
    }
  }

  // Check if user has active spotlight
  Future<bool> hasActiveSpotlight() async {
    try {
      final activeSpotlights = await getActiveSpotlights();
      return activeSpotlights.any((spotlight) => 
          spotlight.isActive && !spotlight.isExpired);
    } catch (e) {
      return false;
    }
  }

  // Get current spotlight status
  Future<UserSpotlight?> getCurrentSpotlight() async {
    try {
      final activeSpotlights = await getActiveSpotlights();
      return activeSpotlights.firstWhere(
        (spotlight) => spotlight.isActive && !spotlight.isExpired,
        orElse: () => throw StateError('No active spotlight'),
      );
    } catch (e) {
      return null;
    }
  }

  // Private helper methods
  Razorpay _initializeRazorpay({
    required String orderId,
    required int amount,
    required Map<String, dynamic> userDetails,
    required Function(Map<String, dynamic>) onPaymentSuccess,
    required Function(Map<String, dynamic>) onPaymentError,
  }) {
    final razorpay = Razorpay();

    razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, (response) {
      onPaymentSuccess(response);
      razorpay.clear();
    });

    razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, (response) {
      onPaymentError(response);
      razorpay.clear();
    });

    razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, (response) {
      // Handle external wallet
      onPaymentError({'description': 'External wallet not supported'});
      razorpay.clear();
    });

    return razorpay;
  }

  Future<String> _getRazorpayKey() async {
    try {
      final response = await _apiClient.get('/payments/config');
      return response['razorpayKey'] ?? '';
    } catch (e) {
      // Fallback to environment or default key
      return 'rzp_test_your_key_here'; // Replace with actual key
    }
  }

  // Helper method to format spotlight duration
  static String formatDuration(int hours) {
    if (hours < 24) {
      return '$hours hours';
    } else if (hours == 24) {
      return '1 day';
    } else if (hours < 168) {
      return '${(hours / 24).round()} days';
    } else {
      return '${(hours / 168).round()} weeks';
    }
  }

  // Helper method to get spotlight icon
  static String getSpotlightIcon(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'trendingup':
        return '📈';
      case 'flashon':
        return '⚡';
      case 'favorite':
        return '❤️';
      case 'verified':
        return '✅';
      case 'star':
        return '⭐';
      default:
        return '🔥';
    }
  }
}
