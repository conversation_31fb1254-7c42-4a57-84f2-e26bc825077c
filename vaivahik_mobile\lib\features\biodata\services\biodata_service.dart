import 'dart:io';
import 'package:http/http.dart' as http;
import '../../../core/api/api_client.dart';
import '../../../core/api/api_endpoints.dart';
import '../models/biodata_models.dart';

class BiodataService {
  final ApiClient _apiClient = ApiClient();

  /// Get all biodata templates with optional gender filter
  Future<List<BiodataTemplate>> getBiodataTemplates({String? gender}) async {
    try {
      final Map<String, dynamic> params = {};
      if (gender != null) {
        params['gender'] = gender;
      }

      final response = await _apiClient.get(
        ApiEndpoints.biodataTemplates,
        queryParams: params,
      );

      if (response['success'] == true) {
        final List<dynamic> templatesData = response['templates'] ?? response['data'] ?? [];
        return templatesData
            .map((json) => BiodataTemplate.fromJson(json))
            .toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch biodata templates');
      }
    } catch (e) {
      throw Exception('Error fetching biodata templates: $e');
    }
  }

  /// Get biodata template by ID
  Future<BiodataTemplate> getBiodataTemplateById(String templateId) async {
    try {
      final response = await _apiClient.get('${ApiEndpoints.biodataTemplates}/$templateId');

      if (response['success'] == true) {
        return BiodataTemplate.fromJson(response['template'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Template not found');
      }
    } catch (e) {
      throw Exception('Error fetching biodata template: $e');
    }
  }

  /// Generate biodata PDF
  Future<BiodataGenerationResult> generateBiodata({
    required String templateId,
    Map<String, dynamic>? customData,
    bool preview = false,
  }) async {
    try {
      final response = await _apiClient.post(
        ApiEndpoints.generateBiodata,
        {
          'templateId': templateId,
          'customData': customData ?? {},
          'preview': preview,
          'format': 'pdf',
        },
      );

      if (response['success'] == true) {
        return BiodataGenerationResult.fromJson(response);
      } else {
        throw Exception(response['message'] ?? 'Failed to generate biodata');
      }
    } catch (e) {
      throw Exception('Error generating biodata: $e');
    }
  }

  /// Get user's biodata settings
  Future<BiodataSettings> getBiodataSettings() async {
    try {
      final response = await _apiClient.get(ApiEndpoints.biodataSettings);

      if (response['success'] == true) {
        return BiodataSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch biodata settings');
      }
    } catch (e) {
      throw Exception('Error fetching biodata settings: $e');
    }
  }

  /// Update biodata settings
  Future<BiodataSettings> updateBiodataSettings(BiodataSettings settings) async {
    try {
      final response = await _apiClient.put(
        ApiEndpoints.biodataSettings,
        settings.toJson(),
      );

      if (response['success'] == true) {
        return BiodataSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update biodata settings');
      }
    } catch (e) {
      throw Exception('Error updating biodata settings: $e');
    }
  }

  /// Purchase biodata template
  Future<PurchaseResult> purchaseBiodataTemplate(String templateId) async {
    try {
      final response = await _apiClient.post(
        '/biodata/purchase',
        {'templateId': templateId},
      );

      if (response['success'] == true) {
        return PurchaseResult.fromJson(response);
      } else {
        throw Exception(response['message'] ?? 'Failed to purchase template');
      }
    } catch (e) {
      throw Exception('Error purchasing template: $e');
    }
  }

  /// Get user's purchased templates
  Future<List<BiodataTemplate>> getPurchasedTemplates() async {
    try {
      final response = await _apiClient.get('/biodata/purchased');

      if (response['success'] == true) {
        final List<dynamic> templatesData = response['templates'] ?? response['data'] ?? [];
        return templatesData
            .map((json) => BiodataTemplate.fromJson(json))
            .toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch purchased templates');
      }
    } catch (e) {
      throw Exception('Error fetching purchased templates: $e');
    }
  }

  /// Download biodata PDF
  Future<File> downloadBiodataPdf(String downloadUrl, String fileName) async {
    try {
      final response = await http.get(Uri.parse(downloadUrl));
      
      if (response.statusCode == 200) {
        final directory = Directory.systemTemp;
        final file = File('${directory.path}/$fileName');
        await file.writeAsBytes(response.bodyBytes);
        return file;
      } else {
        throw Exception('Failed to download PDF: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error downloading PDF: $e');
    }
  }

  /// Get biodata generation history
  Future<List<GeneratedBiodata>> getBiodataHistory() async {
    try {
      final response = await _apiClient.get('/biodata/history');

      if (response['success'] == true) {
        final List<dynamic> historyData = response['history'] ?? response['data'] ?? [];
        return historyData
            .map((json) => GeneratedBiodata.fromJson(json))
            .toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch biodata history');
      }
    } catch (e) {
      throw Exception('Error fetching biodata history: $e');
    }
  }

  /// Delete generated biodata
  Future<bool> deleteBiodata(String biodataId) async {
    try {
      final response = await _apiClient.delete('/biodata/$biodataId');

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error deleting biodata: $e');
    }
  }

  /// Get biodata analytics
  Future<BiodataAnalytics> getBiodataAnalytics() async {
    try {
      final response = await _apiClient.get('/biodata/analytics');

      if (response['success'] == true) {
        return BiodataAnalytics.fromJson(response['analytics'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch biodata analytics');
      }
    } catch (e) {
      throw Exception('Error fetching biodata analytics: $e');
    }
  }
}

/// Result class for biodata generation
class BiodataGenerationResult {
  final bool success;
  final String? downloadUrl;
  final String? previewUrl;
  final String? fileName;
  final String? message;
  final bool? purchaseRequired;

  const BiodataGenerationResult({
    required this.success,
    this.downloadUrl,
    this.previewUrl,
    this.fileName,
    this.message,
    this.purchaseRequired,
  });

  factory BiodataGenerationResult.fromJson(Map<String, dynamic> json) {
    return BiodataGenerationResult(
      success: json['success'] ?? false,
      downloadUrl: json['downloadUrl'] ?? json['data']?['download_url'],
      previewUrl: json['previewUrl'] ?? json['data']?['preview_url'],
      fileName: json['fileName'] ?? json['data']?['file_name'],
      message: json['message'],
      purchaseRequired: json['purchaseRequired'],
    );
  }
}

/// Result class for template purchase
class PurchaseResult {
  final bool success;
  final String? message;
  final String? transactionId;
  final double? amount;

  const PurchaseResult({
    required this.success,
    this.message,
    this.transactionId,
    this.amount,
  });

  factory PurchaseResult.fromJson(Map<String, dynamic> json) {
    return PurchaseResult(
      success: json['success'] ?? false,
      message: json['message'],
      transactionId: json['transactionId'],
      amount: json['amount']?.toDouble(),
    );
  }
}


