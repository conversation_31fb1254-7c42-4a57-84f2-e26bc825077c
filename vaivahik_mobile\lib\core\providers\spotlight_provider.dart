import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/spotlight_service.dart';
import '../models/spotlight_models.dart';

// Spotlight service provider
final spotlightServiceProvider = Provider<SpotlightService>((ref) {
  return SpotlightService();
});

// Spotlight features provider
final spotlightFeaturesProvider = FutureProvider<List<SpotlightFeature>>((ref) async {
  final service = ref.watch(spotlightServiceProvider);
  return await service.getSpotlightFeatures();
});

// Active spotlights provider
final activeSpotlightsProvider = FutureProvider<List<UserSpotlight>>((ref) async {
  final service = ref.watch(spotlightServiceProvider);
  return await service.getActiveSpotlights();
});

// Current spotlight provider
final currentSpotlightProvider = FutureProvider<UserSpotlight?>((ref) async {
  final service = ref.watch(spotlightServiceProvider);
  return await service.getCurrentSpotlight();
});

// Spotlight history provider
final spotlightHistoryProvider = FutureProvider.family<List<UserSpotlight>, SpotlightHistoryParams>((ref, params) async {
  final service = ref.watch(spotlightServiceProvider);
  return await service.getSpotlightHistory(
    page: params.page,
    limit: params.limit,
  );
});

// Spotlight analytics provider
final spotlightAnalyticsProvider = FutureProvider.family<SpotlightAnalytics, SpotlightAnalyticsParams>((ref, params) async {
  final service = ref.watch(spotlightServiceProvider);
  return await service.getSpotlightAnalytics(
    spotlightId: params.spotlightId,
    startDate: params.startDate,
    endDate: params.endDate,
  );
});

// Has active spotlight provider
final hasActiveSpotlightProvider = FutureProvider<bool>((ref) async {
  final service = ref.watch(spotlightServiceProvider);
  return await service.hasActiveSpotlight();
});

// Spotlight state notifier for managing spotlight state
class SpotlightNotifier extends StateNotifier<SpotlightState> {
  final SpotlightService _service;

  SpotlightNotifier(this._service) : super(const SpotlightState());

  // Load spotlight features
  Future<void> loadFeatures() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final features = await _service.getSpotlightFeatures();
      state = state.copyWith(
        features: features,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  // Load current spotlight
  Future<void> loadCurrentSpotlight() async {
    try {
      final currentSpotlight = await _service.getCurrentSpotlight();
      state = state.copyWith(currentSpotlight: currentSpotlight);
    } catch (e) {
      // Current spotlight is optional, so don't set error state
      state = state.copyWith(currentSpotlight: null);
    }
  }

  // Purchase spotlight
  Future<void> purchaseSpotlight({
    required SpotlightFeature feature,
    required Map<String, dynamic> userDetails,
    required Function(UserSpotlight) onSuccess,
    required Function(String) onError,
  }) async {
    state = state.copyWith(isPurchasing: true);
    
    try {
      await _service.purchaseSpotlight(
        feature: feature,
        userDetails: userDetails,
        onSuccess: (spotlight) {
          state = state.copyWith(
            currentSpotlight: spotlight,
            isPurchasing: false,
          );
          onSuccess(spotlight);
        },
        onError: (error) {
          state = state.copyWith(isPurchasing: false);
          onError(error);
        },
      );
    } catch (e) {
      state = state.copyWith(isPurchasing: false);
      onError(e.toString());
    }
  }

  // Cancel spotlight
  Future<bool> cancelSpotlight(String spotlightId) async {
    try {
      final success = await _service.cancelSpotlight(spotlightId);
      if (success) {
        state = state.copyWith(currentSpotlight: null);
      }
      return success;
    } catch (e) {
      return false;
    }
  }

  // Refresh all data
  Future<void> refresh() async {
    await Future.wait([
      loadFeatures(),
      loadCurrentSpotlight(),
    ]);
  }
}

// Spotlight state notifier provider
final spotlightProvider = StateNotifierProvider<SpotlightNotifier, SpotlightState>((ref) {
  final service = ref.watch(spotlightServiceProvider);
  return SpotlightNotifier(service);
});

// Spotlight state class
class SpotlightState {
  final List<SpotlightFeature> features;
  final UserSpotlight? currentSpotlight;
  final bool isLoading;
  final bool isPurchasing;
  final String? error;

  const SpotlightState({
    this.features = const [],
    this.currentSpotlight,
    this.isLoading = false,
    this.isPurchasing = false,
    this.error,
  });

  SpotlightState copyWith({
    List<SpotlightFeature>? features,
    UserSpotlight? currentSpotlight,
    bool? isLoading,
    bool? isPurchasing,
    String? error,
  }) {
    return SpotlightState(
      features: features ?? this.features,
      currentSpotlight: currentSpotlight ?? this.currentSpotlight,
      isLoading: isLoading ?? this.isLoading,
      isPurchasing: isPurchasing ?? this.isPurchasing,
      error: error ?? this.error,
    );
  }

  bool get hasActiveSpotlight => 
      currentSpotlight != null && 
      currentSpotlight!.isActive && 
      !currentSpotlight!.isExpired;
}

// Parameter classes for family providers
class SpotlightHistoryParams {
  final int page;
  final int limit;

  const SpotlightHistoryParams({
    required this.page,
    required this.limit,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpotlightHistoryParams &&
          runtimeType == other.runtimeType &&
          page == other.page &&
          limit == other.limit;

  @override
  int get hashCode => page.hashCode ^ limit.hashCode;
}

class SpotlightAnalyticsParams {
  final String? spotlightId;
  final DateTime? startDate;
  final DateTime? endDate;

  const SpotlightAnalyticsParams({
    this.spotlightId,
    this.startDate,
    this.endDate,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SpotlightAnalyticsParams &&
          runtimeType == other.runtimeType &&
          spotlightId == other.spotlightId &&
          startDate == other.startDate &&
          endDate == other.endDate;

  @override
  int get hashCode => 
      spotlightId.hashCode ^ 
      startDate.hashCode ^ 
      endDate.hashCode;
}
