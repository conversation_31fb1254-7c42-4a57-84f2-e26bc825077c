import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// App Colors - Consistent with website theme
/// Based on vaivahik-nextjs/src/styles/theme.css and landing-variables.css
class AppColors {
  // Primary Colors - Matching website coral pink theme
  static const Color primary = Color(0xFFFF5F6D); // Landing page coral pink
  static const Color primaryLight = Color(0xFFFFC371); // Landing page light orange
  static const Color primaryDark = Color(0xFFE94B59); // Darker coral pink

  // Secondary Colors - Purple theme
  static const Color secondary = Color(0xFF4A00E0); // Primary purple
  static const Color secondaryLight = Color(0xFF8E2DE2); // Light purple
  static const Color secondaryDark = Color(0xFF3A00B0); // Dark purple

  // Accent Colors - Gold theme
  static const Color accent = Color(0xFFFFD700); // Gold
  static const Color accentLight = Color(0xFFFFF2BF); // Light gold
  static const Color accentDark = Color(0xFFB8860B); // Dark gold

  // Background Colors - Consistent with website
  static const Color background = Color(0xFFF8F9FA); // Very light gray
  static const Color backgroundAlt = Color(0xFFF0F2F5); // Slightly different light gray
  static const Color surface = Color(0xFFFFFFFF); // Pure white
  static const Color surfaceDark = Color(0xFF1A1A2E); // Dark surface for dark mode

  // Dark Colors - Deep blue/gray theme
  static const Color darkColor = Color(0xFF2D3047); // Deep blue/gray
  static const Color mediumDarkColor = Color(0xFF1A1E2C); // Medium dark

  // Text Colors - Website consistent
  static const Color textPrimary = Color(0xFF333333); // Dark text
  static const Color textSecondary = Color(0xFF555555); // Medium text
  static const Color textLight = Color(0xFFF0F0F0); // Light text
  static const Color textLightMuted = Color(0xFFB0B0B0); // Muted light text
  static const Color textDisabled = Color(0xFF9CA3AF); // Disabled text

  // Border Colors
  static const Color border = Color(0xFFE0E0E0); // Light gray border
  static const Color borderLight = Color(0xFFF0F0F0); // Very light border
  static const Color borderDark = Color(0xFFBDBDBD); // Dark border

  // Status Colors - Material Design 3 consistent
  static const Color success = Color(0xFF10B981); // Green
  static const Color successLight = Color(0xFF34D399); // Light green
  static const Color successDark = Color(0xFF059669); // Dark green

  static const Color warning = Color(0xFFF59E0B); // Orange
  static const Color warningLight = Color(0xFFFBBF24); // Light orange
  static const Color warningDark = Color(0xFFD97706); // Dark orange

  static const Color error = Color(0xFFEF4444); // Red
  static const Color errorLight = Color(0xFFF87171); // Light red
  static const Color errorDark = Color(0xFFB91C1C); // Dark red

  static const Color info = Color(0xFF3B82F6); // Blue
  static const Color infoLight = Color(0xFF60A5FA); // Light blue
  static const Color infoDark = Color(0xFF2563EB); // Dark blue

  // Premium Features Colors
  static const Color premium = Color(0xFFFFD700); // Gold for premium
  static const Color verified = Color(0xFF10B981); // Green for verified
  static const Color online = Color(0xFF10B981); // Green for online status
  static const Color offline = Color(0xFF9E9E9E); // Gray for offline

  // Glassmorphism Colors
  static const Color glassWhite = Color(0x1AFFFFFF); // Glass white
  static const Color glassBlack = Color(0x1A000000); // Glass black
  static const Color glassPrimary = Color(0x1AFF5F6D); // Glass primary

  // Shadow Colors - Subtle shadows matching website
  static const Color shadowSubtle = Color(0x0F2D3047); // Subtle shadow
  static const Color shadowLight = Color(0x0A000000); // Light shadow
  static const Color shadowMedium = Color(0x14000000); // Medium shadow
  static const Color shadowHeavy = Color(0x1F000000); // Heavy shadow
}

/// App Gradients - Consistent with website theme
/// Based on vaivahik-nextjs/src/styles/theme.css gradients
class AppGradients {
  // Primary Gradients - Matching website primary gradient (135deg)
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment(-1.0, -1.0), // 135deg equivalent
    end: Alignment(1.0, 1.0),
    colors: [AppColors.primary, AppColors.primaryLight], // FF5F6D to FFC371
  );

  static const LinearGradient primaryVertical = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [AppColors.primary, AppColors.primaryDark],
  );

  // Secondary Gradients - Purple theme (135deg)
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment(-1.0, -1.0), // 135deg equivalent
    end: Alignment(1.0, 1.0),
    colors: [AppColors.secondary, AppColors.secondaryLight], // 4A00E0 to 8E2DE2
  );

  // Accent Gradients - Premium gold (135deg)
  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment(-1.0, -1.0), // 135deg equivalent
    end: Alignment(1.0, 1.0),
    colors: [AppColors.accent, AppColors.accentLight], // FFD700 to FFF2BF
  );

  // Premium Card Gradient - Enhanced gold theme
  static const LinearGradient premiumGradient = LinearGradient(
    begin: Alignment(-1.0, -1.0), // 135deg equivalent
    end: Alignment(1.0, 1.0),
    colors: [AppColors.accent, AppColors.accentLight], // FFD700 to FFF2BF
  );

  // Background Gradients - Website-style subtle gradients
  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [AppColors.background, AppColors.surface], // F8F9FA to FFFFFF
  );

  static const LinearGradient subtleWhiteGradient = LinearGradient(
    begin: Alignment.topCenter, // 180deg equivalent
    end: Alignment.bottomCenter,
    colors: [AppColors.surface, AppColors.backgroundAlt], // FFFFFF to F0F2F5
  );

  static const LinearGradient subtleLightGradient = LinearGradient(
    begin: Alignment.topCenter, // 180deg equivalent
    end: Alignment.bottomCenter,
    colors: [AppColors.backgroundAlt, AppColors.background], // F0F2F5 to F8F9FA
  );

  // Card Gradients - Premium feel
  static const LinearGradient cardGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [AppColors.surface, Color(0xFFFFFBF0)], // White to cream
  );

  // Glassmorphism Gradient - Modern glass effect
  static const LinearGradient glassGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [AppColors.glassWhite, AppColors.glassWhite],
  );

  // Dark Mode Gradients - For future dark theme support
  static const LinearGradient darkBackgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFF1A1A2E), Color(0xFF1E1E35)], // Dark theme
  );

  static const LinearGradient darkSurfaceGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [Color(0xFF2A2A3C), Color(0xFF252538)], // Dark surface
  );

  // Shimmer Gradient for Loading
  static const LinearGradient shimmerGradient = LinearGradient(
    begin: Alignment(-1.0, -0.3),
    end: Alignment(1.0, 0.3),
    colors: [
      Color(0xFFE0E0E0),
      Color(0xFFF5F5F5),
      Color(0xFFE0E0E0),
    ],
  );
}

/// App Shadows - Consistent with website subtle shadow system
/// Based on vaivahik-nextjs/src/styles/theme.css shadow variables
class AppShadows {
  // Subtle Shadow - Website style (0 2px 8px rgba(45, 48, 71, 0.06))
  static const List<BoxShadow> subtleShadow = [
    BoxShadow(
      color: AppColors.shadowSubtle, // rgba(45, 48, 71, 0.06)
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  // Card Shadows - Enhanced subtle shadows
  static const List<BoxShadow> cardShadow = [
    BoxShadow(
      color: AppColors.shadowSubtle,
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  // Elevated Card Shadows - More prominent
  static const List<BoxShadow> elevatedShadow = [
    BoxShadow(
      color: AppColors.shadowMedium,
      offset: Offset(0, 4),
      blurRadius: 16,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: AppColors.shadowLight,
      offset: Offset(0, 2),
      blurRadius: 8,
      spreadRadius: 0,
    ),
  ];

  // Premium Card Shadows - Gold accent
  static const List<BoxShadow> premiumShadow = [
    BoxShadow(
      color: Color(0x1AFFD700), // Gold shadow
      offset: Offset(0, 8),
      blurRadius: 24,
      spreadRadius: 0,
    ),
    BoxShadow(
      color: AppColors.shadowSubtle,
      offset: Offset(0, 4),
      blurRadius: 12,
      spreadRadius: 0,
    ),
  ];

  // Button Shadows - Interactive elements
  static const List<BoxShadow> buttonShadow = [
    BoxShadow(
      color: AppColors.shadowLight,
      offset: Offset(0, 2),
      blurRadius: 4,
      spreadRadius: 0,
    ),
  ];

  // Floating Action Button Shadow
  static const List<BoxShadow> fabShadow = [
    BoxShadow(
      color: AppColors.shadowMedium,
      offset: Offset(0, 6),
      blurRadius: 20,
      spreadRadius: 0,
    ),
  ];

  // Modal/Dialog Shadows
  static const List<BoxShadow> modalShadow = [
    BoxShadow(
      color: AppColors.shadowHeavy,
      offset: Offset(0, 10),
      blurRadius: 40,
      spreadRadius: 0,
    ),
  ];
}

/// App Text Styles - Consistent with website typography
/// Based on website's font system and Material Design 3
class AppTextStyles {
  // Font Family - Consistent with website
  static const String fontFamily = 'Poppins'; // Primary font
  static const String fontFamilySecondary = 'Inter'; // Secondary font for body text

  // Display Styles - Large headings
  static const displayLarge = TextStyle(
    fontSize: 36,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.75,
    height: 1.1,
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
  );

  static const displayMedium = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.5,
    height: 1.2,
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
  );

  // Headlines - Section headers
  static const h1 = TextStyle(
    fontSize: 28,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.25,
    height: 1.2,
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
  );

  static const h2 = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.w600,
    letterSpacing: 0,
    height: 1.3,
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
  );

  static const h3 = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.15,
    height: 1.3,
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
  );

  static const h4 = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.15,
    height: 1.4,
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
  );

  // Body Text - Content text
  static const bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.15,
    height: 1.5,
    fontFamily: fontFamilySecondary,
    color: AppColors.textPrimary,
  );

  static const bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.4,
    fontFamily: fontFamilySecondary,
    color: AppColors.textSecondary,
  );

  static const bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.3,
    fontFamily: fontFamilySecondary,
    color: AppColors.textSecondary,
  );

  // Button Text Styles
  static const buttonLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.5,
    fontFamily: fontFamily,
    color: Colors.white,
  );

  static const buttonMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.25,
    fontFamily: fontFamily,
    color: Colors.white,
  );

  static const buttonSmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.4,
    fontFamily: fontFamily,
    color: Colors.white,
  );

  // Label Styles
  static const labelLarge = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    fontFamily: fontFamily,
    color: AppColors.textPrimary,
  );

  static const labelMedium = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.5,
    fontFamily: fontFamily,
    color: AppColors.textSecondary,
  );

  // Caption and Helper Text
  static const caption = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    height: 1.3,
    fontFamily: fontFamilySecondary,
    color: AppColors.textSecondary,
  );

  static const overline = TextStyle(
    fontSize: 10,
    fontWeight: FontWeight.w500,
    letterSpacing: 1.5,
    fontFamily: fontFamily,
    color: AppColors.textSecondary,
  );

  // Special Styles
  static const premium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.w600,
    letterSpacing: 0.25,
    fontFamily: fontFamily,
    color: AppColors.accent,
  );

  static const error = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.4,
    fontFamily: fontFamilySecondary,
    color: AppColors.error,
  );
}

/// App Theme - Main theme configuration matching website design
/// Based on vaivahik-nextjs theme system with Material Design 3
class AppTheme {
  // Static getters for easy access to theme properties
  static Color get backgroundColor => AppColors.background;
  static Color get primaryColor => AppColors.primary;
  static Color get secondaryColor => AppColors.secondary;
  static Color get accentColor => AppColors.accent;
  static Color get textColor => AppColors.textPrimary;
  static Color get cardColor => AppColors.surface;

  // Gradient getters
  static LinearGradient get primaryGradient => AppGradients.primaryGradient;
  static LinearGradient get secondaryGradient => AppGradients.secondaryGradient;
  static LinearGradient get accentGradient => AppGradients.accentGradient;
  static LinearGradient get premiumGradient => AppGradients.premiumGradient;
  static LinearGradient get backgroundGradient => AppGradients.backgroundGradient;
  static LinearGradient get successGradient => const LinearGradient(
    begin: Alignment(-1.0, -1.0), // 135deg equivalent
    end: Alignment(1.0, 1.0),
    colors: [AppColors.success, AppColors.successLight],
  );

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Poppins',
      
      // Color Scheme - Website consistent colors
      colorScheme: const ColorScheme.light(
        primary: AppColors.primary, // FF5F6D - Coral pink
        primaryContainer: AppColors.primaryLight, // FFC371 - Light orange
        secondary: AppColors.secondary, // 4A00E0 - Purple
        secondaryContainer: AppColors.secondaryLight, // 8E2DE2 - Light purple
        tertiary: AppColors.accent, // FFD700 - Gold
        tertiaryContainer: AppColors.accentLight, // FFF2BF - Light gold
        surface: AppColors.surface, // FFFFFF - White
        surfaceContainerHighest: AppColors.backgroundAlt, // F0F2F5 - Light gray
        surfaceContainer: AppColors.background, // F8F9FA - Very light gray
        error: AppColors.error, // EF4444 - Red
        errorContainer: AppColors.errorLight, // F87171 - Light red
        onPrimary: Colors.white,
        onPrimaryContainer: AppColors.textPrimary,
        onSecondary: Colors.white,
        onSecondaryContainer: AppColors.textPrimary,
        onTertiary: AppColors.textPrimary,
        onTertiaryContainer: AppColors.textPrimary,
        onSurface: AppColors.textPrimary,
        onSurfaceVariant: AppColors.textSecondary,
        onError: Colors.white,
        onErrorContainer: AppColors.textPrimary,
        outline: AppColors.border,
        outlineVariant: AppColors.borderLight,
        shadow: AppColors.shadowMedium,
        scrim: AppColors.shadowHeavy,
        inverseSurface: AppColors.darkColor,
        onInverseSurface: AppColors.textLight,
        inversePrimary: AppColors.primaryLight,
      ),
      
      // App Bar Theme
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        centerTitle: true,
        titleTextStyle: AppTextStyles.h4,
        iconTheme: IconThemeData(color: AppColors.textPrimary),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),
      
      // Elevated Button Theme
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 2,
          shadowColor: AppColors.primary.withValues(alpha: 0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          textStyle: AppTextStyles.buttonLarge,
        ),
      ),
      
      // Text Button Theme
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: AppTextStyles.buttonMedium,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      
      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.textLight.withValues(alpha: 0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: AppColors.textLight.withValues(alpha: 0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.textLight),
        labelStyle: AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
      ),
      
      // Card Theme
      cardTheme: const CardThemeData(
        elevation: 2,
        shadowColor: Colors.black12,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
        color: AppColors.surface,
      ),
      
      // Bottom Navigation Bar Theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: AppColors.surface,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textLight,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      
      // Divider Theme
      dividerTheme: DividerThemeData(
        color: AppColors.textLight.withValues(alpha: 0.2),
        thickness: 1,
      ),
    );
  }
  
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      fontFamily: 'Poppins',
      brightness: Brightness.dark,
      
      // Color Scheme
      colorScheme: const ColorScheme.dark(
        primary: AppColors.primaryLight,
        primaryContainer: AppColors.primary,
        secondary: AppColors.accent,
        secondaryContainer: AppColors.accentDark,
        surface: AppColors.surfaceDark,
        error: AppColors.error,
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Colors.white,
        onError: Colors.white,
      ),
    );
  }
}
