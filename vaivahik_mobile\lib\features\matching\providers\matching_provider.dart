import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/api/api_client.dart';
import '../services/matching_service.dart';
import '../models/match_model.dart';

// API Client provider
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

// Service provider
final matchingServiceProvider = Provider<MatchingService>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return MatchingService(apiClient);
});

// Match suggestions state
class MatchSuggestionsState {
  final List<MatchModel> matches;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int currentPage;

  const MatchSuggestionsState({
    this.matches = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 1,
  });

  MatchSuggestionsState copyWith({
    List<MatchModel>? matches,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentPage,
  }) {
    return MatchSuggestionsState(
      matches: matches ?? this.matches,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

// Match suggestions provider
class MatchSuggestionsNotifier extends StateNotifier<MatchSuggestionsState> {
  final MatchingService _matchingService;

  MatchSuggestionsNotifier(this._matchingService) : super(const MatchSuggestionsState());

  Future<void> loadMatches({MatchFilters? filters, bool refresh = false}) async {
    if (refresh) {
      state = const MatchSuggestionsState(isLoading: true);
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final matches = await _matchingService.getMatchSuggestions(
        page: page,
        filters: filters,
      );

      if (refresh) {
        state = MatchSuggestionsState(
          matches: matches,
          isLoading: false,
          hasMore: matches.length >= 10,
          currentPage: 1,
        );
      } else {
        state = state.copyWith(
          matches: [...state.matches, ...matches],
          isLoading: false,
          hasMore: matches.length >= 10,
          currentPage: state.currentPage + 1,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> refreshMatches({MatchFilters? filters}) async {
    await loadMatches(filters: filters, refresh: true);
  }

  void removeMatch(String matchId) {
    final updatedMatches = state.matches.where((match) => match.id != matchId).toList();
    state = state.copyWith(matches: updatedMatches);
  }

  void updateMatch(MatchModel updatedMatch) {
    final updatedMatches = state.matches.map((match) {
      return match.id == updatedMatch.id ? updatedMatch : match;
    }).toList();
    state = state.copyWith(matches: updatedMatches);
  }
}

final matchSuggestionsProvider = StateNotifierProvider<MatchSuggestionsNotifier, MatchSuggestionsState>((ref) {
  final matchingService = ref.read(matchingServiceProvider);
  return MatchSuggestionsNotifier(matchingService);
});

// Daily recommendations provider
final dailyRecommendationsProvider = FutureProvider<List<MatchModel>>((ref) async {
  final matchingService = ref.read(matchingServiceProvider);
  return await matchingService.getDailyRecommendations();
});

// Premium matches provider
final premiumMatchesProvider = FutureProvider<List<MatchModel>>((ref) async {
  final matchingService = ref.read(matchingServiceProvider);
  return await matchingService.getPremiumMatches();
});

// Recently viewed provider
final recentlyViewedProvider = FutureProvider<List<MatchModel>>((ref) async {
  final matchingService = ref.read(matchingServiceProvider);
  return await matchingService.getRecentlyViewed();
});

// Profile by ID provider
final profileByIdProvider = FutureProvider.family<MatchModel, String>((ref, profileId) async {
  final matchingService = ref.read(matchingServiceProvider);
  return await matchingService.getProfileById(profileId);
});

// Interests state
class InterestsState {
  final List<InterestModel> receivedInterests;
  final List<InterestModel> sentInterests;
  final bool isLoading;
  final String? error;

  const InterestsState({
    this.receivedInterests = const [],
    this.sentInterests = const [],
    this.isLoading = false,
    this.error,
  });

  InterestsState copyWith({
    List<InterestModel>? receivedInterests,
    List<InterestModel>? sentInterests,
    bool? isLoading,
    String? error,
  }) {
    return InterestsState(
      receivedInterests: receivedInterests ?? this.receivedInterests,
      sentInterests: sentInterests ?? this.sentInterests,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Interests provider
class InterestsNotifier extends StateNotifier<InterestsState> {
  final MatchingService _matchingService;

  InterestsNotifier(this._matchingService) : super(const InterestsState());

  Future<void> loadReceivedInterests() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final interests = await _matchingService.getReceivedInterests();
      state = state.copyWith(
        receivedInterests: interests,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> loadSentInterests() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final interests = await _matchingService.getSentInterests();
      state = state.copyWith(
        sentInterests: interests,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> sendInterest(String profileId, {String? message}) async {
    try {
      final success = await _matchingService.sendInterest(profileId, message: message);
      if (success) {
        await loadSentInterests(); // Refresh sent interests
      }
      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> acceptInterest(String interestId) async {
    try {
      final success = await _matchingService.acceptInterest(interestId);
      if (success) {
        await loadReceivedInterests(); // Refresh received interests
      }
      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  Future<bool> declineInterest(String interestId) async {
    try {
      final success = await _matchingService.declineInterest(interestId);
      if (success) {
        await loadReceivedInterests(); // Refresh received interests
      }
      return success;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }
}

final interestsProvider = StateNotifierProvider<InterestsNotifier, InterestsState>((ref) {
  final matchingService = ref.read(matchingServiceProvider);
  return InterestsNotifier(matchingService);
});

// Shortlist provider
final shortlistProvider = FutureProvider<List<ShortlistModel>>((ref) async {
  final matchingService = ref.read(matchingServiceProvider);
  return await matchingService.getShortlistedProfiles();
});

// Match actions provider
class MatchActionsNotifier extends StateNotifier<bool> {
  final MatchingService _matchingService;

  MatchActionsNotifier(this._matchingService) : super(false);

  Future<bool> performAction(MatchAction action, String profileId, {String? message, String? reason}) async {
    state = true;
    try {
      bool success = false;
      
      switch (action) {
        case MatchAction.like:
          success = await _matchingService.likeProfile(profileId);
          break;
        case MatchAction.pass:
          success = await _matchingService.passProfile(profileId);
          break;
        case MatchAction.superLike:
        case MatchAction.interest:
          success = await _matchingService.sendInterest(profileId, message: message);
          break;
        case MatchAction.shortlist:
          success = await _matchingService.addToShortlist(profileId, note: message);
          break;
        case MatchAction.block:
          success = await _matchingService.blockProfile(profileId, reason: reason);
          break;
        case MatchAction.report:
          success = await _matchingService.reportProfile(profileId, reason ?? 'Inappropriate content', details: message);
          break;
      }
      
      state = false;
      return success;
    } catch (e) {
      state = false;
      rethrow;
    }
  }
}

final matchActionsProvider = StateNotifierProvider<MatchActionsNotifier, bool>((ref) {
  final matchingService = ref.read(matchingServiceProvider);
  return MatchActionsNotifier(matchingService);
});

// Match statistics provider
final matchStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final matchingService = ref.read(matchingServiceProvider);
  return await matchingService.getMatchStatistics();
});
