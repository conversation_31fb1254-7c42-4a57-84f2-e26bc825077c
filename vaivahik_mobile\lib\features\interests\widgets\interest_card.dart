import 'package:flutter/material.dart';

import '../../../app/theme.dart';
import '../models/interest_model.dart';

/// 💝 INTEREST CARD - Beautiful Interest Display Component
/// Features: Status Indicators, Action Buttons, Responsive Design

class InterestCard extends StatelessWidget {
  final InterestModel interest;
  final InterestType type;
  final VoidCallback? onTap;
  final Function(String response, String? message)? onRespond;
  final VoidCallback? onDelete;
  final bool showActions;
  final bool isCompact;

  const InterestCard({
    super.key,
    required this.interest,
    required this.type,
    this.onTap,
    this.onRespond,
    this.onDelete,
    this.showActions = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final user = type == InterestType.received ? interest.user : interest.targetUser;
    final status = InterestStatus.values.firstWhere(
      (s) => s.name.toLowerCase() == interest.status.toLowerCase(),
      orElse: () => InterestStatus.pending,
    );

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: AppShadows.cardShadow,
          border: Border.all(
            color: _getBorderColor(status),
            width: 1.5,
          ),
        ),
        child: Column(
          children: [
            _buildHeader(user, status),
            if (interest.message != null && interest.message!.isNotEmpty)
              _buildMessage(),
            if (interest.responseMessage != null && interest.responseMessage!.isNotEmpty)
              _buildResponseMessage(),
            if (showActions) _buildActions(status),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(UserProfileModel? user, InterestStatus status) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          _buildAvatar(user),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _getDisplayName(user),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    _buildStatusBadge(status),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _getUserDetails(user),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      type.icon,
                      size: 14,
                      color: type.color,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${type.displayName} • ${InterestHelper.formatTimeAgo(interest.createdAt)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(UserProfileModel? user) {
    return Stack(
      children: [
        CircleAvatar(
          radius: isCompact ? 20 : 25,
          backgroundColor: Colors.grey[300],
          backgroundImage: user?.profilePicUrl != null
              ? NetworkImage(user!.profilePicUrl!)
              : null,
          child: user?.profilePicUrl == null
              ? Icon(
                  Icons.person,
                  size: isCompact ? 20 : 25,
                  color: Colors.grey[600],
                )
              : null,
        ),
        if (user?.isOnline == true)
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: Colors.green,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
            ),
          ),
        if (user?.isPremium == true)
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.amber,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.star,
                size: 10,
                color: Colors.white,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildStatusBadge(InterestStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: status.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: status.color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            status.icon,
            size: 12,
            color: status.color,
          ),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: status.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessage() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.message,
                size: 14,
                color: AppColors.primary,
              ),
              SizedBox(width: 4),
              Text(
                'Message',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: AppColors.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            interest.message!,
            style: const TextStyle(
              fontSize: 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResponseMessage() {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.reply,
                size: 14,
                color: Colors.green,
              ),
              SizedBox(width: 4),
              Text(
                'Response',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            interest.responseMessage!,
            style: const TextStyle(
              fontSize: 13,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(InterestStatus status) {
    if (type == InterestType.received && status == InterestStatus.pending && onRespond != null) {
      return _buildRespondActions();
    } else if (type == InterestType.sent && status == InterestStatus.pending && onDelete != null) {
      return _buildDeleteAction();
    } else if (status == InterestStatus.accepted) {
      return _buildAcceptedActions();
    }
    return const SizedBox.shrink();
  }

  Widget _buildRespondActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _showRespondDialog('DECLINED'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[100],
                foregroundColor: Colors.grey[700],
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.close, size: 18),
              label: const Text('Decline'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _showRespondDialog('ACCEPTED'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.check, size: 18),
              label: const Text('Accept'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeleteAction() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: onDelete,
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.red,
            side: const BorderSide(color: Colors.red),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          icon: const Icon(Icons.delete_outline, size: 18),
          label: const Text('Withdraw Interest'),
        ),
      ),
    );
  }

  Widget _buildAcceptedActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () {}, // Navigate to chat
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.chat, size: 18),
              label: const Text('Start Chat'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {}, // View full profile
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: const BorderSide(color: AppColors.primary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              icon: const Icon(Icons.person, size: 18),
              label: const Text('View Profile'),
            ),
          ),
        ],
      ),
    );
  }

  void _showRespondDialog(String response) {
    // This would show a dialog to add optional message
    onRespond?.call(response, null);
  }

  String _getDisplayName(UserProfileModel? user) {
    if (user == null) return 'Unknown User';
    return InterestHelper.getFullName(user.firstName, user.lastName);
  }

  String _getUserDetails(UserProfileModel? user) {
    if (user == null) return '';
    
    final details = <String>[];
    
    if (user.age != null) {
      details.add('${user.age} years');
    }
    
    final location = InterestHelper.getLocationString(user.city, user.state);
    if (location != 'Location not specified') {
      details.add(location);
    }
    
    if (user.occupation != null && user.occupation!.isNotEmpty) {
      details.add(user.occupation!);
    }
    
    return details.join(' • ');
  }

  Color _getBorderColor(InterestStatus status) {
    switch (status) {
      case InterestStatus.pending:
        return Colors.orange.withValues(alpha: 0.3);
      case InterestStatus.accepted:
        return Colors.green.withValues(alpha: 0.3);
      case InterestStatus.declined:
        return Colors.red.withValues(alpha: 0.3);
      case InterestStatus.expired:
        return Colors.grey.withValues(alpha: 0.3);
    }
  }
}
