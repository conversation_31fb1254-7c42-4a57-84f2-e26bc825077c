import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../app/theme.dart';

/// ❌ CUSTOM ERROR WIDGET - Beautiful Error Display
/// Features: Multiple Error Types, Retry Actions, Animated

class CustomErrorWidget extends StatelessWidget {
  final String message;
  final String? title;
  final VoidCallback? onRetry;
  final ErrorType type;
  final IconData? customIcon;
  final Color? iconColor;
  final bool showRetryButton;

  const CustomErrorWidget({
    super.key,
    required this.message,
    this.title,
    this.onRetry,
    this.type = ErrorType.general,
    this.customIcon,
    this.iconColor,
    this.showRetryButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildErrorIcon(),
            const SizedBox(height: 16),
            _buildTitle(),
            const SizedBox(height: 8),
            _buildMessage(),
            if (showRetryButton && onRetry != null) ...[
              const SizedBox(height: 24),
              _buildRetryButton(),
            ],
          ],
        ),
      ),
    ).animate()
     .fadeIn(duration: 600.ms)
     .slideY(begin: 0.3, curve: Curves.easeOut);
  }

  Widget _buildErrorIcon() {
    final icon = customIcon ?? _getIconForType();
    final color = iconColor ?? _getColorForType();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        size: 48,
        color: color,
      ),
    ).animate()
     .scale(delay: 200.ms, curve: Curves.elasticOut);
  }

  Widget _buildTitle() {
    final titleText = title ?? _getTitleForType();
    
    return Text(
      titleText,
      style: const TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
      textAlign: TextAlign.center,
    ).animate()
     .slideX(begin: -0.3, delay: 400.ms, curve: Curves.easeOut);
  }

  Widget _buildMessage() {
    return Text(
      message,
      style: TextStyle(
        fontSize: 14,
        color: Colors.grey[600],
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    ).animate()
     .slideX(begin: 0.3, delay: 600.ms, curve: Curves.easeOut);
  }

  Widget _buildRetryButton() {
    return ElevatedButton.icon(
      onPressed: onRetry,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      icon: const Icon(Icons.refresh, size: 20),
      label: const Text(
        'Try Again',
        style: TextStyle(fontWeight: FontWeight.w600),
      ),
    ).animate()
     .scale(delay: 800.ms, curve: Curves.elasticOut);
  }

  IconData _getIconForType() {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.server:
        return Icons.cloud_off;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.permission:
        return Icons.lock;
      case ErrorType.validation:
        return Icons.warning;
      case ErrorType.timeout:
        return Icons.timer_off;
      case ErrorType.general:
        return Icons.error_outline;
    }
  }

  Color _getColorForType() {
    switch (type) {
      case ErrorType.network:
        return Colors.orange;
      case ErrorType.server:
        return Colors.red;
      case ErrorType.notFound:
        return Colors.blue;
      case ErrorType.permission:
        return Colors.purple;
      case ErrorType.validation:
        return Colors.amber;
      case ErrorType.timeout:
        return Colors.indigo;
      case ErrorType.general:
        return Colors.red;
    }
  }

  String _getTitleForType() {
    switch (type) {
      case ErrorType.network:
        return 'Connection Problem';
      case ErrorType.server:
        return 'Server Error';
      case ErrorType.notFound:
        return 'Not Found';
      case ErrorType.permission:
        return 'Access Denied';
      case ErrorType.validation:
        return 'Invalid Data';
      case ErrorType.timeout:
        return 'Request Timeout';
      case ErrorType.general:
        return 'Something Went Wrong';
    }
  }
}

/// Compact Error Widget for smaller spaces
class CompactErrorWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;

  const CompactErrorWidget({
    super.key,
    required this.message,
    this.onRetry,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(
            icon ?? Icons.error_outline,
            color: Colors.red,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.red,
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh, size: 20),
              color: Colors.red,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Error Banner Widget
class ErrorBanner extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;
  final VoidCallback? onRetry;
  final ErrorType type;

  const ErrorBanner({
    super.key,
    required this.message,
    this.onDismiss,
    this.onRetry,
    this.type = ErrorType.general,
  });

  @override
  Widget build(BuildContext context) {
    final color = _getColorForType();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        border: Border(
          left: BorderSide(color: color, width: 4),
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getIconForType(),
            color: color,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              message,
              style: TextStyle(
                fontSize: 14,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (onRetry != null) ...[
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh, size: 18),
              color: color,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
          if (onDismiss != null) ...[
            IconButton(
              onPressed: onDismiss,
              icon: const Icon(Icons.close, size: 18),
              color: color,
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
        ],
      ),
    ).animate()
     .slideY(begin: -1, curve: Curves.easeOut);
  }

  IconData _getIconForType() {
    switch (type) {
      case ErrorType.network:
        return Icons.wifi_off;
      case ErrorType.server:
        return Icons.cloud_off;
      case ErrorType.notFound:
        return Icons.search_off;
      case ErrorType.permission:
        return Icons.lock;
      case ErrorType.validation:
        return Icons.warning;
      case ErrorType.timeout:
        return Icons.timer_off;
      case ErrorType.general:
        return Icons.error_outline;
    }
  }

  Color _getColorForType() {
    switch (type) {
      case ErrorType.network:
        return Colors.orange;
      case ErrorType.server:
        return Colors.red;
      case ErrorType.notFound:
        return Colors.blue;
      case ErrorType.permission:
        return Colors.purple;
      case ErrorType.validation:
        return Colors.amber;
      case ErrorType.timeout:
        return Colors.indigo;
      case ErrorType.general:
        return Colors.red;
    }
  }
}

/// Error Dialog Widget
class ErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;

  const ErrorDialog({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.red,
            size: 24,
          ),
          const SizedBox(width: 8),
          Text(title),
        ],
      ),
      content: Text(message),
      actions: [
        if (onDismiss != null)
          TextButton(
            onPressed: onDismiss,
            child: const Text('Dismiss'),
          ),
        if (onRetry != null)
          ElevatedButton(
            onPressed: onRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Retry'),
          ),
      ],
    );
  }

  static Future<void> show(
    BuildContext context, {
    required String title,
    required String message,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
  }) {
    return showDialog(
      context: context,
      builder: (context) => ErrorDialog(
        title: title,
        message: message,
        onRetry: onRetry,
        onDismiss: onDismiss,
      ),
    );
  }
}

enum ErrorType {
  general,
  network,
  server,
  notFound,
  permission,
  validation,
  timeout,
}
