import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Box,
  Alert,
  Divider,
  Chip,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Analytics as AnalyticsIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import EnhancedAdminLayout from '../../components/admin/EnhancedAdminLayout';
import { toast } from 'react-toastify';

export default function KundaliSettings() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState({});
  const [testResults, setTestResults] = useState(null);

  useEffect(() => {
    fetchKundaliSettings();
  }, []);

  const fetchKundaliSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/kundali-settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSettings(data.settings || {});
      } else {
        toast.error('Failed to fetch kundali settings');
      }
    } catch (error) {
      console.error('Error fetching kundali settings:', error);
      toast.error('Error loading kundali settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const saveSettings = async () => {
    try {
      setSaving(true);
      const response = await fetch('/api/admin/kundali-settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({ settings })
      });

      if (response.ok) {
        toast.success('Kundali settings saved successfully!');
      } else {
        toast.error('Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Error saving settings');
    } finally {
      setSaving(false);
    }
  };

  const runSystemTest = async () => {
    try {
      setTestResults(null);
      const response = await fetch('/api/admin/kundali-test', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setTestResults(data);
        if (data.success) {
          toast.success('Kundali system test completed successfully!');
        } else {
          toast.warning('Some tests failed. Check results below.');
        }
      } else {
        toast.error('Failed to run system test');
      }
    } catch (error) {
      console.error('Error running test:', error);
      toast.error('Error running system test');
    }
  };

  if (loading) {
    return (
      <EnhancedAdminLayout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
            <CircularProgress />
          </Box>
        </Container>
      </EnhancedAdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <SettingsIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            <Box>
              <Typography variant="h4" gutterBottom>
                Kundali Matching Settings
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Configure and manage the Vedic astrology matching system
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Core Settings */}
        <Accordion defaultExpanded sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" gap={2}>
              <SecurityIcon color="primary" />
              <Typography variant="h6">Core Settings</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.kundali_matching_enabled === 'true'}
                          onChange={(e) => handleSettingChange('kundali_matching_enabled', e.target.checked.toString())}
                        />
                      }
                      label="Enable Kundali Matching"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Master switch for the entire kundali matching system
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.kundali_matching_premium_only === 'true'}
                          onChange={(e) => handleSettingChange('kundali_matching_premium_only', e.target.checked.toString())}
                        />
                      }
                      label="Premium Only"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Restrict kundali matching to premium users only
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={settings.kundali_matching_free_promotion === 'true'}
                          onChange={(e) => handleSettingChange('kundali_matching_free_promotion', e.target.checked.toString())}
                        />
                      }
                      label="Free Promotion Mode"
                    />
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Make kundali matching free for all users (promotional)
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <TextField
                      fullWidth
                      label="Daily Match Limit"
                      type="number"
                      value={settings.kundali_max_daily_matches || '10'}
                      onChange={(e) => handleSettingChange('kundali_max_daily_matches', e.target.value)}
                      helperText="Maximum kundali matches per user per day"
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* Feature Settings */}
        <Accordion sx={{ mb: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" gap={2}>
              <AnalyticsIcon color="primary" />
              <Typography variant="h6">Feature Settings</Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Grid container spacing={3}>
              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.kundali_ml_scoring_enabled === 'true'}
                      onChange={(e) => handleSettingChange('kundali_ml_scoring_enabled', e.target.checked.toString())}
                    />
                  }
                  label="ML Enhanced Scoring"
                />
              </Grid>
              
              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.kundali_detailed_analysis_enabled === 'true'}
                      onChange={(e) => handleSettingChange('kundali_detailed_analysis_enabled', e.target.checked.toString())}
                    />
                  }
                  label="Detailed Analysis"
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.kundali_remedies_enabled === 'true'}
                      onChange={(e) => handleSettingChange('kundali_remedies_enabled', e.target.checked.toString())}
                    />
                  }
                  label="Show Remedies"
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.kundali_chart_generation_enabled === 'true'}
                      onChange={(e) => handleSettingChange('kundali_chart_generation_enabled', e.target.checked.toString())}
                    />
                  }
                  label="Chart Generation"
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.kundali_auspicious_dates_enabled === 'true'}
                      onChange={(e) => handleSettingChange('kundali_auspicious_dates_enabled', e.target.checked.toString())}
                    />
                  }
                  label="Auspicious Dates"
                />
              </Grid>

              <Grid item xs={12} md={4}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.kundali_pdf_reports_enabled === 'true'}
                      onChange={(e) => handleSettingChange('kundali_pdf_reports_enabled', e.target.checked.toString())}
                    />
                  }
                  label="PDF Reports"
                />
              </Grid>
            </Grid>
          </AccordionDetails>
        </Accordion>

        {/* System Test */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
              <Typography variant="h6">System Test</Typography>
              <Button
                variant="contained"
                onClick={runSystemTest}
                startIcon={<ScheduleIcon />}
              >
                Run Test
              </Button>
            </Box>
            
            {testResults && (
              <Alert severity={testResults.success ? 'success' : 'warning'} sx={{ mt: 2 }}>
                <Typography variant="subtitle2">
                  Test Results: {testResults.passed}/{testResults.total} tests passed
                </Typography>
                <Typography variant="body2">
                  Success Rate: {testResults.successRate}%
                </Typography>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Save Button */}
        <Box display="flex" justifyContent="center">
          <Button
            variant="contained"
            size="large"
            onClick={saveSettings}
            disabled={saving}
            sx={{ minWidth: 200 }}
          >
            {saving ? <CircularProgress size={24} /> : 'Save Settings'}
          </Button>
        </Box>
      </Container>
    </EnhancedAdminLayout>
  );
}
