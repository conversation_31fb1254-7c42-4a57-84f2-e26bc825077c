// Seasonal Themes System for Vaivahik Website
import { useState, useEffect } from 'react';

// Theme definitions
const SEASONAL_THEMES = {
  spring: {
    name: 'Spring Bloom',
    period: { start: [3, 1], end: [5, 31] }, // March 1 - May 31
    colors: {
      primary: '#ff6b9d',
      secondary: '#c44569',
      accent: '#f8b500',
      background: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.9)',
      textPrimary: '#2d3436',
      textSecondary: '#636e72'
    },
    decorations: {
      particles: '🌸',
      headerIcon: '🌺',
      footerPattern: 'spring-flowers.svg'
    },
    animations: {
      entrance: 'fadeInUp',
      hover: 'bounce',
      background: 'floating-petals'
    }
  },
  
  summer: {
    name: 'Summer Vibes',
    period: { start: [6, 1], end: [8, 31] }, // June 1 - August 31
    colors: {
      primary: '#00b894',
      secondary: '#00a085',
      accent: '#fdcb6e',
      background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.95)',
      textPrimary: '#2d3436',
      textSecondary: '#636e72'
    },
    decorations: {
      particles: '☀️',
      headerIcon: '🏖️',
      footerPattern: 'summer-waves.svg'
    },
    animations: {
      entrance: 'slideInLeft',
      hover: 'pulse',
      background: 'floating-sun'
    }
  },
  
  monsoon: {
    name: 'Monsoon Magic',
    period: { start: [7, 1], end: [9, 30] }, // July 1 - September 30 (overlaps with summer)
    colors: {
      primary: '#6c5ce7',
      secondary: '#5f3dc4',
      accent: '#a29bfe',
      background: 'linear-gradient(135deg, #81ecec 0%, #74b9ff 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.9)',
      textPrimary: '#2d3436',
      textSecondary: '#636e72'
    },
    decorations: {
      particles: '🌧️',
      headerIcon: '☔',
      footerPattern: 'rain-drops.svg'
    },
    animations: {
      entrance: 'fadeInDown',
      hover: 'shake',
      background: 'falling-rain'
    }
  },
  
  autumn: {
    name: 'Autumn Warmth',
    period: { start: [9, 1], end: [11, 30] }, // September 1 - November 30
    colors: {
      primary: '#e17055',
      secondary: '#d63031',
      accent: '#fdcb6e',
      background: 'linear-gradient(135deg, #fab1a0 0%, #e17055 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.9)',
      textPrimary: '#2d3436',
      textSecondary: '#636e72'
    },
    decorations: {
      particles: '🍂',
      headerIcon: '🍁',
      footerPattern: 'autumn-leaves.svg'
    },
    animations: {
      entrance: 'rotateIn',
      hover: 'swing',
      background: 'falling-leaves'
    }
  },
  
  winter: {
    name: 'Winter Elegance',
    period: { start: [12, 1], end: [2, 28] }, // December 1 - February 28
    colors: {
      primary: '#74b9ff',
      secondary: '#0984e3',
      accent: '#fd79a8',
      background: 'linear-gradient(135deg, #ddd6fe 0%, #c7d2fe 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.95)',
      textPrimary: '#2d3436',
      textSecondary: '#636e72'
    },
    decorations: {
      particles: '❄️',
      headerIcon: '⛄',
      footerPattern: 'snowflakes.svg'
    },
    animations: {
      entrance: 'zoomIn',
      hover: 'tada',
      background: 'falling-snow'
    }
  },
  
  // Festival themes
  diwali: {
    name: 'Diwali Celebration',
    period: { start: [10, 15], end: [11, 15] }, // Mid October - Mid November
    colors: {
      primary: '#ff6b35',
      secondary: '#f7931e',
      accent: '#ffd700',
      background: 'linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.9)',
      textPrimary: '#2d3436',
      textSecondary: '#636e72'
    },
    decorations: {
      particles: '🪔',
      headerIcon: '✨',
      footerPattern: 'diwali-lights.svg'
    },
    animations: {
      entrance: 'lightSpeedIn',
      hover: 'flash',
      background: 'twinkling-lights'
    }
  },
  
  valentine: {
    name: 'Valentine Special',
    period: { start: [2, 1], end: [2, 28] }, // February
    colors: {
      primary: '#e84393',
      secondary: '#d63031',
      accent: '#fd79a8',
      background: 'linear-gradient(135deg, #fd79a8 0%, #e84393 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.9)',
      textPrimary: '#2d3436',
      textSecondary: '#636e72'
    },
    decorations: {
      particles: '💕',
      headerIcon: '💖',
      footerPattern: 'hearts.svg'
    },
    animations: {
      entrance: 'heartBeat',
      hover: 'pulse',
      background: 'floating-hearts'
    }
  },
  
  // Default theme
  default: {
    name: 'Classic Vaivahik',
    colors: {
      primary: '#667eea',
      secondary: '#764ba2',
      accent: '#f093fb',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      cardBackground: 'rgba(255, 255, 255, 0.1)',
      textPrimary: '#333333',
      textSecondary: '#666666'
    },
    decorations: {
      particles: '✨',
      headerIcon: '💫',
      footerPattern: 'default-pattern.svg'
    },
    animations: {
      entrance: 'fadeIn',
      hover: 'scale',
      background: 'subtle-glow'
    }
  }
};

// Hook for seasonal theme management
export const useSeasonalTheme = () => {
  const [currentTheme, setCurrentTheme] = useState('default');
  const [themeData, setThemeData] = useState(SEASONAL_THEMES.default);
  const [isAutoThemeEnabled, setIsAutoThemeEnabled] = useState(true);

  useEffect(() => {
    if (isAutoThemeEnabled) {
      const theme = getCurrentSeasonalTheme();
      setCurrentTheme(theme);
      setThemeData(SEASONAL_THEMES[theme]);
    }
  }, [isAutoThemeEnabled]);

  const setManualTheme = (themeName) => {
    setIsAutoThemeEnabled(false);
    setCurrentTheme(themeName);
    setThemeData(SEASONAL_THEMES[themeName] || SEASONAL_THEMES.default);
    
    // Save preference
    if (typeof window !== 'undefined') {
      localStorage.setItem('vaivahik_theme_preference', JSON.stringify({
        theme: themeName,
        isAuto: false
      }));
    }
  };

  const enableAutoTheme = () => {
    setIsAutoThemeEnabled(true);
    const theme = getCurrentSeasonalTheme();
    setCurrentTheme(theme);
    setThemeData(SEASONAL_THEMES[theme]);
    
    // Save preference
    if (typeof window !== 'undefined') {
      localStorage.setItem('vaivahik_theme_preference', JSON.stringify({
        theme: theme,
        isAuto: true
      }));
    }
  };

  return {
    currentTheme,
    themeData,
    isAutoThemeEnabled,
    setManualTheme,
    enableAutoTheme,
    availableThemes: Object.keys(SEASONAL_THEMES)
  };
};

// Get current seasonal theme based on date
export const getCurrentSeasonalTheme = () => {
  const now = new Date();
  const month = now.getMonth() + 1; // JavaScript months are 0-indexed
  const day = now.getDate();

  // Check festival themes first (they have priority)
  for (const [themeName, theme] of Object.entries(SEASONAL_THEMES)) {
    if (theme.period && isDateInPeriod(month, day, theme.period)) {
      // Special handling for overlapping periods
      if (themeName === 'diwali' || themeName === 'valentine') {
        return themeName;
      }
    }
  }

  // Check seasonal themes
  for (const [themeName, theme] of Object.entries(SEASONAL_THEMES)) {
    if (theme.period && isDateInPeriod(month, day, theme.period)) {
      return themeName;
    }
  }

  return 'default';
};

// Check if current date is in theme period
const isDateInPeriod = (month, day, period) => {
  const [startMonth, startDay] = period.start;
  const [endMonth, endDay] = period.end;

  // Handle year-crossing periods (like winter)
  if (startMonth > endMonth) {
    return (month > startMonth || month < endMonth) ||
           (month === startMonth && day >= startDay) ||
           (month === endMonth && day <= endDay);
  }

  // Normal periods within same year
  return (month > startMonth || (month === startMonth && day >= startDay)) &&
         (month < endMonth || (month === endMonth && day <= endDay));
};

// Apply theme to document
export const applyThemeToDocument = (themeData) => {
  if (typeof document === 'undefined') return;

  const root = document.documentElement;
  
  // Apply CSS custom properties
  Object.entries(themeData.colors).forEach(([key, value]) => {
    root.style.setProperty(`--theme-${key}`, value);
  });

  // Add theme class to body
  document.body.className = document.body.className.replace(/theme-\w+/g, '');
  document.body.classList.add(`theme-${getCurrentSeasonalTheme()}`);
};

// Generate theme CSS
export const generateThemeCSS = (themeData) => {
  return `
    :root {
      --theme-primary: ${themeData.colors.primary};
      --theme-secondary: ${themeData.colors.secondary};
      --theme-accent: ${themeData.colors.accent};
      --theme-background: ${themeData.colors.background};
      --theme-card-background: ${themeData.colors.cardBackground};
      --theme-text-primary: ${themeData.colors.textPrimary};
      --theme-text-secondary: ${themeData.colors.textSecondary};
    }

    .hero-section {
      background: var(--theme-background);
    }

    .card, .feature-card, .pricing-card {
      background: var(--theme-card-background);
      backdrop-filter: blur(20px);
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--theme-primary), var(--theme-secondary));
    }

    .btn-primary:hover {
      background: linear-gradient(135deg, var(--theme-secondary), var(--theme-primary));
    }

    .text-primary {
      color: var(--theme-text-primary);
    }

    .text-secondary {
      color: var(--theme-text-secondary);
    }

    /* Theme-specific animations */
    .theme-spring .floating-element {
      animation: floatingPetals 6s ease-in-out infinite;
    }

    .theme-summer .floating-element {
      animation: floatingSun 8s ease-in-out infinite;
    }

    .theme-monsoon .floating-element {
      animation: fallingRain 4s linear infinite;
    }

    .theme-autumn .floating-element {
      animation: fallingLeaves 10s ease-in-out infinite;
    }

    .theme-winter .floating-element {
      animation: fallingSnow 12s linear infinite;
    }

    .theme-diwali .floating-element {
      animation: twinklingLights 2s ease-in-out infinite alternate;
    }

    .theme-valentine .floating-element {
      animation: floatingHearts 5s ease-in-out infinite;
    }

    /* Keyframe animations */
    @keyframes floatingPetals {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(180deg); }
    }

    @keyframes floatingSun {
      0%, 100% { transform: translateY(0px) scale(1); }
      50% { transform: translateY(-15px) scale(1.1); }
    }

    @keyframes fallingRain {
      0% { transform: translateY(-100vh) rotate(0deg); opacity: 0; }
      10% { opacity: 1; }
      90% { opacity: 1; }
      100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
    }

    @keyframes fallingLeaves {
      0% { transform: translateY(-100vh) rotate(0deg); }
      100% { transform: translateY(100vh) rotate(720deg); }
    }

    @keyframes fallingSnow {
      0% { transform: translateY(-100vh) rotate(0deg); opacity: 0; }
      10% { opacity: 1; }
      90% { opacity: 1; }
      100% { transform: translateY(100vh) rotate(180deg); opacity: 0; }
    }

    @keyframes twinklingLights {
      0% { opacity: 0.5; transform: scale(1); }
      100% { opacity: 1; transform: scale(1.2); }
    }

    @keyframes floatingHearts {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      25% { transform: translateY(-10px) rotate(5deg); }
      75% { transform: translateY(-5px) rotate(-5deg); }
    }
  `;
};

// Theme selector component
export const ThemeSelector = ({ currentTheme, onThemeChange, isAutoEnabled, onAutoToggle }) => {
  return (
    <div className="theme-selector">
      <div className="auto-theme-toggle">
        <label>
          <input
            type="checkbox"
            checked={isAutoEnabled}
            onChange={onAutoToggle}
          />
          Auto Seasonal Theme
        </label>
      </div>
      
      {!isAutoEnabled && (
        <div className="manual-theme-selector">
          <select value={currentTheme} onChange={(e) => onThemeChange(e.target.value)}>
            {Object.entries(SEASONAL_THEMES).map(([key, theme]) => (
              <option key={key} value={key}>
                {theme.name}
              </option>
            ))}
          </select>
        </div>
      )}
    </div>
  );
};

export default SEASONAL_THEMES;
