import 'package:flutter/material.dart';
import '../animations/app_animations.dart';
import '../responsive/responsive_helper.dart';
import '../../app/theme.dart';

/// Enhanced UI Components - World-class design matching website
/// Based on vaivahik-nextjs components with modern Flutter implementation
class EnhancedCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final bool isPremium;
  final bool isElevated;
  final VoidCallback? onTap;
  final bool showShimmer;
  
  const EnhancedCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.isPremium = false,
    this.isElevated = false,
    this.onTap,
    this.showShimmer = false,
  });
  
  @override
  Widget build(BuildContext context) {
    Widget card = Container(
      margin: margin ?? ResponsiveHelper.responsiveMargin(context),
      decoration: BoxDecoration(
        gradient: isPremium ? AppGradients.premiumGradient : null,
        color: isPremium ? null : AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: isElevated ? AppShadows.elevatedShadow : AppShadows.cardShadow,
        border: isPremium ? Border.all(
          color: AppColors.accent.withValues(alpha: 0.3),
          width: 1,
        ) : null,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: padding ?? ResponsiveHelper.responsivePadding(context),
            child: child,
          ),
        ),
      ),
    );
    
    if (showShimmer) {
      card = AppAnimations.shimmerLoading(child: card);
    }
    
    return card;
  }
}

/// Enhanced Button - Premium design with animations
class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final Widget? icon;
  final bool isFullWidth;
  
  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.icon,
    this.isFullWidth = false,
  });
  
  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppAnimations.fast,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            width: widget.isFullWidth ? double.infinity : null,
            height: _getButtonHeight(),
            decoration: BoxDecoration(
              gradient: _getButtonGradient(),
              borderRadius: BorderRadius.circular(12),
              boxShadow: widget.type == ButtonType.primary
                  ? AppShadows.buttonShadow
                  : null,
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              child: InkWell(
                onTap: widget.isLoading ? null : () {
                  _controller.forward().then((_) {
                    _controller.reverse();
                    widget.onPressed?.call();
                  });
                },
                onTapDown: (_) => _controller.forward(),
                onTapUp: (_) => _controller.reverse(),
                onTapCancel: () => _controller.reverse(),
                borderRadius: BorderRadius.circular(12),
                child: Center(
                  child: widget.isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              _getButtonTextColor(),
                            ),
                          ),
                        )
                      : Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (widget.icon != null) ...[
                              widget.icon!,
                              const SizedBox(width: 8),
                            ],
                            Text(
                              widget.text,
                              style: _getButtonTextStyle(),
                            ),
                          ],
                        ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
  
  double _getButtonHeight() {
    switch (widget.size) {
      case ButtonSize.small:
        return 40;
      case ButtonSize.medium:
        return 48;
      case ButtonSize.large:
        return 56;
    }
  }
  
  LinearGradient _getButtonGradient() {
    switch (widget.type) {
      case ButtonType.primary:
        return AppGradients.primaryGradient;
      case ButtonType.secondary:
        return AppGradients.secondaryGradient;
      case ButtonType.accent:
        return AppGradients.accentGradient;
      case ButtonType.outline:
        return const LinearGradient(colors: [Colors.transparent, Colors.transparent]);
    }
  }
  
  Color _getButtonTextColor() {
    switch (widget.type) {
      case ButtonType.primary:
      case ButtonType.secondary:
      case ButtonType.accent:
        return Colors.white;
      case ButtonType.outline:
        return AppColors.primary;
    }
  }
  
  TextStyle _getButtonTextStyle() {
    TextStyle baseStyle;
    switch (widget.size) {
      case ButtonSize.small:
        baseStyle = AppTextStyles.buttonSmall;
        break;
      case ButtonSize.medium:
        baseStyle = AppTextStyles.buttonMedium;
        break;
      case ButtonSize.large:
        baseStyle = AppTextStyles.buttonLarge;
        break;
    }
    
    return baseStyle.copyWith(color: _getButtonTextColor());
  }
}

/// Enhanced Input Field - Modern design with animations
class EnhancedTextField extends StatefulWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final bool isPassword;
  final TextInputType keyboardType;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool isRequired;
  
  const EnhancedTextField({
    super.key,
    this.label,
    this.hint,
    this.controller,
    this.isPassword = false,
    this.keyboardType = TextInputType.text,
    this.validator,
    this.onChanged,
    this.prefixIcon,
    this.suffixIcon,
    this.isRequired = false,
  });
  
  @override
  State<EnhancedTextField> createState() => _EnhancedTextFieldState();
}

class _EnhancedTextFieldState extends State<EnhancedTextField>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Color?> _borderColorAnimation;
  bool _obscureText = true;
  
  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: AppAnimations.fast,
      vsync: this,
    );
    _borderColorAnimation = ColorTween(
      begin: AppColors.border,
      end: AppColors.primary,
    ).animate(_controller);
    _obscureText = widget.isPassword;
  }
  
  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          RichText(
            text: TextSpan(
              text: widget.label!,
              style: AppTextStyles.labelLarge,
              children: widget.isRequired
                  ? [
                      const TextSpan(
                        text: ' *',
                        style: TextStyle(color: AppColors.error),
                      ),
                    ]
                  : null,
            ),
          ),
          const SizedBox(height: 8),
        ],
        AnimatedBuilder(
          animation: _borderColorAnimation,
          builder: (context, child) {
            return TextFormField(
              controller: widget.controller,
              obscureText: widget.isPassword ? _obscureText : false,
              keyboardType: widget.keyboardType,
              validator: widget.validator,
              onChanged: widget.onChanged,
              onTap: () {
                _controller.forward();
              },
              onTapOutside: (_) {
                _controller.reverse();
              },
              style: AppTextStyles.bodyLarge,
              decoration: InputDecoration(
                hintText: widget.hint,
                hintStyle: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                prefixIcon: widget.prefixIcon,
                suffixIcon: widget.isPassword
                    ? IconButton(
                        icon: Icon(
                          _obscureText ? Icons.visibility : Icons.visibility_off,
                          color: AppColors.textSecondary,
                        ),
                        onPressed: () {
                          setState(() => _obscureText = !_obscureText);
                        },
                      )
                    : widget.suffixIcon,
                filled: true,
                fillColor: AppColors.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.border),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.border),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: _borderColorAnimation.value ?? AppColors.primary,
                    width: 2,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.error),
                ),
                focusedErrorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.error, width: 2),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}

// Enums
enum ButtonType { primary, secondary, accent, outline }
enum ButtonSize { small, medium, large }
