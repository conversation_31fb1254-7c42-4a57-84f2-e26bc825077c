import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../app/theme.dart';

import '../models/content_model.dart';
import '../providers/content_provider.dart';
import '../widgets/faq_widget.dart';
import '../widgets/chatbot_widget.dart';

/// 📄 CONTENT MANAGEMENT SYSTEM - Dynamic Content Pages
/// Features: Privacy Policy, Terms & Conditions, FAQ, Chatbot Support
/// Uses existing website backend API: /api/content/:key

class ContentScreen extends ConsumerStatefulWidget {
  final String contentType;
  
  const ContentScreen({
    super.key,
    required this.contentType,
  });

  @override
  ConsumerState<ContentScreen> createState() => _ContentScreenState();
}

class _ContentScreenState extends ConsumerState<ContentScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isLoading = true;
  ContentModel? _contentData;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadContent();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadContent() async {
    try {
      setState(() {
        _isLoading = true;
        _error = '';
      });

      // Use existing website API endpoint
      final response = await ref.read(apiClientProvider).get(
        '/content/${widget.contentType}',
      );

      if (response['success'] == true && response['page'] != null) {
        setState(() {
          _contentData = ContentModel.fromJson(response['page']);
          _isLoading = false;
        });
        _animationController.forward();
      } else {
        throw Exception(response['message'] ?? 'Failed to load content');
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
      // Load fallback content
      _loadFallbackContent();
    }
  }

  void _loadFallbackContent() {
    setState(() {
      _contentData = ContentModel(
        key: widget.contentType,
        title: _getTitle(),
        content: _getFallbackContent(),
        metaTitle: _getTitle(),
        metaDescription: 'Vaivahik ${_getTitle()}',
        updatedAt: DateTime.now(),
      );
    });
    _animationController.forward();
  }

  String _getTitle() {
    switch (widget.contentType) {
      case 'privacy-policy':
        return 'Privacy Policy';
      case 'terms-of-service':
        return 'Terms & Conditions';
      case 'refund-policy':
        return 'Refund Policy';
      case 'faq':
        return 'Frequently Asked Questions';
      case 'about-us':
        return 'About Vaivahik';
      case 'contact-us':
        return 'Contact Us';
      default:
        return 'Content';
    }
  }

  String _getFallbackContent() {
    switch (widget.contentType) {
      case 'privacy-policy':
        return '''
        <h2>Privacy Policy</h2>
        <p>At Vaivahik, we are committed to protecting your privacy and personal information.</p>
        
        <h3>Information We Collect</h3>
        <p>We collect information you provide directly to us, such as when you create an account, complete your profile, or contact us.</p>
        
        <h3>How We Use Your Information</h3>
        <p>We use the information we collect to provide, maintain, and improve our services, including matching you with compatible profiles.</p>
        
        <h3>Information Sharing</h3>
        <p>We do not sell, trade, or otherwise transfer your personal information to third parties without your consent.</p>
        
        <h3>Data Security</h3>
        <p>We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.</p>
        
        <h3>Contact Us</h3>
        <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL></p>
        ''';
        
      case 'terms-of-service':
        return '''
        <h2>Terms of Service</h2>
        <p>Welcome to Vaivahik. By using our service, you agree to these terms.</p>
        
        <h3>Acceptance of Terms</h3>
        <p>By accessing and using Vaivahik, you accept and agree to be bound by the terms and provision of this agreement.</p>
        
        <h3>User Responsibilities</h3>
        <p>You are responsible for maintaining the confidentiality of your account and password and for restricting access to your account.</p>
        
        <h3>Prohibited Uses</h3>
        <p>You may not use our service for any unlawful purpose or to solicit others to perform unlawful acts.</p>
        
        <h3>Privacy Policy</h3>
        <p>Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the service.</p>
        
        <h3>Termination</h3>
        <p>We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability.</p>
        ''';
        
      case 'refund-policy':
        return '''
        <h2>Refund Policy</h2>
        <p>We want you to be satisfied with your Vaivahik experience.</p>
        
        <h3>Premium Subscription Refunds</h3>
        <p>Premium subscriptions are eligible for refund within 7 days of purchase if no premium features have been used.</p>
        
        <h3>Biodata Template Refunds</h3>
        <p>Biodata template purchases are non-refundable once downloaded.</p>
        
        <h3>Refund Process</h3>
        <p>To request a refund, contact our support <NAME_EMAIL> with your order details.</p>
        
        <h3>Processing Time</h3>
        <p>Approved refunds will be processed within 5-7 business days to your original payment method.</p>
        ''';
        
      default:
        return '<p>Content not available at the moment. Please try again later.</p>';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(_contentData?.title ?? _getTitle()),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          if (widget.contentType == 'faq')
            IconButton(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  backgroundColor: Colors.transparent,
                  builder: (context) => const ChatbotWidget(),
                );
              },
              icon: const Icon(Icons.chat),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error.isNotEmpty
              ? _buildErrorWidget()
              : _buildContent(),
      floatingActionButton: widget.contentType == 'faq'
          ? const FloatingChatbotButton()
          : null,
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load content',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _error,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadContent,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (widget.contentType == 'faq') {
      return const SingleChildScrollView(
        padding: EdgeInsets.all(20),
        child: Column(
          children: [
            FAQSearchWidget(),
            SizedBox(height: 24),
            FAQWidget(),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_contentData != null) ...[
            Text(
              _contentData!.title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ).animate().fadeIn().slideY(begin: -0.3),
            const SizedBox(height: 8),
            Text(
              'Last updated: ${_formatDate(_contentData!.updatedAt)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
            const SizedBox(height: 24),
            Html(
              data: _contentData!.content,
              style: {
                'body': Style(
                  fontSize: FontSize(16),
                  lineHeight: const LineHeight(1.6),
                  color: Colors.black87,
                ),
                'h2': Style(
                  fontSize: FontSize(20),
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                  margin: Margins.only(top: 24, bottom: 12),
                ),
                'h3': Style(
                  fontSize: FontSize(18),
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                  margin: Margins.only(top: 20, bottom: 8),
                ),
                'p': Style(
                  margin: Margins.only(bottom: 16),
                ),
              },
              onLinkTap: (url, _, __) {
                if (url != null) {
                  launchUrl(Uri.parse(url));
                }
              },
            ).animate().fadeIn(delay: const Duration(milliseconds: 400)),
          ],
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
