// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'verification_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$VerificationDocument {
  String get id;
  String get userId;
  DocumentType get type;
  String get url;
  String get filename;
  int get filesize;
  String get mimeType;
  DocumentStatus get status;
  String? get adminNotes;
  DateTime get uploadedAt;
  DateTime? get reviewedAt;
  String? get reviewedBy;

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VerificationDocumentCopyWith<VerificationDocument> get copyWith =>
      _$VerificationDocumentCopyWithImpl<VerificationDocument>(
          this as VerificationDocument, _$identity);

  /// Serializes this VerificationDocument to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VerificationDocument &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.filename, filename) ||
                other.filename == filename) &&
            (identical(other.filesize, filesize) ||
                other.filesize == filesize) &&
            (identical(other.mimeType, mimeType) ||
                other.mimeType == mimeType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.adminNotes, adminNotes) ||
                other.adminNotes == adminNotes) &&
            (identical(other.uploadedAt, uploadedAt) ||
                other.uploadedAt == uploadedAt) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      type,
      url,
      filename,
      filesize,
      mimeType,
      status,
      adminNotes,
      uploadedAt,
      reviewedAt,
      reviewedBy);

  @override
  String toString() {
    return 'VerificationDocument(id: $id, userId: $userId, type: $type, url: $url, filename: $filename, filesize: $filesize, mimeType: $mimeType, status: $status, adminNotes: $adminNotes, uploadedAt: $uploadedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy)';
  }
}

/// @nodoc
abstract mixin class $VerificationDocumentCopyWith<$Res> {
  factory $VerificationDocumentCopyWith(VerificationDocument value,
          $Res Function(VerificationDocument) _then) =
      _$VerificationDocumentCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      DocumentType type,
      String url,
      String filename,
      int filesize,
      String mimeType,
      DocumentStatus status,
      String? adminNotes,
      DateTime uploadedAt,
      DateTime? reviewedAt,
      String? reviewedBy});
}

/// @nodoc
class _$VerificationDocumentCopyWithImpl<$Res>
    implements $VerificationDocumentCopyWith<$Res> {
  _$VerificationDocumentCopyWithImpl(this._self, this._then);

  final VerificationDocument _self;
  final $Res Function(VerificationDocument) _then;

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? url = null,
    Object? filename = null,
    Object? filesize = null,
    Object? mimeType = null,
    Object? status = null,
    Object? adminNotes = freezed,
    Object? uploadedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      url: null == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      filename: null == filename
          ? _self.filename
          : filename // ignore: cast_nullable_to_non_nullable
              as String,
      filesize: null == filesize
          ? _self.filesize
          : filesize // ignore: cast_nullable_to_non_nullable
              as int,
      mimeType: null == mimeType
          ? _self.mimeType
          : mimeType // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as DocumentStatus,
      adminNotes: freezed == adminNotes
          ? _self.adminNotes
          : adminNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      uploadedAt: null == uploadedAt
          ? _self.uploadedAt
          : uploadedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      reviewedAt: freezed == reviewedAt
          ? _self.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reviewedBy: freezed == reviewedBy
          ? _self.reviewedBy
          : reviewedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [VerificationDocument].
extension VerificationDocumentPatterns on VerificationDocument {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VerificationDocument value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerificationDocument() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VerificationDocument value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationDocument():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VerificationDocument value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationDocument() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            DocumentType type,
            String url,
            String filename,
            int filesize,
            String mimeType,
            DocumentStatus status,
            String? adminNotes,
            DateTime uploadedAt,
            DateTime? reviewedAt,
            String? reviewedBy)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerificationDocument() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.type,
            _that.url,
            _that.filename,
            _that.filesize,
            _that.mimeType,
            _that.status,
            _that.adminNotes,
            _that.uploadedAt,
            _that.reviewedAt,
            _that.reviewedBy);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            DocumentType type,
            String url,
            String filename,
            int filesize,
            String mimeType,
            DocumentStatus status,
            String? adminNotes,
            DateTime uploadedAt,
            DateTime? reviewedAt,
            String? reviewedBy)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationDocument():
        return $default(
            _that.id,
            _that.userId,
            _that.type,
            _that.url,
            _that.filename,
            _that.filesize,
            _that.mimeType,
            _that.status,
            _that.adminNotes,
            _that.uploadedAt,
            _that.reviewedAt,
            _that.reviewedBy);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String userId,
            DocumentType type,
            String url,
            String filename,
            int filesize,
            String mimeType,
            DocumentStatus status,
            String? adminNotes,
            DateTime uploadedAt,
            DateTime? reviewedAt,
            String? reviewedBy)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationDocument() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.type,
            _that.url,
            _that.filename,
            _that.filesize,
            _that.mimeType,
            _that.status,
            _that.adminNotes,
            _that.uploadedAt,
            _that.reviewedAt,
            _that.reviewedBy);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _VerificationDocument implements VerificationDocument {
  const _VerificationDocument(
      {required this.id,
      required this.userId,
      required this.type,
      required this.url,
      required this.filename,
      required this.filesize,
      required this.mimeType,
      required this.status,
      this.adminNotes,
      required this.uploadedAt,
      this.reviewedAt,
      this.reviewedBy});
  factory _VerificationDocument.fromJson(Map<String, dynamic> json) =>
      _$VerificationDocumentFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final DocumentType type;
  @override
  final String url;
  @override
  final String filename;
  @override
  final int filesize;
  @override
  final String mimeType;
  @override
  final DocumentStatus status;
  @override
  final String? adminNotes;
  @override
  final DateTime uploadedAt;
  @override
  final DateTime? reviewedAt;
  @override
  final String? reviewedBy;

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$VerificationDocumentCopyWith<_VerificationDocument> get copyWith =>
      __$VerificationDocumentCopyWithImpl<_VerificationDocument>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$VerificationDocumentToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _VerificationDocument &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.url, url) || other.url == url) &&
            (identical(other.filename, filename) ||
                other.filename == filename) &&
            (identical(other.filesize, filesize) ||
                other.filesize == filesize) &&
            (identical(other.mimeType, mimeType) ||
                other.mimeType == mimeType) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.adminNotes, adminNotes) ||
                other.adminNotes == adminNotes) &&
            (identical(other.uploadedAt, uploadedAt) ||
                other.uploadedAt == uploadedAt) &&
            (identical(other.reviewedAt, reviewedAt) ||
                other.reviewedAt == reviewedAt) &&
            (identical(other.reviewedBy, reviewedBy) ||
                other.reviewedBy == reviewedBy));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      type,
      url,
      filename,
      filesize,
      mimeType,
      status,
      adminNotes,
      uploadedAt,
      reviewedAt,
      reviewedBy);

  @override
  String toString() {
    return 'VerificationDocument(id: $id, userId: $userId, type: $type, url: $url, filename: $filename, filesize: $filesize, mimeType: $mimeType, status: $status, adminNotes: $adminNotes, uploadedAt: $uploadedAt, reviewedAt: $reviewedAt, reviewedBy: $reviewedBy)';
  }
}

/// @nodoc
abstract mixin class _$VerificationDocumentCopyWith<$Res>
    implements $VerificationDocumentCopyWith<$Res> {
  factory _$VerificationDocumentCopyWith(_VerificationDocument value,
          $Res Function(_VerificationDocument) _then) =
      __$VerificationDocumentCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      DocumentType type,
      String url,
      String filename,
      int filesize,
      String mimeType,
      DocumentStatus status,
      String? adminNotes,
      DateTime uploadedAt,
      DateTime? reviewedAt,
      String? reviewedBy});
}

/// @nodoc
class __$VerificationDocumentCopyWithImpl<$Res>
    implements _$VerificationDocumentCopyWith<$Res> {
  __$VerificationDocumentCopyWithImpl(this._self, this._then);

  final _VerificationDocument _self;
  final $Res Function(_VerificationDocument) _then;

  /// Create a copy of VerificationDocument
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? url = null,
    Object? filename = null,
    Object? filesize = null,
    Object? mimeType = null,
    Object? status = null,
    Object? adminNotes = freezed,
    Object? uploadedAt = null,
    Object? reviewedAt = freezed,
    Object? reviewedBy = freezed,
  }) {
    return _then(_VerificationDocument(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      url: null == url
          ? _self.url
          : url // ignore: cast_nullable_to_non_nullable
              as String,
      filename: null == filename
          ? _self.filename
          : filename // ignore: cast_nullable_to_non_nullable
              as String,
      filesize: null == filesize
          ? _self.filesize
          : filesize // ignore: cast_nullable_to_non_nullable
              as int,
      mimeType: null == mimeType
          ? _self.mimeType
          : mimeType // ignore: cast_nullable_to_non_nullable
              as String,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as DocumentStatus,
      adminNotes: freezed == adminNotes
          ? _self.adminNotes
          : adminNotes // ignore: cast_nullable_to_non_nullable
              as String?,
      uploadedAt: null == uploadedAt
          ? _self.uploadedAt
          : uploadedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      reviewedAt: freezed == reviewedAt
          ? _self.reviewedAt
          : reviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      reviewedBy: freezed == reviewedBy
          ? _self.reviewedBy
          : reviewedBy // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$DocumentUploadRequest {
  DocumentType get documentType;
  String get filePath;
  String get filename;

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DocumentUploadRequestCopyWith<DocumentUploadRequest> get copyWith =>
      _$DocumentUploadRequestCopyWithImpl<DocumentUploadRequest>(
          this as DocumentUploadRequest, _$identity);

  /// Serializes this DocumentUploadRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DocumentUploadRequest &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.filename, filename) ||
                other.filename == filename));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, documentType, filePath, filename);

  @override
  String toString() {
    return 'DocumentUploadRequest(documentType: $documentType, filePath: $filePath, filename: $filename)';
  }
}

/// @nodoc
abstract mixin class $DocumentUploadRequestCopyWith<$Res> {
  factory $DocumentUploadRequestCopyWith(DocumentUploadRequest value,
          $Res Function(DocumentUploadRequest) _then) =
      _$DocumentUploadRequestCopyWithImpl;
  @useResult
  $Res call({DocumentType documentType, String filePath, String filename});
}

/// @nodoc
class _$DocumentUploadRequestCopyWithImpl<$Res>
    implements $DocumentUploadRequestCopyWith<$Res> {
  _$DocumentUploadRequestCopyWithImpl(this._self, this._then);

  final DocumentUploadRequest _self;
  final $Res Function(DocumentUploadRequest) _then;

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? documentType = null,
    Object? filePath = null,
    Object? filename = null,
  }) {
    return _then(_self.copyWith(
      documentType: null == documentType
          ? _self.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      filePath: null == filePath
          ? _self.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      filename: null == filename
          ? _self.filename
          : filename // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [DocumentUploadRequest].
extension DocumentUploadRequestPatterns on DocumentUploadRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DocumentUploadRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DocumentUploadRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DocumentUploadRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            DocumentType documentType, String filePath, String filename)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadRequest() when $default != null:
        return $default(_that.documentType, _that.filePath, _that.filename);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            DocumentType documentType, String filePath, String filename)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadRequest():
        return $default(_that.documentType, _that.filePath, _that.filename);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            DocumentType documentType, String filePath, String filename)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadRequest() when $default != null:
        return $default(_that.documentType, _that.filePath, _that.filename);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DocumentUploadRequest implements DocumentUploadRequest {
  const _DocumentUploadRequest(
      {required this.documentType,
      required this.filePath,
      required this.filename});
  factory _DocumentUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentUploadRequestFromJson(json);

  @override
  final DocumentType documentType;
  @override
  final String filePath;
  @override
  final String filename;

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DocumentUploadRequestCopyWith<_DocumentUploadRequest> get copyWith =>
      __$DocumentUploadRequestCopyWithImpl<_DocumentUploadRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DocumentUploadRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DocumentUploadRequest &&
            (identical(other.documentType, documentType) ||
                other.documentType == documentType) &&
            (identical(other.filePath, filePath) ||
                other.filePath == filePath) &&
            (identical(other.filename, filename) ||
                other.filename == filename));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, documentType, filePath, filename);

  @override
  String toString() {
    return 'DocumentUploadRequest(documentType: $documentType, filePath: $filePath, filename: $filename)';
  }
}

/// @nodoc
abstract mixin class _$DocumentUploadRequestCopyWith<$Res>
    implements $DocumentUploadRequestCopyWith<$Res> {
  factory _$DocumentUploadRequestCopyWith(_DocumentUploadRequest value,
          $Res Function(_DocumentUploadRequest) _then) =
      __$DocumentUploadRequestCopyWithImpl;
  @override
  @useResult
  $Res call({DocumentType documentType, String filePath, String filename});
}

/// @nodoc
class __$DocumentUploadRequestCopyWithImpl<$Res>
    implements _$DocumentUploadRequestCopyWith<$Res> {
  __$DocumentUploadRequestCopyWithImpl(this._self, this._then);

  final _DocumentUploadRequest _self;
  final $Res Function(_DocumentUploadRequest) _then;

  /// Create a copy of DocumentUploadRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? documentType = null,
    Object? filePath = null,
    Object? filename = null,
  }) {
    return _then(_DocumentUploadRequest(
      documentType: null == documentType
          ? _self.documentType
          : documentType // ignore: cast_nullable_to_non_nullable
              as DocumentType,
      filePath: null == filePath
          ? _self.filePath
          : filePath // ignore: cast_nullable_to_non_nullable
              as String,
      filename: null == filename
          ? _self.filename
          : filename // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$DocumentUploadResponse {
  bool get success;
  String get message;
  VerificationDocument? get document;
  String? get error;

  /// Create a copy of DocumentUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DocumentUploadResponseCopyWith<DocumentUploadResponse> get copyWith =>
      _$DocumentUploadResponseCopyWithImpl<DocumentUploadResponse>(
          this as DocumentUploadResponse, _$identity);

  /// Serializes this DocumentUploadResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DocumentUploadResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.document, document) ||
                other.document == document) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, message, document, error);

  @override
  String toString() {
    return 'DocumentUploadResponse(success: $success, message: $message, document: $document, error: $error)';
  }
}

/// @nodoc
abstract mixin class $DocumentUploadResponseCopyWith<$Res> {
  factory $DocumentUploadResponseCopyWith(DocumentUploadResponse value,
          $Res Function(DocumentUploadResponse) _then) =
      _$DocumentUploadResponseCopyWithImpl;
  @useResult
  $Res call(
      {bool success,
      String message,
      VerificationDocument? document,
      String? error});

  $VerificationDocumentCopyWith<$Res>? get document;
}

/// @nodoc
class _$DocumentUploadResponseCopyWithImpl<$Res>
    implements $DocumentUploadResponseCopyWith<$Res> {
  _$DocumentUploadResponseCopyWithImpl(this._self, this._then);

  final DocumentUploadResponse _self;
  final $Res Function(DocumentUploadResponse) _then;

  /// Create a copy of DocumentUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? document = freezed,
    Object? error = freezed,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      document: freezed == document
          ? _self.document
          : document // ignore: cast_nullable_to_non_nullable
              as VerificationDocument?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of DocumentUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VerificationDocumentCopyWith<$Res>? get document {
    if (_self.document == null) {
      return null;
    }

    return $VerificationDocumentCopyWith<$Res>(_self.document!, (value) {
      return _then(_self.copyWith(document: value));
    });
  }
}

/// Adds pattern-matching-related methods to [DocumentUploadResponse].
extension DocumentUploadResponsePatterns on DocumentUploadResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DocumentUploadResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DocumentUploadResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DocumentUploadResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool success, String message,
            VerificationDocument? document, String? error)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadResponse() when $default != null:
        return $default(
            _that.success, _that.message, _that.document, _that.error);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool success, String message,
            VerificationDocument? document, String? error)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadResponse():
        return $default(
            _that.success, _that.message, _that.document, _that.error);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool success, String message,
            VerificationDocument? document, String? error)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentUploadResponse() when $default != null:
        return $default(
            _that.success, _that.message, _that.document, _that.error);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DocumentUploadResponse implements DocumentUploadResponse {
  const _DocumentUploadResponse(
      {required this.success,
      required this.message,
      this.document,
      this.error});
  factory _DocumentUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentUploadResponseFromJson(json);

  @override
  final bool success;
  @override
  final String message;
  @override
  final VerificationDocument? document;
  @override
  final String? error;

  /// Create a copy of DocumentUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DocumentUploadResponseCopyWith<_DocumentUploadResponse> get copyWith =>
      __$DocumentUploadResponseCopyWithImpl<_DocumentUploadResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DocumentUploadResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DocumentUploadResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.document, document) ||
                other.document == document) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, message, document, error);

  @override
  String toString() {
    return 'DocumentUploadResponse(success: $success, message: $message, document: $document, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$DocumentUploadResponseCopyWith<$Res>
    implements $DocumentUploadResponseCopyWith<$Res> {
  factory _$DocumentUploadResponseCopyWith(_DocumentUploadResponse value,
          $Res Function(_DocumentUploadResponse) _then) =
      __$DocumentUploadResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool success,
      String message,
      VerificationDocument? document,
      String? error});

  @override
  $VerificationDocumentCopyWith<$Res>? get document;
}

/// @nodoc
class __$DocumentUploadResponseCopyWithImpl<$Res>
    implements _$DocumentUploadResponseCopyWith<$Res> {
  __$DocumentUploadResponseCopyWithImpl(this._self, this._then);

  final _DocumentUploadResponse _self;
  final $Res Function(_DocumentUploadResponse) _then;

  /// Create a copy of DocumentUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? document = freezed,
    Object? error = freezed,
  }) {
    return _then(_DocumentUploadResponse(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      document: freezed == document
          ? _self.document
          : document // ignore: cast_nullable_to_non_nullable
              as VerificationDocument?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of DocumentUploadResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $VerificationDocumentCopyWith<$Res>? get document {
    if (_self.document == null) {
      return null;
    }

    return $VerificationDocumentCopyWith<$Res>(_self.document!, (value) {
      return _then(_self.copyWith(document: value));
    });
  }
}

/// @nodoc
mixin _$VerificationStatus {
  bool get isVerified;
  String get profileStatus;
  List<VerificationDocument> get documents;
  int get totalDocuments;
  int get approvedDocuments;
  int get pendingDocuments;
  int get rejectedDocuments;
  DateTime? get lastUploadedAt;
  DateTime? get lastReviewedAt;

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VerificationStatusCopyWith<VerificationStatus> get copyWith =>
      _$VerificationStatusCopyWithImpl<VerificationStatus>(
          this as VerificationStatus, _$identity);

  /// Serializes this VerificationStatus to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VerificationStatus &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.profileStatus, profileStatus) ||
                other.profileStatus == profileStatus) &&
            const DeepCollectionEquality().equals(other.documents, documents) &&
            (identical(other.totalDocuments, totalDocuments) ||
                other.totalDocuments == totalDocuments) &&
            (identical(other.approvedDocuments, approvedDocuments) ||
                other.approvedDocuments == approvedDocuments) &&
            (identical(other.pendingDocuments, pendingDocuments) ||
                other.pendingDocuments == pendingDocuments) &&
            (identical(other.rejectedDocuments, rejectedDocuments) ||
                other.rejectedDocuments == rejectedDocuments) &&
            (identical(other.lastUploadedAt, lastUploadedAt) ||
                other.lastUploadedAt == lastUploadedAt) &&
            (identical(other.lastReviewedAt, lastReviewedAt) ||
                other.lastReviewedAt == lastReviewedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isVerified,
      profileStatus,
      const DeepCollectionEquality().hash(documents),
      totalDocuments,
      approvedDocuments,
      pendingDocuments,
      rejectedDocuments,
      lastUploadedAt,
      lastReviewedAt);

  @override
  String toString() {
    return 'VerificationStatus(isVerified: $isVerified, profileStatus: $profileStatus, documents: $documents, totalDocuments: $totalDocuments, approvedDocuments: $approvedDocuments, pendingDocuments: $pendingDocuments, rejectedDocuments: $rejectedDocuments, lastUploadedAt: $lastUploadedAt, lastReviewedAt: $lastReviewedAt)';
  }
}

/// @nodoc
abstract mixin class $VerificationStatusCopyWith<$Res> {
  factory $VerificationStatusCopyWith(
          VerificationStatus value, $Res Function(VerificationStatus) _then) =
      _$VerificationStatusCopyWithImpl;
  @useResult
  $Res call(
      {bool isVerified,
      String profileStatus,
      List<VerificationDocument> documents,
      int totalDocuments,
      int approvedDocuments,
      int pendingDocuments,
      int rejectedDocuments,
      DateTime? lastUploadedAt,
      DateTime? lastReviewedAt});
}

/// @nodoc
class _$VerificationStatusCopyWithImpl<$Res>
    implements $VerificationStatusCopyWith<$Res> {
  _$VerificationStatusCopyWithImpl(this._self, this._then);

  final VerificationStatus _self;
  final $Res Function(VerificationStatus) _then;

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isVerified = null,
    Object? profileStatus = null,
    Object? documents = null,
    Object? totalDocuments = null,
    Object? approvedDocuments = null,
    Object? pendingDocuments = null,
    Object? rejectedDocuments = null,
    Object? lastUploadedAt = freezed,
    Object? lastReviewedAt = freezed,
  }) {
    return _then(_self.copyWith(
      isVerified: null == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      profileStatus: null == profileStatus
          ? _self.profileStatus
          : profileStatus // ignore: cast_nullable_to_non_nullable
              as String,
      documents: null == documents
          ? _self.documents
          : documents // ignore: cast_nullable_to_non_nullable
              as List<VerificationDocument>,
      totalDocuments: null == totalDocuments
          ? _self.totalDocuments
          : totalDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      approvedDocuments: null == approvedDocuments
          ? _self.approvedDocuments
          : approvedDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      pendingDocuments: null == pendingDocuments
          ? _self.pendingDocuments
          : pendingDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      rejectedDocuments: null == rejectedDocuments
          ? _self.rejectedDocuments
          : rejectedDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      lastUploadedAt: freezed == lastUploadedAt
          ? _self.lastUploadedAt
          : lastUploadedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastReviewedAt: freezed == lastReviewedAt
          ? _self.lastReviewedAt
          : lastReviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// Adds pattern-matching-related methods to [VerificationStatus].
extension VerificationStatusPatterns on VerificationStatus {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VerificationStatus value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerificationStatus() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VerificationStatus value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStatus():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VerificationStatus value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStatus() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool isVerified,
            String profileStatus,
            List<VerificationDocument> documents,
            int totalDocuments,
            int approvedDocuments,
            int pendingDocuments,
            int rejectedDocuments,
            DateTime? lastUploadedAt,
            DateTime? lastReviewedAt)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerificationStatus() when $default != null:
        return $default(
            _that.isVerified,
            _that.profileStatus,
            _that.documents,
            _that.totalDocuments,
            _that.approvedDocuments,
            _that.pendingDocuments,
            _that.rejectedDocuments,
            _that.lastUploadedAt,
            _that.lastReviewedAt);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool isVerified,
            String profileStatus,
            List<VerificationDocument> documents,
            int totalDocuments,
            int approvedDocuments,
            int pendingDocuments,
            int rejectedDocuments,
            DateTime? lastUploadedAt,
            DateTime? lastReviewedAt)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStatus():
        return $default(
            _that.isVerified,
            _that.profileStatus,
            _that.documents,
            _that.totalDocuments,
            _that.approvedDocuments,
            _that.pendingDocuments,
            _that.rejectedDocuments,
            _that.lastUploadedAt,
            _that.lastReviewedAt);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool isVerified,
            String profileStatus,
            List<VerificationDocument> documents,
            int totalDocuments,
            int approvedDocuments,
            int pendingDocuments,
            int rejectedDocuments,
            DateTime? lastUploadedAt,
            DateTime? lastReviewedAt)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStatus() when $default != null:
        return $default(
            _that.isVerified,
            _that.profileStatus,
            _that.documents,
            _that.totalDocuments,
            _that.approvedDocuments,
            _that.pendingDocuments,
            _that.rejectedDocuments,
            _that.lastUploadedAt,
            _that.lastReviewedAt);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _VerificationStatus implements VerificationStatus {
  const _VerificationStatus(
      {required this.isVerified,
      required this.profileStatus,
      required final List<VerificationDocument> documents,
      required this.totalDocuments,
      required this.approvedDocuments,
      required this.pendingDocuments,
      required this.rejectedDocuments,
      this.lastUploadedAt,
      this.lastReviewedAt})
      : _documents = documents;
  factory _VerificationStatus.fromJson(Map<String, dynamic> json) =>
      _$VerificationStatusFromJson(json);

  @override
  final bool isVerified;
  @override
  final String profileStatus;
  final List<VerificationDocument> _documents;
  @override
  List<VerificationDocument> get documents {
    if (_documents is EqualUnmodifiableListView) return _documents;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_documents);
  }

  @override
  final int totalDocuments;
  @override
  final int approvedDocuments;
  @override
  final int pendingDocuments;
  @override
  final int rejectedDocuments;
  @override
  final DateTime? lastUploadedAt;
  @override
  final DateTime? lastReviewedAt;

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$VerificationStatusCopyWith<_VerificationStatus> get copyWith =>
      __$VerificationStatusCopyWithImpl<_VerificationStatus>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$VerificationStatusToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _VerificationStatus &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.profileStatus, profileStatus) ||
                other.profileStatus == profileStatus) &&
            const DeepCollectionEquality()
                .equals(other._documents, _documents) &&
            (identical(other.totalDocuments, totalDocuments) ||
                other.totalDocuments == totalDocuments) &&
            (identical(other.approvedDocuments, approvedDocuments) ||
                other.approvedDocuments == approvedDocuments) &&
            (identical(other.pendingDocuments, pendingDocuments) ||
                other.pendingDocuments == pendingDocuments) &&
            (identical(other.rejectedDocuments, rejectedDocuments) ||
                other.rejectedDocuments == rejectedDocuments) &&
            (identical(other.lastUploadedAt, lastUploadedAt) ||
                other.lastUploadedAt == lastUploadedAt) &&
            (identical(other.lastReviewedAt, lastReviewedAt) ||
                other.lastReviewedAt == lastReviewedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isVerified,
      profileStatus,
      const DeepCollectionEquality().hash(_documents),
      totalDocuments,
      approvedDocuments,
      pendingDocuments,
      rejectedDocuments,
      lastUploadedAt,
      lastReviewedAt);

  @override
  String toString() {
    return 'VerificationStatus(isVerified: $isVerified, profileStatus: $profileStatus, documents: $documents, totalDocuments: $totalDocuments, approvedDocuments: $approvedDocuments, pendingDocuments: $pendingDocuments, rejectedDocuments: $rejectedDocuments, lastUploadedAt: $lastUploadedAt, lastReviewedAt: $lastReviewedAt)';
  }
}

/// @nodoc
abstract mixin class _$VerificationStatusCopyWith<$Res>
    implements $VerificationStatusCopyWith<$Res> {
  factory _$VerificationStatusCopyWith(
          _VerificationStatus value, $Res Function(_VerificationStatus) _then) =
      __$VerificationStatusCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool isVerified,
      String profileStatus,
      List<VerificationDocument> documents,
      int totalDocuments,
      int approvedDocuments,
      int pendingDocuments,
      int rejectedDocuments,
      DateTime? lastUploadedAt,
      DateTime? lastReviewedAt});
}

/// @nodoc
class __$VerificationStatusCopyWithImpl<$Res>
    implements _$VerificationStatusCopyWith<$Res> {
  __$VerificationStatusCopyWithImpl(this._self, this._then);

  final _VerificationStatus _self;
  final $Res Function(_VerificationStatus) _then;

  /// Create a copy of VerificationStatus
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? isVerified = null,
    Object? profileStatus = null,
    Object? documents = null,
    Object? totalDocuments = null,
    Object? approvedDocuments = null,
    Object? pendingDocuments = null,
    Object? rejectedDocuments = null,
    Object? lastUploadedAt = freezed,
    Object? lastReviewedAt = freezed,
  }) {
    return _then(_VerificationStatus(
      isVerified: null == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool,
      profileStatus: null == profileStatus
          ? _self.profileStatus
          : profileStatus // ignore: cast_nullable_to_non_nullable
              as String,
      documents: null == documents
          ? _self._documents
          : documents // ignore: cast_nullable_to_non_nullable
              as List<VerificationDocument>,
      totalDocuments: null == totalDocuments
          ? _self.totalDocuments
          : totalDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      approvedDocuments: null == approvedDocuments
          ? _self.approvedDocuments
          : approvedDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      pendingDocuments: null == pendingDocuments
          ? _self.pendingDocuments
          : pendingDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      rejectedDocuments: null == rejectedDocuments
          ? _self.rejectedDocuments
          : rejectedDocuments // ignore: cast_nullable_to_non_nullable
              as int,
      lastUploadedAt: freezed == lastUploadedAt
          ? _self.lastUploadedAt
          : lastUploadedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      lastReviewedAt: freezed == lastReviewedAt
          ? _self.lastReviewedAt
          : lastReviewedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
mixin _$DocumentDeleteRequest {
  String get documentId;

  /// Create a copy of DocumentDeleteRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DocumentDeleteRequestCopyWith<DocumentDeleteRequest> get copyWith =>
      _$DocumentDeleteRequestCopyWithImpl<DocumentDeleteRequest>(
          this as DocumentDeleteRequest, _$identity);

  /// Serializes this DocumentDeleteRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DocumentDeleteRequest &&
            (identical(other.documentId, documentId) ||
                other.documentId == documentId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, documentId);

  @override
  String toString() {
    return 'DocumentDeleteRequest(documentId: $documentId)';
  }
}

/// @nodoc
abstract mixin class $DocumentDeleteRequestCopyWith<$Res> {
  factory $DocumentDeleteRequestCopyWith(DocumentDeleteRequest value,
          $Res Function(DocumentDeleteRequest) _then) =
      _$DocumentDeleteRequestCopyWithImpl;
  @useResult
  $Res call({String documentId});
}

/// @nodoc
class _$DocumentDeleteRequestCopyWithImpl<$Res>
    implements $DocumentDeleteRequestCopyWith<$Res> {
  _$DocumentDeleteRequestCopyWithImpl(this._self, this._then);

  final DocumentDeleteRequest _self;
  final $Res Function(DocumentDeleteRequest) _then;

  /// Create a copy of DocumentDeleteRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? documentId = null,
  }) {
    return _then(_self.copyWith(
      documentId: null == documentId
          ? _self.documentId
          : documentId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [DocumentDeleteRequest].
extension DocumentDeleteRequestPatterns on DocumentDeleteRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DocumentDeleteRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DocumentDeleteRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DocumentDeleteRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String documentId)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteRequest() when $default != null:
        return $default(_that.documentId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String documentId) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteRequest():
        return $default(_that.documentId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String documentId)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteRequest() when $default != null:
        return $default(_that.documentId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DocumentDeleteRequest implements DocumentDeleteRequest {
  const _DocumentDeleteRequest({required this.documentId});
  factory _DocumentDeleteRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentDeleteRequestFromJson(json);

  @override
  final String documentId;

  /// Create a copy of DocumentDeleteRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DocumentDeleteRequestCopyWith<_DocumentDeleteRequest> get copyWith =>
      __$DocumentDeleteRequestCopyWithImpl<_DocumentDeleteRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DocumentDeleteRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DocumentDeleteRequest &&
            (identical(other.documentId, documentId) ||
                other.documentId == documentId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, documentId);

  @override
  String toString() {
    return 'DocumentDeleteRequest(documentId: $documentId)';
  }
}

/// @nodoc
abstract mixin class _$DocumentDeleteRequestCopyWith<$Res>
    implements $DocumentDeleteRequestCopyWith<$Res> {
  factory _$DocumentDeleteRequestCopyWith(_DocumentDeleteRequest value,
          $Res Function(_DocumentDeleteRequest) _then) =
      __$DocumentDeleteRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String documentId});
}

/// @nodoc
class __$DocumentDeleteRequestCopyWithImpl<$Res>
    implements _$DocumentDeleteRequestCopyWith<$Res> {
  __$DocumentDeleteRequestCopyWithImpl(this._self, this._then);

  final _DocumentDeleteRequest _self;
  final $Res Function(_DocumentDeleteRequest) _then;

  /// Create a copy of DocumentDeleteRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? documentId = null,
  }) {
    return _then(_DocumentDeleteRequest(
      documentId: null == documentId
          ? _self.documentId
          : documentId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$DocumentDeleteResponse {
  bool get success;
  String get message;
  String? get error;

  /// Create a copy of DocumentDeleteResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DocumentDeleteResponseCopyWith<DocumentDeleteResponse> get copyWith =>
      _$DocumentDeleteResponseCopyWithImpl<DocumentDeleteResponse>(
          this as DocumentDeleteResponse, _$identity);

  /// Serializes this DocumentDeleteResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DocumentDeleteResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, message, error);

  @override
  String toString() {
    return 'DocumentDeleteResponse(success: $success, message: $message, error: $error)';
  }
}

/// @nodoc
abstract mixin class $DocumentDeleteResponseCopyWith<$Res> {
  factory $DocumentDeleteResponseCopyWith(DocumentDeleteResponse value,
          $Res Function(DocumentDeleteResponse) _then) =
      _$DocumentDeleteResponseCopyWithImpl;
  @useResult
  $Res call({bool success, String message, String? error});
}

/// @nodoc
class _$DocumentDeleteResponseCopyWithImpl<$Res>
    implements $DocumentDeleteResponseCopyWith<$Res> {
  _$DocumentDeleteResponseCopyWithImpl(this._self, this._then);

  final DocumentDeleteResponse _self;
  final $Res Function(DocumentDeleteResponse) _then;

  /// Create a copy of DocumentDeleteResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? error = freezed,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [DocumentDeleteResponse].
extension DocumentDeleteResponsePatterns on DocumentDeleteResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_DocumentDeleteResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_DocumentDeleteResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_DocumentDeleteResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool success, String message, String? error)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteResponse() when $default != null:
        return $default(_that.success, _that.message, _that.error);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool success, String message, String? error) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteResponse():
        return $default(_that.success, _that.message, _that.error);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool success, String message, String? error)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _DocumentDeleteResponse() when $default != null:
        return $default(_that.success, _that.message, _that.error);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _DocumentDeleteResponse implements DocumentDeleteResponse {
  const _DocumentDeleteResponse(
      {required this.success, required this.message, this.error});
  factory _DocumentDeleteResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentDeleteResponseFromJson(json);

  @override
  final bool success;
  @override
  final String message;
  @override
  final String? error;

  /// Create a copy of DocumentDeleteResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DocumentDeleteResponseCopyWith<_DocumentDeleteResponse> get copyWith =>
      __$DocumentDeleteResponseCopyWithImpl<_DocumentDeleteResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DocumentDeleteResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DocumentDeleteResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.error, error) || other.error == error));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, message, error);

  @override
  String toString() {
    return 'DocumentDeleteResponse(success: $success, message: $message, error: $error)';
  }
}

/// @nodoc
abstract mixin class _$DocumentDeleteResponseCopyWith<$Res>
    implements $DocumentDeleteResponseCopyWith<$Res> {
  factory _$DocumentDeleteResponseCopyWith(_DocumentDeleteResponse value,
          $Res Function(_DocumentDeleteResponse) _then) =
      __$DocumentDeleteResponseCopyWithImpl;
  @override
  @useResult
  $Res call({bool success, String message, String? error});
}

/// @nodoc
class __$DocumentDeleteResponseCopyWithImpl<$Res>
    implements _$DocumentDeleteResponseCopyWith<$Res> {
  __$DocumentDeleteResponseCopyWithImpl(this._self, this._then);

  final _DocumentDeleteResponse _self;
  final $Res Function(_DocumentDeleteResponse) _then;

  /// Create a copy of DocumentDeleteResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? message = null,
    Object? error = freezed,
  }) {
    return _then(_DocumentDeleteResponse(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: null == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$VerificationStats {
  int get totalUploaded;
  int get approved;
  int get pending;
  int get rejected;
  double get completionPercentage;
  List<DocumentType> get missingDocuments;
  List<DocumentType> get requiredDocuments;

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $VerificationStatsCopyWith<VerificationStats> get copyWith =>
      _$VerificationStatsCopyWithImpl<VerificationStats>(
          this as VerificationStats, _$identity);

  /// Serializes this VerificationStats to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is VerificationStats &&
            (identical(other.totalUploaded, totalUploaded) ||
                other.totalUploaded == totalUploaded) &&
            (identical(other.approved, approved) ||
                other.approved == approved) &&
            (identical(other.pending, pending) || other.pending == pending) &&
            (identical(other.rejected, rejected) ||
                other.rejected == rejected) &&
            (identical(other.completionPercentage, completionPercentage) ||
                other.completionPercentage == completionPercentage) &&
            const DeepCollectionEquality()
                .equals(other.missingDocuments, missingDocuments) &&
            const DeepCollectionEquality()
                .equals(other.requiredDocuments, requiredDocuments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalUploaded,
      approved,
      pending,
      rejected,
      completionPercentage,
      const DeepCollectionEquality().hash(missingDocuments),
      const DeepCollectionEquality().hash(requiredDocuments));

  @override
  String toString() {
    return 'VerificationStats(totalUploaded: $totalUploaded, approved: $approved, pending: $pending, rejected: $rejected, completionPercentage: $completionPercentage, missingDocuments: $missingDocuments, requiredDocuments: $requiredDocuments)';
  }
}

/// @nodoc
abstract mixin class $VerificationStatsCopyWith<$Res> {
  factory $VerificationStatsCopyWith(
          VerificationStats value, $Res Function(VerificationStats) _then) =
      _$VerificationStatsCopyWithImpl;
  @useResult
  $Res call(
      {int totalUploaded,
      int approved,
      int pending,
      int rejected,
      double completionPercentage,
      List<DocumentType> missingDocuments,
      List<DocumentType> requiredDocuments});
}

/// @nodoc
class _$VerificationStatsCopyWithImpl<$Res>
    implements $VerificationStatsCopyWith<$Res> {
  _$VerificationStatsCopyWithImpl(this._self, this._then);

  final VerificationStats _self;
  final $Res Function(VerificationStats) _then;

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalUploaded = null,
    Object? approved = null,
    Object? pending = null,
    Object? rejected = null,
    Object? completionPercentage = null,
    Object? missingDocuments = null,
    Object? requiredDocuments = null,
  }) {
    return _then(_self.copyWith(
      totalUploaded: null == totalUploaded
          ? _self.totalUploaded
          : totalUploaded // ignore: cast_nullable_to_non_nullable
              as int,
      approved: null == approved
          ? _self.approved
          : approved // ignore: cast_nullable_to_non_nullable
              as int,
      pending: null == pending
          ? _self.pending
          : pending // ignore: cast_nullable_to_non_nullable
              as int,
      rejected: null == rejected
          ? _self.rejected
          : rejected // ignore: cast_nullable_to_non_nullable
              as int,
      completionPercentage: null == completionPercentage
          ? _self.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      missingDocuments: null == missingDocuments
          ? _self.missingDocuments
          : missingDocuments // ignore: cast_nullable_to_non_nullable
              as List<DocumentType>,
      requiredDocuments: null == requiredDocuments
          ? _self.requiredDocuments
          : requiredDocuments // ignore: cast_nullable_to_non_nullable
              as List<DocumentType>,
    ));
  }
}

/// Adds pattern-matching-related methods to [VerificationStats].
extension VerificationStatsPatterns on VerificationStats {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_VerificationStats value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerificationStats() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_VerificationStats value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStats():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_VerificationStats value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStats() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int totalUploaded,
            int approved,
            int pending,
            int rejected,
            double completionPercentage,
            List<DocumentType> missingDocuments,
            List<DocumentType> requiredDocuments)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _VerificationStats() when $default != null:
        return $default(
            _that.totalUploaded,
            _that.approved,
            _that.pending,
            _that.rejected,
            _that.completionPercentage,
            _that.missingDocuments,
            _that.requiredDocuments);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int totalUploaded,
            int approved,
            int pending,
            int rejected,
            double completionPercentage,
            List<DocumentType> missingDocuments,
            List<DocumentType> requiredDocuments)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStats():
        return $default(
            _that.totalUploaded,
            _that.approved,
            _that.pending,
            _that.rejected,
            _that.completionPercentage,
            _that.missingDocuments,
            _that.requiredDocuments);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int totalUploaded,
            int approved,
            int pending,
            int rejected,
            double completionPercentage,
            List<DocumentType> missingDocuments,
            List<DocumentType> requiredDocuments)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _VerificationStats() when $default != null:
        return $default(
            _that.totalUploaded,
            _that.approved,
            _that.pending,
            _that.rejected,
            _that.completionPercentage,
            _that.missingDocuments,
            _that.requiredDocuments);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _VerificationStats implements VerificationStats {
  const _VerificationStats(
      {required this.totalUploaded,
      required this.approved,
      required this.pending,
      required this.rejected,
      required this.completionPercentage,
      required final List<DocumentType> missingDocuments,
      required final List<DocumentType> requiredDocuments})
      : _missingDocuments = missingDocuments,
        _requiredDocuments = requiredDocuments;
  factory _VerificationStats.fromJson(Map<String, dynamic> json) =>
      _$VerificationStatsFromJson(json);

  @override
  final int totalUploaded;
  @override
  final int approved;
  @override
  final int pending;
  @override
  final int rejected;
  @override
  final double completionPercentage;
  final List<DocumentType> _missingDocuments;
  @override
  List<DocumentType> get missingDocuments {
    if (_missingDocuments is EqualUnmodifiableListView)
      return _missingDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_missingDocuments);
  }

  final List<DocumentType> _requiredDocuments;
  @override
  List<DocumentType> get requiredDocuments {
    if (_requiredDocuments is EqualUnmodifiableListView)
      return _requiredDocuments;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_requiredDocuments);
  }

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$VerificationStatsCopyWith<_VerificationStats> get copyWith =>
      __$VerificationStatsCopyWithImpl<_VerificationStats>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$VerificationStatsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _VerificationStats &&
            (identical(other.totalUploaded, totalUploaded) ||
                other.totalUploaded == totalUploaded) &&
            (identical(other.approved, approved) ||
                other.approved == approved) &&
            (identical(other.pending, pending) || other.pending == pending) &&
            (identical(other.rejected, rejected) ||
                other.rejected == rejected) &&
            (identical(other.completionPercentage, completionPercentage) ||
                other.completionPercentage == completionPercentage) &&
            const DeepCollectionEquality()
                .equals(other._missingDocuments, _missingDocuments) &&
            const DeepCollectionEquality()
                .equals(other._requiredDocuments, _requiredDocuments));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalUploaded,
      approved,
      pending,
      rejected,
      completionPercentage,
      const DeepCollectionEquality().hash(_missingDocuments),
      const DeepCollectionEquality().hash(_requiredDocuments));

  @override
  String toString() {
    return 'VerificationStats(totalUploaded: $totalUploaded, approved: $approved, pending: $pending, rejected: $rejected, completionPercentage: $completionPercentage, missingDocuments: $missingDocuments, requiredDocuments: $requiredDocuments)';
  }
}

/// @nodoc
abstract mixin class _$VerificationStatsCopyWith<$Res>
    implements $VerificationStatsCopyWith<$Res> {
  factory _$VerificationStatsCopyWith(
          _VerificationStats value, $Res Function(_VerificationStats) _then) =
      __$VerificationStatsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int totalUploaded,
      int approved,
      int pending,
      int rejected,
      double completionPercentage,
      List<DocumentType> missingDocuments,
      List<DocumentType> requiredDocuments});
}

/// @nodoc
class __$VerificationStatsCopyWithImpl<$Res>
    implements _$VerificationStatsCopyWith<$Res> {
  __$VerificationStatsCopyWithImpl(this._self, this._then);

  final _VerificationStats _self;
  final $Res Function(_VerificationStats) _then;

  /// Create a copy of VerificationStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? totalUploaded = null,
    Object? approved = null,
    Object? pending = null,
    Object? rejected = null,
    Object? completionPercentage = null,
    Object? missingDocuments = null,
    Object? requiredDocuments = null,
  }) {
    return _then(_VerificationStats(
      totalUploaded: null == totalUploaded
          ? _self.totalUploaded
          : totalUploaded // ignore: cast_nullable_to_non_nullable
              as int,
      approved: null == approved
          ? _self.approved
          : approved // ignore: cast_nullable_to_non_nullable
              as int,
      pending: null == pending
          ? _self.pending
          : pending // ignore: cast_nullable_to_non_nullable
              as int,
      rejected: null == rejected
          ? _self.rejected
          : rejected // ignore: cast_nullable_to_non_nullable
              as int,
      completionPercentage: null == completionPercentage
          ? _self.completionPercentage
          : completionPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      missingDocuments: null == missingDocuments
          ? _self._missingDocuments
          : missingDocuments // ignore: cast_nullable_to_non_nullable
              as List<DocumentType>,
      requiredDocuments: null == requiredDocuments
          ? _self._requiredDocuments
          : requiredDocuments // ignore: cast_nullable_to_non_nullable
              as List<DocumentType>,
    ));
  }
}

/// @nodoc
mixin _$FileUploadProgress {
  String get filename;
  double get progress;
  bool get isCompleted;
  bool get hasError;
  String? get errorMessage;

  /// Create a copy of FileUploadProgress
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FileUploadProgressCopyWith<FileUploadProgress> get copyWith =>
      _$FileUploadProgressCopyWithImpl<FileUploadProgress>(
          this as FileUploadProgress, _$identity);

  /// Serializes this FileUploadProgress to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FileUploadProgress &&
            (identical(other.filename, filename) ||
                other.filename == filename) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, filename, progress, isCompleted, hasError, errorMessage);

  @override
  String toString() {
    return 'FileUploadProgress(filename: $filename, progress: $progress, isCompleted: $isCompleted, hasError: $hasError, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class $FileUploadProgressCopyWith<$Res> {
  factory $FileUploadProgressCopyWith(
          FileUploadProgress value, $Res Function(FileUploadProgress) _then) =
      _$FileUploadProgressCopyWithImpl;
  @useResult
  $Res call(
      {String filename,
      double progress,
      bool isCompleted,
      bool hasError,
      String? errorMessage});
}

/// @nodoc
class _$FileUploadProgressCopyWithImpl<$Res>
    implements $FileUploadProgressCopyWith<$Res> {
  _$FileUploadProgressCopyWithImpl(this._self, this._then);

  final FileUploadProgress _self;
  final $Res Function(FileUploadProgress) _then;

  /// Create a copy of FileUploadProgress
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? filename = null,
    Object? progress = null,
    Object? isCompleted = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_self.copyWith(
      filename: null == filename
          ? _self.filename
          : filename // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _self.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _self.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [FileUploadProgress].
extension FileUploadProgressPatterns on FileUploadProgress {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_FileUploadProgress value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _FileUploadProgress() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_FileUploadProgress value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FileUploadProgress():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_FileUploadProgress value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FileUploadProgress() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String filename, double progress, bool isCompleted,
            bool hasError, String? errorMessage)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _FileUploadProgress() when $default != null:
        return $default(_that.filename, _that.progress, _that.isCompleted,
            _that.hasError, _that.errorMessage);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String filename, double progress, bool isCompleted,
            bool hasError, String? errorMessage)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FileUploadProgress():
        return $default(_that.filename, _that.progress, _that.isCompleted,
            _that.hasError, _that.errorMessage);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String filename, double progress, bool isCompleted,
            bool hasError, String? errorMessage)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _FileUploadProgress() when $default != null:
        return $default(_that.filename, _that.progress, _that.isCompleted,
            _that.hasError, _that.errorMessage);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _FileUploadProgress implements FileUploadProgress {
  const _FileUploadProgress(
      {required this.filename,
      required this.progress,
      required this.isCompleted,
      required this.hasError,
      this.errorMessage});
  factory _FileUploadProgress.fromJson(Map<String, dynamic> json) =>
      _$FileUploadProgressFromJson(json);

  @override
  final String filename;
  @override
  final double progress;
  @override
  final bool isCompleted;
  @override
  final bool hasError;
  @override
  final String? errorMessage;

  /// Create a copy of FileUploadProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FileUploadProgressCopyWith<_FileUploadProgress> get copyWith =>
      __$FileUploadProgressCopyWithImpl<_FileUploadProgress>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FileUploadProgressToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FileUploadProgress &&
            (identical(other.filename, filename) ||
                other.filename == filename) &&
            (identical(other.progress, progress) ||
                other.progress == progress) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted) &&
            (identical(other.hasError, hasError) ||
                other.hasError == hasError) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, filename, progress, isCompleted, hasError, errorMessage);

  @override
  String toString() {
    return 'FileUploadProgress(filename: $filename, progress: $progress, isCompleted: $isCompleted, hasError: $hasError, errorMessage: $errorMessage)';
  }
}

/// @nodoc
abstract mixin class _$FileUploadProgressCopyWith<$Res>
    implements $FileUploadProgressCopyWith<$Res> {
  factory _$FileUploadProgressCopyWith(
          _FileUploadProgress value, $Res Function(_FileUploadProgress) _then) =
      __$FileUploadProgressCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String filename,
      double progress,
      bool isCompleted,
      bool hasError,
      String? errorMessage});
}

/// @nodoc
class __$FileUploadProgressCopyWithImpl<$Res>
    implements _$FileUploadProgressCopyWith<$Res> {
  __$FileUploadProgressCopyWithImpl(this._self, this._then);

  final _FileUploadProgress _self;
  final $Res Function(_FileUploadProgress) _then;

  /// Create a copy of FileUploadProgress
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? filename = null,
    Object? progress = null,
    Object? isCompleted = null,
    Object? hasError = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_FileUploadProgress(
      filename: null == filename
          ? _self.filename
          : filename // ignore: cast_nullable_to_non_nullable
              as String,
      progress: null == progress
          ? _self.progress
          : progress // ignore: cast_nullable_to_non_nullable
              as double,
      isCompleted: null == isCompleted
          ? _self.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
      hasError: null == hasError
          ? _self.hasError
          : hasError // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
