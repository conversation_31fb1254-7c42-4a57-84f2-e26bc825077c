import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/interest_model.dart';
import '../services/interests_service.dart';

/// 💝 INTERESTS PROVIDERS - State Management for Interest System
/// Features: Real-time Updates, Caching, Error Handling, Optimistic Updates

// State classes for better state management
class InterestsState {
  final List<InterestModel> received;
  final List<InterestModel> sent;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int currentPage;

  const InterestsState({
    this.received = const [],
    this.sent = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 1,
  });

  InterestsState copyWith({
    List<InterestModel>? received,
    List<InterestModel>? sent,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentPage,
  }) {
    return InterestsState(
      received: received ?? this.received,
      sent: sent ?? this.sent,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class DashboardState {
  final ActivityStatsModel? stats;
  final List<InterestActivityModel> recentActivities;
  final List<InterestModel> pendingInterests;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const DashboardState({
    this.stats,
    this.recentActivities = const [],
    this.pendingInterests = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  DashboardState copyWith({
    ActivityStatsModel? stats,
    List<InterestActivityModel>? recentActivities,
    List<InterestModel>? pendingInterests,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return DashboardState(
      stats: stats ?? this.stats,
      recentActivities: recentActivities ?? this.recentActivities,
      pendingInterests: pendingInterests ?? this.pendingInterests,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

// Interests List Provider
class InterestsNotifier extends StateNotifier<InterestsState> {
  final InterestsService _interestsService;

  InterestsNotifier(this._interestsService) : super(const InterestsState());

  /// Load interests (both sent and received)
  Future<void> loadInterests({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(currentPage: 1, hasMore: true);
    }

    if (state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _interestsService.getInterests(
        page: refresh ? 1 : state.currentPage,
        limit: 20,
      );

      if (refresh) {
        state = state.copyWith(
          received: response.data.received,
          sent: response.data.sent,
          isLoading: false,
          currentPage: 2,
          hasMore: response.data.received.length >= 20 || response.data.sent.length >= 20,
        );
      } else {
        state = state.copyWith(
          received: [...state.received, ...response.data.received],
          sent: [...state.sent, ...response.data.sent],
          isLoading: false,
          currentPage: state.currentPage + 1,
          hasMore: response.data.received.length >= 20 || response.data.sent.length >= 20,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Send interest with optimistic update
  Future<bool> sendInterest({
    required String targetUserId,
    String? message,
  }) async {
    try {
      final interest = await _interestsService.sendInterest(
        targetUserId: targetUserId,
        message: message,
      );

      // Add to sent interests
      state = state.copyWith(
        sent: [interest, ...state.sent],
      );

      return true;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// Respond to interest with optimistic update
  Future<bool> respondToInterest({
    required String interestId,
    required String response,
    String? message,
  }) async {
    // Find the interest in received list
    final interestIndex = state.received.indexWhere((i) => i.id == interestId);
    if (interestIndex == -1) return false;

    final originalInterest = state.received[interestIndex];

    // Optimistic update
    final updatedInterest = originalInterest.copyWith(
      status: response,
      responseMessage: message,
      respondedAt: DateTime.now(),
    );

    final updatedReceived = [...state.received];
    updatedReceived[interestIndex] = updatedInterest;

    state = state.copyWith(received: updatedReceived);

    try {
      await _interestsService.respondToInterest(
        interestId: interestId,
        response: response,
        message: message,
      );
      return true;
    } catch (e) {
      // Revert optimistic update on error
      final revertedReceived = [...state.received];
      revertedReceived[interestIndex] = originalInterest;
      state = state.copyWith(
        received: revertedReceived,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Delete/withdraw interest
  Future<bool> deleteInterest(String interestId) async {
    // Find in sent interests
    final sentIndex = state.sent.indexWhere((i) => i.id == interestId);
    if (sentIndex == -1) return false;

    final originalSent = [...state.sent];

    // Optimistic update - remove from sent
    final updatedSent = [...state.sent];
    updatedSent.removeAt(sentIndex);
    state = state.copyWith(sent: updatedSent);

    try {
      await _interestsService.deleteInterest(interestId);
      return true;
    } catch (e) {
      // Revert on error
      state = state.copyWith(
        sent: originalSent,
        error: e.toString(),
      );
      return false;
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Refresh interests
  Future<void> refresh() async {
    await loadInterests(refresh: true);
  }
}

// Dashboard Provider
class DashboardNotifier extends StateNotifier<DashboardState> {
  final InterestsService _interestsService;

  DashboardNotifier(this._interestsService) : super(const DashboardState());

  /// Load dashboard data
  Future<void> loadDashboard({bool refresh = false}) async {
    if (state.isLoading && !refresh) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final response = await _interestsService.getDashboardData();

      state = state.copyWith(
        stats: response.data.stats,
        recentActivities: response.data.recentActivities,
        pendingInterests: response.data.pendingInterests,
        isLoading: false,
        lastUpdated: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// Refresh dashboard
  Future<void> refresh() async {
    await loadDashboard(refresh: true);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final interestsProvider = StateNotifierProvider<InterestsNotifier, InterestsState>((ref) {
  final interestsService = ref.watch(interestsServiceProvider);
  return InterestsNotifier(interestsService);
});

final dashboardProvider = StateNotifierProvider<DashboardNotifier, DashboardState>((ref) {
  final interestsService = ref.watch(interestsServiceProvider);
  return DashboardNotifier(interestsService);
});

// Computed providers for specific data
final receivedInterestsProvider = Provider<List<InterestModel>>((ref) {
  return ref.watch(interestsProvider).received;
});

final sentInterestsProvider = Provider<List<InterestModel>>((ref) {
  return ref.watch(interestsProvider).sent;
});

final pendingReceivedInterestsProvider = Provider<List<InterestModel>>((ref) {
  return ref.watch(receivedInterestsProvider)
      .where((interest) => interest.status == 'PENDING')
      .toList();
});

final pendingSentInterestsProvider = Provider<List<InterestModel>>((ref) {
  return ref.watch(sentInterestsProvider)
      .where((interest) => interest.status == 'PENDING')
      .toList();
});

final acceptedInterestsProvider = Provider<List<InterestModel>>((ref) {
  final received = ref.watch(receivedInterestsProvider)
      .where((interest) => interest.status == 'ACCEPTED');
  final sent = ref.watch(sentInterestsProvider)
      .where((interest) => interest.status == 'ACCEPTED');
  return [...received, ...sent];
});

// Pending counts provider
final pendingCountsProvider = FutureProvider<Map<String, int>>((ref) async {
  final interestsService = ref.watch(interestsServiceProvider);
  return await interestsService.getPendingCounts();
});

// Interest stats provider
final interestStatsProvider = FutureProvider<ActivityStatsModel>((ref) async {
  final interestsService = ref.watch(interestsServiceProvider);
  return await interestsService.getInterestStats();
});

// Check if interest sent provider
final hasInterestSentProvider = FutureProvider.family<bool, String>((ref, targetUserId) async {
  final interestsService = ref.watch(interestsServiceProvider);
  return await interestsService.hasInterestSent(targetUserId);
});

// Mutual interests provider
final mutualInterestsProvider = FutureProvider<List<InterestModel>>((ref) async {
  final interestsService = ref.watch(interestsServiceProvider);
  return await interestsService.getMutualInterests();
});
