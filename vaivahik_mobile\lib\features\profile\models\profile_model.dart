class ProfileModel {
  final String id;
  final String userId;
  final String fullName;
  final String? firstName;
  final String? lastName;
  final String gender;
  final DateTime? dateOfBirth;
  final int? age;
  final String? birthTime;
  final String? birthPlace;
  final String? height;
  final String? weight;
  final String? bloodGroup;
  final String? complexion;
  final String? physicalStatus;
  final String? motherTongue;
  final String? religion;
  final String? caste;
  final String? subCaste;
  final String? gotra;
  final String? manglik;
  final String? maritalStatus;
  final String? education;
  final String? occupation;
  final String? income;
  final String? workLocation;
  final String? aboutMe;
  final List<String>? photos;
  final String? profilePhoto;
  final Map<String, dynamic>? address;
  final Map<String, dynamic>? familyDetails;
  final Map<String, dynamic>? preferences;
  final Map<String, dynamic>? horoscope;
  final bool isVerified;
  final bool isPremium;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastSeen;

  const ProfileModel({
    required this.id,
    required this.userId,
    required this.fullName,
    this.firstName,
    this.lastName,
    required this.gender,
    this.dateOfBirth,
    this.age,
    this.birthTime,
    this.birthPlace,
    this.height,
    this.weight,
    this.bloodGroup,
    this.complexion,
    this.physicalStatus,
    this.motherTongue,
    this.religion,
    this.caste,
    this.subCaste,
    this.gotra,
    this.manglik,
    this.maritalStatus,
    this.education,
    this.occupation,
    this.income,
    this.workLocation,
    this.aboutMe,
    this.photos,
    this.profilePhoto,
    this.address,
    this.familyDetails,
    this.preferences,
    this.horoscope,
    this.isVerified = false,
    this.isPremium = false,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.lastSeen,
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      fullName: json['fullName'] ?? '',
      firstName: json['firstName'],
      lastName: json['lastName'],
      gender: json['gender'] ?? '',
      dateOfBirth: json['dateOfBirth'] != null 
          ? DateTime.parse(json['dateOfBirth']) 
          : null,
      age: json['age'],
      birthTime: json['birthTime'],
      birthPlace: json['birthPlace'],
      height: json['height'],
      weight: json['weight'],
      bloodGroup: json['bloodGroup'],
      complexion: json['complexion'],
      physicalStatus: json['physicalStatus'],
      motherTongue: json['motherTongue'],
      religion: json['religion'],
      caste: json['caste'],
      subCaste: json['subCaste'],
      gotra: json['gotra'],
      manglik: json['manglik'],
      maritalStatus: json['maritalStatus'],
      education: json['education'],
      occupation: json['occupation'],
      income: json['income'],
      workLocation: json['workLocation'],
      aboutMe: json['aboutMe'],
      photos: json['photos'] != null 
          ? List<String>.from(json['photos']) 
          : null,
      profilePhoto: json['profilePhoto'],
      address: json['address'],
      familyDetails: json['familyDetails'],
      preferences: json['preferences'],
      horoscope: json['horoscope'],
      isVerified: json['isVerified'] ?? false,
      isPremium: json['isPremium'] ?? false,
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      lastSeen: json['lastSeen'] != null 
          ? DateTime.parse(json['lastSeen']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'fullName': fullName,
      'firstName': firstName,
      'lastName': lastName,
      'gender': gender,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'age': age,
      'birthTime': birthTime,
      'birthPlace': birthPlace,
      'height': height,
      'weight': weight,
      'bloodGroup': bloodGroup,
      'complexion': complexion,
      'physicalStatus': physicalStatus,
      'motherTongue': motherTongue,
      'religion': religion,
      'caste': caste,
      'subCaste': subCaste,
      'gotra': gotra,
      'manglik': manglik,
      'maritalStatus': maritalStatus,
      'education': education,
      'occupation': occupation,
      'income': income,
      'workLocation': workLocation,
      'aboutMe': aboutMe,
      'photos': photos,
      'profilePhoto': profilePhoto,
      'address': address,
      'familyDetails': familyDetails,
      'preferences': preferences,
      'horoscope': horoscope,
      'isVerified': isVerified,
      'isPremium': isPremium,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastSeen': lastSeen?.toIso8601String(),
    };
  }

  ProfileModel copyWith({
    String? id,
    String? userId,
    String? fullName,
    String? firstName,
    String? lastName,
    String? gender,
    DateTime? dateOfBirth,
    int? age,
    String? birthTime,
    String? birthPlace,
    String? height,
    String? weight,
    String? bloodGroup,
    String? complexion,
    String? physicalStatus,
    String? motherTongue,
    String? religion,
    String? caste,
    String? subCaste,
    String? gotra,
    String? manglik,
    String? maritalStatus,
    String? education,
    String? occupation,
    String? income,
    String? workLocation,
    String? aboutMe,
    List<String>? photos,
    String? profilePhoto,
    Map<String, dynamic>? address,
    Map<String, dynamic>? familyDetails,
    Map<String, dynamic>? preferences,
    Map<String, dynamic>? horoscope,
    bool? isVerified,
    bool? isPremium,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastSeen,
  }) {
    return ProfileModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fullName: fullName ?? this.fullName,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      age: age ?? this.age,
      birthTime: birthTime ?? this.birthTime,
      birthPlace: birthPlace ?? this.birthPlace,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      bloodGroup: bloodGroup ?? this.bloodGroup,
      complexion: complexion ?? this.complexion,
      physicalStatus: physicalStatus ?? this.physicalStatus,
      motherTongue: motherTongue ?? this.motherTongue,
      religion: religion ?? this.religion,
      caste: caste ?? this.caste,
      subCaste: subCaste ?? this.subCaste,
      gotra: gotra ?? this.gotra,
      manglik: manglik ?? this.manglik,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      education: education ?? this.education,
      occupation: occupation ?? this.occupation,
      income: income ?? this.income,
      workLocation: workLocation ?? this.workLocation,
      aboutMe: aboutMe ?? this.aboutMe,
      photos: photos ?? this.photos,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      address: address ?? this.address,
      familyDetails: familyDetails ?? this.familyDetails,
      preferences: preferences ?? this.preferences,
      horoscope: horoscope ?? this.horoscope,
      isVerified: isVerified ?? this.isVerified,
      isPremium: isPremium ?? this.isPremium,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  String get displayName => fullName.isNotEmpty ? fullName : firstName ?? 'User';
  
  String get ageDisplay => age != null ? '$age years' : 'Age not specified';
  
  String get heightDisplay => height ?? 'Height not specified';
  
  String get locationDisplay {
    if (address != null && address!['city'] != null) {
      return address!['city'];
    }
    return 'Location not specified';
  }
  
  bool get hasProfilePhoto => profilePhoto != null && profilePhoto!.isNotEmpty;
  
  bool get hasPhotos => photos != null && photos!.isNotEmpty;
  
  int get photoCount => photos?.length ?? 0;
}
