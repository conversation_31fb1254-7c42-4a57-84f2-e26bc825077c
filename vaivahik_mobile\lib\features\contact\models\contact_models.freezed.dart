// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'contact_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ContactRevealRequest {
  String get targetUserId;
  String? get reason;
  String get platform;

  /// Create a copy of ContactRevealRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactRevealRequestCopyWith<ContactRevealRequest> get copyWith =>
      _$ContactRevealRequestCopyWithImpl<ContactRevealRequest>(
          this as ContactRevealRequest, _$identity);

  /// Serializes this ContactRevealRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactRevealRequest &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.platform, platform) ||
                other.platform == platform));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, targetUserId, reason, platform);

  @override
  String toString() {
    return 'ContactRevealRequest(targetUserId: $targetUserId, reason: $reason, platform: $platform)';
  }
}

/// @nodoc
abstract mixin class $ContactRevealRequestCopyWith<$Res> {
  factory $ContactRevealRequestCopyWith(ContactRevealRequest value,
          $Res Function(ContactRevealRequest) _then) =
      _$ContactRevealRequestCopyWithImpl;
  @useResult
  $Res call({String targetUserId, String? reason, String platform});
}

/// @nodoc
class _$ContactRevealRequestCopyWithImpl<$Res>
    implements $ContactRevealRequestCopyWith<$Res> {
  _$ContactRevealRequestCopyWithImpl(this._self, this._then);

  final ContactRevealRequest _self;
  final $Res Function(ContactRevealRequest) _then;

  /// Create a copy of ContactRevealRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? targetUserId = null,
    Object? reason = freezed,
    Object? platform = null,
  }) {
    return _then(_self.copyWith(
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: freezed == reason
          ? _self.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      platform: null == platform
          ? _self.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactRevealRequest].
extension ContactRevealRequestPatterns on ContactRevealRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactRevealRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactRevealRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactRevealRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String targetUserId, String? reason, String platform)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealRequest() when $default != null:
        return $default(_that.targetUserId, _that.reason, _that.platform);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String targetUserId, String? reason, String platform)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealRequest():
        return $default(_that.targetUserId, _that.reason, _that.platform);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String targetUserId, String? reason, String platform)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealRequest() when $default != null:
        return $default(_that.targetUserId, _that.reason, _that.platform);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactRevealRequest implements ContactRevealRequest {
  const _ContactRevealRequest(
      {required this.targetUserId, this.reason, this.platform = 'mobile'});
  factory _ContactRevealRequest.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealRequestFromJson(json);

  @override
  final String targetUserId;
  @override
  final String? reason;
  @override
  @JsonKey()
  final String platform;

  /// Create a copy of ContactRevealRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactRevealRequestCopyWith<_ContactRevealRequest> get copyWith =>
      __$ContactRevealRequestCopyWithImpl<_ContactRevealRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactRevealRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactRevealRequest &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.reason, reason) || other.reason == reason) &&
            (identical(other.platform, platform) ||
                other.platform == platform));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, targetUserId, reason, platform);

  @override
  String toString() {
    return 'ContactRevealRequest(targetUserId: $targetUserId, reason: $reason, platform: $platform)';
  }
}

/// @nodoc
abstract mixin class _$ContactRevealRequestCopyWith<$Res>
    implements $ContactRevealRequestCopyWith<$Res> {
  factory _$ContactRevealRequestCopyWith(_ContactRevealRequest value,
          $Res Function(_ContactRevealRequest) _then) =
      __$ContactRevealRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String targetUserId, String? reason, String platform});
}

/// @nodoc
class __$ContactRevealRequestCopyWithImpl<$Res>
    implements _$ContactRevealRequestCopyWith<$Res> {
  __$ContactRevealRequestCopyWithImpl(this._self, this._then);

  final _ContactRevealRequest _self;
  final $Res Function(_ContactRevealRequest) _then;

  /// Create a copy of ContactRevealRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? targetUserId = null,
    Object? reason = freezed,
    Object? platform = null,
  }) {
    return _then(_ContactRevealRequest(
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      reason: freezed == reason
          ? _self.reason
          : reason // ignore: cast_nullable_to_non_nullable
              as String?,
      platform: null == platform
          ? _self.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$ContactRevealResponse {
  bool get success;
  String? get contactNumber;
  String? get contactOwnerName;
  String? get accessReason;
  String? get callAvailability;
  String? get error;
  String? get message;
  bool get upgradeRequired;

  /// Create a copy of ContactRevealResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactRevealResponseCopyWith<ContactRevealResponse> get copyWith =>
      _$ContactRevealResponseCopyWithImpl<ContactRevealResponse>(
          this as ContactRevealResponse, _$identity);

  /// Serializes this ContactRevealResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactRevealResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.contactNumber, contactNumber) ||
                other.contactNumber == contactNumber) &&
            (identical(other.contactOwnerName, contactOwnerName) ||
                other.contactOwnerName == contactOwnerName) &&
            (identical(other.accessReason, accessReason) ||
                other.accessReason == accessReason) &&
            (identical(other.callAvailability, callAvailability) ||
                other.callAvailability == callAvailability) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.upgradeRequired, upgradeRequired) ||
                other.upgradeRequired == upgradeRequired));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      success,
      contactNumber,
      contactOwnerName,
      accessReason,
      callAvailability,
      error,
      message,
      upgradeRequired);

  @override
  String toString() {
    return 'ContactRevealResponse(success: $success, contactNumber: $contactNumber, contactOwnerName: $contactOwnerName, accessReason: $accessReason, callAvailability: $callAvailability, error: $error, message: $message, upgradeRequired: $upgradeRequired)';
  }
}

/// @nodoc
abstract mixin class $ContactRevealResponseCopyWith<$Res> {
  factory $ContactRevealResponseCopyWith(ContactRevealResponse value,
          $Res Function(ContactRevealResponse) _then) =
      _$ContactRevealResponseCopyWithImpl;
  @useResult
  $Res call(
      {bool success,
      String? contactNumber,
      String? contactOwnerName,
      String? accessReason,
      String? callAvailability,
      String? error,
      String? message,
      bool upgradeRequired});
}

/// @nodoc
class _$ContactRevealResponseCopyWithImpl<$Res>
    implements $ContactRevealResponseCopyWith<$Res> {
  _$ContactRevealResponseCopyWithImpl(this._self, this._then);

  final ContactRevealResponse _self;
  final $Res Function(ContactRevealResponse) _then;

  /// Create a copy of ContactRevealResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? contactNumber = freezed,
    Object? contactOwnerName = freezed,
    Object? accessReason = freezed,
    Object? callAvailability = freezed,
    Object? error = freezed,
    Object? message = freezed,
    Object? upgradeRequired = null,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      contactNumber: freezed == contactNumber
          ? _self.contactNumber
          : contactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contactOwnerName: freezed == contactOwnerName
          ? _self.contactOwnerName
          : contactOwnerName // ignore: cast_nullable_to_non_nullable
              as String?,
      accessReason: freezed == accessReason
          ? _self.accessReason
          : accessReason // ignore: cast_nullable_to_non_nullable
              as String?,
      callAvailability: freezed == callAvailability
          ? _self.callAvailability
          : callAvailability // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      upgradeRequired: null == upgradeRequired
          ? _self.upgradeRequired
          : upgradeRequired // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactRevealResponse].
extension ContactRevealResponsePatterns on ContactRevealResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactRevealResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactRevealResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactRevealResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool success,
            String? contactNumber,
            String? contactOwnerName,
            String? accessReason,
            String? callAvailability,
            String? error,
            String? message,
            bool upgradeRequired)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealResponse() when $default != null:
        return $default(
            _that.success,
            _that.contactNumber,
            _that.contactOwnerName,
            _that.accessReason,
            _that.callAvailability,
            _that.error,
            _that.message,
            _that.upgradeRequired);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool success,
            String? contactNumber,
            String? contactOwnerName,
            String? accessReason,
            String? callAvailability,
            String? error,
            String? message,
            bool upgradeRequired)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealResponse():
        return $default(
            _that.success,
            _that.contactNumber,
            _that.contactOwnerName,
            _that.accessReason,
            _that.callAvailability,
            _that.error,
            _that.message,
            _that.upgradeRequired);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool success,
            String? contactNumber,
            String? contactOwnerName,
            String? accessReason,
            String? callAvailability,
            String? error,
            String? message,
            bool upgradeRequired)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealResponse() when $default != null:
        return $default(
            _that.success,
            _that.contactNumber,
            _that.contactOwnerName,
            _that.accessReason,
            _that.callAvailability,
            _that.error,
            _that.message,
            _that.upgradeRequired);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactRevealResponse implements ContactRevealResponse {
  const _ContactRevealResponse(
      {required this.success,
      this.contactNumber,
      this.contactOwnerName,
      this.accessReason,
      this.callAvailability,
      this.error,
      this.message,
      this.upgradeRequired = false});
  factory _ContactRevealResponse.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealResponseFromJson(json);

  @override
  final bool success;
  @override
  final String? contactNumber;
  @override
  final String? contactOwnerName;
  @override
  final String? accessReason;
  @override
  final String? callAvailability;
  @override
  final String? error;
  @override
  final String? message;
  @override
  @JsonKey()
  final bool upgradeRequired;

  /// Create a copy of ContactRevealResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactRevealResponseCopyWith<_ContactRevealResponse> get copyWith =>
      __$ContactRevealResponseCopyWithImpl<_ContactRevealResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactRevealResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactRevealResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.contactNumber, contactNumber) ||
                other.contactNumber == contactNumber) &&
            (identical(other.contactOwnerName, contactOwnerName) ||
                other.contactOwnerName == contactOwnerName) &&
            (identical(other.accessReason, accessReason) ||
                other.accessReason == accessReason) &&
            (identical(other.callAvailability, callAvailability) ||
                other.callAvailability == callAvailability) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.upgradeRequired, upgradeRequired) ||
                other.upgradeRequired == upgradeRequired));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      success,
      contactNumber,
      contactOwnerName,
      accessReason,
      callAvailability,
      error,
      message,
      upgradeRequired);

  @override
  String toString() {
    return 'ContactRevealResponse(success: $success, contactNumber: $contactNumber, contactOwnerName: $contactOwnerName, accessReason: $accessReason, callAvailability: $callAvailability, error: $error, message: $message, upgradeRequired: $upgradeRequired)';
  }
}

/// @nodoc
abstract mixin class _$ContactRevealResponseCopyWith<$Res>
    implements $ContactRevealResponseCopyWith<$Res> {
  factory _$ContactRevealResponseCopyWith(_ContactRevealResponse value,
          $Res Function(_ContactRevealResponse) _then) =
      __$ContactRevealResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool success,
      String? contactNumber,
      String? contactOwnerName,
      String? accessReason,
      String? callAvailability,
      String? error,
      String? message,
      bool upgradeRequired});
}

/// @nodoc
class __$ContactRevealResponseCopyWithImpl<$Res>
    implements _$ContactRevealResponseCopyWith<$Res> {
  __$ContactRevealResponseCopyWithImpl(this._self, this._then);

  final _ContactRevealResponse _self;
  final $Res Function(_ContactRevealResponse) _then;

  /// Create a copy of ContactRevealResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? contactNumber = freezed,
    Object? contactOwnerName = freezed,
    Object? accessReason = freezed,
    Object? callAvailability = freezed,
    Object? error = freezed,
    Object? message = freezed,
    Object? upgradeRequired = null,
  }) {
    return _then(_ContactRevealResponse(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      contactNumber: freezed == contactNumber
          ? _self.contactNumber
          : contactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      contactOwnerName: freezed == contactOwnerName
          ? _self.contactOwnerName
          : contactOwnerName // ignore: cast_nullable_to_non_nullable
              as String?,
      accessReason: freezed == accessReason
          ? _self.accessReason
          : accessReason // ignore: cast_nullable_to_non_nullable
              as String?,
      callAvailability: freezed == callAvailability
          ? _self.callAvailability
          : callAvailability // ignore: cast_nullable_to_non_nullable
              as String?,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      upgradeRequired: null == upgradeRequired
          ? _self.upgradeRequired
          : upgradeRequired // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$ContactPrivacySettings {
  bool get allowDirectCalls;
  String get contactRevealPreference;
  bool get requireMutualInterest;
  String get callAvailability;

  /// Create a copy of ContactPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactPrivacySettingsCopyWith<ContactPrivacySettings> get copyWith =>
      _$ContactPrivacySettingsCopyWithImpl<ContactPrivacySettings>(
          this as ContactPrivacySettings, _$identity);

  /// Serializes this ContactPrivacySettings to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactPrivacySettings &&
            (identical(other.allowDirectCalls, allowDirectCalls) ||
                other.allowDirectCalls == allowDirectCalls) &&
            (identical(
                    other.contactRevealPreference, contactRevealPreference) ||
                other.contactRevealPreference == contactRevealPreference) &&
            (identical(other.requireMutualInterest, requireMutualInterest) ||
                other.requireMutualInterest == requireMutualInterest) &&
            (identical(other.callAvailability, callAvailability) ||
                other.callAvailability == callAvailability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, allowDirectCalls,
      contactRevealPreference, requireMutualInterest, callAvailability);

  @override
  String toString() {
    return 'ContactPrivacySettings(allowDirectCalls: $allowDirectCalls, contactRevealPreference: $contactRevealPreference, requireMutualInterest: $requireMutualInterest, callAvailability: $callAvailability)';
  }
}

/// @nodoc
abstract mixin class $ContactPrivacySettingsCopyWith<$Res> {
  factory $ContactPrivacySettingsCopyWith(ContactPrivacySettings value,
          $Res Function(ContactPrivacySettings) _then) =
      _$ContactPrivacySettingsCopyWithImpl;
  @useResult
  $Res call(
      {bool allowDirectCalls,
      String contactRevealPreference,
      bool requireMutualInterest,
      String callAvailability});
}

/// @nodoc
class _$ContactPrivacySettingsCopyWithImpl<$Res>
    implements $ContactPrivacySettingsCopyWith<$Res> {
  _$ContactPrivacySettingsCopyWithImpl(this._self, this._then);

  final ContactPrivacySettings _self;
  final $Res Function(ContactPrivacySettings) _then;

  /// Create a copy of ContactPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? allowDirectCalls = null,
    Object? contactRevealPreference = null,
    Object? requireMutualInterest = null,
    Object? callAvailability = null,
  }) {
    return _then(_self.copyWith(
      allowDirectCalls: null == allowDirectCalls
          ? _self.allowDirectCalls
          : allowDirectCalls // ignore: cast_nullable_to_non_nullable
              as bool,
      contactRevealPreference: null == contactRevealPreference
          ? _self.contactRevealPreference
          : contactRevealPreference // ignore: cast_nullable_to_non_nullable
              as String,
      requireMutualInterest: null == requireMutualInterest
          ? _self.requireMutualInterest
          : requireMutualInterest // ignore: cast_nullable_to_non_nullable
              as bool,
      callAvailability: null == callAvailability
          ? _self.callAvailability
          : callAvailability // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactPrivacySettings].
extension ContactPrivacySettingsPatterns on ContactPrivacySettings {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactPrivacySettings value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactPrivacySettings() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactPrivacySettings value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactPrivacySettings():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactPrivacySettings value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactPrivacySettings() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool allowDirectCalls, String contactRevealPreference,
            bool requireMutualInterest, String callAvailability)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactPrivacySettings() when $default != null:
        return $default(_that.allowDirectCalls, _that.contactRevealPreference,
            _that.requireMutualInterest, _that.callAvailability);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool allowDirectCalls, String contactRevealPreference,
            bool requireMutualInterest, String callAvailability)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactPrivacySettings():
        return $default(_that.allowDirectCalls, _that.contactRevealPreference,
            _that.requireMutualInterest, _that.callAvailability);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool allowDirectCalls, String contactRevealPreference,
            bool requireMutualInterest, String callAvailability)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactPrivacySettings() when $default != null:
        return $default(_that.allowDirectCalls, _that.contactRevealPreference,
            _that.requireMutualInterest, _that.callAvailability);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactPrivacySettings implements ContactPrivacySettings {
  const _ContactPrivacySettings(
      {this.allowDirectCalls = true,
      this.contactRevealPreference = 'PREMIUM_ONLY',
      this.requireMutualInterest = true,
      this.callAvailability = 'ANYTIME'});
  factory _ContactPrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$ContactPrivacySettingsFromJson(json);

  @override
  @JsonKey()
  final bool allowDirectCalls;
  @override
  @JsonKey()
  final String contactRevealPreference;
  @override
  @JsonKey()
  final bool requireMutualInterest;
  @override
  @JsonKey()
  final String callAvailability;

  /// Create a copy of ContactPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactPrivacySettingsCopyWith<_ContactPrivacySettings> get copyWith =>
      __$ContactPrivacySettingsCopyWithImpl<_ContactPrivacySettings>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactPrivacySettingsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactPrivacySettings &&
            (identical(other.allowDirectCalls, allowDirectCalls) ||
                other.allowDirectCalls == allowDirectCalls) &&
            (identical(
                    other.contactRevealPreference, contactRevealPreference) ||
                other.contactRevealPreference == contactRevealPreference) &&
            (identical(other.requireMutualInterest, requireMutualInterest) ||
                other.requireMutualInterest == requireMutualInterest) &&
            (identical(other.callAvailability, callAvailability) ||
                other.callAvailability == callAvailability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, allowDirectCalls,
      contactRevealPreference, requireMutualInterest, callAvailability);

  @override
  String toString() {
    return 'ContactPrivacySettings(allowDirectCalls: $allowDirectCalls, contactRevealPreference: $contactRevealPreference, requireMutualInterest: $requireMutualInterest, callAvailability: $callAvailability)';
  }
}

/// @nodoc
abstract mixin class _$ContactPrivacySettingsCopyWith<$Res>
    implements $ContactPrivacySettingsCopyWith<$Res> {
  factory _$ContactPrivacySettingsCopyWith(_ContactPrivacySettings value,
          $Res Function(_ContactPrivacySettings) _then) =
      __$ContactPrivacySettingsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool allowDirectCalls,
      String contactRevealPreference,
      bool requireMutualInterest,
      String callAvailability});
}

/// @nodoc
class __$ContactPrivacySettingsCopyWithImpl<$Res>
    implements _$ContactPrivacySettingsCopyWith<$Res> {
  __$ContactPrivacySettingsCopyWithImpl(this._self, this._then);

  final _ContactPrivacySettings _self;
  final $Res Function(_ContactPrivacySettings) _then;

  /// Create a copy of ContactPrivacySettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? allowDirectCalls = null,
    Object? contactRevealPreference = null,
    Object? requireMutualInterest = null,
    Object? callAvailability = null,
  }) {
    return _then(_ContactPrivacySettings(
      allowDirectCalls: null == allowDirectCalls
          ? _self.allowDirectCalls
          : allowDirectCalls // ignore: cast_nullable_to_non_nullable
              as bool,
      contactRevealPreference: null == contactRevealPreference
          ? _self.contactRevealPreference
          : contactRevealPreference // ignore: cast_nullable_to_non_nullable
              as String,
      requireMutualInterest: null == requireMutualInterest
          ? _self.requireMutualInterest
          : requireMutualInterest // ignore: cast_nullable_to_non_nullable
              as bool,
      callAvailability: null == callAvailability
          ? _self.callAvailability
          : callAvailability // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$ContactRevealOption {
  String get value;
  String get label;
  String get description;
  String get icon;

  /// Create a copy of ContactRevealOption
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactRevealOptionCopyWith<ContactRevealOption> get copyWith =>
      _$ContactRevealOptionCopyWithImpl<ContactRevealOption>(
          this as ContactRevealOption, _$identity);

  /// Serializes this ContactRevealOption to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactRevealOption &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, value, label, description, icon);

  @override
  String toString() {
    return 'ContactRevealOption(value: $value, label: $label, description: $description, icon: $icon)';
  }
}

/// @nodoc
abstract mixin class $ContactRevealOptionCopyWith<$Res> {
  factory $ContactRevealOptionCopyWith(
          ContactRevealOption value, $Res Function(ContactRevealOption) _then) =
      _$ContactRevealOptionCopyWithImpl;
  @useResult
  $Res call({String value, String label, String description, String icon});
}

/// @nodoc
class _$ContactRevealOptionCopyWithImpl<$Res>
    implements $ContactRevealOptionCopyWith<$Res> {
  _$ContactRevealOptionCopyWithImpl(this._self, this._then);

  final ContactRevealOption _self;
  final $Res Function(ContactRevealOption) _then;

  /// Create a copy of ContactRevealOption
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? value = null,
    Object? label = null,
    Object? description = null,
    Object? icon = null,
  }) {
    return _then(_self.copyWith(
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _self.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactRevealOption].
extension ContactRevealOptionPatterns on ContactRevealOption {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactRevealOption value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealOption() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactRevealOption value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealOption():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactRevealOption value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealOption() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String value, String label, String description, String icon)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealOption() when $default != null:
        return $default(
            _that.value, _that.label, _that.description, _that.icon);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String value, String label, String description, String icon)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealOption():
        return $default(
            _that.value, _that.label, _that.description, _that.icon);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String value, String label, String description, String icon)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealOption() when $default != null:
        return $default(
            _that.value, _that.label, _that.description, _that.icon);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactRevealOption implements ContactRevealOption {
  const _ContactRevealOption(
      {required this.value,
      required this.label,
      required this.description,
      required this.icon});
  factory _ContactRevealOption.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealOptionFromJson(json);

  @override
  final String value;
  @override
  final String label;
  @override
  final String description;
  @override
  final String icon;

  /// Create a copy of ContactRevealOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactRevealOptionCopyWith<_ContactRevealOption> get copyWith =>
      __$ContactRevealOptionCopyWithImpl<_ContactRevealOption>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactRevealOptionToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactRevealOption &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.icon, icon) || other.icon == icon));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, value, label, description, icon);

  @override
  String toString() {
    return 'ContactRevealOption(value: $value, label: $label, description: $description, icon: $icon)';
  }
}

/// @nodoc
abstract mixin class _$ContactRevealOptionCopyWith<$Res>
    implements $ContactRevealOptionCopyWith<$Res> {
  factory _$ContactRevealOptionCopyWith(_ContactRevealOption value,
          $Res Function(_ContactRevealOption) _then) =
      __$ContactRevealOptionCopyWithImpl;
  @override
  @useResult
  $Res call({String value, String label, String description, String icon});
}

/// @nodoc
class __$ContactRevealOptionCopyWithImpl<$Res>
    implements _$ContactRevealOptionCopyWith<$Res> {
  __$ContactRevealOptionCopyWithImpl(this._self, this._then);

  final _ContactRevealOption _self;
  final $Res Function(_ContactRevealOption) _then;

  /// Create a copy of ContactRevealOption
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? value = null,
    Object? label = null,
    Object? description = null,
    Object? icon = null,
  }) {
    return _then(_ContactRevealOption(
      value: null == value
          ? _self.value
          : value // ignore: cast_nullable_to_non_nullable
              as String,
      label: null == label
          ? _self.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      icon: null == icon
          ? _self.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$ContactAccessHistory {
  String get id;
  String get accessorId;
  String get contactOwnerId;
  DateTime get accessedAt;
  String? get accessType;
  String? get contactNumber;
  String? get accessReason;
  bool get isPremiumAccess;
  String? get featurePurchaseId;
  int? get callDuration;
  String? get callStatus;
  String? get platform;
  ContactAccessUser? get accessor;
  ContactAccessUser? get contactOwner;

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactAccessHistoryCopyWith<ContactAccessHistory> get copyWith =>
      _$ContactAccessHistoryCopyWithImpl<ContactAccessHistory>(
          this as ContactAccessHistory, _$identity);

  /// Serializes this ContactAccessHistory to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactAccessHistory &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accessorId, accessorId) ||
                other.accessorId == accessorId) &&
            (identical(other.contactOwnerId, contactOwnerId) ||
                other.contactOwnerId == contactOwnerId) &&
            (identical(other.accessedAt, accessedAt) ||
                other.accessedAt == accessedAt) &&
            (identical(other.accessType, accessType) ||
                other.accessType == accessType) &&
            (identical(other.contactNumber, contactNumber) ||
                other.contactNumber == contactNumber) &&
            (identical(other.accessReason, accessReason) ||
                other.accessReason == accessReason) &&
            (identical(other.isPremiumAccess, isPremiumAccess) ||
                other.isPremiumAccess == isPremiumAccess) &&
            (identical(other.featurePurchaseId, featurePurchaseId) ||
                other.featurePurchaseId == featurePurchaseId) &&
            (identical(other.callDuration, callDuration) ||
                other.callDuration == callDuration) &&
            (identical(other.callStatus, callStatus) ||
                other.callStatus == callStatus) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.accessor, accessor) ||
                other.accessor == accessor) &&
            (identical(other.contactOwner, contactOwner) ||
                other.contactOwner == contactOwner));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      accessorId,
      contactOwnerId,
      accessedAt,
      accessType,
      contactNumber,
      accessReason,
      isPremiumAccess,
      featurePurchaseId,
      callDuration,
      callStatus,
      platform,
      accessor,
      contactOwner);

  @override
  String toString() {
    return 'ContactAccessHistory(id: $id, accessorId: $accessorId, contactOwnerId: $contactOwnerId, accessedAt: $accessedAt, accessType: $accessType, contactNumber: $contactNumber, accessReason: $accessReason, isPremiumAccess: $isPremiumAccess, featurePurchaseId: $featurePurchaseId, callDuration: $callDuration, callStatus: $callStatus, platform: $platform, accessor: $accessor, contactOwner: $contactOwner)';
  }
}

/// @nodoc
abstract mixin class $ContactAccessHistoryCopyWith<$Res> {
  factory $ContactAccessHistoryCopyWith(ContactAccessHistory value,
          $Res Function(ContactAccessHistory) _then) =
      _$ContactAccessHistoryCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String accessorId,
      String contactOwnerId,
      DateTime accessedAt,
      String? accessType,
      String? contactNumber,
      String? accessReason,
      bool isPremiumAccess,
      String? featurePurchaseId,
      int? callDuration,
      String? callStatus,
      String? platform,
      ContactAccessUser? accessor,
      ContactAccessUser? contactOwner});

  $ContactAccessUserCopyWith<$Res>? get accessor;
  $ContactAccessUserCopyWith<$Res>? get contactOwner;
}

/// @nodoc
class _$ContactAccessHistoryCopyWithImpl<$Res>
    implements $ContactAccessHistoryCopyWith<$Res> {
  _$ContactAccessHistoryCopyWithImpl(this._self, this._then);

  final ContactAccessHistory _self;
  final $Res Function(ContactAccessHistory) _then;

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accessorId = null,
    Object? contactOwnerId = null,
    Object? accessedAt = null,
    Object? accessType = freezed,
    Object? contactNumber = freezed,
    Object? accessReason = freezed,
    Object? isPremiumAccess = null,
    Object? featurePurchaseId = freezed,
    Object? callDuration = freezed,
    Object? callStatus = freezed,
    Object? platform = freezed,
    Object? accessor = freezed,
    Object? contactOwner = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accessorId: null == accessorId
          ? _self.accessorId
          : accessorId // ignore: cast_nullable_to_non_nullable
              as String,
      contactOwnerId: null == contactOwnerId
          ? _self.contactOwnerId
          : contactOwnerId // ignore: cast_nullable_to_non_nullable
              as String,
      accessedAt: null == accessedAt
          ? _self.accessedAt
          : accessedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      accessType: freezed == accessType
          ? _self.accessType
          : accessType // ignore: cast_nullable_to_non_nullable
              as String?,
      contactNumber: freezed == contactNumber
          ? _self.contactNumber
          : contactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      accessReason: freezed == accessReason
          ? _self.accessReason
          : accessReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isPremiumAccess: null == isPremiumAccess
          ? _self.isPremiumAccess
          : isPremiumAccess // ignore: cast_nullable_to_non_nullable
              as bool,
      featurePurchaseId: freezed == featurePurchaseId
          ? _self.featurePurchaseId
          : featurePurchaseId // ignore: cast_nullable_to_non_nullable
              as String?,
      callDuration: freezed == callDuration
          ? _self.callDuration
          : callDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      callStatus: freezed == callStatus
          ? _self.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      platform: freezed == platform
          ? _self.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
      accessor: freezed == accessor
          ? _self.accessor
          : accessor // ignore: cast_nullable_to_non_nullable
              as ContactAccessUser?,
      contactOwner: freezed == contactOwner
          ? _self.contactOwner
          : contactOwner // ignore: cast_nullable_to_non_nullable
              as ContactAccessUser?,
    ));
  }

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactAccessUserCopyWith<$Res>? get accessor {
    if (_self.accessor == null) {
      return null;
    }

    return $ContactAccessUserCopyWith<$Res>(_self.accessor!, (value) {
      return _then(_self.copyWith(accessor: value));
    });
  }

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactAccessUserCopyWith<$Res>? get contactOwner {
    if (_self.contactOwner == null) {
      return null;
    }

    return $ContactAccessUserCopyWith<$Res>(_self.contactOwner!, (value) {
      return _then(_self.copyWith(contactOwner: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ContactAccessHistory].
extension ContactAccessHistoryPatterns on ContactAccessHistory {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactAccessHistory value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactAccessHistory() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactAccessHistory value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessHistory():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactAccessHistory value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessHistory() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String accessorId,
            String contactOwnerId,
            DateTime accessedAt,
            String? accessType,
            String? contactNumber,
            String? accessReason,
            bool isPremiumAccess,
            String? featurePurchaseId,
            int? callDuration,
            String? callStatus,
            String? platform,
            ContactAccessUser? accessor,
            ContactAccessUser? contactOwner)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactAccessHistory() when $default != null:
        return $default(
            _that.id,
            _that.accessorId,
            _that.contactOwnerId,
            _that.accessedAt,
            _that.accessType,
            _that.contactNumber,
            _that.accessReason,
            _that.isPremiumAccess,
            _that.featurePurchaseId,
            _that.callDuration,
            _that.callStatus,
            _that.platform,
            _that.accessor,
            _that.contactOwner);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String accessorId,
            String contactOwnerId,
            DateTime accessedAt,
            String? accessType,
            String? contactNumber,
            String? accessReason,
            bool isPremiumAccess,
            String? featurePurchaseId,
            int? callDuration,
            String? callStatus,
            String? platform,
            ContactAccessUser? accessor,
            ContactAccessUser? contactOwner)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessHistory():
        return $default(
            _that.id,
            _that.accessorId,
            _that.contactOwnerId,
            _that.accessedAt,
            _that.accessType,
            _that.contactNumber,
            _that.accessReason,
            _that.isPremiumAccess,
            _that.featurePurchaseId,
            _that.callDuration,
            _that.callStatus,
            _that.platform,
            _that.accessor,
            _that.contactOwner);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String accessorId,
            String contactOwnerId,
            DateTime accessedAt,
            String? accessType,
            String? contactNumber,
            String? accessReason,
            bool isPremiumAccess,
            String? featurePurchaseId,
            int? callDuration,
            String? callStatus,
            String? platform,
            ContactAccessUser? accessor,
            ContactAccessUser? contactOwner)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessHistory() when $default != null:
        return $default(
            _that.id,
            _that.accessorId,
            _that.contactOwnerId,
            _that.accessedAt,
            _that.accessType,
            _that.contactNumber,
            _that.accessReason,
            _that.isPremiumAccess,
            _that.featurePurchaseId,
            _that.callDuration,
            _that.callStatus,
            _that.platform,
            _that.accessor,
            _that.contactOwner);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactAccessHistory implements ContactAccessHistory {
  const _ContactAccessHistory(
      {required this.id,
      required this.accessorId,
      required this.contactOwnerId,
      required this.accessedAt,
      this.accessType,
      this.contactNumber,
      this.accessReason,
      this.isPremiumAccess = false,
      this.featurePurchaseId,
      this.callDuration,
      this.callStatus,
      this.platform,
      this.accessor,
      this.contactOwner});
  factory _ContactAccessHistory.fromJson(Map<String, dynamic> json) =>
      _$ContactAccessHistoryFromJson(json);

  @override
  final String id;
  @override
  final String accessorId;
  @override
  final String contactOwnerId;
  @override
  final DateTime accessedAt;
  @override
  final String? accessType;
  @override
  final String? contactNumber;
  @override
  final String? accessReason;
  @override
  @JsonKey()
  final bool isPremiumAccess;
  @override
  final String? featurePurchaseId;
  @override
  final int? callDuration;
  @override
  final String? callStatus;
  @override
  final String? platform;
  @override
  final ContactAccessUser? accessor;
  @override
  final ContactAccessUser? contactOwner;

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactAccessHistoryCopyWith<_ContactAccessHistory> get copyWith =>
      __$ContactAccessHistoryCopyWithImpl<_ContactAccessHistory>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactAccessHistoryToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactAccessHistory &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accessorId, accessorId) ||
                other.accessorId == accessorId) &&
            (identical(other.contactOwnerId, contactOwnerId) ||
                other.contactOwnerId == contactOwnerId) &&
            (identical(other.accessedAt, accessedAt) ||
                other.accessedAt == accessedAt) &&
            (identical(other.accessType, accessType) ||
                other.accessType == accessType) &&
            (identical(other.contactNumber, contactNumber) ||
                other.contactNumber == contactNumber) &&
            (identical(other.accessReason, accessReason) ||
                other.accessReason == accessReason) &&
            (identical(other.isPremiumAccess, isPremiumAccess) ||
                other.isPremiumAccess == isPremiumAccess) &&
            (identical(other.featurePurchaseId, featurePurchaseId) ||
                other.featurePurchaseId == featurePurchaseId) &&
            (identical(other.callDuration, callDuration) ||
                other.callDuration == callDuration) &&
            (identical(other.callStatus, callStatus) ||
                other.callStatus == callStatus) &&
            (identical(other.platform, platform) ||
                other.platform == platform) &&
            (identical(other.accessor, accessor) ||
                other.accessor == accessor) &&
            (identical(other.contactOwner, contactOwner) ||
                other.contactOwner == contactOwner));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      accessorId,
      contactOwnerId,
      accessedAt,
      accessType,
      contactNumber,
      accessReason,
      isPremiumAccess,
      featurePurchaseId,
      callDuration,
      callStatus,
      platform,
      accessor,
      contactOwner);

  @override
  String toString() {
    return 'ContactAccessHistory(id: $id, accessorId: $accessorId, contactOwnerId: $contactOwnerId, accessedAt: $accessedAt, accessType: $accessType, contactNumber: $contactNumber, accessReason: $accessReason, isPremiumAccess: $isPremiumAccess, featurePurchaseId: $featurePurchaseId, callDuration: $callDuration, callStatus: $callStatus, platform: $platform, accessor: $accessor, contactOwner: $contactOwner)';
  }
}

/// @nodoc
abstract mixin class _$ContactAccessHistoryCopyWith<$Res>
    implements $ContactAccessHistoryCopyWith<$Res> {
  factory _$ContactAccessHistoryCopyWith(_ContactAccessHistory value,
          $Res Function(_ContactAccessHistory) _then) =
      __$ContactAccessHistoryCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String accessorId,
      String contactOwnerId,
      DateTime accessedAt,
      String? accessType,
      String? contactNumber,
      String? accessReason,
      bool isPremiumAccess,
      String? featurePurchaseId,
      int? callDuration,
      String? callStatus,
      String? platform,
      ContactAccessUser? accessor,
      ContactAccessUser? contactOwner});

  @override
  $ContactAccessUserCopyWith<$Res>? get accessor;
  @override
  $ContactAccessUserCopyWith<$Res>? get contactOwner;
}

/// @nodoc
class __$ContactAccessHistoryCopyWithImpl<$Res>
    implements _$ContactAccessHistoryCopyWith<$Res> {
  __$ContactAccessHistoryCopyWithImpl(this._self, this._then);

  final _ContactAccessHistory _self;
  final $Res Function(_ContactAccessHistory) _then;

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? accessorId = null,
    Object? contactOwnerId = null,
    Object? accessedAt = null,
    Object? accessType = freezed,
    Object? contactNumber = freezed,
    Object? accessReason = freezed,
    Object? isPremiumAccess = null,
    Object? featurePurchaseId = freezed,
    Object? callDuration = freezed,
    Object? callStatus = freezed,
    Object? platform = freezed,
    Object? accessor = freezed,
    Object? contactOwner = freezed,
  }) {
    return _then(_ContactAccessHistory(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accessorId: null == accessorId
          ? _self.accessorId
          : accessorId // ignore: cast_nullable_to_non_nullable
              as String,
      contactOwnerId: null == contactOwnerId
          ? _self.contactOwnerId
          : contactOwnerId // ignore: cast_nullable_to_non_nullable
              as String,
      accessedAt: null == accessedAt
          ? _self.accessedAt
          : accessedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      accessType: freezed == accessType
          ? _self.accessType
          : accessType // ignore: cast_nullable_to_non_nullable
              as String?,
      contactNumber: freezed == contactNumber
          ? _self.contactNumber
          : contactNumber // ignore: cast_nullable_to_non_nullable
              as String?,
      accessReason: freezed == accessReason
          ? _self.accessReason
          : accessReason // ignore: cast_nullable_to_non_nullable
              as String?,
      isPremiumAccess: null == isPremiumAccess
          ? _self.isPremiumAccess
          : isPremiumAccess // ignore: cast_nullable_to_non_nullable
              as bool,
      featurePurchaseId: freezed == featurePurchaseId
          ? _self.featurePurchaseId
          : featurePurchaseId // ignore: cast_nullable_to_non_nullable
              as String?,
      callDuration: freezed == callDuration
          ? _self.callDuration
          : callDuration // ignore: cast_nullable_to_non_nullable
              as int?,
      callStatus: freezed == callStatus
          ? _self.callStatus
          : callStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      platform: freezed == platform
          ? _self.platform
          : platform // ignore: cast_nullable_to_non_nullable
              as String?,
      accessor: freezed == accessor
          ? _self.accessor
          : accessor // ignore: cast_nullable_to_non_nullable
              as ContactAccessUser?,
      contactOwner: freezed == contactOwner
          ? _self.contactOwner
          : contactOwner // ignore: cast_nullable_to_non_nullable
              as ContactAccessUser?,
    ));
  }

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactAccessUserCopyWith<$Res>? get accessor {
    if (_self.accessor == null) {
      return null;
    }

    return $ContactAccessUserCopyWith<$Res>(_self.accessor!, (value) {
      return _then(_self.copyWith(accessor: value));
    });
  }

  /// Create a copy of ContactAccessHistory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ContactAccessUserCopyWith<$Res>? get contactOwner {
    if (_self.contactOwner == null) {
      return null;
    }

    return $ContactAccessUserCopyWith<$Res>(_self.contactOwner!, (value) {
      return _then(_self.copyWith(contactOwner: value));
    });
  }
}

/// @nodoc
mixin _$ContactAccessUser {
  String get id;
  String? get fullName;
  String? get profilePhoto;
  int? get age;
  String? get location;

  /// Create a copy of ContactAccessUser
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactAccessUserCopyWith<ContactAccessUser> get copyWith =>
      _$ContactAccessUserCopyWithImpl<ContactAccessUser>(
          this as ContactAccessUser, _$identity);

  /// Serializes this ContactAccessUser to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactAccessUser &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.profilePhoto, profilePhoto) ||
                other.profilePhoto == profilePhoto) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.location, location) ||
                other.location == location));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, fullName, profilePhoto, age, location);

  @override
  String toString() {
    return 'ContactAccessUser(id: $id, fullName: $fullName, profilePhoto: $profilePhoto, age: $age, location: $location)';
  }
}

/// @nodoc
abstract mixin class $ContactAccessUserCopyWith<$Res> {
  factory $ContactAccessUserCopyWith(
          ContactAccessUser value, $Res Function(ContactAccessUser) _then) =
      _$ContactAccessUserCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String? fullName,
      String? profilePhoto,
      int? age,
      String? location});
}

/// @nodoc
class _$ContactAccessUserCopyWithImpl<$Res>
    implements $ContactAccessUserCopyWith<$Res> {
  _$ContactAccessUserCopyWithImpl(this._self, this._then);

  final ContactAccessUser _self;
  final $Res Function(ContactAccessUser) _then;

  /// Create a copy of ContactAccessUser
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? fullName = freezed,
    Object? profilePhoto = freezed,
    Object? age = freezed,
    Object? location = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePhoto: freezed == profilePhoto
          ? _self.profilePhoto
          : profilePhoto // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactAccessUser].
extension ContactAccessUserPatterns on ContactAccessUser {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactAccessUser value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactAccessUser() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactAccessUser value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessUser():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactAccessUser value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessUser() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String id, String? fullName, String? profilePhoto,
            int? age, String? location)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactAccessUser() when $default != null:
        return $default(_that.id, _that.fullName, _that.profilePhoto, _that.age,
            _that.location);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String id, String? fullName, String? profilePhoto,
            int? age, String? location)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessUser():
        return $default(_that.id, _that.fullName, _that.profilePhoto, _that.age,
            _that.location);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String id, String? fullName, String? profilePhoto,
            int? age, String? location)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactAccessUser() when $default != null:
        return $default(_that.id, _that.fullName, _that.profilePhoto, _that.age,
            _that.location);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactAccessUser implements ContactAccessUser {
  const _ContactAccessUser(
      {required this.id,
      this.fullName,
      this.profilePhoto,
      this.age,
      this.location});
  factory _ContactAccessUser.fromJson(Map<String, dynamic> json) =>
      _$ContactAccessUserFromJson(json);

  @override
  final String id;
  @override
  final String? fullName;
  @override
  final String? profilePhoto;
  @override
  final int? age;
  @override
  final String? location;

  /// Create a copy of ContactAccessUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactAccessUserCopyWith<_ContactAccessUser> get copyWith =>
      __$ContactAccessUserCopyWithImpl<_ContactAccessUser>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactAccessUserToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactAccessUser &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.fullName, fullName) ||
                other.fullName == fullName) &&
            (identical(other.profilePhoto, profilePhoto) ||
                other.profilePhoto == profilePhoto) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.location, location) ||
                other.location == location));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, fullName, profilePhoto, age, location);

  @override
  String toString() {
    return 'ContactAccessUser(id: $id, fullName: $fullName, profilePhoto: $profilePhoto, age: $age, location: $location)';
  }
}

/// @nodoc
abstract mixin class _$ContactAccessUserCopyWith<$Res>
    implements $ContactAccessUserCopyWith<$Res> {
  factory _$ContactAccessUserCopyWith(
          _ContactAccessUser value, $Res Function(_ContactAccessUser) _then) =
      __$ContactAccessUserCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String? fullName,
      String? profilePhoto,
      int? age,
      String? location});
}

/// @nodoc
class __$ContactAccessUserCopyWithImpl<$Res>
    implements _$ContactAccessUserCopyWith<$Res> {
  __$ContactAccessUserCopyWithImpl(this._self, this._then);

  final _ContactAccessUser _self;
  final $Res Function(_ContactAccessUser) _then;

  /// Create a copy of ContactAccessUser
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? fullName = freezed,
    Object? profilePhoto = freezed,
    Object? age = freezed,
    Object? location = freezed,
  }) {
    return _then(_ContactAccessUser(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      fullName: freezed == fullName
          ? _self.fullName
          : fullName // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePhoto: freezed == profilePhoto
          ? _self.profilePhoto
          : profilePhoto // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$ContactRevealStats {
  int get totalRevealed;
  int get totalRequested;
  int get premiumReveals;
  int get mutualInterestReveals;
  int get acceptedInterestReveals;
  int get callsInitiated;
  int get callsConnected;
  Map<String, int>? get platformStats;
  Map<String, int>? get reasonStats;

  /// Create a copy of ContactRevealStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ContactRevealStatsCopyWith<ContactRevealStats> get copyWith =>
      _$ContactRevealStatsCopyWithImpl<ContactRevealStats>(
          this as ContactRevealStats, _$identity);

  /// Serializes this ContactRevealStats to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ContactRevealStats &&
            (identical(other.totalRevealed, totalRevealed) ||
                other.totalRevealed == totalRevealed) &&
            (identical(other.totalRequested, totalRequested) ||
                other.totalRequested == totalRequested) &&
            (identical(other.premiumReveals, premiumReveals) ||
                other.premiumReveals == premiumReveals) &&
            (identical(other.mutualInterestReveals, mutualInterestReveals) ||
                other.mutualInterestReveals == mutualInterestReveals) &&
            (identical(
                    other.acceptedInterestReveals, acceptedInterestReveals) ||
                other.acceptedInterestReveals == acceptedInterestReveals) &&
            (identical(other.callsInitiated, callsInitiated) ||
                other.callsInitiated == callsInitiated) &&
            (identical(other.callsConnected, callsConnected) ||
                other.callsConnected == callsConnected) &&
            const DeepCollectionEquality()
                .equals(other.platformStats, platformStats) &&
            const DeepCollectionEquality()
                .equals(other.reasonStats, reasonStats));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalRevealed,
      totalRequested,
      premiumReveals,
      mutualInterestReveals,
      acceptedInterestReveals,
      callsInitiated,
      callsConnected,
      const DeepCollectionEquality().hash(platformStats),
      const DeepCollectionEquality().hash(reasonStats));

  @override
  String toString() {
    return 'ContactRevealStats(totalRevealed: $totalRevealed, totalRequested: $totalRequested, premiumReveals: $premiumReveals, mutualInterestReveals: $mutualInterestReveals, acceptedInterestReveals: $acceptedInterestReveals, callsInitiated: $callsInitiated, callsConnected: $callsConnected, platformStats: $platformStats, reasonStats: $reasonStats)';
  }
}

/// @nodoc
abstract mixin class $ContactRevealStatsCopyWith<$Res> {
  factory $ContactRevealStatsCopyWith(
          ContactRevealStats value, $Res Function(ContactRevealStats) _then) =
      _$ContactRevealStatsCopyWithImpl;
  @useResult
  $Res call(
      {int totalRevealed,
      int totalRequested,
      int premiumReveals,
      int mutualInterestReveals,
      int acceptedInterestReveals,
      int callsInitiated,
      int callsConnected,
      Map<String, int>? platformStats,
      Map<String, int>? reasonStats});
}

/// @nodoc
class _$ContactRevealStatsCopyWithImpl<$Res>
    implements $ContactRevealStatsCopyWith<$Res> {
  _$ContactRevealStatsCopyWithImpl(this._self, this._then);

  final ContactRevealStats _self;
  final $Res Function(ContactRevealStats) _then;

  /// Create a copy of ContactRevealStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalRevealed = null,
    Object? totalRequested = null,
    Object? premiumReveals = null,
    Object? mutualInterestReveals = null,
    Object? acceptedInterestReveals = null,
    Object? callsInitiated = null,
    Object? callsConnected = null,
    Object? platformStats = freezed,
    Object? reasonStats = freezed,
  }) {
    return _then(_self.copyWith(
      totalRevealed: null == totalRevealed
          ? _self.totalRevealed
          : totalRevealed // ignore: cast_nullable_to_non_nullable
              as int,
      totalRequested: null == totalRequested
          ? _self.totalRequested
          : totalRequested // ignore: cast_nullable_to_non_nullable
              as int,
      premiumReveals: null == premiumReveals
          ? _self.premiumReveals
          : premiumReveals // ignore: cast_nullable_to_non_nullable
              as int,
      mutualInterestReveals: null == mutualInterestReveals
          ? _self.mutualInterestReveals
          : mutualInterestReveals // ignore: cast_nullable_to_non_nullable
              as int,
      acceptedInterestReveals: null == acceptedInterestReveals
          ? _self.acceptedInterestReveals
          : acceptedInterestReveals // ignore: cast_nullable_to_non_nullable
              as int,
      callsInitiated: null == callsInitiated
          ? _self.callsInitiated
          : callsInitiated // ignore: cast_nullable_to_non_nullable
              as int,
      callsConnected: null == callsConnected
          ? _self.callsConnected
          : callsConnected // ignore: cast_nullable_to_non_nullable
              as int,
      platformStats: freezed == platformStats
          ? _self.platformStats
          : platformStats // ignore: cast_nullable_to_non_nullable
              as Map<String, int>?,
      reasonStats: freezed == reasonStats
          ? _self.reasonStats
          : reasonStats // ignore: cast_nullable_to_non_nullable
              as Map<String, int>?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ContactRevealStats].
extension ContactRevealStatsPatterns on ContactRevealStats {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ContactRevealStats value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealStats() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ContactRevealStats value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealStats():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ContactRevealStats value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealStats() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int totalRevealed,
            int totalRequested,
            int premiumReveals,
            int mutualInterestReveals,
            int acceptedInterestReveals,
            int callsInitiated,
            int callsConnected,
            Map<String, int>? platformStats,
            Map<String, int>? reasonStats)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ContactRevealStats() when $default != null:
        return $default(
            _that.totalRevealed,
            _that.totalRequested,
            _that.premiumReveals,
            _that.mutualInterestReveals,
            _that.acceptedInterestReveals,
            _that.callsInitiated,
            _that.callsConnected,
            _that.platformStats,
            _that.reasonStats);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int totalRevealed,
            int totalRequested,
            int premiumReveals,
            int mutualInterestReveals,
            int acceptedInterestReveals,
            int callsInitiated,
            int callsConnected,
            Map<String, int>? platformStats,
            Map<String, int>? reasonStats)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealStats():
        return $default(
            _that.totalRevealed,
            _that.totalRequested,
            _that.premiumReveals,
            _that.mutualInterestReveals,
            _that.acceptedInterestReveals,
            _that.callsInitiated,
            _that.callsConnected,
            _that.platformStats,
            _that.reasonStats);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int totalRevealed,
            int totalRequested,
            int premiumReveals,
            int mutualInterestReveals,
            int acceptedInterestReveals,
            int callsInitiated,
            int callsConnected,
            Map<String, int>? platformStats,
            Map<String, int>? reasonStats)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ContactRevealStats() when $default != null:
        return $default(
            _that.totalRevealed,
            _that.totalRequested,
            _that.premiumReveals,
            _that.mutualInterestReveals,
            _that.acceptedInterestReveals,
            _that.callsInitiated,
            _that.callsConnected,
            _that.platformStats,
            _that.reasonStats);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ContactRevealStats implements ContactRevealStats {
  const _ContactRevealStats(
      {this.totalRevealed = 0,
      this.totalRequested = 0,
      this.premiumReveals = 0,
      this.mutualInterestReveals = 0,
      this.acceptedInterestReveals = 0,
      this.callsInitiated = 0,
      this.callsConnected = 0,
      final Map<String, int>? platformStats,
      final Map<String, int>? reasonStats})
      : _platformStats = platformStats,
        _reasonStats = reasonStats;
  factory _ContactRevealStats.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealStatsFromJson(json);

  @override
  @JsonKey()
  final int totalRevealed;
  @override
  @JsonKey()
  final int totalRequested;
  @override
  @JsonKey()
  final int premiumReveals;
  @override
  @JsonKey()
  final int mutualInterestReveals;
  @override
  @JsonKey()
  final int acceptedInterestReveals;
  @override
  @JsonKey()
  final int callsInitiated;
  @override
  @JsonKey()
  final int callsConnected;
  final Map<String, int>? _platformStats;
  @override
  Map<String, int>? get platformStats {
    final value = _platformStats;
    if (value == null) return null;
    if (_platformStats is EqualUnmodifiableMapView) return _platformStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, int>? _reasonStats;
  @override
  Map<String, int>? get reasonStats {
    final value = _reasonStats;
    if (value == null) return null;
    if (_reasonStats is EqualUnmodifiableMapView) return _reasonStats;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of ContactRevealStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ContactRevealStatsCopyWith<_ContactRevealStats> get copyWith =>
      __$ContactRevealStatsCopyWithImpl<_ContactRevealStats>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ContactRevealStatsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ContactRevealStats &&
            (identical(other.totalRevealed, totalRevealed) ||
                other.totalRevealed == totalRevealed) &&
            (identical(other.totalRequested, totalRequested) ||
                other.totalRequested == totalRequested) &&
            (identical(other.premiumReveals, premiumReveals) ||
                other.premiumReveals == premiumReveals) &&
            (identical(other.mutualInterestReveals, mutualInterestReveals) ||
                other.mutualInterestReveals == mutualInterestReveals) &&
            (identical(
                    other.acceptedInterestReveals, acceptedInterestReveals) ||
                other.acceptedInterestReveals == acceptedInterestReveals) &&
            (identical(other.callsInitiated, callsInitiated) ||
                other.callsInitiated == callsInitiated) &&
            (identical(other.callsConnected, callsConnected) ||
                other.callsConnected == callsConnected) &&
            const DeepCollectionEquality()
                .equals(other._platformStats, _platformStats) &&
            const DeepCollectionEquality()
                .equals(other._reasonStats, _reasonStats));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      totalRevealed,
      totalRequested,
      premiumReveals,
      mutualInterestReveals,
      acceptedInterestReveals,
      callsInitiated,
      callsConnected,
      const DeepCollectionEquality().hash(_platformStats),
      const DeepCollectionEquality().hash(_reasonStats));

  @override
  String toString() {
    return 'ContactRevealStats(totalRevealed: $totalRevealed, totalRequested: $totalRequested, premiumReveals: $premiumReveals, mutualInterestReveals: $mutualInterestReveals, acceptedInterestReveals: $acceptedInterestReveals, callsInitiated: $callsInitiated, callsConnected: $callsConnected, platformStats: $platformStats, reasonStats: $reasonStats)';
  }
}

/// @nodoc
abstract mixin class _$ContactRevealStatsCopyWith<$Res>
    implements $ContactRevealStatsCopyWith<$Res> {
  factory _$ContactRevealStatsCopyWith(
          _ContactRevealStats value, $Res Function(_ContactRevealStats) _then) =
      __$ContactRevealStatsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int totalRevealed,
      int totalRequested,
      int premiumReveals,
      int mutualInterestReveals,
      int acceptedInterestReveals,
      int callsInitiated,
      int callsConnected,
      Map<String, int>? platformStats,
      Map<String, int>? reasonStats});
}

/// @nodoc
class __$ContactRevealStatsCopyWithImpl<$Res>
    implements _$ContactRevealStatsCopyWith<$Res> {
  __$ContactRevealStatsCopyWithImpl(this._self, this._then);

  final _ContactRevealStats _self;
  final $Res Function(_ContactRevealStats) _then;

  /// Create a copy of ContactRevealStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? totalRevealed = null,
    Object? totalRequested = null,
    Object? premiumReveals = null,
    Object? mutualInterestReveals = null,
    Object? acceptedInterestReveals = null,
    Object? callsInitiated = null,
    Object? callsConnected = null,
    Object? platformStats = freezed,
    Object? reasonStats = freezed,
  }) {
    return _then(_ContactRevealStats(
      totalRevealed: null == totalRevealed
          ? _self.totalRevealed
          : totalRevealed // ignore: cast_nullable_to_non_nullable
              as int,
      totalRequested: null == totalRequested
          ? _self.totalRequested
          : totalRequested // ignore: cast_nullable_to_non_nullable
              as int,
      premiumReveals: null == premiumReveals
          ? _self.premiumReveals
          : premiumReveals // ignore: cast_nullable_to_non_nullable
              as int,
      mutualInterestReveals: null == mutualInterestReveals
          ? _self.mutualInterestReveals
          : mutualInterestReveals // ignore: cast_nullable_to_non_nullable
              as int,
      acceptedInterestReveals: null == acceptedInterestReveals
          ? _self.acceptedInterestReveals
          : acceptedInterestReveals // ignore: cast_nullable_to_non_nullable
              as int,
      callsInitiated: null == callsInitiated
          ? _self.callsInitiated
          : callsInitiated // ignore: cast_nullable_to_non_nullable
              as int,
      callsConnected: null == callsConnected
          ? _self.callsConnected
          : callsConnected // ignore: cast_nullable_to_non_nullable
              as int,
      platformStats: freezed == platformStats
          ? _self._platformStats
          : platformStats // ignore: cast_nullable_to_non_nullable
              as Map<String, int>?,
      reasonStats: freezed == reasonStats
          ? _self._reasonStats
          : reasonStats // ignore: cast_nullable_to_non_nullable
              as Map<String, int>?,
    ));
  }
}

// dart format on
