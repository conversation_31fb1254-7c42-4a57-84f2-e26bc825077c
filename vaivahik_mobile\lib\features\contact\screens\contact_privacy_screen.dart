import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../providers/contact_provider.dart';
import '../widgets/contact_privacy_settings_card.dart';
import '../models/contact_models.dart';

class ContactPrivacyScreen extends StatefulWidget {
  const ContactPrivacyScreen({super.key});

  @override
  State<ContactPrivacyScreen> createState() => _ContactPrivacyScreenState();
}

class _ContactPrivacyScreenState extends State<ContactPrivacyScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ContactProvider>().loadPrivacySettings();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Consumer<ContactProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.privacySettings == null) {
            return const LoadingWidget(size: 50);
          }

          if (provider.error != null && provider.privacySettings == null) {
            return CustomErrorWidget(
              message: provider.error!,
              onRetry: () => provider.loadPrivacySettings(),
            );
          }

          return RefreshIndicator(
            onRefresh: () => provider.loadPrivacySettings(),
            child: SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Privacy Settings Card
                  if (provider.privacySettings != null)
                    ContactPrivacySettingsCard(
                      settings: provider.privacySettings!,
                      onUpdate: (settings) => _updateSettings(provider, settings),
                    ),
                  
                  const SizedBox(height: 20),
                  
                  // Privacy Information
                  _buildPrivacyInfo(),
                  
                  const SizedBox(height: 20),
                  
                  // Contact Reveal Options
                  _buildContactRevealOptions(),
                  
                  const SizedBox(height: 20),
                  
                  // Call Availability Options
                  _buildCallAvailabilityOptions(),
                  
                  const SizedBox(height: 20),
                  
                  // Security Tips
                  _buildSecurityTips(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Privacy Settings',
        style: AppTextStyles.h2.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
    );
  }

  Widget _buildPrivacyInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Privacy Information',
                  style: AppTextStyles.h4.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Your contact information is protected by our privacy policy. You have full control over who can access your contact details and when they can contact you.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactRevealOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Reveal Options',
              style: AppTextStyles.h4.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...ContactRevealPreference.values.map((pref) => 
              _buildOptionTile(
                icon: pref.icon,
                title: pref.label,
                description: _getRevealDescription(pref.value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCallAvailabilityOptions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Call Availability Options',
              style: AppTextStyles.h4.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...CallAvailability.values.map((availability) => 
              _buildOptionTile(
                icon: availability.icon,
                title: availability.label,
                description: _getAvailabilityDescription(availability.value),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityTips() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Security Tips',
                  style: AppTextStyles.h4.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSecurityTip(
              '🔒',
              'Never share personal information in initial conversations',
            ),
            _buildSecurityTip(
              '📞',
              'Use the app\'s calling feature for initial contact',
            ),
            _buildSecurityTip(
              '⚠️',
              'Report suspicious behavior immediately',
            ),
            _buildSecurityTip(
              '🛡️',
              'Keep your privacy settings updated regularly',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionTile({
    required String icon,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(icon, style: const TextStyle(fontSize: 20)),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecurityTip(String icon, String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(icon, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              tip,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getRevealDescription(String value) {
    switch (value) {
      case 'PREMIUM_ONLY':
        return 'Only premium users can access your contact information';
      case 'MUTUAL_INTEREST':
        return 'Users who have mutual interest can access your contact';
      case 'ACCEPTED_INTEREST':
        return 'Users whose interest you have accepted can access your contact';
      case 'NEVER':
        return 'Your contact information will never be shared automatically';
      default:
        return '';
    }
  }

  String _getAvailabilityDescription(String value) {
    switch (value) {
      case 'ANYTIME':
        return 'You can be contacted at any time';
      case 'BUSINESS_HOURS':
        return 'Prefer calls during business hours (9 AM - 6 PM)';
      case 'EVENING_ONLY':
        return 'Prefer calls in the evening (6 PM - 10 PM)';
      case 'WEEKEND_ONLY':
        return 'Prefer calls only on weekends';
      default:
        return '';
    }
  }

  Future<void> _updateSettings(ContactProvider provider, ContactPrivacySettings settings) async {
    final success = await provider.updatePrivacySettings(settings);
    
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Privacy settings updated successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update settings: ${provider.error ?? 'Unknown error'}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
