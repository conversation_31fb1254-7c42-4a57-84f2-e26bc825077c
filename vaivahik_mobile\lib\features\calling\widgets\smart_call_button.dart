import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../contact/services/contact_service.dart';
import '../../premium/screens/premium_plans_screen.dart';

class SmartCallButton extends ConsumerStatefulWidget {
  final String targetUserId;
  final String targetUserName;
  final VoidCallback? onCallInitiated;
  final VoidCallback? onError;
  final ButtonStyle? style;
  final bool showLabel;
  final IconData? icon;
  final String? label;
  final bool fullWidth;

  const SmartCallButton({
    super.key,
    required this.targetUserId,
    required this.targetUserName,
    this.onCallInitiated,
    this.onError,
    this.style,
    this.showLabel = true,
    this.icon,
    this.label,
    this.fullWidth = false,
  });

  @override
  ConsumerState<SmartCallButton> createState() => _SmartCallButtonState();
}

class _SmartCallButtonState extends ConsumerState<SmartCallButton> {
  bool _loading = false;
  bool? _canAccess;

  @override
  void initState() {
    super.initState();
    _checkAccess();
  }

  Future<void> _checkAccess() async {
    try {
      // For now, assume basic access - in real app, check user's premium status
      setState(() {
        _canAccess = true; // Will be enhanced with proper feature access checking
      });
    } catch (e) {
      setState(() {
        _canAccess = false;
      });
    }
  }

  Future<void> _handleCall() async {
    if (_canAccess != true) {
      _showUpgradeDialog();
      return;
    }

    setState(() {
      _loading = true;
    });

    try {
      final contactService = ContactService();
      final result = await contactService.requestContactReveal(widget.targetUserId);

      if (result.success) {
        final contactNumber = result.contactNumber ?? '';

        // Show contact details dialog
        if (mounted) {
          await _showContactDialog(contactNumber, {
            'success': true,
            'contactNumber': contactNumber,
            'message': result.message,
          });
        }

        widget.onCallInitiated?.call();
      } else {
        _showErrorDialog(result.message ?? 'Failed to reveal contact');
      }
    } catch (e) {
      _showErrorDialog('Error: $e');
      widget.onError?.call();
    } finally {
      if (mounted) {
        setState(() {
          _loading = false;
        });
      }
    }
  }

  Future<void> _showContactDialog(String contactNumber, Map<String, dynamic> result) async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.phone, color: AppColors.primary),
            SizedBox(width: 8),
            Text('Contact Details'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calling: ${widget.targetUserName}',
              style: AppTextStyles.h4.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.phone, size: 16, color: AppColors.textSecondary),
                const SizedBox(width: 8),
                Text(
                  contactNumber,
                  style: AppTextStyles.bodyLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
            if (result['callAvailability'] != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(Icons.access_time, size: 16, color: AppColors.textSecondary),
                  const SizedBox(width: 8),
                  Text(
                    'Available: ${result['callAvailability']}',
                    style: AppTextStyles.bodyMedium,
                  ),
                ],
              ),
            ],
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info_outline, size: 16, color: AppColors.primary),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Your call will be logged for security and quality purposes.',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              _openDialer(contactNumber);
            },
            icon: const Icon(Icons.phone),
            label: const Text('Call Now'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _openDialer(String phoneNumber) async {
    try {
      // Clean phone number (remove spaces, dashes, etc.)
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
      final Uri phoneUri = Uri(scheme: 'tel', path: cleanNumber);
      
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
        
        // Log the call attempt
        _logCallAttempt(phoneNumber);
      } else {
        throw Exception('Cannot launch phone dialer');
      }
    } catch (e) {
      _showErrorDialog('Error opening dialer: $e');
    }
  }

  Future<void> _logCallAttempt(String phoneNumber) async {
    try {
      // Log call attempt - this would be handled by the backend when making the call
      print('Call attempt logged for user: ${widget.targetUserId}, phone: $phoneNumber');
    } catch (e) {
      // Log error but don't show to user
      debugPrint('Error logging call attempt: $e');
    }
  }

  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.star, color: Colors.orange),
            SizedBox(width: 8),
            Text('Premium Feature'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Upgrade to Premium to access contact details and call users directly.',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.security, size: 16, color: Colors.orange),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Premium members get verified contact access with enhanced security.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PremiumPlansScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Upgrade'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error_outline, color: Colors.red),
            SizedBox(width: 8),
            Text('Error'),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final buttonIcon = widget.icon ?? Icons.phone;
    final buttonLabel = widget.label ?? 'Call';
    
    Widget button = ElevatedButton.icon(
      onPressed: _loading ? null : _handleCall,
      icon: _loading 
        ? const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
          )
        : Icon(buttonIcon),
      label: widget.showLabel 
        ? Text(_loading ? 'Connecting...' : buttonLabel)
        : const SizedBox.shrink(),
      style: widget.style ?? ElevatedButton.styleFrom(
        backgroundColor: _canAccess == true 
          ? AppColors.primary 
          : Colors.orange,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );

    if (widget.fullWidth) {
      button = SizedBox(width: double.infinity, child: button);
    }

    return button;
  }
}
