import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/verification_models.dart';
import '../services/verification_service.dart';

class VerificationProvider with ChangeNotifier {
  final VerificationService _verificationService = VerificationService();

  // State variables
  VerificationStatus? _verificationStatus;
  VerificationStats? _verificationStats;
  bool _isLoading = false;
  String? _error;
  final Map<String, FileUploadProgress> _uploadProgress = {};

  // Getters
  VerificationStatus? get verificationStatus => _verificationStatus;
  VerificationStats? get verificationStats => _verificationStats;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<String, FileUploadProgress> get uploadProgress => _uploadProgress;

  // Computed getters
  bool get isVerified => _verificationStatus?.isVerified ?? false;
  List<VerificationDocument> get documents => _verificationStatus?.documents ?? [];
  int get totalDocuments => _verificationStatus?.totalDocuments ?? 0;
  int get approvedDocuments => _verificationStatus?.approvedDocuments ?? 0;
  int get pendingDocuments => _verificationStatus?.pendingDocuments ?? 0;
  int get rejectedDocuments => _verificationStatus?.rejectedDocuments ?? 0;
  double get completionPercentage => _verificationStats?.completionPercentage ?? 0.0;

  // Load verification status
  Future<void> loadVerificationStatus() async {
    _setLoading(true);
    _clearError();

    try {
      _verificationStatus = await _verificationService.getVerificationStatus();
      _verificationStats = await _verificationService.getVerificationStats();
    } catch (e) {
      _setError('Failed to load verification status: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Upload document
  Future<bool> uploadDocument({
    required DocumentType documentType,
    required File file,
  }) async {
    final filename = file.path.split('/').last;
    
    // Validate file
    if (!_verificationService.validateFile(file)) {
      _setError('Invalid file. Please select a valid image or PDF file under 5MB.');
      return false;
    }

    // Check if document type already exists
    if (documents.any((doc) => doc.type == documentType)) {
      _setError('Document of this type already exists. Please delete the existing one first.');
      return false;
    }

    _clearError();
    
    // Initialize upload progress
    _uploadProgress[filename] = FileUploadProgress(
      filename: filename,
      progress: 0.0,
      isCompleted: false,
      hasError: false,
    );
    notifyListeners();

    try {
      // Simulate progress updates (in real implementation, you'd get this from the upload)
      _updateUploadProgress(filename, 0.1);
      
      final response = await _verificationService.uploadDocument(
        documentType: documentType,
        file: file,
      );

      if (response.success && response.document != null) {
        _updateUploadProgress(filename, 1.0, isCompleted: true);
        
        // Add the new document to the list
        if (_verificationStatus != null) {
          final updatedDocuments = [...documents, response.document!];
          _verificationStatus = _verificationStatus!.copyWith(
            documents: updatedDocuments,
            totalDocuments: updatedDocuments.length,
          );
        }
        
        // Refresh stats
        await _refreshStats();
        
        // Remove progress after delay
        Future.delayed(const Duration(seconds: 2), () {
          _uploadProgress.remove(filename);
          notifyListeners();
        });
        
        return true;
      } else {
        _updateUploadProgress(filename, 0.0, hasError: true, errorMessage: response.error);
        _setError(response.error ?? 'Upload failed');
        return false;
      }
    } catch (e) {
      _updateUploadProgress(filename, 0.0, hasError: true, errorMessage: e.toString());
      _setError('Upload failed: $e');
      return false;
    }
  }

  // Delete document
  Future<bool> deleteDocument(String documentId) async {
    _clearError();

    try {
      final response = await _verificationService.deleteDocument(documentId);
      
      if (response.success) {
        // Remove the document from the list
        if (_verificationStatus != null) {
          final updatedDocuments = documents.where((doc) => doc.id != documentId).toList();
          _verificationStatus = _verificationStatus!.copyWith(
            documents: updatedDocuments,
            totalDocuments: updatedDocuments.length,
          );
        }
        
        // Refresh stats
        await _refreshStats();
        return true;
      } else {
        _setError(response.error ?? 'Delete failed');
        return false;
      }
    } catch (e) {
      _setError('Delete failed: $e');
      return false;
    }
  }

  // Get document by ID
  VerificationDocument? getDocumentById(String documentId) {
    return documents.firstWhere(
      (doc) => doc.id == documentId,
      orElse: () => throw StateError('Document not found'),
    );
  }

  // Check if document type exists
  bool hasDocumentType(DocumentType documentType) {
    return documents.any((doc) => doc.type == documentType);
  }

  // Get documents by status
  List<VerificationDocument> getDocumentsByStatus(DocumentStatus status) {
    return documents.where((doc) => doc.status == status).toList();
  }

  // Refresh verification data
  Future<void> refresh() async {
    await loadVerificationStatus();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void _updateUploadProgress(
    String filename,
    double progress, {
    bool isCompleted = false,
    bool hasError = false,
    String? errorMessage,
  }) {
    _uploadProgress[filename] = FileUploadProgress(
      filename: filename,
      progress: progress,
      isCompleted: isCompleted,
      hasError: hasError,
      errorMessage: errorMessage,
    );
    notifyListeners();
  }

  Future<void> _refreshStats() async {
    try {
      _verificationStats = await _verificationService.getVerificationStats();
      notifyListeners();
    } catch (e) {
      // Don't set error for stats refresh failure
      debugPrint('Failed to refresh verification stats: $e');
    }
  }

  @override
  void dispose() {
    _uploadProgress.clear();
    super.dispose();
  }
}
