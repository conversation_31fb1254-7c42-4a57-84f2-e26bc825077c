import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/feature_access_widget.dart';
import '../../../core/services/feature_access_service.dart';

class KundaliMatchingScreen extends ConsumerStatefulWidget {
  final String userId1;
  final String userId2;

  const KundaliMatchingScreen({
    super.key,
    required this.userId1,
    required this.userId2,
  });

  @override
  ConsumerState<KundaliMatchingScreen> createState() => _KundaliMatchingScreenState();
}

class _KundaliMatchingScreenState extends ConsumerState<KundaliMatchingScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kundali Matching'),
        actions: [
          FeatureAccessWidget(
            feature: FeatureType.kundaliMatching,
            child: IconButton(
              icon: const Icon(Icons.download),
              onPressed: () {
                // Download kundali matching report
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Downloading matching report...')),
                );
                // This would integrate with PDF generation service
              },
            ),
          ),
        ],
      ),
      body: const FeatureAccessWidget(
        feature: FeatureType.kundaliMatching,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.favorite, size: 80, color: Colors.red),
              SizedBox(height: 16),
              Text(
                'Kundali Matching Screen',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('Compatibility analysis will be shown here...'),
              SizedBox(height: 32),
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Text(
                        'Compatibility Score',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Text(
                        '24/36',
                        style: TextStyle(fontSize: 32, fontWeight: FontWeight.bold, color: Colors.green),
                      ),
                      SizedBox(height: 8),
                      Text('Good Match'),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
