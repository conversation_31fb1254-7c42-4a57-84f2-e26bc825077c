import 'package:freezed_annotation/freezed_annotation.dart';

part 'contact_models.freezed.dart';
part 'contact_models.g.dart';

// ignore_for_file: non_abstract_class_inherits_abstract_member

@freezed
class ContactRevealRequest with _$ContactRevealRequest {
  const factory ContactRevealRequest({
    required String targetUserId,
    String? reason,
    @Default('mobile') String platform,
  }) = _ContactRevealRequest;

  factory ContactRevealRequest.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealRequestFromJson(json);
}

@freezed
class ContactRevealResponse with _$ContactRevealResponse {
  const factory ContactRevealResponse({
    required bool success,
    String? contactNumber,
    String? contactOwnerName,
    String? accessReason,
    String? callAvailability,
    String? error,
    String? message,
    @Default(false) bool upgradeRequired,
  }) = _ContactRevealResponse;

  factory ContactRevealResponse.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealResponseFrom<PERSON>son(json);
}

@freezed
class ContactPrivacySettings with _$ContactPrivacySettings {
  const factory ContactPrivacySettings({
    @Default(true) bool allowDirectCalls,
    @Default('PREMIUM_ONLY') String contactRevealPreference,
    @Default(true) bool requireMutualInterest,
    @Default('ANYTIME') String callAvailability,
  }) = _ContactPrivacySettings;

  factory ContactPrivacySettings.fromJson(Map<String, dynamic> json) =>
      _$ContactPrivacySettingsFromJson(json);
}

@freezed
class ContactRevealOption with _$ContactRevealOption {
  const factory ContactRevealOption({
    required String value,
    required String label,
    required String description,
    required String icon,
  }) = _ContactRevealOption;

  factory ContactRevealOption.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealOptionFromJson(json);
}

@freezed
class ContactAccessHistory with _$ContactAccessHistory {
  const factory ContactAccessHistory({
    required String id,
    required String accessorId,
    required String contactOwnerId,
    required DateTime accessedAt,
    String? accessType,
    String? contactNumber,
    String? accessReason,
    @Default(false) bool isPremiumAccess,
    String? featurePurchaseId,
    int? callDuration,
    String? callStatus,
    String? platform,
    ContactAccessUser? accessor,
    ContactAccessUser? contactOwner,
  }) = _ContactAccessHistory;

  factory ContactAccessHistory.fromJson(Map<String, dynamic> json) =>
      _$ContactAccessHistoryFromJson(json);
}

@freezed
class ContactAccessUser with _$ContactAccessUser {
  const factory ContactAccessUser({
    required String id,
    String? fullName,
    String? profilePhoto,
    int? age,
    String? location,
  }) = _ContactAccessUser;

  factory ContactAccessUser.fromJson(Map<String, dynamic> json) =>
      _$ContactAccessUserFromJson(json);
}

@freezed
class ContactRevealStats with _$ContactRevealStats {
  const factory ContactRevealStats({
    @Default(0) int totalRevealed,
    @Default(0) int totalRequested,
    @Default(0) int premiumReveals,
    @Default(0) int mutualInterestReveals,
    @Default(0) int acceptedInterestReveals,
    @Default(0) int callsInitiated,
    @Default(0) int callsConnected,
    Map<String, int>? platformStats,
    Map<String, int>? reasonStats,
  }) = _ContactRevealStats;

  factory ContactRevealStats.fromJson(Map<String, dynamic> json) =>
      _$ContactRevealStatsFromJson(json);
}

enum ContactRevealPreference {
  premiumOnly('PREMIUM_ONLY', 'Premium Users Only', '💎'),
  mutualInterest('MUTUAL_INTEREST', 'Mutual Interest', '💕'),
  acceptedInterest('ACCEPTED_INTEREST', 'Accepted Interest', '✅'),
  never('NEVER', 'Never Share', '🔒');

  const ContactRevealPreference(this.value, this.label, this.icon);
  
  final String value;
  final String label;
  final String icon;
}

enum CallAvailability {
  anytime('ANYTIME', 'Anytime', '🕐'),
  businessHours('BUSINESS_HOURS', 'Business Hours', '🕘'),
  eveningOnly('EVENING_ONLY', 'Evening Only', '🌆'),
  weekendOnly('WEEKEND_ONLY', 'Weekend Only', '📅');

  const CallAvailability(this.value, this.label, this.icon);
  
  final String value;
  final String label;
  final String icon;
}

enum ContactAccessType {
  contactReveal('CONTACT_REVEAL', 'Contact Reveal'),
  callAttempt('CALL_ATTEMPT', 'Call Attempt'),
  callConnected('CALL_CONNECTED', 'Call Connected');

  const ContactAccessType(this.value, this.label);
  
  final String value;
  final String label;
}
