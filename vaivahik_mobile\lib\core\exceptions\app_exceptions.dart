/// 🚨 APP EXCEPTIONS - Comprehensive Error Handling
/// Features: Typed Exceptions, Error Messages, HTTP Status Codes
library;

abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic details;

  const AppException(this.message, {this.code, this.details});

  @override
  String toString() => message;
}

/// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(super.message, {super.code, super.details});
}

class NoInternetException extends NetworkException {
  const NoInternetException() 
      : super('No internet connection. Please check your network settings.');
}

class TimeoutException extends NetworkException {
  const TimeoutException() 
      : super('Request timeout. Please try again.');
}

class ConnectionException extends NetworkException {
  const ConnectionException() 
      : super('Unable to connect to server. Please try again later.');
}

/// Server related exceptions
class ServerException extends AppException {
  final int? statusCode;

  const ServerException(super.message, {this.statusCode, super.code, super.details});
}

class BadRequestException extends ServerException {
  const BadRequestException(super.message) : super(statusCode: 400);
}

class UnauthorizedException extends ServerException {
  const UnauthorizedException() 
      : super('Unauthorized access. Please login again.', statusCode: 401);
}

class ForbiddenException extends ServerException {
  const ForbiddenException() 
      : super('Access forbidden. You don\'t have permission.', statusCode: 403);
}

class NotFoundException extends ServerException {
  const NotFoundException(super.message) : super(statusCode: 404);
}

class ConflictException extends ServerException {
  const ConflictException(super.message) : super(statusCode: 409);
}

class ValidationException extends ServerException {
  final Map<String, List<String>>? errors;

  const ValidationException(super.message, {this.errors}) : super(statusCode: 422);
}

class InternalServerException extends ServerException {
  const InternalServerException() 
      : super('Internal server error. Please try again later.', statusCode: 500);
}

class ServiceUnavailableException extends ServerException {
  const ServiceUnavailableException() 
      : super('Service temporarily unavailable. Please try again later.', statusCode: 503);
}

/// Authentication related exceptions
class AuthException extends AppException {
  const AuthException(super.message, {super.code, super.details});
}

class InvalidCredentialsException extends AuthException {
  const InvalidCredentialsException() 
      : super('Invalid email or password. Please try again.');
}

class AccountNotVerifiedException extends AuthException {
  const AccountNotVerifiedException() 
      : super('Account not verified. Please check your email for verification link.');
}

class AccountSuspendedException extends AuthException {
  const AccountSuspendedException() 
      : super('Your account has been suspended. Please contact support.');
}

class SessionExpiredException extends AuthException {
  const SessionExpiredException() 
      : super('Your session has expired. Please login again.');
}

/// Data related exceptions
class DataException extends AppException {
  const DataException(super.message, {super.code, super.details});
}

class ParseException extends DataException {
  const ParseException() 
      : super('Failed to parse server response. Please try again.');
}

class CacheException extends DataException {
  const CacheException(super.message);
}

class StorageException extends DataException {
  const StorageException(super.message);
}

/// Business logic exceptions
class BusinessException extends AppException {
  const BusinessException(super.message, {super.code, super.details});
}

class InsufficientFundsException extends BusinessException {
  const InsufficientFundsException() 
      : super('Insufficient funds to complete this transaction.');
}

class SubscriptionExpiredException extends BusinessException {
  const SubscriptionExpiredException() 
      : super('Your subscription has expired. Please renew to continue.');
}

class FeatureNotAvailableException extends BusinessException {
  const FeatureNotAvailableException(super.message);
}

class RateLimitException extends BusinessException {
  const RateLimitException() 
      : super('Too many requests. Please wait before trying again.');
}

/// File related exceptions
class FileException extends AppException {
  const FileException(super.message, {super.code, super.details});
}

class FileNotFound extends FileException {
  const FileNotFound(String fileName) 
      : super('File not found: $fileName');
}

class FileTooLargeException extends FileException {
  const FileTooLargeException(int maxSize) 
      : super('File size exceeds maximum limit of ${maxSize}MB');
}

class UnsupportedFileTypeException extends FileException {
  const UnsupportedFileTypeException(String fileType) 
      : super('Unsupported file type: $fileType');
}

/// Permission related exceptions
class PermissionException extends AppException {
  const PermissionException(super.message, {super.code, super.details});
}

class CameraPermissionException extends PermissionException {
  const CameraPermissionException() 
      : super('Camera permission is required to take photos.');
}

class StoragePermissionException extends PermissionException {
  const StoragePermissionException() 
      : super('Storage permission is required to save files.');
}

class LocationPermissionException extends PermissionException {
  const LocationPermissionException() 
      : super('Location permission is required for this feature.');
}

class NotificationPermissionException extends PermissionException {
  const NotificationPermissionException() 
      : super('Notification permission is required to receive updates.');
}

/// Utility class for exception handling
class ExceptionHandler {
  static String getErrorMessage(dynamic error) {
    if (error is AppException) {
      return error.message;
    }
    
    // Handle common Flutter/Dart exceptions
    if (error is FormatException) {
      return 'Invalid data format received from server.';
    }
    
    if (error is TypeError) {
      return 'Data type mismatch. Please try again.';
    }
    
    // Default error message
    return 'An unexpected error occurred. Please try again.';
  }

  static AppException mapHttpException(int statusCode, String message) {
    switch (statusCode) {
      case 400:
        return BadRequestException(message);
      case 401:
        return const UnauthorizedException();
      case 403:
        return const ForbiddenException();
      case 404:
        return NotFoundException(message);
      case 409:
        return ConflictException(message);
      case 422:
        return ValidationException(message);
      case 429:
        return const RateLimitException();
      case 500:
        return const InternalServerException();
      case 503:
        return const ServiceUnavailableException();
      default:
        return ServerException(message, statusCode: statusCode);
    }
  }

  static bool isNetworkError(dynamic error) {
    return error is NetworkException ||
           error is NoInternetException ||
           error is TimeoutException ||
           error is ConnectionException;
  }

  static bool isAuthError(dynamic error) {
    return error is AuthException ||
           error is UnauthorizedException ||
           error is SessionExpiredException;
  }

  static bool isServerError(dynamic error) {
    return error is ServerException;
  }

  static bool isRetryableError(dynamic error) {
    if (error is ServerException) {
      return error.statusCode == null || 
             error.statusCode! >= 500 ||
             error.statusCode == 408 || // Request Timeout
             error.statusCode == 429;   // Too Many Requests
    }
    
    return error is NetworkException ||
           error is TimeoutException ||
           error is ConnectionException;
  }
}

/// Exception logging utility
class ExceptionLogger {
  static void log(dynamic error, {StackTrace? stackTrace, Map<String, dynamic>? context}) {
    // In production, this would send to crash reporting service
    print('🚨 Exception: $error');
    if (stackTrace != null) {
      print('📍 Stack Trace: $stackTrace');
    }
    if (context != null) {
      print('🔍 Context: $context');
    }
  }

  static void logNetworkError(NetworkException error, {String? endpoint, Map<String, dynamic>? requestData}) {
    log(error, context: {
      'type': 'network_error',
      'endpoint': endpoint,
      'request_data': requestData,
    });
  }

  static void logServerError(ServerException error, {String? endpoint, int? statusCode}) {
    log(error, context: {
      'type': 'server_error',
      'endpoint': endpoint,
      'status_code': statusCode,
    });
  }

  static void logAuthError(AuthException error, {String? userId}) {
    log(error, context: {
      'type': 'auth_error',
      'user_id': userId,
    });
  }
}
