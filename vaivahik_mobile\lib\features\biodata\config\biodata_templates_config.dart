import 'package:flutter/material.dart';
import '../models/biodata_models.dart';

/// Configuration for all 8 biodata templates (4 male-oriented, 4 female-oriented)
/// Based on the website's comprehensive biodata template system
class BiodataTemplatesConfig {
  
  /// Get all 8 biodata templates
  static List<BiodataTemplate> getAllTemplates() {
    return [
      ...getMaleTemplates(),
      ...getFemaleTemplates(),
    ];
  }

  /// Get 4 male-oriented templates
  static List<BiodataTemplate> getMaleTemplates() {
    return [
      _traditionalHeritage(),
      _modernProfessional(),
      _royalElegance(),
      _minimalistChic(),
    ];
  }

  /// Get 4 female-oriented templates
  static List<BiodataTemplate> getFemaleTemplates() {
    return [
      _culturalGrace(),
      _contemporaryChic(),
      _floralElegance(),
      _classicBeauty(),
    ];
  }

  /// Template 1: Traditional Heritage (Male-oriented)
  static BiodataTemplate _traditionalHeritage() {
    return BiodataTemplate(
      id: '1',
      name: '🏛️ Traditional Heritage',
      description: 'Timeless Maratha design with cultural elegance and traditional motifs. Perfect for families who value tradition and heritage.',
      type: BiodataType.traditional,
      category: 'traditional',
      genderOrientation: 'male',
      targetAudience: 'Traditional families, cultural enthusiasts',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/traditional-heritage.html',
      previewImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/traditional-heritage-thumb.jpg',
      headerText: '॥ श्री गणेशाय नमः ॥',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#8B4513',
        'secondaryColor': '#DAA520',
        'fontFamily': 'Devanagari',
        'showGotra': true,
        'showKuldevta': true,
        'showNakshatra': true,
        'includeMantra': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'occupation', 'gotra', 'kuldevta'],
      optionalFields: ['hobbies', 'achievements', 'family_details', 'expectations'],
      isActive: true,
      isPremium: false,
      price: 0,
      originalPrice: 299,
      discountPercent: 100,
      discountedPrice: 0,
      currency: 'INR',
      purchaseCount: 285,
      downloadCount: 238,
      revenue: 113715,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    );
  }

  /// Template 2: Modern Professional (Male-oriented)
  static BiodataTemplate _modernProfessional() {
    return BiodataTemplate(
      id: '2',
      name: '💼 Modern Professional',
      description: 'Clean, contemporary design perfect for working professionals. Emphasizes career achievements and modern lifestyle.',
      type: BiodataType.modern,
      category: 'modern',
      genderOrientation: 'male',
      targetAudience: 'Working professionals, corporate employees',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/modern-professional.html',
      previewImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/modern-professional-thumb.jpg',
      headerText: 'Professional Profile',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#2E86AB',
        'secondaryColor': '#A23B72',
        'fontFamily': 'Roboto',
        'showCareer': true,
        'showSkills': true,
        'showAchievements': true,
        'modernLayout': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'occupation', 'company', 'salary'],
      optionalFields: ['skills', 'certifications', 'projects', 'languages'],
      isActive: true,
      isPremium: false,
      price: 199,
      originalPrice: 399,
      discountPercent: 50,
      discountedPrice: 199,
      currency: 'INR',
      purchaseCount: 156,
      downloadCount: 142,
      revenue: 31044,
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now(),
    );
  }

  /// Template 3: Royal Elegance (Male-oriented)
  static BiodataTemplate _royalElegance() {
    return BiodataTemplate(
      id: '3',
      name: '👑 Royal Elegance',
      description: 'Luxurious design with royal Maratha elements. Perfect for families with royal heritage or those who appreciate grandeur.',
      type: BiodataType.royal,
      category: 'royal',
      genderOrientation: 'male',
      targetAudience: 'Royal families, luxury preference',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/royal-elegance.html',
      previewImage: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/royal-elegance-thumb.jpg',
      headerText: '॥ श्री छत्रपति शिवाजी महाराज की जय ॥',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#FFD700',
        'secondaryColor': '#8B0000',
        'fontFamily': 'Cinzel',
        'showLineage': true,
        'showTitles': true,
        'royalBorder': true,
        'goldAccents': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'family_background', 'lineage'],
      optionalFields: ['titles', 'properties', 'social_status', 'cultural_activities'],
      isActive: true,
      isPremium: true,
      price: 599,
      originalPrice: 999,
      discountPercent: 40,
      discountedPrice: 599,
      currency: 'INR',
      purchaseCount: 89,
      downloadCount: 76,
      revenue: 53311,
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now(),
    );
  }

  /// Template 4: Minimalist Chic (Male-oriented)
  static BiodataTemplate _minimalistChic() {
    return BiodataTemplate(
      id: '4',
      name: '✨ Minimalist Chic',
      description: 'Clean, simple design focusing on essential information. Perfect for those who prefer understated elegance.',
      type: BiodataType.minimalist,
      category: 'minimalist',
      genderOrientation: 'male',
      targetAudience: 'Modern minimalists, young professionals',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/minimalist-chic.html',
      previewImage: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/minimalist-chic-thumb.jpg',
      headerText: 'Simple. Elegant. Perfect.',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#2C3E50',
        'secondaryColor': '#3498DB',
        'fontFamily': 'Lato',
        'cleanLayout': true,
        'minimalBorder': true,
        'focusOnEssentials': true,
        'whiteSpace': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'occupation'],
      optionalFields: ['interests', 'values', 'lifestyle', 'preferences'],
      isActive: true,
      isPremium: false,
      price: 149,
      originalPrice: 299,
      discountPercent: 50,
      discountedPrice: 149,
      currency: 'INR',
      purchaseCount: 203,
      downloadCount: 189,
      revenue: 30247,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
    );
  }

  /// Template 5: Cultural Grace (Female-oriented)
  static BiodataTemplate _culturalGrace() {
    return BiodataTemplate(
      id: '5',
      name: '🌸 Cultural Grace',
      description: 'Beautiful traditional design with feminine elegance and cultural motifs. Perfect for families who cherish traditions.',
      type: BiodataType.traditional,
      category: 'traditional',
      genderOrientation: 'female',
      targetAudience: 'Traditional families, cultural values',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/cultural-grace.html',
      previewImage: 'https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/cultural-grace-thumb.jpg',
      headerText: '॥ श्री गणेशाय नमः ॥',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#E91E63',
        'secondaryColor': '#FF9800',
        'fontFamily': 'Devanagari',
        'floralBorder': true,
        'traditionalMotifs': true,
        'feminineTouch': true,
        'culturalElements': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'family_background', 'gotra'],
      optionalFields: ['skills', 'hobbies', 'cultural_activities', 'expectations'],
      isActive: true,
      isPremium: false,
      price: 0,
      originalPrice: 299,
      discountPercent: 100,
      discountedPrice: 0,
      currency: 'INR',
      purchaseCount: 342,
      downloadCount: 298,
      revenue: 102258,
      createdAt: DateTime.now().subtract(const Duration(days: 28)),
      updatedAt: DateTime.now(),
    );
  }

  /// Template 6: Contemporary Chic (Female-oriented)
  static BiodataTemplate _contemporaryChic() {
    return BiodataTemplate(
      id: '6',
      name: '💫 Contemporary Chic',
      description: 'Modern, stylish design for the contemporary woman. Perfect for working professionals and independent women.',
      type: BiodataType.modern,
      category: 'modern',
      genderOrientation: 'female',
      targetAudience: 'Working women, modern families',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/contemporary-chic.html',
      previewImage: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/contemporary-chic-thumb.jpg',
      headerText: 'Modern. Independent. Beautiful.',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#9C27B0',
        'secondaryColor': '#00BCD4',
        'fontFamily': 'Montserrat',
        'modernLayout': true,
        'professionalFocus': true,
        'independentSpirit': true,
        'stylishDesign': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'occupation', 'achievements'],
      optionalFields: ['career_goals', 'interests', 'lifestyle', 'values'],
      isActive: true,
      isPremium: false,
      price: 249,
      originalPrice: 399,
      discountPercent: 38,
      discountedPrice: 249,
      currency: 'INR',
      purchaseCount: 178,
      downloadCount: 156,
      revenue: 44322,
      createdAt: DateTime.now().subtract(const Duration(days: 22)),
      updatedAt: DateTime.now(),
    );
  }

  /// Template 7: Floral Elegance (Female-oriented)
  static BiodataTemplate _floralElegance() {
    return BiodataTemplate(
      id: '7',
      name: '🌺 Floral Elegance',
      description: 'Elegant design with beautiful floral patterns and soft colors. Perfect for those who love nature and beauty.',
      type: BiodataType.elegant,
      category: 'elegant',
      genderOrientation: 'female',
      targetAudience: 'Nature lovers, artistic families',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/floral-elegance.html',
      previewImage: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/floral-elegance-thumb.jpg',
      headerText: '🌸 Beauty in Simplicity 🌸',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#FF69B4',
        'secondaryColor': '#98FB98',
        'fontFamily': 'Dancing Script',
        'floralPatterns': true,
        'softColors': true,
        'naturalTheme': true,
        'elegantBorders': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'family_background'],
      optionalFields: ['artistic_skills', 'hobbies', 'nature_activities', 'creative_pursuits'],
      isActive: true,
      isPremium: true,
      price: 399,
      originalPrice: 599,
      discountPercent: 33,
      discountedPrice: 399,
      currency: 'INR',
      purchaseCount: 124,
      downloadCount: 108,
      revenue: 49476,
      createdAt: DateTime.now().subtract(const Duration(days: 18)),
      updatedAt: DateTime.now(),
    );
  }

  /// Template 8: Classic Beauty (Female-oriented)
  static BiodataTemplate _classicBeauty() {
    return BiodataTemplate(
      id: '8',
      name: '💎 Classic Beauty',
      description: 'Timeless classic design with sophisticated elements. Perfect for those who appreciate enduring beauty and grace.',
      type: BiodataType.classic,
      category: 'classic',
      genderOrientation: 'female',
      targetAudience: 'Classic families, timeless values',
      templateHtml: '',
      templateCss: '',
      designFile: '/templates/biodata/classic-beauty.html',
      previewImage: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=300&h=400&fit=crop&crop=center',
      thumbnail: '/images/biodata-templates/classic-beauty-thumb.jpg',
      headerText: 'Timeless Grace & Beauty',
      footerText: 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: {
        'primaryColor': '#4A4A4A',
        'secondaryColor': '#D4AF37',
        'fontFamily': 'Playfair Display',
        'classicBorders': true,
        'sophisticatedLayout': true,
        'timelessDesign': true,
        'elegantTypography': true,
      },
      requiredFields: ['name', 'age', 'height', 'education', 'family_values'],
      optionalFields: ['accomplishments', 'cultural_interests', 'family_traditions', 'personal_values'],
      isActive: true,
      isPremium: true,
      price: 499,
      originalPrice: 799,
      discountPercent: 38,
      discountedPrice: 499,
      currency: 'INR',
      purchaseCount: 95,
      downloadCount: 82,
      revenue: 47405,
      createdAt: DateTime.now().subtract(const Duration(days: 12)),
      updatedAt: DateTime.now(),
    );
  }

  /// Get template by ID
  static BiodataTemplate? getTemplateById(String id) {
    try {
      return getAllTemplates().firstWhere((template) => template.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get templates by gender
  static List<BiodataTemplate> getTemplatesByGender(String gender) {
    return getAllTemplates()
        .where((template) => template.genderOrientation == gender)
        .toList();
  }

  /// Get templates by category
  static List<BiodataTemplate> getTemplatesByCategory(String category) {
    return getAllTemplates()
        .where((template) => template.category == category)
        .toList();
  }

  /// Get premium templates
  static List<BiodataTemplate> getPremiumTemplates() {
    return getAllTemplates()
        .where((template) => template.isPremium)
        .toList();
  }

  /// Get free templates
  static List<BiodataTemplate> getFreeTemplates() {
    return getAllTemplates()
        .where((template) => !template.isPremium)
        .toList();
  }

  /// Get template categories
  static List<String> getCategories() {
    return ['traditional', 'modern', 'royal', 'minimalist', 'elegant', 'classic'];
  }

  /// Get template colors for UI
  static Map<String, Color> getTemplateColors() {
    return {
      'traditional': const Color(0xFF8B4513),
      'modern': const Color(0xFF2E86AB),
      'royal': const Color(0xFFFFD700),
      'minimalist': const Color(0xFF2C3E50),
      'elegant': const Color(0xFFFF69B4),
      'classic': const Color(0xFF4A4A4A),
    };
  }
}
