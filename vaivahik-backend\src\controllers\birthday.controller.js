/**
 * Birthday Controller
 * 
 * Handles birthday-related API endpoints including birthday detection,
 * AI-powered wishes generation, and birthday celebration features.
 */

const birthdayService = require('../services/birthday.service');
const { validationResult } = require('express-validator');

/**
 * Check if today is user's birthday and get birthday wishes
 * GET /api/users/birthday/check
 */
const checkBirthday = async (req, res, next) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if today is user's birthday
    const isBirthday = await birthdayService.isTodayUserBirthday(userId);
    
    let birthdayData = null;
    if (isBirthday) {
      // Get user data for generating wishes
      const user = await req.prisma.user.findUnique({
        where: { id: userId },
        include: { 
          profile: true,
          photos: true
        }
      });

      if (user) {
        birthdayData = await birthdayService.generateBirthdayWishes(user);
      }
    }

    res.json({
      success: true,
      data: {
        isBirthday,
        birthdayWishes: birthdayData
      }
    });

  } catch (error) {
    console.error('Error checking birthday:', error);
    next(error);
  }
};

/**
 * Get birthday wishes for user (can be called anytime for testing)
 * GET /api/users/birthday/wishes
 */
const getBirthdayWishes = async (req, res, next) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Get user data
    const user = await req.prisma.user.findUnique({
      where: { id: userId },
      include: { 
        profile: true,
        photos: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const birthdayWishes = await birthdayService.generateBirthdayWishes(user);

    res.json({
      success: true,
      data: {
        birthdayWishes
      }
    });

  } catch (error) {
    console.error('Error getting birthday wishes:', error);
    next(error);
  }
};

/**
 * Send birthday notifications to user
 * POST /api/users/birthday/notify
 */
const sendBirthdayNotification = async (req, res, next) => {
  try {
    const userId = req.user?.userId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const success = await birthdayService.sendBirthdayNotifications(userId);

    if (success) {
      res.json({
        success: true,
        message: 'Birthday notification sent successfully'
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to send birthday notification'
      });
    }

  } catch (error) {
    console.error('Error sending birthday notification:', error);
    next(error);
  }
};

/**
 * Get all users with birthdays today (Admin only)
 * GET /api/admin/birthday/today
 */
const getTodaysBirthdays = async (req, res, next) => {
  try {
    // Check if user is admin
    if (req.user?.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const birthdayUsers = await birthdayService.getTodaysBirthdayUsers();

    res.json({
      success: true,
      data: {
        count: birthdayUsers.length,
        users: birthdayUsers.map(user => ({
          id: user.id,
          name: user.profile?.fullName || 'Unknown',
          email: user.email,
          age: birthdayService.calculateAge(user.profile.dateOfBirth),
          gender: user.profile?.gender
        }))
      }
    });

  } catch (error) {
    console.error('Error getting today\'s birthdays:', error);
    next(error);
  }
};

/**
 * Send birthday notifications to all users with birthdays today (Admin only)
 * POST /api/admin/birthday/notify-all
 */
const notifyAllBirthdayUsers = async (req, res, next) => {
  try {
    // Check if user is admin
    if (req.user?.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const birthdayUsers = await birthdayService.getTodaysBirthdayUsers();
    let successCount = 0;
    let failureCount = 0;

    for (const user of birthdayUsers) {
      try {
        const success = await birthdayService.sendBirthdayNotifications(user.id);
        if (success) {
          successCount++;
        } else {
          failureCount++;
        }
      } catch (error) {
        console.error(`Error sending birthday notification to user ${user.id}:`, error);
        failureCount++;
      }
    }

    res.json({
      success: true,
      data: {
        totalUsers: birthdayUsers.length,
        successCount,
        failureCount,
        message: `Birthday notifications sent to ${successCount} users`
      }
    });

  } catch (error) {
    console.error('Error notifying all birthday users:', error);
    next(error);
  }
};

/**
 * Get birthday statistics (Admin only)
 * GET /api/admin/birthday/stats
 */
const getBirthdayStats = async (req, res, next) => {
  try {
    // Check if user is admin
    if (req.user?.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    const today = new Date();
    const currentMonth = today.getMonth() + 1;
    
    // Get users with birthdays this month
    const users = await req.prisma.user.findMany({
      where: {
        profile: {
          dateOfBirth: {
            not: null
          }
        }
      },
      include: {
        profile: true
      }
    });

    const thisMonthBirthdays = users.filter(user => {
      if (!user.profile?.dateOfBirth) return false;
      const birthDate = new Date(user.profile.dateOfBirth);
      return birthDate.getMonth() + 1 === currentMonth;
    });

    const todaysBirthdays = users.filter(user => {
      if (!user.profile?.dateOfBirth) return false;
      const birthDate = new Date(user.profile.dateOfBirth);
      return birthDate.getMonth() + 1 === currentMonth && birthDate.getDate() === today.getDate();
    });

    res.json({
      success: true,
      data: {
        todaysBirthdays: todaysBirthdays.length,
        thisMonthBirthdays: thisMonthBirthdays.length,
        totalUsersWithBirthday: users.filter(u => u.profile?.dateOfBirth).length,
        currentMonth: today.toLocaleString('default', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Error getting birthday stats:', error);
    next(error);
  }
};

module.exports = {
  checkBirthday,
  getBirthdayWishes,
  sendBirthdayNotification,
  getTodaysBirthdays,
  notifyAllBirthdayUsers,
  getBirthdayStats
};
