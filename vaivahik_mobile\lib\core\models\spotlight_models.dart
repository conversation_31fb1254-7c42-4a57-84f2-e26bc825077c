// Spotlight feature model matching website's structure
class SpotlightFeature {
  final String id;
  final String name;
  final String description;
  final double price;
  final double? discountedPrice;
  final int? discountPercent;
  final int durationHours;
  final int defaultCount;
  final bool isActive;
  final List<String> benefits;
  final String icon;
  final int popularity;
  final int successRate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const SpotlightFeature({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.discountedPrice,
    this.discountPercent,
    required this.durationHours,
    required this.defaultCount,
    required this.isActive,
    required this.benefits,
    required this.icon,
    required this.popularity,
    required this.successRate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SpotlightFeature.fromJson(Map<String, dynamic> json) {
    return SpotlightFeature(
      id: json['id']?.toString() ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      discountedPrice: json['discountedPrice']?.toDouble(),
      discountPercent: json['discountPercent'],
      durationHours: json['durationHours'] ?? json['duration_hours'] ?? 24,
      defaultCount: json['defaultCount'] ?? json['default_count'] ?? 1,
      isActive: json['isActive'] ?? json['is_active'] ?? true,
      benefits: List<String>.from(json['benefits'] ?? []),
      icon: json['icon'] ?? 'TrendingUp',
      popularity: json['popularity'] ?? 0,
      successRate: json['successRate'] ?? json['success_rate'] ?? 0,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'discountedPrice': discountedPrice,
      'discountPercent': discountPercent,
      'durationHours': durationHours,
      'defaultCount': defaultCount,
      'isActive': isActive,
      'benefits': benefits,
      'icon': icon,
      'popularity': popularity,
      'successRate': successRate,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  double get finalPrice => discountedPrice ?? price;
  
  bool get hasDiscount => discountedPrice != null && discountedPrice! < price;
  
  String get duration {
    if (durationHours < 24) {
      return '$durationHours hours';
    } else if (durationHours == 24) {
      return '1 day';
    } else if (durationHours < 168) {
      return '${(durationHours / 24).round()} days';
    } else {
      return '${(durationHours / 168).round()} weeks';
    }
  }
}

// User spotlight purchase model
class UserSpotlight {
  final String id;
  final String userId;
  final String spotlightId;
  final SpotlightFeature? spotlight;
  final bool isActive;
  final double pricePaid;
  final DateTime startTime;
  final DateTime endTime;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserSpotlight({
    required this.id,
    required this.userId,
    required this.spotlightId,
    this.spotlight,
    required this.isActive,
    required this.pricePaid,
    required this.startTime,
    required this.endTime,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserSpotlight.fromJson(Map<String, dynamic> json) {
    return UserSpotlight(
      id: json['id']?.toString() ?? '',
      userId: json['userId'] ?? json['user_id'] ?? '',
      spotlightId: json['spotlightId'] ?? json['spotlight_id'] ?? '',
      spotlight: json['spotlight'] != null 
          ? SpotlightFeature.fromJson(json['spotlight']) 
          : null,
      isActive: json['isActive'] ?? json['is_active'] ?? false,
      pricePaid: (json['pricePaid'] ?? json['price_paid'] ?? 0).toDouble(),
      startTime: DateTime.parse(json['startTime'] ?? json['start_time'] ?? DateTime.now().toIso8601String()),
      endTime: DateTime.parse(json['endTime'] ?? json['end_time'] ?? DateTime.now().toIso8601String()),
      createdAt: DateTime.parse(json['createdAt'] ?? json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updatedAt'] ?? json['updated_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'spotlightId': spotlightId,
      'spotlight': spotlight?.toJson(),
      'isActive': isActive,
      'pricePaid': pricePaid,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  bool get isExpired => DateTime.now().isAfter(endTime);
  
  Duration get timeRemaining {
    if (isExpired) return Duration.zero;
    return endTime.difference(DateTime.now());
  }
  
  String get timeRemainingText {
    final remaining = timeRemaining;
    if (remaining.inDays > 0) {
      return '${remaining.inDays}d ${remaining.inHours % 24}h';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours}h ${remaining.inMinutes % 60}m';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes}m';
    } else {
      return 'Expired';
    }
  }
  
  double get progressPercentage {
    final total = endTime.difference(startTime);
    final elapsed = DateTime.now().difference(startTime);
    if (total.inMilliseconds == 0) return 1.0;
    return (elapsed.inMilliseconds / total.inMilliseconds).clamp(0.0, 1.0);
  }
}

// Spotlight purchase request model
class SpotlightPurchaseRequest {
  final String featureId;
  final int? duration;
  final int? count;
  final Map<String, dynamic>? userDetails;

  const SpotlightPurchaseRequest({
    required this.featureId,
    this.duration,
    this.count,
    this.userDetails,
  });

  Map<String, dynamic> toJson() {
    return {
      'featureId': featureId,
      if (duration != null) 'duration': duration,
      if (count != null) 'count': count,
      if (userDetails != null) 'userDetails': userDetails,
    };
  }
}

// Spotlight analytics model
class SpotlightAnalytics {
  final int profileViews;
  final int interests;
  final int matches;
  final int contactRequests;
  final double engagementRate;
  final List<SpotlightViewData> viewsOverTime;

  const SpotlightAnalytics({
    required this.profileViews,
    required this.interests,
    required this.matches,
    required this.contactRequests,
    required this.engagementRate,
    required this.viewsOverTime,
  });

  factory SpotlightAnalytics.fromJson(Map<String, dynamic> json) {
    return SpotlightAnalytics(
      profileViews: json['profileViews'] ?? 0,
      interests: json['interests'] ?? 0,
      matches: json['matches'] ?? 0,
      contactRequests: json['contactRequests'] ?? 0,
      engagementRate: (json['engagementRate'] ?? 0).toDouble(),
      viewsOverTime: (json['viewsOverTime'] as List<dynamic>?)
          ?.map((item) => SpotlightViewData.fromJson(item))
          .toList() ?? [],
    );
  }
}

// Spotlight view data for analytics
class SpotlightViewData {
  final DateTime timestamp;
  final int views;

  const SpotlightViewData({
    required this.timestamp,
    required this.views,
  });

  factory SpotlightViewData.fromJson(Map<String, dynamic> json) {
    return SpotlightViewData(
      timestamp: DateTime.parse(json['timestamp']),
      views: json['views'] ?? 0,
    );
  }
}
