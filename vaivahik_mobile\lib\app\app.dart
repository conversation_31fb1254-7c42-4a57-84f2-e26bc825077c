import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'theme.dart';
import '../features/auth/screens/enhanced_login_screen.dart';

class VaivahikApp extends ConsumerWidget {
  const VaivahikApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Set premium system UI overlay style - Website consistent
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark, // Dark icons on light background
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return MaterialApp(
      title: 'Vaivahik - World\'s Most Beautiful Matrimony App',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      home: const SplashScreen(),
    );
  }
}

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;

  @override
  void initState() {
    super.initState();
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _startAnimations();
    _navigateToLogin();
  }

  void _startAnimations() {
    _logoController.forward();
    Future.delayed(const Duration(milliseconds: 500), () {
      _textController.forward();
    });
  }

  _navigateToLogin() async {
    await Future.delayed(const Duration(seconds: 4));
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const EnhancedLoginScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 800),
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppGradients.primaryGradient,
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Premium Logo with Advanced Animation
              Container(
                width: 140,
                height: 140,
                decoration: BoxDecoration(
                  gradient: AppGradients.accentGradient,
                  borderRadius: BorderRadius.circular(35),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.accent.withValues(alpha: 0.4),
                      blurRadius: 30,
                      spreadRadius: 5,
                      offset: const Offset(0, 15),
                    ),
                  ],
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 3,
                  ),
                ),
                child: const Icon(
                  Icons.favorite,
                  size: 70,
                  color: Colors.white,
                ),
              )
                  .animate(controller: _logoController)
                  .scale(duration: 1200.ms, curve: Curves.elasticOut)
                  .then()
                  .shimmer(duration: 2000.ms, color: Colors.white.withValues(alpha: 0.5)),

              const SizedBox(height: 50),

              // App Name with Gradient Text
              ShaderMask(
                shaderCallback: (bounds) => AppGradients.accentGradient.createShader(bounds),
                child: const Text(
                  'Vaivahik',
                  style: TextStyle(
                    fontSize: 42,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'Poppins',
                    letterSpacing: 3,
                  ),
                ),
              )
                  .animate(controller: _textController)
                  .fadeIn(duration: 1000.ms)
                  .slideY(begin: 0.5, end: 0, curve: Curves.easeOutBack),

              const SizedBox(height: 20),

              // Premium Tagline
              const Text(
                'World\'s Most Beautiful Matrimony App',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w400,
                  letterSpacing: 1,
                ),
                textAlign: TextAlign.center,
              )
                  .animate(controller: _textController)
                  .fadeIn(delay: 300.ms, duration: 1000.ms)
                  .slideY(begin: 0.3, end: 0),

              const SizedBox(height: 80),

              // Premium Loading Indicator
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 3,
                ),
              )
                  .animate()
                  .fadeIn(delay: 1500.ms)
                  .scale(begin: const Offset(0.5, 0.5)),

              const SizedBox(height: 30),

              // Loading Text
              const Text(
                'Loading your perfect match...',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white60,
                  fontFamily: 'Poppins',
                  fontWeight: FontWeight.w300,
                ),
              )
                  .animate()
                  .fadeIn(delay: 2000.ms)
                  .shimmer(delay: 2500.ms, duration: 1500.ms),
            ],
          ),
        ),
      ),
    );
  }
}
