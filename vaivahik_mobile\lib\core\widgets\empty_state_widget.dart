import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../app/theme.dart';

/// 📭 EMPTY STATE WIDGET - Beautiful Empty State Display
/// Features: Multiple Empty State Types, Call-to-Action, Animated

class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final IconData? icon;
  final String? imagePath;
  final String? actionText;
  final VoidCallback? onAction;
  final EmptyStateType type;
  final Color? iconColor;
  final double iconSize;

  const EmptyStateWidget({
    super.key,
    required this.title,
    required this.message,
    this.icon,
    this.imagePath,
    this.actionText,
    this.onAction,
    this.type = EmptyStateType.general,
    this.iconColor,
    this.iconSize = 80,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildIllustration(),
            const SizedBox(height: 24),
            _buildTitle(),
            const SizedBox(height: 12),
            _buildMessage(),
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: 32),
              _buildActionButton(),
            ],
          ],
        ),
      ),
    ).animate()
     .fadeIn(duration: 800.ms)
     .slideY(begin: 0.3, curve: Curves.easeOut);
  }

  Widget _buildIllustration() {
    if (imagePath != null) {
      return Image.asset(
        imagePath!,
        width: 120,
        height: 120,
        fit: BoxFit.contain,
      ).animate()
       .scale(delay: 200.ms, curve: Curves.elasticOut);
    }

    final displayIcon = icon ?? _getIconForType();
    final displayColor = iconColor ?? _getColorForType();

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: displayColor.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        displayIcon,
        size: iconSize,
        color: displayColor,
      ),
    ).animate()
     .scale(delay: 200.ms, curve: Curves.elasticOut)
     .then()
     .shimmer(delay: 1000.ms, duration: 2000.ms);
  }

  Widget _buildTitle() {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
      textAlign: TextAlign.center,
    ).animate()
     .slideX(begin: -0.3, delay: 400.ms, curve: Curves.easeOut);
  }

  Widget _buildMessage() {
    return Text(
      message,
      style: TextStyle(
        fontSize: 16,
        color: Colors.grey[600],
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    ).animate()
     .slideX(begin: 0.3, delay: 600.ms, curve: Curves.easeOut);
  }

  Widget _buildActionButton() {
    return ElevatedButton.icon(
      onPressed: onAction,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 2,
      ),
      icon: Icon(_getActionIconForType(), size: 20),
      label: Text(
        actionText!,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    ).animate()
     .scale(delay: 800.ms, curve: Curves.elasticOut);
  }

  IconData _getIconForType() {
    switch (type) {
      case EmptyStateType.interests:
        return Icons.favorite_border;
      case EmptyStateType.matches:
        return Icons.people_outline;
      case EmptyStateType.messages:
        return Icons.chat_bubble_outline;
      case EmptyStateType.search:
        return Icons.search_off;
      case EmptyStateType.notifications:
        return Icons.notifications_none;
      case EmptyStateType.favorites:
        return Icons.bookmark_border;
      case EmptyStateType.history:
        return Icons.history;
      case EmptyStateType.network:
        return Icons.wifi_off;
      case EmptyStateType.general:
        return Icons.inbox_outlined;
    }
  }

  Color _getColorForType() {
    switch (type) {
      case EmptyStateType.interests:
        return Colors.pink;
      case EmptyStateType.matches:
        return Colors.purple;
      case EmptyStateType.messages:
        return Colors.blue;
      case EmptyStateType.search:
        return Colors.orange;
      case EmptyStateType.notifications:
        return Colors.green;
      case EmptyStateType.favorites:
        return Colors.red;
      case EmptyStateType.history:
        return Colors.indigo;
      case EmptyStateType.network:
        return Colors.grey;
      case EmptyStateType.general:
        return Colors.grey;
    }
  }

  IconData _getActionIconForType() {
    switch (type) {
      case EmptyStateType.interests:
        return Icons.favorite;
      case EmptyStateType.matches:
        return Icons.search;
      case EmptyStateType.messages:
        return Icons.chat;
      case EmptyStateType.search:
        return Icons.search;
      case EmptyStateType.notifications:
        return Icons.refresh;
      case EmptyStateType.favorites:
        return Icons.explore;
      case EmptyStateType.history:
        return Icons.refresh;
      case EmptyStateType.network:
        return Icons.refresh;
      case EmptyStateType.general:
        return Icons.add;
    }
  }
}

/// Compact Empty State Widget for smaller spaces
class CompactEmptyStateWidget extends StatelessWidget {
  final String message;
  final IconData? icon;
  final String? actionText;
  final VoidCallback? onAction;

  const CompactEmptyStateWidget({
    super.key,
    required this.message,
    this.icon,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon ?? Icons.inbox_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          if (actionText != null && onAction != null) ...[
            const SizedBox(height: 16),
            TextButton(
              onPressed: onAction,
              child: Text(actionText!),
            ),
          ],
        ],
      ),
    );
  }
}

/// List Empty State Widget
class ListEmptyStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final IconData icon;
  final String? actionText;
  final VoidCallback? onAction;

  const ListEmptyStateWidget({
    super.key,
    required this.title,
    required this.message,
    required this.icon,
    this.actionText,
    this.onAction,
  });

  @override
  Widget build(BuildContext context) {
    return SliverFillRemaining(
      child: EmptyStateWidget(
        title: title,
        message: message,
        icon: icon,
        actionText: actionText,
        onAction: onAction,
      ),
    );
  }
}

/// Empty State with Animation
class AnimatedEmptyStateWidget extends StatefulWidget {
  final String title;
  final String message;
  final IconData icon;
  final String? actionText;
  final VoidCallback? onAction;

  const AnimatedEmptyStateWidget({
    super.key,
    required this.title,
    required this.message,
    required this.icon,
    this.actionText,
    this.onAction,
  });

  @override
  State<AnimatedEmptyStateWidget> createState() => _AnimatedEmptyStateWidgetState();
}

class _AnimatedEmptyStateWidgetState extends State<AnimatedEmptyStateWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: EmptyStateWidget(
              title: widget.title,
              message: widget.message,
              icon: widget.icon,
              actionText: widget.actionText,
              onAction: widget.onAction,
            ),
          ),
        );
      },
    );
  }
}

enum EmptyStateType {
  general,
  interests,
  matches,
  messages,
  search,
  notifications,
  favorites,
  history,
  network,
}
