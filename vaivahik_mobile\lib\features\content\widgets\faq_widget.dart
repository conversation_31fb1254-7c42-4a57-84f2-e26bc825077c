import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/theme.dart';
import '../models/content_model.dart';
import '../providers/content_provider.dart';

/// 📄 FAQ Widget - Expandable FAQ List
/// Using existing website FAQ structure

class FAQWidget extends ConsumerStatefulWidget {
  const FAQWidget({super.key});

  @override
  ConsumerState<FAQWidget> createState() => _FAQWidgetState();
}

class _FAQWidgetState extends ConsumerState<FAQWidget> {
  String _selectedCategory = 'all';
  final Set<int> _expandedItems = {};

  @override
  Widget build(BuildContext context) {
    final faqAsync = ref.watch(faqProvider);

    return faqAsync.when(
      loading: () => const Center(
        child: CircularProgressIndicator(),
      ),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load FAQ',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () => ref.refresh(faqProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
      data: (faqItems) => Column(
        children: [
          _buildCategoryFilter(faqItems),
          const SizedBox(height: 16),
          _buildFAQList(faqItems),
        ],
      ),
    );
  }

  Widget _buildCategoryFilter(List<FAQItem> faqItems) {
    final categories = ['all', ...faqItems.map((item) => item.category).toSet()];
    
    return SizedBox(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories.elementAt(index);
          final isSelected = _selectedCategory == category;
          
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: FilterChip(
              label: Text(
                _getCategoryDisplayName(category),
                style: TextStyle(
                  color: isSelected ? Colors.white : AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = category;
                  _expandedItems.clear();
                });
              },
              backgroundColor: Colors.white,
              selectedColor: AppColors.primary,
              checkmarkColor: Colors.white,
              elevation: isSelected ? 4 : 2,
              shadowColor: AppColors.primary.withValues(alpha: 0.3),
            ).animate().fadeIn(delay: Duration(milliseconds: index * 100)),
          );
        },
      ),
    );
  }

  Widget _buildFAQList(List<FAQItem> faqItems) {
    final filteredItems = _selectedCategory == 'all'
        ? faqItems
        : faqItems.where((item) => item.category == _selectedCategory).toList();

    if (filteredItems.isEmpty) {
      return Center(
        child: Column(
          children: [
            Icon(
              Icons.help_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No FAQ items found for this category',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        final item = filteredItems[index];
        final isExpanded = _expandedItems.contains(index);
        
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              ListTile(
                title: Text(
                  item.question,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                trailing: Icon(
                  isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: AppColors.primary,
                ),
                onTap: () {
                  setState(() {
                    if (isExpanded) {
                      _expandedItems.remove(index);
                    } else {
                      _expandedItems.add(index);
                    }
                  });
                },
              ),
              if (isExpanded)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  child: Text(
                    item.answer,
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                      color: Colors.grey[700],
                    ),
                  ),
                ).animate().fadeIn().slideY(
                  begin: -0.2,
                  duration: const Duration(milliseconds: 300),
                ),
            ],
          ),
        ).animate().fadeIn(delay: Duration(milliseconds: index * 100));
      },
    );
  }

  String _getCategoryDisplayName(String category) {
    switch (category) {
      case 'all':
        return 'All';
      case 'registration':
        return 'Registration';
      case 'verification':
        return 'Verification';
      case 'matching':
        return 'Matching';
      case 'premium':
        return 'Premium';
      case 'privacy':
        return 'Privacy';
      case 'kundli':
        return 'Kundli';
      default:
        return category.toUpperCase();
    }
  }
}

/// 📄 FAQ Search Widget
class FAQSearchWidget extends ConsumerStatefulWidget {
  const FAQSearchWidget({super.key});

  @override
  ConsumerState<FAQSearchWidget> createState() => _FAQSearchWidgetState();
}

class _FAQSearchWidgetState extends ConsumerState<FAQSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<FAQItem> _searchResults = [];
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query, List<FAQItem> allItems) {
    if (query.isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
      _searchResults = allItems
          .where((item) =>
              item.question.toLowerCase().contains(query.toLowerCase()) ||
              item.answer.toLowerCase().contains(query.toLowerCase()))
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    final faqAsync = ref.watch(faqProvider);

    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search FAQ...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _performSearch('', faqAsync.value ?? []);
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (query) {
              _performSearch(query, faqAsync.value ?? []);
            },
          ),
        ),
        if (_isSearching) ...[
          const SizedBox(height: 16),
          if (_searchResults.isEmpty)
            Center(
              child: Text(
                'No results found for "${_searchController.text}"',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final item = _searchResults[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ExpansionTile(
                    title: Text(
                      item.question,
                      style: const TextStyle(fontWeight: FontWeight.w600),
                    ),
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          item.answer,
                          style: TextStyle(
                            color: Colors.grey[700],
                            height: 1.5,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ],
    );
  }
}
