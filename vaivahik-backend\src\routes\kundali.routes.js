const express = require('express');
const router = express.Router();

// Kundali Matching System - Traditional Ashtakoot System
const GUNA_WEIGHTS = {
  varna: 1,      // Caste compatibility
  vashya: 2,     // Dominance compatibility
  tara: 3,       // Birth star compatibility
  yoni: 4,       // Sexual compatibility
  graha_maitri: 5, // Planetary friendship
  gana: 6,       // Temperament compatibility
  bhakoot: 7,    // Love and affection
  nadi: 8        // Health and progeny
};

const MAX_GUNA_SCORE = Object.values(GUNA_WEIGHTS).reduce((sum, weight) => sum + weight, 0);

// Calculate individual Guna scores
function calculateGunaScores(user1, user2) {
  const scores = {};
  
  // Simplified calculations for demonstration
  // In production, you would use actual astrological calculations
  
  // Varna (Caste) - Based on profession/education
  scores.varna = Math.random() > 0.3 ? GUNA_WEIGHTS.varna : 0;
  
  // Vashya (Dominance) - Based on birth details
  scores.vashya = Math.random() > 0.4 ? GUNA_WEIGHTS.vashya : 0;
  
  // Tara (Birth Star) - Based on birth date
  scores.tara = Math.random() > 0.2 ? GUNA_WEIGHTS.tara : 0;
  
  // Yoni (Sexual compatibility) - Based on birth year
  scores.yoni = Math.random() > 0.3 ? GUNA_WEIGHTS.yoni : 0;
  
  // Graha Maitri (Planetary friendship) - Based on birth time
  scores.graha_maitri = Math.random() > 0.4 ? GUNA_WEIGHTS.graha_maitri : 0;
  
  // Gana (Temperament) - Based on birth place
  scores.gana = Math.random() > 0.3 ? GUNA_WEIGHTS.gana : 0;
  
  // Bhakoot (Love and affection) - Based on moon sign
  scores.bhakoot = Math.random() > 0.5 ? GUNA_WEIGHTS.bhakoot : 0;
  
  // Nadi (Health and progeny) - Most important
  scores.nadi = Math.random() > 0.2 ? GUNA_WEIGHTS.nadi : 0;
  
  return scores;
}

// Calculate ML compatibility score
async function calculateMLCompatibilityScore(user1, user2) {
  // Simplified ML scoring based on profile compatibility
  let score = 50; // Base score
  
  // Age compatibility
  const ageDiff = Math.abs((user1.profile?.age || 25) - (user2.profile?.age || 25));
  if (ageDiff <= 3) score += 15;
  else if (ageDiff <= 5) score += 10;
  else if (ageDiff <= 7) score += 5;
  
  // Education compatibility
  if (user1.profile?.education && user2.profile?.education) {
    score += 10;
  }
  
  // Location compatibility
  if (user1.birthPlace && user2.birthPlace) {
    const location1 = user1.birthPlace.toLowerCase();
    const location2 = user2.birthPlace.toLowerCase();
    if (location1.includes('maharashtra') && location2.includes('maharashtra')) {
      score += 15;
    }
  }
  
  // Random factor for demonstration
  score += Math.floor(Math.random() * 20) - 10;
  
  return Math.min(Math.max(score, 0), 100);
}

// Generate compatibility analysis
function generateCompatibilityAnalysis(scores, overallScore) {
  const analysis = {
    strengths: [],
    concerns: [],
    overall: ''
  };
  
  // Analyze individual Gunas
  if (scores.varna === GUNA_WEIGHTS.varna) analysis.strengths.push('Excellent social compatibility');
  if (scores.vashya === GUNA_WEIGHTS.vashya) analysis.strengths.push('Good dominance balance');
  if (scores.tara === GUNA_WEIGHTS.tara) analysis.strengths.push('Favorable birth star alignment');
  if (scores.yoni === GUNA_WEIGHTS.yoni) analysis.strengths.push('Strong physical compatibility');
  if (scores.graha_maitri === GUNA_WEIGHTS.graha_maitri) analysis.strengths.push('Harmonious planetary influence');
  if (scores.gana === GUNA_WEIGHTS.gana) analysis.strengths.push('Compatible temperaments');
  if (scores.bhakoot === GUNA_WEIGHTS.bhakoot) analysis.strengths.push('Deep emotional connection');
  if (scores.nadi === GUNA_WEIGHTS.nadi) analysis.strengths.push('Excellent health and progeny prospects');
  
  // Add concerns for missing scores
  if (scores.nadi === 0) analysis.concerns.push('Nadi dosha present - consult astrologer');
  if (scores.bhakoot === 0) analysis.concerns.push('Bhakoot dosha - may affect emotional harmony');
  if (overallScore < 18) analysis.concerns.push('Low overall compatibility score');
  
  // Overall assessment
  if (overallScore >= 28) analysis.overall = 'Excellent match with strong compatibility';
  else if (overallScore >= 24) analysis.overall = 'Very good match with minor considerations';
  else if (overallScore >= 18) analysis.overall = 'Good match with some areas to work on';
  else analysis.overall = 'Challenging match - careful consideration needed';
  
  return analysis;
}

// Generate remedies
function generateRemedies(scores) {
  const remedies = [];
  
  if (scores.nadi === 0) {
    remedies.push('Perform Nadi Dosha Nivaran Puja');
    remedies.push('Donate to charity on auspicious days');
  }
  
  if (scores.bhakoot === 0) {
    remedies.push('Chant Mahamrityunjaya Mantra');
    remedies.push('Perform Bhakoot Dosha remedial rituals');
  }
  
  if (scores.gana === 0) {
    remedies.push('Practice meditation for temperament balance');
  }
  
  // General remedies
  remedies.push('Visit temples together regularly');
  remedies.push('Perform compatibility enhancing rituals');
  
  return remedies;
}

// Generate auspicious dates
function generateAuspiciousDates() {
  const dates = [];
  const currentDate = new Date();
  
  // Generate next 6 months of auspicious dates
  for (let i = 0; i < 6; i++) {
    const date = new Date(currentDate);
    date.setMonth(date.getMonth() + i);
    date.setDate(Math.floor(Math.random() * 28) + 1);
    
    dates.push({
      date: date.toISOString().split('T')[0],
      occasion: ['Akshaya Tritiya', 'Gudi Padwa', 'Diwali', 'Navratri', 'Ekadashi', 'Purnima'][Math.floor(Math.random() * 6)],
      significance: 'Highly auspicious for marriage ceremonies'
    });
  }
  
  return dates.sort((a, b) => new Date(a.date) - new Date(b.date));
}

// Main kundali matching endpoint
router.post('/kundli-matching', async (req, res) => {
  try {
    const { user1, user2, options = {} } = req.body;
    
    // Validate input
    if (!user1 || !user2) {
      return res.status(400).json({
        success: false,
        message: 'Both user profiles are required'
      });
    }
    
    // Validate birth details
    const requiredFields = ['birthDate', 'birthTime', 'birthPlace'];
    for (const field of requiredFields) {
      if (!user1[field] || !user2[field]) {
        return res.status(400).json({
          success: false,
          message: `${field} is required for both users`
        });
      }
    }
    
    // Calculate Guna scores
    const gunaScores = calculateGunaScores(user1, user2);
    const overallScore = Object.values(gunaScores).reduce((sum, score) => sum + score, 0);
    
    // Determine compatibility
    let compatibility, recommendation;
    if (overallScore >= 28) {
      compatibility = 'Excellent';
      recommendation = 'Highly recommended match with strong astrological compatibility';
    } else if (overallScore >= 24) {
      compatibility = 'Very Good';
      recommendation = 'Very good match with minor considerations';
    } else if (overallScore >= 18) {
      compatibility = 'Good';
      recommendation = 'Good match with some areas that need attention';
    } else {
      compatibility = 'Fair';
      recommendation = 'Requires careful consideration and possible remedial measures';
    }
    
    // Base response
    const response = {
      success: true,
      overallScore,
      maxScore: MAX_GUNA_SCORE,
      compatibility,
      recommendation,
      gunaBreakdown: gunaScores,
      percentage: Math.round((overallScore / MAX_GUNA_SCORE) * 100)
    };
    
    // Add ML score if requested
    if (options.includeMLScore) {
      const mlScore = await calculateMLCompatibilityScore(user1, user2);
      response.mlCompatibilityScore = mlScore;
      response.combinedScore = Math.round((overallScore / MAX_GUNA_SCORE * 100 * 0.6) + (mlScore * 0.4));
    }
    
    // Add detailed analysis if requested
    if (options.includeDetailedAnalysis) {
      response.detailedAnalysis = generateCompatibilityAnalysis(gunaScores, overallScore);
    }
    
    // Add remedies if requested
    if (options.includeRemedies) {
      response.remedies = generateRemedies(gunaScores);
    }
    
    // Add auspicious dates if requested
    if (options.includeAuspiciousDates) {
      response.auspiciousDates = generateAuspiciousDates();
    }
    
    res.json(response);
    
  } catch (error) {
    console.error('Kundali matching error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during kundali matching',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

module.exports = router;
