// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'shortlist_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ShortlistItem {
  String get id;
  String get userId;
  String get targetUserId;
  String? get note;
  DateTime get createdAt;
  DateTime? get updatedAt;
  bool get interestSent;
  String? get interestStatus;
  ShortlistProfile get profile;

  /// Create a copy of ShortlistItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShortlistItemCopyWith<ShortlistItem> get copyWith =>
      _$ShortlistItemCopyWithImpl<ShortlistItem>(
          this as ShortlistItem, _$identity);

  /// Serializes this ShortlistItem to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShortlistItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.interestSent, interestSent) ||
                other.interestSent == interestSent) &&
            (identical(other.interestStatus, interestStatus) ||
                other.interestStatus == interestStatus) &&
            (identical(other.profile, profile) || other.profile == profile));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, targetUserId, note,
      createdAt, updatedAt, interestSent, interestStatus, profile);

  @override
  String toString() {
    return 'ShortlistItem(id: $id, userId: $userId, targetUserId: $targetUserId, note: $note, createdAt: $createdAt, updatedAt: $updatedAt, interestSent: $interestSent, interestStatus: $interestStatus, profile: $profile)';
  }
}

/// @nodoc
abstract mixin class $ShortlistItemCopyWith<$Res> {
  factory $ShortlistItemCopyWith(
          ShortlistItem value, $Res Function(ShortlistItem) _then) =
      _$ShortlistItemCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String targetUserId,
      String? note,
      DateTime createdAt,
      DateTime? updatedAt,
      bool interestSent,
      String? interestStatus,
      ShortlistProfile profile});

  $ShortlistProfileCopyWith<$Res> get profile;
}

/// @nodoc
class _$ShortlistItemCopyWithImpl<$Res>
    implements $ShortlistItemCopyWith<$Res> {
  _$ShortlistItemCopyWithImpl(this._self, this._then);

  final ShortlistItem _self;
  final $Res Function(ShortlistItem) _then;

  /// Create a copy of ShortlistItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? targetUserId = null,
    Object? note = freezed,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? interestSent = null,
    Object? interestStatus = freezed,
    Object? profile = null,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      interestSent: null == interestSent
          ? _self.interestSent
          : interestSent // ignore: cast_nullable_to_non_nullable
              as bool,
      interestStatus: freezed == interestStatus
          ? _self.interestStatus
          : interestStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      profile: null == profile
          ? _self.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ShortlistProfile,
    ));
  }

  /// Create a copy of ShortlistItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistProfileCopyWith<$Res> get profile {
    return $ShortlistProfileCopyWith<$Res>(_self.profile, (value) {
      return _then(_self.copyWith(profile: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ShortlistItem].
extension ShortlistItemPatterns on ShortlistItem {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShortlistItem value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistItem() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShortlistItem value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistItem():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShortlistItem value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistItem() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String targetUserId,
            String? note,
            DateTime createdAt,
            DateTime? updatedAt,
            bool interestSent,
            String? interestStatus,
            ShortlistProfile profile)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistItem() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.targetUserId,
            _that.note,
            _that.createdAt,
            _that.updatedAt,
            _that.interestSent,
            _that.interestStatus,
            _that.profile);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String targetUserId,
            String? note,
            DateTime createdAt,
            DateTime? updatedAt,
            bool interestSent,
            String? interestStatus,
            ShortlistProfile profile)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistItem():
        return $default(
            _that.id,
            _that.userId,
            _that.targetUserId,
            _that.note,
            _that.createdAt,
            _that.updatedAt,
            _that.interestSent,
            _that.interestStatus,
            _that.profile);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String userId,
            String targetUserId,
            String? note,
            DateTime createdAt,
            DateTime? updatedAt,
            bool interestSent,
            String? interestStatus,
            ShortlistProfile profile)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistItem() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.targetUserId,
            _that.note,
            _that.createdAt,
            _that.updatedAt,
            _that.interestSent,
            _that.interestStatus,
            _that.profile);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShortlistItem implements ShortlistItem {
  const _ShortlistItem(
      {required this.id,
      required this.userId,
      required this.targetUserId,
      this.note,
      required this.createdAt,
      this.updatedAt,
      required this.interestSent,
      this.interestStatus,
      required this.profile});
  factory _ShortlistItem.fromJson(Map<String, dynamic> json) =>
      _$ShortlistItemFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String targetUserId;
  @override
  final String? note;
  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final bool interestSent;
  @override
  final String? interestStatus;
  @override
  final ShortlistProfile profile;

  /// Create a copy of ShortlistItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShortlistItemCopyWith<_ShortlistItem> get copyWith =>
      __$ShortlistItemCopyWithImpl<_ShortlistItem>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShortlistItemToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShortlistItem &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.interestSent, interestSent) ||
                other.interestSent == interestSent) &&
            (identical(other.interestStatus, interestStatus) ||
                other.interestStatus == interestStatus) &&
            (identical(other.profile, profile) || other.profile == profile));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, userId, targetUserId, note,
      createdAt, updatedAt, interestSent, interestStatus, profile);

  @override
  String toString() {
    return 'ShortlistItem(id: $id, userId: $userId, targetUserId: $targetUserId, note: $note, createdAt: $createdAt, updatedAt: $updatedAt, interestSent: $interestSent, interestStatus: $interestStatus, profile: $profile)';
  }
}

/// @nodoc
abstract mixin class _$ShortlistItemCopyWith<$Res>
    implements $ShortlistItemCopyWith<$Res> {
  factory _$ShortlistItemCopyWith(
          _ShortlistItem value, $Res Function(_ShortlistItem) _then) =
      __$ShortlistItemCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String targetUserId,
      String? note,
      DateTime createdAt,
      DateTime? updatedAt,
      bool interestSent,
      String? interestStatus,
      ShortlistProfile profile});

  @override
  $ShortlistProfileCopyWith<$Res> get profile;
}

/// @nodoc
class __$ShortlistItemCopyWithImpl<$Res>
    implements _$ShortlistItemCopyWith<$Res> {
  __$ShortlistItemCopyWithImpl(this._self, this._then);

  final _ShortlistItem _self;
  final $Res Function(_ShortlistItem) _then;

  /// Create a copy of ShortlistItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? targetUserId = null,
    Object? note = freezed,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? interestSent = null,
    Object? interestStatus = freezed,
    Object? profile = null,
  }) {
    return _then(_ShortlistItem(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      interestSent: null == interestSent
          ? _self.interestSent
          : interestSent // ignore: cast_nullable_to_non_nullable
              as bool,
      interestStatus: freezed == interestStatus
          ? _self.interestStatus
          : interestStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      profile: null == profile
          ? _self.profile
          : profile // ignore: cast_nullable_to_non_nullable
              as ShortlistProfile,
    ));
  }

  /// Create a copy of ShortlistItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistProfileCopyWith<$Res> get profile {
    return $ShortlistProfileCopyWith<$Res>(_self.profile, (value) {
      return _then(_self.copyWith(profile: value));
    });
  }
}

/// @nodoc
mixin _$ShortlistProfile {
  String get id;
  String get firstName;
  String? get lastName;
  int? get age;
  String? get location;
  String? get education;
  String? get occupation;
  String? get profilePicture;
  int? get compatibility;
  String? get religion;
  String? get caste;
  String? get motherTongue;
  String? get maritalStatus;
  double? get height;
  String? get income;
  bool? get isVerified;
  bool? get isOnline;
  DateTime? get lastSeen;

  /// Create a copy of ShortlistProfile
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShortlistProfileCopyWith<ShortlistProfile> get copyWith =>
      _$ShortlistProfileCopyWithImpl<ShortlistProfile>(
          this as ShortlistProfile, _$identity);

  /// Serializes this ShortlistProfile to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShortlistProfile &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.profilePicture, profilePicture) ||
                other.profilePicture == profilePicture) &&
            (identical(other.compatibility, compatibility) ||
                other.compatibility == compatibility) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            (identical(other.caste, caste) || other.caste == caste) &&
            (identical(other.motherTongue, motherTongue) ||
                other.motherTongue == motherTongue) &&
            (identical(other.maritalStatus, maritalStatus) ||
                other.maritalStatus == maritalStatus) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.income, income) || other.income == income) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline) &&
            (identical(other.lastSeen, lastSeen) ||
                other.lastSeen == lastSeen));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      age,
      location,
      education,
      occupation,
      profilePicture,
      compatibility,
      religion,
      caste,
      motherTongue,
      maritalStatus,
      height,
      income,
      isVerified,
      isOnline,
      lastSeen);

  @override
  String toString() {
    return 'ShortlistProfile(id: $id, firstName: $firstName, lastName: $lastName, age: $age, location: $location, education: $education, occupation: $occupation, profilePicture: $profilePicture, compatibility: $compatibility, religion: $religion, caste: $caste, motherTongue: $motherTongue, maritalStatus: $maritalStatus, height: $height, income: $income, isVerified: $isVerified, isOnline: $isOnline, lastSeen: $lastSeen)';
  }
}

/// @nodoc
abstract mixin class $ShortlistProfileCopyWith<$Res> {
  factory $ShortlistProfileCopyWith(
          ShortlistProfile value, $Res Function(ShortlistProfile) _then) =
      _$ShortlistProfileCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String firstName,
      String? lastName,
      int? age,
      String? location,
      String? education,
      String? occupation,
      String? profilePicture,
      int? compatibility,
      String? religion,
      String? caste,
      String? motherTongue,
      String? maritalStatus,
      double? height,
      String? income,
      bool? isVerified,
      bool? isOnline,
      DateTime? lastSeen});
}

/// @nodoc
class _$ShortlistProfileCopyWithImpl<$Res>
    implements $ShortlistProfileCopyWith<$Res> {
  _$ShortlistProfileCopyWithImpl(this._self, this._then);

  final ShortlistProfile _self;
  final $Res Function(ShortlistProfile) _then;

  /// Create a copy of ShortlistProfile
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firstName = null,
    Object? lastName = freezed,
    Object? age = freezed,
    Object? location = freezed,
    Object? education = freezed,
    Object? occupation = freezed,
    Object? profilePicture = freezed,
    Object? compatibility = freezed,
    Object? religion = freezed,
    Object? caste = freezed,
    Object? motherTongue = freezed,
    Object? maritalStatus = freezed,
    Object? height = freezed,
    Object? income = freezed,
    Object? isVerified = freezed,
    Object? isOnline = freezed,
    Object? lastSeen = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePicture: freezed == profilePicture
          ? _self.profilePicture
          : profilePicture // ignore: cast_nullable_to_non_nullable
              as String?,
      compatibility: freezed == compatibility
          ? _self.compatibility
          : compatibility // ignore: cast_nullable_to_non_nullable
              as int?,
      religion: freezed == religion
          ? _self.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      caste: freezed == caste
          ? _self.caste
          : caste // ignore: cast_nullable_to_non_nullable
              as String?,
      motherTongue: freezed == motherTongue
          ? _self.motherTongue
          : motherTongue // ignore: cast_nullable_to_non_nullable
              as String?,
      maritalStatus: freezed == maritalStatus
          ? _self.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _self.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      income: freezed == income
          ? _self.income
          : income // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: freezed == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOnline: freezed == isOnline
          ? _self.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSeen: freezed == lastSeen
          ? _self.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ShortlistProfile].
extension ShortlistProfilePatterns on ShortlistProfile {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShortlistProfile value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistProfile() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShortlistProfile value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistProfile():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShortlistProfile value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistProfile() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String firstName,
            String? lastName,
            int? age,
            String? location,
            String? education,
            String? occupation,
            String? profilePicture,
            int? compatibility,
            String? religion,
            String? caste,
            String? motherTongue,
            String? maritalStatus,
            double? height,
            String? income,
            bool? isVerified,
            bool? isOnline,
            DateTime? lastSeen)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistProfile() when $default != null:
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.age,
            _that.location,
            _that.education,
            _that.occupation,
            _that.profilePicture,
            _that.compatibility,
            _that.religion,
            _that.caste,
            _that.motherTongue,
            _that.maritalStatus,
            _that.height,
            _that.income,
            _that.isVerified,
            _that.isOnline,
            _that.lastSeen);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String firstName,
            String? lastName,
            int? age,
            String? location,
            String? education,
            String? occupation,
            String? profilePicture,
            int? compatibility,
            String? religion,
            String? caste,
            String? motherTongue,
            String? maritalStatus,
            double? height,
            String? income,
            bool? isVerified,
            bool? isOnline,
            DateTime? lastSeen)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistProfile():
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.age,
            _that.location,
            _that.education,
            _that.occupation,
            _that.profilePicture,
            _that.compatibility,
            _that.religion,
            _that.caste,
            _that.motherTongue,
            _that.maritalStatus,
            _that.height,
            _that.income,
            _that.isVerified,
            _that.isOnline,
            _that.lastSeen);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String firstName,
            String? lastName,
            int? age,
            String? location,
            String? education,
            String? occupation,
            String? profilePicture,
            int? compatibility,
            String? religion,
            String? caste,
            String? motherTongue,
            String? maritalStatus,
            double? height,
            String? income,
            bool? isVerified,
            bool? isOnline,
            DateTime? lastSeen)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistProfile() when $default != null:
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.age,
            _that.location,
            _that.education,
            _that.occupation,
            _that.profilePicture,
            _that.compatibility,
            _that.religion,
            _that.caste,
            _that.motherTongue,
            _that.maritalStatus,
            _that.height,
            _that.income,
            _that.isVerified,
            _that.isOnline,
            _that.lastSeen);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShortlistProfile implements ShortlistProfile {
  const _ShortlistProfile(
      {required this.id,
      required this.firstName,
      this.lastName,
      this.age,
      this.location,
      this.education,
      this.occupation,
      this.profilePicture,
      this.compatibility,
      this.religion,
      this.caste,
      this.motherTongue,
      this.maritalStatus,
      this.height,
      this.income,
      this.isVerified,
      this.isOnline,
      this.lastSeen});
  factory _ShortlistProfile.fromJson(Map<String, dynamic> json) =>
      _$ShortlistProfileFromJson(json);

  @override
  final String id;
  @override
  final String firstName;
  @override
  final String? lastName;
  @override
  final int? age;
  @override
  final String? location;
  @override
  final String? education;
  @override
  final String? occupation;
  @override
  final String? profilePicture;
  @override
  final int? compatibility;
  @override
  final String? religion;
  @override
  final String? caste;
  @override
  final String? motherTongue;
  @override
  final String? maritalStatus;
  @override
  final double? height;
  @override
  final String? income;
  @override
  final bool? isVerified;
  @override
  final bool? isOnline;
  @override
  final DateTime? lastSeen;

  /// Create a copy of ShortlistProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShortlistProfileCopyWith<_ShortlistProfile> get copyWith =>
      __$ShortlistProfileCopyWithImpl<_ShortlistProfile>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShortlistProfileToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShortlistProfile &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.profilePicture, profilePicture) ||
                other.profilePicture == profilePicture) &&
            (identical(other.compatibility, compatibility) ||
                other.compatibility == compatibility) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            (identical(other.caste, caste) || other.caste == caste) &&
            (identical(other.motherTongue, motherTongue) ||
                other.motherTongue == motherTongue) &&
            (identical(other.maritalStatus, maritalStatus) ||
                other.maritalStatus == maritalStatus) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.income, income) || other.income == income) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline) &&
            (identical(other.lastSeen, lastSeen) ||
                other.lastSeen == lastSeen));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      age,
      location,
      education,
      occupation,
      profilePicture,
      compatibility,
      religion,
      caste,
      motherTongue,
      maritalStatus,
      height,
      income,
      isVerified,
      isOnline,
      lastSeen);

  @override
  String toString() {
    return 'ShortlistProfile(id: $id, firstName: $firstName, lastName: $lastName, age: $age, location: $location, education: $education, occupation: $occupation, profilePicture: $profilePicture, compatibility: $compatibility, religion: $religion, caste: $caste, motherTongue: $motherTongue, maritalStatus: $maritalStatus, height: $height, income: $income, isVerified: $isVerified, isOnline: $isOnline, lastSeen: $lastSeen)';
  }
}

/// @nodoc
abstract mixin class _$ShortlistProfileCopyWith<$Res>
    implements $ShortlistProfileCopyWith<$Res> {
  factory _$ShortlistProfileCopyWith(
          _ShortlistProfile value, $Res Function(_ShortlistProfile) _then) =
      __$ShortlistProfileCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String firstName,
      String? lastName,
      int? age,
      String? location,
      String? education,
      String? occupation,
      String? profilePicture,
      int? compatibility,
      String? religion,
      String? caste,
      String? motherTongue,
      String? maritalStatus,
      double? height,
      String? income,
      bool? isVerified,
      bool? isOnline,
      DateTime? lastSeen});
}

/// @nodoc
class __$ShortlistProfileCopyWithImpl<$Res>
    implements _$ShortlistProfileCopyWith<$Res> {
  __$ShortlistProfileCopyWithImpl(this._self, this._then);

  final _ShortlistProfile _self;
  final $Res Function(_ShortlistProfile) _then;

  /// Create a copy of ShortlistProfile
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? firstName = null,
    Object? lastName = freezed,
    Object? age = freezed,
    Object? location = freezed,
    Object? education = freezed,
    Object? occupation = freezed,
    Object? profilePicture = freezed,
    Object? compatibility = freezed,
    Object? religion = freezed,
    Object? caste = freezed,
    Object? motherTongue = freezed,
    Object? maritalStatus = freezed,
    Object? height = freezed,
    Object? income = freezed,
    Object? isVerified = freezed,
    Object? isOnline = freezed,
    Object? lastSeen = freezed,
  }) {
    return _then(_ShortlistProfile(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePicture: freezed == profilePicture
          ? _self.profilePicture
          : profilePicture // ignore: cast_nullable_to_non_nullable
              as String?,
      compatibility: freezed == compatibility
          ? _self.compatibility
          : compatibility // ignore: cast_nullable_to_non_nullable
              as int?,
      religion: freezed == religion
          ? _self.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      caste: freezed == caste
          ? _self.caste
          : caste // ignore: cast_nullable_to_non_nullable
              as String?,
      motherTongue: freezed == motherTongue
          ? _self.motherTongue
          : motherTongue // ignore: cast_nullable_to_non_nullable
              as String?,
      maritalStatus: freezed == maritalStatus
          ? _self.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _self.height
          : height // ignore: cast_nullable_to_non_nullable
              as double?,
      income: freezed == income
          ? _self.income
          : income // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: freezed == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOnline: freezed == isOnline
          ? _self.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSeen: freezed == lastSeen
          ? _self.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
mixin _$ShortlistStats {
  int get totalShortlisted;
  int get interestsSent;
  int get interestsReceived;
  int get mutualInterests;
  int get recentlyAdded;
  int get thisWeek;
  int get thisMonth;

  /// Create a copy of ShortlistStats
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShortlistStatsCopyWith<ShortlistStats> get copyWith =>
      _$ShortlistStatsCopyWithImpl<ShortlistStats>(
          this as ShortlistStats, _$identity);

  /// Serializes this ShortlistStats to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShortlistStats &&
            (identical(other.totalShortlisted, totalShortlisted) ||
                other.totalShortlisted == totalShortlisted) &&
            (identical(other.interestsSent, interestsSent) ||
                other.interestsSent == interestsSent) &&
            (identical(other.interestsReceived, interestsReceived) ||
                other.interestsReceived == interestsReceived) &&
            (identical(other.mutualInterests, mutualInterests) ||
                other.mutualInterests == mutualInterests) &&
            (identical(other.recentlyAdded, recentlyAdded) ||
                other.recentlyAdded == recentlyAdded) &&
            (identical(other.thisWeek, thisWeek) ||
                other.thisWeek == thisWeek) &&
            (identical(other.thisMonth, thisMonth) ||
                other.thisMonth == thisMonth));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalShortlisted, interestsSent,
      interestsReceived, mutualInterests, recentlyAdded, thisWeek, thisMonth);

  @override
  String toString() {
    return 'ShortlistStats(totalShortlisted: $totalShortlisted, interestsSent: $interestsSent, interestsReceived: $interestsReceived, mutualInterests: $mutualInterests, recentlyAdded: $recentlyAdded, thisWeek: $thisWeek, thisMonth: $thisMonth)';
  }
}

/// @nodoc
abstract mixin class $ShortlistStatsCopyWith<$Res> {
  factory $ShortlistStatsCopyWith(
          ShortlistStats value, $Res Function(ShortlistStats) _then) =
      _$ShortlistStatsCopyWithImpl;
  @useResult
  $Res call(
      {int totalShortlisted,
      int interestsSent,
      int interestsReceived,
      int mutualInterests,
      int recentlyAdded,
      int thisWeek,
      int thisMonth});
}

/// @nodoc
class _$ShortlistStatsCopyWithImpl<$Res>
    implements $ShortlistStatsCopyWith<$Res> {
  _$ShortlistStatsCopyWithImpl(this._self, this._then);

  final ShortlistStats _self;
  final $Res Function(ShortlistStats) _then;

  /// Create a copy of ShortlistStats
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? totalShortlisted = null,
    Object? interestsSent = null,
    Object? interestsReceived = null,
    Object? mutualInterests = null,
    Object? recentlyAdded = null,
    Object? thisWeek = null,
    Object? thisMonth = null,
  }) {
    return _then(_self.copyWith(
      totalShortlisted: null == totalShortlisted
          ? _self.totalShortlisted
          : totalShortlisted // ignore: cast_nullable_to_non_nullable
              as int,
      interestsSent: null == interestsSent
          ? _self.interestsSent
          : interestsSent // ignore: cast_nullable_to_non_nullable
              as int,
      interestsReceived: null == interestsReceived
          ? _self.interestsReceived
          : interestsReceived // ignore: cast_nullable_to_non_nullable
              as int,
      mutualInterests: null == mutualInterests
          ? _self.mutualInterests
          : mutualInterests // ignore: cast_nullable_to_non_nullable
              as int,
      recentlyAdded: null == recentlyAdded
          ? _self.recentlyAdded
          : recentlyAdded // ignore: cast_nullable_to_non_nullable
              as int,
      thisWeek: null == thisWeek
          ? _self.thisWeek
          : thisWeek // ignore: cast_nullable_to_non_nullable
              as int,
      thisMonth: null == thisMonth
          ? _self.thisMonth
          : thisMonth // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// Adds pattern-matching-related methods to [ShortlistStats].
extension ShortlistStatsPatterns on ShortlistStats {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShortlistStats value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistStats() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShortlistStats value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistStats():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShortlistStats value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistStats() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int totalShortlisted,
            int interestsSent,
            int interestsReceived,
            int mutualInterests,
            int recentlyAdded,
            int thisWeek,
            int thisMonth)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistStats() when $default != null:
        return $default(
            _that.totalShortlisted,
            _that.interestsSent,
            _that.interestsReceived,
            _that.mutualInterests,
            _that.recentlyAdded,
            _that.thisWeek,
            _that.thisMonth);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int totalShortlisted,
            int interestsSent,
            int interestsReceived,
            int mutualInterests,
            int recentlyAdded,
            int thisWeek,
            int thisMonth)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistStats():
        return $default(
            _that.totalShortlisted,
            _that.interestsSent,
            _that.interestsReceived,
            _that.mutualInterests,
            _that.recentlyAdded,
            _that.thisWeek,
            _that.thisMonth);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int totalShortlisted,
            int interestsSent,
            int interestsReceived,
            int mutualInterests,
            int recentlyAdded,
            int thisWeek,
            int thisMonth)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistStats() when $default != null:
        return $default(
            _that.totalShortlisted,
            _that.interestsSent,
            _that.interestsReceived,
            _that.mutualInterests,
            _that.recentlyAdded,
            _that.thisWeek,
            _that.thisMonth);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShortlistStats implements ShortlistStats {
  const _ShortlistStats(
      {required this.totalShortlisted,
      required this.interestsSent,
      required this.interestsReceived,
      required this.mutualInterests,
      required this.recentlyAdded,
      required this.thisWeek,
      required this.thisMonth});
  factory _ShortlistStats.fromJson(Map<String, dynamic> json) =>
      _$ShortlistStatsFromJson(json);

  @override
  final int totalShortlisted;
  @override
  final int interestsSent;
  @override
  final int interestsReceived;
  @override
  final int mutualInterests;
  @override
  final int recentlyAdded;
  @override
  final int thisWeek;
  @override
  final int thisMonth;

  /// Create a copy of ShortlistStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShortlistStatsCopyWith<_ShortlistStats> get copyWith =>
      __$ShortlistStatsCopyWithImpl<_ShortlistStats>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShortlistStatsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShortlistStats &&
            (identical(other.totalShortlisted, totalShortlisted) ||
                other.totalShortlisted == totalShortlisted) &&
            (identical(other.interestsSent, interestsSent) ||
                other.interestsSent == interestsSent) &&
            (identical(other.interestsReceived, interestsReceived) ||
                other.interestsReceived == interestsReceived) &&
            (identical(other.mutualInterests, mutualInterests) ||
                other.mutualInterests == mutualInterests) &&
            (identical(other.recentlyAdded, recentlyAdded) ||
                other.recentlyAdded == recentlyAdded) &&
            (identical(other.thisWeek, thisWeek) ||
                other.thisWeek == thisWeek) &&
            (identical(other.thisMonth, thisMonth) ||
                other.thisMonth == thisMonth));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, totalShortlisted, interestsSent,
      interestsReceived, mutualInterests, recentlyAdded, thisWeek, thisMonth);

  @override
  String toString() {
    return 'ShortlistStats(totalShortlisted: $totalShortlisted, interestsSent: $interestsSent, interestsReceived: $interestsReceived, mutualInterests: $mutualInterests, recentlyAdded: $recentlyAdded, thisWeek: $thisWeek, thisMonth: $thisMonth)';
  }
}

/// @nodoc
abstract mixin class _$ShortlistStatsCopyWith<$Res>
    implements $ShortlistStatsCopyWith<$Res> {
  factory _$ShortlistStatsCopyWith(
          _ShortlistStats value, $Res Function(_ShortlistStats) _then) =
      __$ShortlistStatsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int totalShortlisted,
      int interestsSent,
      int interestsReceived,
      int mutualInterests,
      int recentlyAdded,
      int thisWeek,
      int thisMonth});
}

/// @nodoc
class __$ShortlistStatsCopyWithImpl<$Res>
    implements _$ShortlistStatsCopyWith<$Res> {
  __$ShortlistStatsCopyWithImpl(this._self, this._then);

  final _ShortlistStats _self;
  final $Res Function(_ShortlistStats) _then;

  /// Create a copy of ShortlistStats
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? totalShortlisted = null,
    Object? interestsSent = null,
    Object? interestsReceived = null,
    Object? mutualInterests = null,
    Object? recentlyAdded = null,
    Object? thisWeek = null,
    Object? thisMonth = null,
  }) {
    return _then(_ShortlistStats(
      totalShortlisted: null == totalShortlisted
          ? _self.totalShortlisted
          : totalShortlisted // ignore: cast_nullable_to_non_nullable
              as int,
      interestsSent: null == interestsSent
          ? _self.interestsSent
          : interestsSent // ignore: cast_nullable_to_non_nullable
              as int,
      interestsReceived: null == interestsReceived
          ? _self.interestsReceived
          : interestsReceived // ignore: cast_nullable_to_non_nullable
              as int,
      mutualInterests: null == mutualInterests
          ? _self.mutualInterests
          : mutualInterests // ignore: cast_nullable_to_non_nullable
              as int,
      recentlyAdded: null == recentlyAdded
          ? _self.recentlyAdded
          : recentlyAdded // ignore: cast_nullable_to_non_nullable
              as int,
      thisWeek: null == thisWeek
          ? _self.thisWeek
          : thisWeek // ignore: cast_nullable_to_non_nullable
              as int,
      thisMonth: null == thisMonth
          ? _self.thisMonth
          : thisMonth // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$ShortlistFilters {
  String? get searchQuery;
  bool? get interestSent;
  String? get interestStatus;
  String? get location;
  String? get education;
  String? get occupation;
  int? get minAge;
  int? get maxAge;
  double? get minHeight;
  double? get maxHeight;
  String? get religion;
  String? get caste;
  String? get maritalStatus;
  bool? get isVerified;
  bool? get isOnline;
  String? get sortBy; // 'recent', 'name', 'age', 'compatibility'
  bool? get sortAscending;

  /// Create a copy of ShortlistFilters
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShortlistFiltersCopyWith<ShortlistFilters> get copyWith =>
      _$ShortlistFiltersCopyWithImpl<ShortlistFilters>(
          this as ShortlistFilters, _$identity);

  /// Serializes this ShortlistFilters to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShortlistFilters &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.interestSent, interestSent) ||
                other.interestSent == interestSent) &&
            (identical(other.interestStatus, interestStatus) ||
                other.interestStatus == interestStatus) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.minAge, minAge) || other.minAge == minAge) &&
            (identical(other.maxAge, maxAge) || other.maxAge == maxAge) &&
            (identical(other.minHeight, minHeight) ||
                other.minHeight == minHeight) &&
            (identical(other.maxHeight, maxHeight) ||
                other.maxHeight == maxHeight) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            (identical(other.caste, caste) || other.caste == caste) &&
            (identical(other.maritalStatus, maritalStatus) ||
                other.maritalStatus == maritalStatus) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            (identical(other.sortAscending, sortAscending) ||
                other.sortAscending == sortAscending));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      searchQuery,
      interestSent,
      interestStatus,
      location,
      education,
      occupation,
      minAge,
      maxAge,
      minHeight,
      maxHeight,
      religion,
      caste,
      maritalStatus,
      isVerified,
      isOnline,
      sortBy,
      sortAscending);

  @override
  String toString() {
    return 'ShortlistFilters(searchQuery: $searchQuery, interestSent: $interestSent, interestStatus: $interestStatus, location: $location, education: $education, occupation: $occupation, minAge: $minAge, maxAge: $maxAge, minHeight: $minHeight, maxHeight: $maxHeight, religion: $religion, caste: $caste, maritalStatus: $maritalStatus, isVerified: $isVerified, isOnline: $isOnline, sortBy: $sortBy, sortAscending: $sortAscending)';
  }
}

/// @nodoc
abstract mixin class $ShortlistFiltersCopyWith<$Res> {
  factory $ShortlistFiltersCopyWith(
          ShortlistFilters value, $Res Function(ShortlistFilters) _then) =
      _$ShortlistFiltersCopyWithImpl;
  @useResult
  $Res call(
      {String? searchQuery,
      bool? interestSent,
      String? interestStatus,
      String? location,
      String? education,
      String? occupation,
      int? minAge,
      int? maxAge,
      double? minHeight,
      double? maxHeight,
      String? religion,
      String? caste,
      String? maritalStatus,
      bool? isVerified,
      bool? isOnline,
      String? sortBy,
      bool? sortAscending});
}

/// @nodoc
class _$ShortlistFiltersCopyWithImpl<$Res>
    implements $ShortlistFiltersCopyWith<$Res> {
  _$ShortlistFiltersCopyWithImpl(this._self, this._then);

  final ShortlistFilters _self;
  final $Res Function(ShortlistFilters) _then;

  /// Create a copy of ShortlistFilters
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? searchQuery = freezed,
    Object? interestSent = freezed,
    Object? interestStatus = freezed,
    Object? location = freezed,
    Object? education = freezed,
    Object? occupation = freezed,
    Object? minAge = freezed,
    Object? maxAge = freezed,
    Object? minHeight = freezed,
    Object? maxHeight = freezed,
    Object? religion = freezed,
    Object? caste = freezed,
    Object? maritalStatus = freezed,
    Object? isVerified = freezed,
    Object? isOnline = freezed,
    Object? sortBy = freezed,
    Object? sortAscending = freezed,
  }) {
    return _then(_self.copyWith(
      searchQuery: freezed == searchQuery
          ? _self.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String?,
      interestSent: freezed == interestSent
          ? _self.interestSent
          : interestSent // ignore: cast_nullable_to_non_nullable
              as bool?,
      interestStatus: freezed == interestStatus
          ? _self.interestStatus
          : interestStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      minAge: freezed == minAge
          ? _self.minAge
          : minAge // ignore: cast_nullable_to_non_nullable
              as int?,
      maxAge: freezed == maxAge
          ? _self.maxAge
          : maxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      minHeight: freezed == minHeight
          ? _self.minHeight
          : minHeight // ignore: cast_nullable_to_non_nullable
              as double?,
      maxHeight: freezed == maxHeight
          ? _self.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as double?,
      religion: freezed == religion
          ? _self.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      caste: freezed == caste
          ? _self.caste
          : caste // ignore: cast_nullable_to_non_nullable
              as String?,
      maritalStatus: freezed == maritalStatus
          ? _self.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: freezed == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOnline: freezed == isOnline
          ? _self.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      sortBy: freezed == sortBy
          ? _self.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as String?,
      sortAscending: freezed == sortAscending
          ? _self.sortAscending
          : sortAscending // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// Adds pattern-matching-related methods to [ShortlistFilters].
extension ShortlistFiltersPatterns on ShortlistFilters {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShortlistFilters value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistFilters() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShortlistFilters value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistFilters():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShortlistFilters value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistFilters() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String? searchQuery,
            bool? interestSent,
            String? interestStatus,
            String? location,
            String? education,
            String? occupation,
            int? minAge,
            int? maxAge,
            double? minHeight,
            double? maxHeight,
            String? religion,
            String? caste,
            String? maritalStatus,
            bool? isVerified,
            bool? isOnline,
            String? sortBy,
            bool? sortAscending)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistFilters() when $default != null:
        return $default(
            _that.searchQuery,
            _that.interestSent,
            _that.interestStatus,
            _that.location,
            _that.education,
            _that.occupation,
            _that.minAge,
            _that.maxAge,
            _that.minHeight,
            _that.maxHeight,
            _that.religion,
            _that.caste,
            _that.maritalStatus,
            _that.isVerified,
            _that.isOnline,
            _that.sortBy,
            _that.sortAscending);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String? searchQuery,
            bool? interestSent,
            String? interestStatus,
            String? location,
            String? education,
            String? occupation,
            int? minAge,
            int? maxAge,
            double? minHeight,
            double? maxHeight,
            String? religion,
            String? caste,
            String? maritalStatus,
            bool? isVerified,
            bool? isOnline,
            String? sortBy,
            bool? sortAscending)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistFilters():
        return $default(
            _that.searchQuery,
            _that.interestSent,
            _that.interestStatus,
            _that.location,
            _that.education,
            _that.occupation,
            _that.minAge,
            _that.maxAge,
            _that.minHeight,
            _that.maxHeight,
            _that.religion,
            _that.caste,
            _that.maritalStatus,
            _that.isVerified,
            _that.isOnline,
            _that.sortBy,
            _that.sortAscending);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String? searchQuery,
            bool? interestSent,
            String? interestStatus,
            String? location,
            String? education,
            String? occupation,
            int? minAge,
            int? maxAge,
            double? minHeight,
            double? maxHeight,
            String? religion,
            String? caste,
            String? maritalStatus,
            bool? isVerified,
            bool? isOnline,
            String? sortBy,
            bool? sortAscending)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistFilters() when $default != null:
        return $default(
            _that.searchQuery,
            _that.interestSent,
            _that.interestStatus,
            _that.location,
            _that.education,
            _that.occupation,
            _that.minAge,
            _that.maxAge,
            _that.minHeight,
            _that.maxHeight,
            _that.religion,
            _that.caste,
            _that.maritalStatus,
            _that.isVerified,
            _that.isOnline,
            _that.sortBy,
            _that.sortAscending);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShortlistFilters implements ShortlistFilters {
  const _ShortlistFilters(
      {this.searchQuery,
      this.interestSent,
      this.interestStatus,
      this.location,
      this.education,
      this.occupation,
      this.minAge,
      this.maxAge,
      this.minHeight,
      this.maxHeight,
      this.religion,
      this.caste,
      this.maritalStatus,
      this.isVerified,
      this.isOnline,
      this.sortBy,
      this.sortAscending});
  factory _ShortlistFilters.fromJson(Map<String, dynamic> json) =>
      _$ShortlistFiltersFromJson(json);

  @override
  final String? searchQuery;
  @override
  final bool? interestSent;
  @override
  final String? interestStatus;
  @override
  final String? location;
  @override
  final String? education;
  @override
  final String? occupation;
  @override
  final int? minAge;
  @override
  final int? maxAge;
  @override
  final double? minHeight;
  @override
  final double? maxHeight;
  @override
  final String? religion;
  @override
  final String? caste;
  @override
  final String? maritalStatus;
  @override
  final bool? isVerified;
  @override
  final bool? isOnline;
  @override
  final String? sortBy;
// 'recent', 'name', 'age', 'compatibility'
  @override
  final bool? sortAscending;

  /// Create a copy of ShortlistFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShortlistFiltersCopyWith<_ShortlistFilters> get copyWith =>
      __$ShortlistFiltersCopyWithImpl<_ShortlistFilters>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShortlistFiltersToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShortlistFilters &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.interestSent, interestSent) ||
                other.interestSent == interestSent) &&
            (identical(other.interestStatus, interestStatus) ||
                other.interestStatus == interestStatus) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.minAge, minAge) || other.minAge == minAge) &&
            (identical(other.maxAge, maxAge) || other.maxAge == maxAge) &&
            (identical(other.minHeight, minHeight) ||
                other.minHeight == minHeight) &&
            (identical(other.maxHeight, maxHeight) ||
                other.maxHeight == maxHeight) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            (identical(other.caste, caste) || other.caste == caste) &&
            (identical(other.maritalStatus, maritalStatus) ||
                other.maritalStatus == maritalStatus) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            (identical(other.sortAscending, sortAscending) ||
                other.sortAscending == sortAscending));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      searchQuery,
      interestSent,
      interestStatus,
      location,
      education,
      occupation,
      minAge,
      maxAge,
      minHeight,
      maxHeight,
      religion,
      caste,
      maritalStatus,
      isVerified,
      isOnline,
      sortBy,
      sortAscending);

  @override
  String toString() {
    return 'ShortlistFilters(searchQuery: $searchQuery, interestSent: $interestSent, interestStatus: $interestStatus, location: $location, education: $education, occupation: $occupation, minAge: $minAge, maxAge: $maxAge, minHeight: $minHeight, maxHeight: $maxHeight, religion: $religion, caste: $caste, maritalStatus: $maritalStatus, isVerified: $isVerified, isOnline: $isOnline, sortBy: $sortBy, sortAscending: $sortAscending)';
  }
}

/// @nodoc
abstract mixin class _$ShortlistFiltersCopyWith<$Res>
    implements $ShortlistFiltersCopyWith<$Res> {
  factory _$ShortlistFiltersCopyWith(
          _ShortlistFilters value, $Res Function(_ShortlistFilters) _then) =
      __$ShortlistFiltersCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? searchQuery,
      bool? interestSent,
      String? interestStatus,
      String? location,
      String? education,
      String? occupation,
      int? minAge,
      int? maxAge,
      double? minHeight,
      double? maxHeight,
      String? religion,
      String? caste,
      String? maritalStatus,
      bool? isVerified,
      bool? isOnline,
      String? sortBy,
      bool? sortAscending});
}

/// @nodoc
class __$ShortlistFiltersCopyWithImpl<$Res>
    implements _$ShortlistFiltersCopyWith<$Res> {
  __$ShortlistFiltersCopyWithImpl(this._self, this._then);

  final _ShortlistFilters _self;
  final $Res Function(_ShortlistFilters) _then;

  /// Create a copy of ShortlistFilters
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? searchQuery = freezed,
    Object? interestSent = freezed,
    Object? interestStatus = freezed,
    Object? location = freezed,
    Object? education = freezed,
    Object? occupation = freezed,
    Object? minAge = freezed,
    Object? maxAge = freezed,
    Object? minHeight = freezed,
    Object? maxHeight = freezed,
    Object? religion = freezed,
    Object? caste = freezed,
    Object? maritalStatus = freezed,
    Object? isVerified = freezed,
    Object? isOnline = freezed,
    Object? sortBy = freezed,
    Object? sortAscending = freezed,
  }) {
    return _then(_ShortlistFilters(
      searchQuery: freezed == searchQuery
          ? _self.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String?,
      interestSent: freezed == interestSent
          ? _self.interestSent
          : interestSent // ignore: cast_nullable_to_non_nullable
              as bool?,
      interestStatus: freezed == interestStatus
          ? _self.interestStatus
          : interestStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      location: freezed == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      minAge: freezed == minAge
          ? _self.minAge
          : minAge // ignore: cast_nullable_to_non_nullable
              as int?,
      maxAge: freezed == maxAge
          ? _self.maxAge
          : maxAge // ignore: cast_nullable_to_non_nullable
              as int?,
      minHeight: freezed == minHeight
          ? _self.minHeight
          : minHeight // ignore: cast_nullable_to_non_nullable
              as double?,
      maxHeight: freezed == maxHeight
          ? _self.maxHeight
          : maxHeight // ignore: cast_nullable_to_non_nullable
              as double?,
      religion: freezed == religion
          ? _self.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      caste: freezed == caste
          ? _self.caste
          : caste // ignore: cast_nullable_to_non_nullable
              as String?,
      maritalStatus: freezed == maritalStatus
          ? _self.maritalStatus
          : maritalStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: freezed == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isOnline: freezed == isOnline
          ? _self.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
      sortBy: freezed == sortBy
          ? _self.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as String?,
      sortAscending: freezed == sortAscending
          ? _self.sortAscending
          : sortAscending // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
mixin _$AddToShortlistRequest {
  String get profileId;
  String? get note;

  /// Create a copy of AddToShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AddToShortlistRequestCopyWith<AddToShortlistRequest> get copyWith =>
      _$AddToShortlistRequestCopyWithImpl<AddToShortlistRequest>(
          this as AddToShortlistRequest, _$identity);

  /// Serializes this AddToShortlistRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AddToShortlistRequest &&
            (identical(other.profileId, profileId) ||
                other.profileId == profileId) &&
            (identical(other.note, note) || other.note == note));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, profileId, note);

  @override
  String toString() {
    return 'AddToShortlistRequest(profileId: $profileId, note: $note)';
  }
}

/// @nodoc
abstract mixin class $AddToShortlistRequestCopyWith<$Res> {
  factory $AddToShortlistRequestCopyWith(AddToShortlistRequest value,
          $Res Function(AddToShortlistRequest) _then) =
      _$AddToShortlistRequestCopyWithImpl;
  @useResult
  $Res call({String profileId, String? note});
}

/// @nodoc
class _$AddToShortlistRequestCopyWithImpl<$Res>
    implements $AddToShortlistRequestCopyWith<$Res> {
  _$AddToShortlistRequestCopyWithImpl(this._self, this._then);

  final AddToShortlistRequest _self;
  final $Res Function(AddToShortlistRequest) _then;

  /// Create a copy of AddToShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? profileId = null,
    Object? note = freezed,
  }) {
    return _then(_self.copyWith(
      profileId: null == profileId
          ? _self.profileId
          : profileId // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [AddToShortlistRequest].
extension AddToShortlistRequestPatterns on AddToShortlistRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AddToShortlistRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AddToShortlistRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AddToShortlistRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AddToShortlistRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AddToShortlistRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AddToShortlistRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String profileId, String? note)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AddToShortlistRequest() when $default != null:
        return $default(_that.profileId, _that.note);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String profileId, String? note) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AddToShortlistRequest():
        return $default(_that.profileId, _that.note);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String profileId, String? note)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AddToShortlistRequest() when $default != null:
        return $default(_that.profileId, _that.note);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AddToShortlistRequest implements AddToShortlistRequest {
  const _AddToShortlistRequest({required this.profileId, this.note});
  factory _AddToShortlistRequest.fromJson(Map<String, dynamic> json) =>
      _$AddToShortlistRequestFromJson(json);

  @override
  final String profileId;
  @override
  final String? note;

  /// Create a copy of AddToShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AddToShortlistRequestCopyWith<_AddToShortlistRequest> get copyWith =>
      __$AddToShortlistRequestCopyWithImpl<_AddToShortlistRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AddToShortlistRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AddToShortlistRequest &&
            (identical(other.profileId, profileId) ||
                other.profileId == profileId) &&
            (identical(other.note, note) || other.note == note));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, profileId, note);

  @override
  String toString() {
    return 'AddToShortlistRequest(profileId: $profileId, note: $note)';
  }
}

/// @nodoc
abstract mixin class _$AddToShortlistRequestCopyWith<$Res>
    implements $AddToShortlistRequestCopyWith<$Res> {
  factory _$AddToShortlistRequestCopyWith(_AddToShortlistRequest value,
          $Res Function(_AddToShortlistRequest) _then) =
      __$AddToShortlistRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String profileId, String? note});
}

/// @nodoc
class __$AddToShortlistRequestCopyWithImpl<$Res>
    implements _$AddToShortlistRequestCopyWith<$Res> {
  __$AddToShortlistRequestCopyWithImpl(this._self, this._then);

  final _AddToShortlistRequest _self;
  final $Res Function(_AddToShortlistRequest) _then;

  /// Create a copy of AddToShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? profileId = null,
    Object? note = freezed,
  }) {
    return _then(_AddToShortlistRequest(
      profileId: null == profileId
          ? _self.profileId
          : profileId // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$UpdateShortlistNoteRequest {
  String get shortlistId;
  String? get note;

  /// Create a copy of UpdateShortlistNoteRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdateShortlistNoteRequestCopyWith<UpdateShortlistNoteRequest>
      get copyWith =>
          _$UpdateShortlistNoteRequestCopyWithImpl<UpdateShortlistNoteRequest>(
              this as UpdateShortlistNoteRequest, _$identity);

  /// Serializes this UpdateShortlistNoteRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdateShortlistNoteRequest &&
            (identical(other.shortlistId, shortlistId) ||
                other.shortlistId == shortlistId) &&
            (identical(other.note, note) || other.note == note));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, shortlistId, note);

  @override
  String toString() {
    return 'UpdateShortlistNoteRequest(shortlistId: $shortlistId, note: $note)';
  }
}

/// @nodoc
abstract mixin class $UpdateShortlistNoteRequestCopyWith<$Res> {
  factory $UpdateShortlistNoteRequestCopyWith(UpdateShortlistNoteRequest value,
          $Res Function(UpdateShortlistNoteRequest) _then) =
      _$UpdateShortlistNoteRequestCopyWithImpl;
  @useResult
  $Res call({String shortlistId, String? note});
}

/// @nodoc
class _$UpdateShortlistNoteRequestCopyWithImpl<$Res>
    implements $UpdateShortlistNoteRequestCopyWith<$Res> {
  _$UpdateShortlistNoteRequestCopyWithImpl(this._self, this._then);

  final UpdateShortlistNoteRequest _self;
  final $Res Function(UpdateShortlistNoteRequest) _then;

  /// Create a copy of UpdateShortlistNoteRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shortlistId = null,
    Object? note = freezed,
  }) {
    return _then(_self.copyWith(
      shortlistId: null == shortlistId
          ? _self.shortlistId
          : shortlistId // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [UpdateShortlistNoteRequest].
extension UpdateShortlistNoteRequestPatterns on UpdateShortlistNoteRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UpdateShortlistNoteRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UpdateShortlistNoteRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UpdateShortlistNoteRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateShortlistNoteRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UpdateShortlistNoteRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateShortlistNoteRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String shortlistId, String? note)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UpdateShortlistNoteRequest() when $default != null:
        return $default(_that.shortlistId, _that.note);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String shortlistId, String? note) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateShortlistNoteRequest():
        return $default(_that.shortlistId, _that.note);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String shortlistId, String? note)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UpdateShortlistNoteRequest() when $default != null:
        return $default(_that.shortlistId, _that.note);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UpdateShortlistNoteRequest implements UpdateShortlistNoteRequest {
  const _UpdateShortlistNoteRequest({required this.shortlistId, this.note});
  factory _UpdateShortlistNoteRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateShortlistNoteRequestFromJson(json);

  @override
  final String shortlistId;
  @override
  final String? note;

  /// Create a copy of UpdateShortlistNoteRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdateShortlistNoteRequestCopyWith<_UpdateShortlistNoteRequest>
      get copyWith => __$UpdateShortlistNoteRequestCopyWithImpl<
          _UpdateShortlistNoteRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UpdateShortlistNoteRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdateShortlistNoteRequest &&
            (identical(other.shortlistId, shortlistId) ||
                other.shortlistId == shortlistId) &&
            (identical(other.note, note) || other.note == note));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, shortlistId, note);

  @override
  String toString() {
    return 'UpdateShortlistNoteRequest(shortlistId: $shortlistId, note: $note)';
  }
}

/// @nodoc
abstract mixin class _$UpdateShortlistNoteRequestCopyWith<$Res>
    implements $UpdateShortlistNoteRequestCopyWith<$Res> {
  factory _$UpdateShortlistNoteRequestCopyWith(
          _UpdateShortlistNoteRequest value,
          $Res Function(_UpdateShortlistNoteRequest) _then) =
      __$UpdateShortlistNoteRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String shortlistId, String? note});
}

/// @nodoc
class __$UpdateShortlistNoteRequestCopyWithImpl<$Res>
    implements _$UpdateShortlistNoteRequestCopyWith<$Res> {
  __$UpdateShortlistNoteRequestCopyWithImpl(this._self, this._then);

  final _UpdateShortlistNoteRequest _self;
  final $Res Function(_UpdateShortlistNoteRequest) _then;

  /// Create a copy of UpdateShortlistNoteRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? shortlistId = null,
    Object? note = freezed,
  }) {
    return _then(_UpdateShortlistNoteRequest(
      shortlistId: null == shortlistId
          ? _self.shortlistId
          : shortlistId // ignore: cast_nullable_to_non_nullable
              as String,
      note: freezed == note
          ? _self.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$RemoveFromShortlistRequest {
  String get shortlistId;

  /// Create a copy of RemoveFromShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RemoveFromShortlistRequestCopyWith<RemoveFromShortlistRequest>
      get copyWith =>
          _$RemoveFromShortlistRequestCopyWithImpl<RemoveFromShortlistRequest>(
              this as RemoveFromShortlistRequest, _$identity);

  /// Serializes this RemoveFromShortlistRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RemoveFromShortlistRequest &&
            (identical(other.shortlistId, shortlistId) ||
                other.shortlistId == shortlistId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, shortlistId);

  @override
  String toString() {
    return 'RemoveFromShortlistRequest(shortlistId: $shortlistId)';
  }
}

/// @nodoc
abstract mixin class $RemoveFromShortlistRequestCopyWith<$Res> {
  factory $RemoveFromShortlistRequestCopyWith(RemoveFromShortlistRequest value,
          $Res Function(RemoveFromShortlistRequest) _then) =
      _$RemoveFromShortlistRequestCopyWithImpl;
  @useResult
  $Res call({String shortlistId});
}

/// @nodoc
class _$RemoveFromShortlistRequestCopyWithImpl<$Res>
    implements $RemoveFromShortlistRequestCopyWith<$Res> {
  _$RemoveFromShortlistRequestCopyWithImpl(this._self, this._then);

  final RemoveFromShortlistRequest _self;
  final $Res Function(RemoveFromShortlistRequest) _then;

  /// Create a copy of RemoveFromShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? shortlistId = null,
  }) {
    return _then(_self.copyWith(
      shortlistId: null == shortlistId
          ? _self.shortlistId
          : shortlistId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// Adds pattern-matching-related methods to [RemoveFromShortlistRequest].
extension RemoveFromShortlistRequestPatterns on RemoveFromShortlistRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RemoveFromShortlistRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RemoveFromShortlistRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RemoveFromShortlistRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RemoveFromShortlistRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RemoveFromShortlistRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RemoveFromShortlistRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String shortlistId)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RemoveFromShortlistRequest() when $default != null:
        return $default(_that.shortlistId);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String shortlistId) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RemoveFromShortlistRequest():
        return $default(_that.shortlistId);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String shortlistId)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RemoveFromShortlistRequest() when $default != null:
        return $default(_that.shortlistId);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RemoveFromShortlistRequest implements RemoveFromShortlistRequest {
  const _RemoveFromShortlistRequest({required this.shortlistId});
  factory _RemoveFromShortlistRequest.fromJson(Map<String, dynamic> json) =>
      _$RemoveFromShortlistRequestFromJson(json);

  @override
  final String shortlistId;

  /// Create a copy of RemoveFromShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RemoveFromShortlistRequestCopyWith<_RemoveFromShortlistRequest>
      get copyWith => __$RemoveFromShortlistRequestCopyWithImpl<
          _RemoveFromShortlistRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RemoveFromShortlistRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RemoveFromShortlistRequest &&
            (identical(other.shortlistId, shortlistId) ||
                other.shortlistId == shortlistId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, shortlistId);

  @override
  String toString() {
    return 'RemoveFromShortlistRequest(shortlistId: $shortlistId)';
  }
}

/// @nodoc
abstract mixin class _$RemoveFromShortlistRequestCopyWith<$Res>
    implements $RemoveFromShortlistRequestCopyWith<$Res> {
  factory _$RemoveFromShortlistRequestCopyWith(
          _RemoveFromShortlistRequest value,
          $Res Function(_RemoveFromShortlistRequest) _then) =
      __$RemoveFromShortlistRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String shortlistId});
}

/// @nodoc
class __$RemoveFromShortlistRequestCopyWithImpl<$Res>
    implements _$RemoveFromShortlistRequestCopyWith<$Res> {
  __$RemoveFromShortlistRequestCopyWithImpl(this._self, this._then);

  final _RemoveFromShortlistRequest _self;
  final $Res Function(_RemoveFromShortlistRequest) _then;

  /// Create a copy of RemoveFromShortlistRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? shortlistId = null,
  }) {
    return _then(_RemoveFromShortlistRequest(
      shortlistId: null == shortlistId
          ? _self.shortlistId
          : shortlistId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$ShortlistResponse {
  bool get success;
  String? get message;
  List<ShortlistItem>? get data;
  ShortlistStats? get stats;
  int? get total;
  int? get page;
  int? get limit;
  bool? get hasMore;

  /// Create a copy of ShortlistResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShortlistResponseCopyWith<ShortlistResponse> get copyWith =>
      _$ShortlistResponseCopyWithImpl<ShortlistResponse>(
          this as ShortlistResponse, _$identity);

  /// Serializes this ShortlistResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShortlistResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      success,
      message,
      const DeepCollectionEquality().hash(data),
      stats,
      total,
      page,
      limit,
      hasMore);

  @override
  String toString() {
    return 'ShortlistResponse(success: $success, message: $message, data: $data, stats: $stats, total: $total, page: $page, limit: $limit, hasMore: $hasMore)';
  }
}

/// @nodoc
abstract mixin class $ShortlistResponseCopyWith<$Res> {
  factory $ShortlistResponseCopyWith(
          ShortlistResponse value, $Res Function(ShortlistResponse) _then) =
      _$ShortlistResponseCopyWithImpl;
  @useResult
  $Res call(
      {bool success,
      String? message,
      List<ShortlistItem>? data,
      ShortlistStats? stats,
      int? total,
      int? page,
      int? limit,
      bool? hasMore});

  $ShortlistStatsCopyWith<$Res>? get stats;
}

/// @nodoc
class _$ShortlistResponseCopyWithImpl<$Res>
    implements $ShortlistResponseCopyWith<$Res> {
  _$ShortlistResponseCopyWithImpl(this._self, this._then);

  final ShortlistResponse _self;
  final $Res Function(ShortlistResponse) _then;

  /// Create a copy of ShortlistResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? data = freezed,
    Object? stats = freezed,
    Object? total = freezed,
    Object? page = freezed,
    Object? limit = freezed,
    Object? hasMore = freezed,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ShortlistItem>?,
      stats: freezed == stats
          ? _self.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as ShortlistStats?,
      total: freezed == total
          ? _self.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMore: freezed == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of ShortlistResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistStatsCopyWith<$Res>? get stats {
    if (_self.stats == null) {
      return null;
    }

    return $ShortlistStatsCopyWith<$Res>(_self.stats!, (value) {
      return _then(_self.copyWith(stats: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ShortlistResponse].
extension ShortlistResponsePatterns on ShortlistResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShortlistResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShortlistResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShortlistResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool success,
            String? message,
            List<ShortlistItem>? data,
            ShortlistStats? stats,
            int? total,
            int? page,
            int? limit,
            bool? hasMore)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistResponse() when $default != null:
        return $default(_that.success, _that.message, _that.data, _that.stats,
            _that.total, _that.page, _that.limit, _that.hasMore);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool success,
            String? message,
            List<ShortlistItem>? data,
            ShortlistStats? stats,
            int? total,
            int? page,
            int? limit,
            bool? hasMore)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistResponse():
        return $default(_that.success, _that.message, _that.data, _that.stats,
            _that.total, _that.page, _that.limit, _that.hasMore);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool success,
            String? message,
            List<ShortlistItem>? data,
            ShortlistStats? stats,
            int? total,
            int? page,
            int? limit,
            bool? hasMore)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistResponse() when $default != null:
        return $default(_that.success, _that.message, _that.data, _that.stats,
            _that.total, _that.page, _that.limit, _that.hasMore);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShortlistResponse implements ShortlistResponse {
  const _ShortlistResponse(
      {required this.success,
      this.message,
      final List<ShortlistItem>? data,
      this.stats,
      this.total,
      this.page,
      this.limit,
      this.hasMore})
      : _data = data;
  factory _ShortlistResponse.fromJson(Map<String, dynamic> json) =>
      _$ShortlistResponseFromJson(json);

  @override
  final bool success;
  @override
  final String? message;
  final List<ShortlistItem>? _data;
  @override
  List<ShortlistItem>? get data {
    final value = _data;
    if (value == null) return null;
    if (_data is EqualUnmodifiableListView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final ShortlistStats? stats;
  @override
  final int? total;
  @override
  final int? page;
  @override
  final int? limit;
  @override
  final bool? hasMore;

  /// Create a copy of ShortlistResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShortlistResponseCopyWith<_ShortlistResponse> get copyWith =>
      __$ShortlistResponseCopyWithImpl<_ShortlistResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShortlistResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShortlistResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.limit, limit) || other.limit == limit) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      success,
      message,
      const DeepCollectionEquality().hash(_data),
      stats,
      total,
      page,
      limit,
      hasMore);

  @override
  String toString() {
    return 'ShortlistResponse(success: $success, message: $message, data: $data, stats: $stats, total: $total, page: $page, limit: $limit, hasMore: $hasMore)';
  }
}

/// @nodoc
abstract mixin class _$ShortlistResponseCopyWith<$Res>
    implements $ShortlistResponseCopyWith<$Res> {
  factory _$ShortlistResponseCopyWith(
          _ShortlistResponse value, $Res Function(_ShortlistResponse) _then) =
      __$ShortlistResponseCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool success,
      String? message,
      List<ShortlistItem>? data,
      ShortlistStats? stats,
      int? total,
      int? page,
      int? limit,
      bool? hasMore});

  @override
  $ShortlistStatsCopyWith<$Res>? get stats;
}

/// @nodoc
class __$ShortlistResponseCopyWithImpl<$Res>
    implements _$ShortlistResponseCopyWith<$Res> {
  __$ShortlistResponseCopyWithImpl(this._self, this._then);

  final _ShortlistResponse _self;
  final $Res Function(_ShortlistResponse) _then;

  /// Create a copy of ShortlistResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? data = freezed,
    Object? stats = freezed,
    Object? total = freezed,
    Object? page = freezed,
    Object? limit = freezed,
    Object? hasMore = freezed,
  }) {
    return _then(_ShortlistResponse(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      data: freezed == data
          ? _self._data
          : data // ignore: cast_nullable_to_non_nullable
              as List<ShortlistItem>?,
      stats: freezed == stats
          ? _self.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as ShortlistStats?,
      total: freezed == total
          ? _self.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _self.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      limit: freezed == limit
          ? _self.limit
          : limit // ignore: cast_nullable_to_non_nullable
              as int?,
      hasMore: freezed == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }

  /// Create a copy of ShortlistResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistStatsCopyWith<$Res>? get stats {
    if (_self.stats == null) {
      return null;
    }

    return $ShortlistStatsCopyWith<$Res>(_self.stats!, (value) {
      return _then(_self.copyWith(stats: value));
    });
  }
}

/// @nodoc
mixin _$ShortlistActionResult {
  bool get success;
  String? get message;
  String? get shortlistId;
  ShortlistItem? get item;

  /// Create a copy of ShortlistActionResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShortlistActionResultCopyWith<ShortlistActionResult> get copyWith =>
      _$ShortlistActionResultCopyWithImpl<ShortlistActionResult>(
          this as ShortlistActionResult, _$identity);

  /// Serializes this ShortlistActionResult to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShortlistActionResult &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.shortlistId, shortlistId) ||
                other.shortlistId == shortlistId) &&
            (identical(other.item, item) || other.item == item));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, message, shortlistId, item);

  @override
  String toString() {
    return 'ShortlistActionResult(success: $success, message: $message, shortlistId: $shortlistId, item: $item)';
  }
}

/// @nodoc
abstract mixin class $ShortlistActionResultCopyWith<$Res> {
  factory $ShortlistActionResultCopyWith(ShortlistActionResult value,
          $Res Function(ShortlistActionResult) _then) =
      _$ShortlistActionResultCopyWithImpl;
  @useResult
  $Res call(
      {bool success,
      String? message,
      String? shortlistId,
      ShortlistItem? item});

  $ShortlistItemCopyWith<$Res>? get item;
}

/// @nodoc
class _$ShortlistActionResultCopyWithImpl<$Res>
    implements $ShortlistActionResultCopyWith<$Res> {
  _$ShortlistActionResultCopyWithImpl(this._self, this._then);

  final ShortlistActionResult _self;
  final $Res Function(ShortlistActionResult) _then;

  /// Create a copy of ShortlistActionResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? shortlistId = freezed,
    Object? item = freezed,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      shortlistId: freezed == shortlistId
          ? _self.shortlistId
          : shortlistId // ignore: cast_nullable_to_non_nullable
              as String?,
      item: freezed == item
          ? _self.item
          : item // ignore: cast_nullable_to_non_nullable
              as ShortlistItem?,
    ));
  }

  /// Create a copy of ShortlistActionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistItemCopyWith<$Res>? get item {
    if (_self.item == null) {
      return null;
    }

    return $ShortlistItemCopyWith<$Res>(_self.item!, (value) {
      return _then(_self.copyWith(item: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ShortlistActionResult].
extension ShortlistActionResultPatterns on ShortlistActionResult {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShortlistActionResult value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistActionResult() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShortlistActionResult value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistActionResult():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShortlistActionResult value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistActionResult() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool success, String? message, String? shortlistId,
            ShortlistItem? item)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistActionResult() when $default != null:
        return $default(
            _that.success, _that.message, _that.shortlistId, _that.item);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool success, String? message, String? shortlistId,
            ShortlistItem? item)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistActionResult():
        return $default(
            _that.success, _that.message, _that.shortlistId, _that.item);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool success, String? message, String? shortlistId,
            ShortlistItem? item)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistActionResult() when $default != null:
        return $default(
            _that.success, _that.message, _that.shortlistId, _that.item);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShortlistActionResult implements ShortlistActionResult {
  const _ShortlistActionResult(
      {required this.success, this.message, this.shortlistId, this.item});
  factory _ShortlistActionResult.fromJson(Map<String, dynamic> json) =>
      _$ShortlistActionResultFromJson(json);

  @override
  final bool success;
  @override
  final String? message;
  @override
  final String? shortlistId;
  @override
  final ShortlistItem? item;

  /// Create a copy of ShortlistActionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShortlistActionResultCopyWith<_ShortlistActionResult> get copyWith =>
      __$ShortlistActionResultCopyWithImpl<_ShortlistActionResult>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShortlistActionResultToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShortlistActionResult &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.shortlistId, shortlistId) ||
                other.shortlistId == shortlistId) &&
            (identical(other.item, item) || other.item == item));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, success, message, shortlistId, item);

  @override
  String toString() {
    return 'ShortlistActionResult(success: $success, message: $message, shortlistId: $shortlistId, item: $item)';
  }
}

/// @nodoc
abstract mixin class _$ShortlistActionResultCopyWith<$Res>
    implements $ShortlistActionResultCopyWith<$Res> {
  factory _$ShortlistActionResultCopyWith(_ShortlistActionResult value,
          $Res Function(_ShortlistActionResult) _then) =
      __$ShortlistActionResultCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool success,
      String? message,
      String? shortlistId,
      ShortlistItem? item});

  @override
  $ShortlistItemCopyWith<$Res>? get item;
}

/// @nodoc
class __$ShortlistActionResultCopyWithImpl<$Res>
    implements _$ShortlistActionResultCopyWith<$Res> {
  __$ShortlistActionResultCopyWithImpl(this._self, this._then);

  final _ShortlistActionResult _self;
  final $Res Function(_ShortlistActionResult) _then;

  /// Create a copy of ShortlistActionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? message = freezed,
    Object? shortlistId = freezed,
    Object? item = freezed,
  }) {
    return _then(_ShortlistActionResult(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      shortlistId: freezed == shortlistId
          ? _self.shortlistId
          : shortlistId // ignore: cast_nullable_to_non_nullable
              as String?,
      item: freezed == item
          ? _self.item
          : item // ignore: cast_nullable_to_non_nullable
              as ShortlistItem?,
    ));
  }

  /// Create a copy of ShortlistActionResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistItemCopyWith<$Res>? get item {
    if (_self.item == null) {
      return null;
    }

    return $ShortlistItemCopyWith<$Res>(_self.item!, (value) {
      return _then(_self.copyWith(item: value));
    });
  }
}

/// @nodoc
mixin _$ShortlistUIState {
  List<ShortlistItem> get items;
  bool get isLoading;
  bool get isRefreshing;
  bool get hasMore;
  int get currentPage;
  String? get error;
  ShortlistFilters? get filters;
  ShortlistStats? get stats;
  String? get selectedItemId;
  bool get showFilters;
  String get sortBy;
  bool get sortAscending;

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ShortlistUIStateCopyWith<ShortlistUIState> get copyWith =>
      _$ShortlistUIStateCopyWithImpl<ShortlistUIState>(
          this as ShortlistUIState, _$identity);

  /// Serializes this ShortlistUIState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ShortlistUIState &&
            const DeepCollectionEquality().equals(other.items, items) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isRefreshing, isRefreshing) ||
                other.isRefreshing == isRefreshing) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.filters, filters) || other.filters == filters) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.selectedItemId, selectedItemId) ||
                other.selectedItemId == selectedItemId) &&
            (identical(other.showFilters, showFilters) ||
                other.showFilters == showFilters) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            (identical(other.sortAscending, sortAscending) ||
                other.sortAscending == sortAscending));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(items),
      isLoading,
      isRefreshing,
      hasMore,
      currentPage,
      error,
      filters,
      stats,
      selectedItemId,
      showFilters,
      sortBy,
      sortAscending);

  @override
  String toString() {
    return 'ShortlistUIState(items: $items, isLoading: $isLoading, isRefreshing: $isRefreshing, hasMore: $hasMore, currentPage: $currentPage, error: $error, filters: $filters, stats: $stats, selectedItemId: $selectedItemId, showFilters: $showFilters, sortBy: $sortBy, sortAscending: $sortAscending)';
  }
}

/// @nodoc
abstract mixin class $ShortlistUIStateCopyWith<$Res> {
  factory $ShortlistUIStateCopyWith(
          ShortlistUIState value, $Res Function(ShortlistUIState) _then) =
      _$ShortlistUIStateCopyWithImpl;
  @useResult
  $Res call(
      {List<ShortlistItem> items,
      bool isLoading,
      bool isRefreshing,
      bool hasMore,
      int currentPage,
      String? error,
      ShortlistFilters? filters,
      ShortlistStats? stats,
      String? selectedItemId,
      bool showFilters,
      String sortBy,
      bool sortAscending});

  $ShortlistFiltersCopyWith<$Res>? get filters;
  $ShortlistStatsCopyWith<$Res>? get stats;
}

/// @nodoc
class _$ShortlistUIStateCopyWithImpl<$Res>
    implements $ShortlistUIStateCopyWith<$Res> {
  _$ShortlistUIStateCopyWithImpl(this._self, this._then);

  final ShortlistUIState _self;
  final $Res Function(ShortlistUIState) _then;

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? items = null,
    Object? isLoading = null,
    Object? isRefreshing = null,
    Object? hasMore = null,
    Object? currentPage = null,
    Object? error = freezed,
    Object? filters = freezed,
    Object? stats = freezed,
    Object? selectedItemId = freezed,
    Object? showFilters = null,
    Object? sortBy = null,
    Object? sortAscending = null,
  }) {
    return _then(_self.copyWith(
      items: null == items
          ? _self.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ShortlistItem>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshing: null == isRefreshing
          ? _self.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      filters: freezed == filters
          ? _self.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as ShortlistFilters?,
      stats: freezed == stats
          ? _self.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as ShortlistStats?,
      selectedItemId: freezed == selectedItemId
          ? _self.selectedItemId
          : selectedItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      showFilters: null == showFilters
          ? _self.showFilters
          : showFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      sortBy: null == sortBy
          ? _self.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as String,
      sortAscending: null == sortAscending
          ? _self.sortAscending
          : sortAscending // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistFiltersCopyWith<$Res>? get filters {
    if (_self.filters == null) {
      return null;
    }

    return $ShortlistFiltersCopyWith<$Res>(_self.filters!, (value) {
      return _then(_self.copyWith(filters: value));
    });
  }

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistStatsCopyWith<$Res>? get stats {
    if (_self.stats == null) {
      return null;
    }

    return $ShortlistStatsCopyWith<$Res>(_self.stats!, (value) {
      return _then(_self.copyWith(stats: value));
    });
  }
}

/// Adds pattern-matching-related methods to [ShortlistUIState].
extension ShortlistUIStatePatterns on ShortlistUIState {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ShortlistUIState value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistUIState() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ShortlistUIState value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistUIState():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ShortlistUIState value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistUIState() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            List<ShortlistItem> items,
            bool isLoading,
            bool isRefreshing,
            bool hasMore,
            int currentPage,
            String? error,
            ShortlistFilters? filters,
            ShortlistStats? stats,
            String? selectedItemId,
            bool showFilters,
            String sortBy,
            bool sortAscending)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ShortlistUIState() when $default != null:
        return $default(
            _that.items,
            _that.isLoading,
            _that.isRefreshing,
            _that.hasMore,
            _that.currentPage,
            _that.error,
            _that.filters,
            _that.stats,
            _that.selectedItemId,
            _that.showFilters,
            _that.sortBy,
            _that.sortAscending);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            List<ShortlistItem> items,
            bool isLoading,
            bool isRefreshing,
            bool hasMore,
            int currentPage,
            String? error,
            ShortlistFilters? filters,
            ShortlistStats? stats,
            String? selectedItemId,
            bool showFilters,
            String sortBy,
            bool sortAscending)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistUIState():
        return $default(
            _that.items,
            _that.isLoading,
            _that.isRefreshing,
            _that.hasMore,
            _that.currentPage,
            _that.error,
            _that.filters,
            _that.stats,
            _that.selectedItemId,
            _that.showFilters,
            _that.sortBy,
            _that.sortAscending);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            List<ShortlistItem> items,
            bool isLoading,
            bool isRefreshing,
            bool hasMore,
            int currentPage,
            String? error,
            ShortlistFilters? filters,
            ShortlistStats? stats,
            String? selectedItemId,
            bool showFilters,
            String sortBy,
            bool sortAscending)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ShortlistUIState() when $default != null:
        return $default(
            _that.items,
            _that.isLoading,
            _that.isRefreshing,
            _that.hasMore,
            _that.currentPage,
            _that.error,
            _that.filters,
            _that.stats,
            _that.selectedItemId,
            _that.showFilters,
            _that.sortBy,
            _that.sortAscending);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ShortlistUIState implements ShortlistUIState {
  const _ShortlistUIState(
      {final List<ShortlistItem> items = const [],
      this.isLoading = false,
      this.isRefreshing = false,
      this.hasMore = false,
      this.currentPage = 1,
      this.error,
      this.filters,
      this.stats,
      this.selectedItemId,
      this.showFilters = false,
      this.sortBy = 'recent',
      this.sortAscending = false})
      : _items = items;
  factory _ShortlistUIState.fromJson(Map<String, dynamic> json) =>
      _$ShortlistUIStateFromJson(json);

  final List<ShortlistItem> _items;
  @override
  @JsonKey()
  List<ShortlistItem> get items {
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_items);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  @JsonKey()
  final bool isRefreshing;
  @override
  @JsonKey()
  final bool hasMore;
  @override
  @JsonKey()
  final int currentPage;
  @override
  final String? error;
  @override
  final ShortlistFilters? filters;
  @override
  final ShortlistStats? stats;
  @override
  final String? selectedItemId;
  @override
  @JsonKey()
  final bool showFilters;
  @override
  @JsonKey()
  final String sortBy;
  @override
  @JsonKey()
  final bool sortAscending;

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ShortlistUIStateCopyWith<_ShortlistUIState> get copyWith =>
      __$ShortlistUIStateCopyWithImpl<_ShortlistUIState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ShortlistUIStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ShortlistUIState &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.isRefreshing, isRefreshing) ||
                other.isRefreshing == isRefreshing) &&
            (identical(other.hasMore, hasMore) || other.hasMore == hasMore) &&
            (identical(other.currentPage, currentPage) ||
                other.currentPage == currentPage) &&
            (identical(other.error, error) || other.error == error) &&
            (identical(other.filters, filters) || other.filters == filters) &&
            (identical(other.stats, stats) || other.stats == stats) &&
            (identical(other.selectedItemId, selectedItemId) ||
                other.selectedItemId == selectedItemId) &&
            (identical(other.showFilters, showFilters) ||
                other.showFilters == showFilters) &&
            (identical(other.sortBy, sortBy) || other.sortBy == sortBy) &&
            (identical(other.sortAscending, sortAscending) ||
                other.sortAscending == sortAscending));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_items),
      isLoading,
      isRefreshing,
      hasMore,
      currentPage,
      error,
      filters,
      stats,
      selectedItemId,
      showFilters,
      sortBy,
      sortAscending);

  @override
  String toString() {
    return 'ShortlistUIState(items: $items, isLoading: $isLoading, isRefreshing: $isRefreshing, hasMore: $hasMore, currentPage: $currentPage, error: $error, filters: $filters, stats: $stats, selectedItemId: $selectedItemId, showFilters: $showFilters, sortBy: $sortBy, sortAscending: $sortAscending)';
  }
}

/// @nodoc
abstract mixin class _$ShortlistUIStateCopyWith<$Res>
    implements $ShortlistUIStateCopyWith<$Res> {
  factory _$ShortlistUIStateCopyWith(
          _ShortlistUIState value, $Res Function(_ShortlistUIState) _then) =
      __$ShortlistUIStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<ShortlistItem> items,
      bool isLoading,
      bool isRefreshing,
      bool hasMore,
      int currentPage,
      String? error,
      ShortlistFilters? filters,
      ShortlistStats? stats,
      String? selectedItemId,
      bool showFilters,
      String sortBy,
      bool sortAscending});

  @override
  $ShortlistFiltersCopyWith<$Res>? get filters;
  @override
  $ShortlistStatsCopyWith<$Res>? get stats;
}

/// @nodoc
class __$ShortlistUIStateCopyWithImpl<$Res>
    implements _$ShortlistUIStateCopyWith<$Res> {
  __$ShortlistUIStateCopyWithImpl(this._self, this._then);

  final _ShortlistUIState _self;
  final $Res Function(_ShortlistUIState) _then;

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? items = null,
    Object? isLoading = null,
    Object? isRefreshing = null,
    Object? hasMore = null,
    Object? currentPage = null,
    Object? error = freezed,
    Object? filters = freezed,
    Object? stats = freezed,
    Object? selectedItemId = freezed,
    Object? showFilters = null,
    Object? sortBy = null,
    Object? sortAscending = null,
  }) {
    return _then(_ShortlistUIState(
      items: null == items
          ? _self._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<ShortlistItem>,
      isLoading: null == isLoading
          ? _self.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      isRefreshing: null == isRefreshing
          ? _self.isRefreshing
          : isRefreshing // ignore: cast_nullable_to_non_nullable
              as bool,
      hasMore: null == hasMore
          ? _self.hasMore
          : hasMore // ignore: cast_nullable_to_non_nullable
              as bool,
      currentPage: null == currentPage
          ? _self.currentPage
          : currentPage // ignore: cast_nullable_to_non_nullable
              as int,
      error: freezed == error
          ? _self.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
      filters: freezed == filters
          ? _self.filters
          : filters // ignore: cast_nullable_to_non_nullable
              as ShortlistFilters?,
      stats: freezed == stats
          ? _self.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as ShortlistStats?,
      selectedItemId: freezed == selectedItemId
          ? _self.selectedItemId
          : selectedItemId // ignore: cast_nullable_to_non_nullable
              as String?,
      showFilters: null == showFilters
          ? _self.showFilters
          : showFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      sortBy: null == sortBy
          ? _self.sortBy
          : sortBy // ignore: cast_nullable_to_non_nullable
              as String,
      sortAscending: null == sortAscending
          ? _self.sortAscending
          : sortAscending // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistFiltersCopyWith<$Res>? get filters {
    if (_self.filters == null) {
      return null;
    }

    return $ShortlistFiltersCopyWith<$Res>(_self.filters!, (value) {
      return _then(_self.copyWith(filters: value));
    });
  }

  /// Create a copy of ShortlistUIState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ShortlistStatsCopyWith<$Res>? get stats {
    if (_self.stats == null) {
      return null;
    }

    return $ShortlistStatsCopyWith<$Res>(_self.stats!, (value) {
      return _then(_self.copyWith(stats: value));
    });
  }
}

// dart format on
