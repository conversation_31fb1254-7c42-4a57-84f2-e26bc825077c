import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../core/services/api_service.dart';
import '../../app/theme.dart';

/// 🤖 ABOUT ME AI SERVICE - Reusing Website Implementation
/// Features: AI-generated suggestions, MCP integration, fallback suggestions
/// Uses same backend APIs and logic as website for consistency

class AboutMeAIService {
  static const String baseUrl = 'http://localhost:8000/api';

  /// Generate About Me suggestions for registration stage
  static Future<List<Map<String, dynamic>>> generateRegistrationAboutMe(Map<String, dynamic> userData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/ai/about-me/registration'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await ApiService.getToken()}',
        },
        body: json.encode({'userData': userData}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['suggestions'] ?? []);
      }
      return _getFallbackRegistrationSuggestions(userData);
    } catch (e) {
      print('AI About Me service error for registration: $e');
      return _getFallbackRegistrationSuggestions(userData);
    }
  }

  /// Generate About Me suggestions for profile completion stage
  static Future<List<Map<String, dynamic>>> generateProfileAboutMe(Map<String, dynamic> userData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/ai/about-me/profile'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await ApiService.getToken()}',
        },
        body: json.encode({'userData': userData}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['suggestions'] ?? []);
      }
      return _getFallbackProfileSuggestions(userData);
    } catch (e) {
      print('AI About Me service error for profile: $e');
      return _getFallbackProfileSuggestions(userData);
    }
  }

  /// Enhance existing About Me text
  static Future<List<Map<String, dynamic>>> enhanceAboutMe(String existingText, Map<String, dynamic> userData) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/ai/about-me/enhance'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await ApiService.getToken()}',
        },
        body: json.encode({
          'existingText': existingText,
          'userData': userData,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return List<Map<String, dynamic>>.from(data['enhancements'] ?? []);
      }
      return _getFallbackEnhancement(existingText);
    } catch (e) {
      print('AI About Me enhancement error: $e');
      return _getFallbackEnhancement(existingText);
    }
  }

  /// Fallback suggestions for registration stage
  static List<Map<String, dynamic>> _getFallbackRegistrationSuggestions(Map<String, dynamic> userData) {
    final name = userData['firstName'] ?? 'User';
    final profession = userData['profession'] ?? 'Professional';
    final location = userData['location'] ?? 'India';

    return [
      {
        'id': 'reg-fallback-1',
        'text': 'Hi! I\'m $name, a $profession from $location. I believe in traditional values and am looking for a life partner who shares similar beliefs and aspirations.',
        'tone': 'traditional',
        'source': 'fallback'
      },
      {
        'id': 'reg-fallback-2',
        'text': 'Hello! I\'m $name, working as a $profession. I value family, respect, and honesty. Looking forward to finding someone special to share life\'s beautiful journey.',
        'tone': 'friendly',
        'source': 'fallback'
      },
      {
        'id': 'reg-fallback-3',
        'text': 'Greetings! I\'m $name, a dedicated $profession based in $location. I\'m here to find a compatible life partner for a meaningful and happy married life.',
        'tone': 'professional',
        'source': 'fallback'
      },
    ];
  }

  /// Fallback suggestions for profile completion stage
  static List<Map<String, dynamic>> _getFallbackProfileSuggestions(Map<String, dynamic> userData) {
    final name = userData['firstName'] ?? 'User';
    final profession = userData['profession'] ?? 'Professional';
    final education = userData['education'] ?? 'Graduate';
    final hobbies = userData['hobbies'] ?? 'reading, music';

    return [
      {
        'id': 'profile-fallback-1',
        'text': 'I\'m $name, a $profession with $education background. I enjoy $hobbies and believe in maintaining a balance between career and personal life. Family values are important to me, and I\'m looking for a partner who shares similar principles and dreams.',
        'tone': 'comprehensive',
        'source': 'fallback'
      },
      {
        'id': 'profile-fallback-2',
        'text': 'Hello! I\'m $name, working as a $profession. My interests include $hobbies, and I love spending quality time with family and friends. I\'m seeking a life partner who values relationships, has a positive outlook, and is ready for a committed partnership.',
        'tone': 'personal',
        'source': 'fallback'
      },
    ];
  }

  /// Fallback enhancement
  static List<Map<String, dynamic>> _getFallbackEnhancement(String existingText) {
    return [
      {
        'id': 'enhanced-fallback',
        'text': existingText,
        'improvements': ['Original text preserved - AI enhancement service temporarily unavailable'],
        'source': 'fallback'
      }
    ];
  }
}

/// About Me AI Provider
final aboutMeAIProvider = StateNotifierProvider<AboutMeAINotifier, AboutMeAIState>((ref) {
  return AboutMeAINotifier();
});

/// About Me AI State
class AboutMeAIState {
  final List<Map<String, dynamic>> suggestions;
  final bool isLoading;
  final String? error;
  final int selectedIndex;

  const AboutMeAIState({
    this.suggestions = const [],
    this.isLoading = false,
    this.error,
    this.selectedIndex = 0,
  });

  AboutMeAIState copyWith({
    List<Map<String, dynamic>>? suggestions,
    bool? isLoading,
    String? error,
    int? selectedIndex,
  }) {
    return AboutMeAIState(
      suggestions: suggestions ?? this.suggestions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedIndex: selectedIndex ?? this.selectedIndex,
    );
  }
}

/// About Me AI Notifier
class AboutMeAINotifier extends StateNotifier<AboutMeAIState> {
  AboutMeAINotifier() : super(const AboutMeAIState());

  Future<void> generateRegistrationSuggestions(Map<String, dynamic> userData) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final suggestions = await AboutMeAIService.generateRegistrationAboutMe(userData);
      state = state.copyWith(
        suggestions: suggestions,
        isLoading: false,
        selectedIndex: 0,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to generate suggestions: $e',
      );
    }
  }

  Future<void> generateProfileSuggestions(Map<String, dynamic> userData) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final suggestions = await AboutMeAIService.generateProfileAboutMe(userData);
      state = state.copyWith(
        suggestions: suggestions,
        isLoading: false,
        selectedIndex: 0,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to generate suggestions: $e',
      );
    }
  }

  Future<void> enhanceText(String existingText, Map<String, dynamic> userData) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final enhancements = await AboutMeAIService.enhanceAboutMe(existingText, userData);
      state = state.copyWith(
        suggestions: enhancements,
        isLoading: false,
        selectedIndex: 0,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to enhance text: $e',
      );
    }
  }

  void selectSuggestion(int index) {
    if (index >= 0 && index < state.suggestions.length) {
      state = state.copyWith(selectedIndex: index);
    }
  }

  void clearSuggestions() {
    state = const AboutMeAIState();
  }
}

/// About Me AI Widget
class AboutMeAIWidget extends ConsumerStatefulWidget {
  final Map<String, dynamic> userData;
  final String stage; // 'registration' or 'profile'
  final String? existingText;
  final Function(String) onTextSelected;

  const AboutMeAIWidget({
    super.key,
    required this.userData,
    required this.stage,
    this.existingText,
    required this.onTextSelected,
  });

  @override
  ConsumerState<AboutMeAIWidget> createState() => _AboutMeAIWidgetState();
}

class _AboutMeAIWidgetState extends ConsumerState<AboutMeAIWidget> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _generateSuggestions();
    });
  }

  void _generateSuggestions() {
    final notifier = ref.read(aboutMeAIProvider.notifier);
    
    if (widget.existingText != null && widget.existingText!.isNotEmpty) {
      notifier.enhanceText(widget.existingText!, widget.userData);
    } else if (widget.stage == 'registration') {
      notifier.generateRegistrationSuggestions(widget.userData);
    } else {
      notifier.generateProfileSuggestions(widget.userData);
    }
  }

  @override
  Widget build(BuildContext context) {
    final aiState = ref.watch(aboutMeAIProvider);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.auto_awesome, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'AI Suggestions',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const Spacer(),
              if (aiState.isLoading)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: 16),
          
          if (aiState.error != null)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                aiState.error!,
                style: const TextStyle(color: Colors.red),
              ),
            )
          else if (aiState.suggestions.isNotEmpty)
            Column(
              children: [
                ...aiState.suggestions.asMap().entries.map((entry) {
                  final index = entry.key;
                  final suggestion = entry.value;
                  final isSelected = index == aiState.selectedIndex;
                  
                  return GestureDetector(
                    onTap: () => ref.read(aboutMeAIProvider.notifier).selectSuggestion(index),
                    child: Container(
                      margin: const EdgeInsets.only(bottom: 12),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: isSelected ? AppColors.primary.withValues(alpha: 0.1) : Colors.grey[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected ? AppColors.primary : Colors.grey[300]!,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            suggestion['text'] ?? '',
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.textPrimary,
                              height: 1.4,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: Text(
                                  suggestion['tone'] ?? 'AI Generated',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: AppColors.primary,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              const Spacer(),
                              if (isSelected)
                                const Icon(
                                  Icons.check_circle,
                                  color: AppColors.primary,
                                  size: 20,
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                }),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _generateSuggestions,
                        child: const Text('Regenerate'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: aiState.suggestions.isNotEmpty
                            ? () {
                                final selectedSuggestion = aiState.suggestions[aiState.selectedIndex];
                                widget.onTextSelected(selectedSuggestion['text'] ?? '');
                              }
                            : null,
                        child: const Text('Use Selected'),
                      ),
                    ),
                  ],
                ),
              ],
            )
          else
            const Center(
              child: Text(
                'Click "Generate" to get AI suggestions',
                style: TextStyle(color: Colors.grey),
              ),
            ),
        ],
      ),
    );
  }
}
