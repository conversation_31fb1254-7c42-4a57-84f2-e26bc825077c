const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Default options for each field type
const DEFAULT_OPTIONS = {
  profileFor: ['Self', 'Son', 'Daughter', 'Brother', 'Sister', 'Relative', 'Friend'],
  maritalStatus: ['Never Married', 'Divorced', 'Widowed', 'Awaiting <PERSON><PERSON><PERSON>'],
  bloodGroup: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Don\'t Know'],
  education: [
    'High School',
    'Diploma',
    'Bachelor\'s',
    'Master\'s',
    'Doctorate',
    'Professional Degree',
    'Other'
  ],
  occupation: [
    'Private Sector',
    'Government/Public Sector',
    'Business/Self Employed',
    'Civil Services',
    'Defence',
    'Not Working',
    'Student',
    'Other'
  ],
  income: [
    'Below 3 Lakhs',
    '3-5 Lakhs',
    '5-7 Lakhs',
    '7-10 Lakhs',
    '10-15 Lakhs',
    '15-20 Lakhs',
    '20-30 Lakhs',
    '30-50 Lakhs',
    '50 Lakhs-1 Crore',
    'Above 1 Crore'
  ],
  familyType: ['Nuclear', 'Joint', 'Extended'],
  familyValues: ['Traditional', 'Moderate', 'Liberal'],
  subCaste: [
    'Kunbi',
    'Kshatriya',
    'Deshmukh',
    '96 Kuli',
    'Maratha-Deshmukh',
    'Maratha-Kshatriya',
    'Other'
  ]
};

/**
 * @route GET /api/admin/registration-fields
 * @desc Get all registration fields with their current options
 * @access Admin
 */
router.get('/registration-fields', async (req, res) => {
  try {
    // Get all registration field settings
    const settings = await prisma.adminSettings.findMany({
      where: {
        category: 'registration_fields'
      }
    });

    // Convert to structured format
    const fields = [];
    const fieldKeys = Object.keys(DEFAULT_OPTIONS);

    for (const fieldKey of fieldKeys) {
      const optionsSetting = settings.find(s => s.key === `${fieldKey}_options`);
      const enabledSetting = settings.find(s => s.key === `${fieldKey}_enabled`);

      fields.push({
        key: fieldKey,
        options: optionsSetting ? JSON.parse(optionsSetting.value) : DEFAULT_OPTIONS[fieldKey],
        enabled: enabledSetting ? enabledSetting.value === 'true' : true
      });
    }

    res.json({
      success: true,
      fields
    });
  } catch (error) {
    console.error('Error fetching registration fields:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch registration fields'
    });
  }
});

/**
 * @route GET /api/admin/registration-fields/:fieldKey/options
 * @desc Get options for a specific field
 * @access Admin
 */
router.get('/registration-fields/:fieldKey/options', async (req, res) => {
  try {
    const { fieldKey } = req.params;

    if (!DEFAULT_OPTIONS[fieldKey]) {
      return res.status(404).json({
        success: false,
        message: 'Field not found'
      });
    }

    // Get current options from database or use defaults
    const setting = await prisma.adminSettings.findUnique({
      where: { key: `${fieldKey}_options` }
    });

    const options = setting ? JSON.parse(setting.value) : DEFAULT_OPTIONS[fieldKey];

    res.json({
      success: true,
      options
    });
  } catch (error) {
    console.error('Error fetching field options:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch field options'
    });
  }
});

/**
 * @route POST /api/admin/registration-fields/:fieldKey/options
 * @desc Add a new option to a field
 * @access Admin
 */
router.post('/registration-fields/:fieldKey/options', async (req, res) => {
  try {
    const { fieldKey } = req.params;
    const { option } = req.body;

    if (!DEFAULT_OPTIONS[fieldKey]) {
      return res.status(404).json({
        success: false,
        message: 'Field not found'
      });
    }

    if (!option || !option.trim()) {
      return res.status(400).json({
        success: false,
        message: 'Option value is required'
      });
    }

    // Get current options
    const setting = await prisma.adminSettings.findUnique({
      where: { key: `${fieldKey}_options` }
    });

    let currentOptions = setting ? JSON.parse(setting.value) : DEFAULT_OPTIONS[fieldKey];

    // Check if option already exists
    if (currentOptions.includes(option.trim())) {
      return res.status(400).json({
        success: false,
        message: 'Option already exists'
      });
    }

    // Add new option
    currentOptions.push(option.trim());

    // Save to database
    await prisma.adminSettings.upsert({
      where: { key: `${fieldKey}_options` },
      update: { 
        value: JSON.stringify(currentOptions),
        updatedAt: new Date()
      },
      create: {
        key: `${fieldKey}_options`,
        value: JSON.stringify(currentOptions),
        category: 'registration_fields',
        dataType: 'json',
        description: `Options for ${fieldKey} field`
      }
    });

    res.json({
      success: true,
      options: currentOptions,
      message: 'Option added successfully'
    });
  } catch (error) {
    console.error('Error adding field option:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add field option'
    });
  }
});

/**
 * @route DELETE /api/admin/registration-fields/:fieldKey/options/:optionIndex
 * @desc Delete an option from a field
 * @access Admin
 */
router.delete('/registration-fields/:fieldKey/options/:optionIndex', async (req, res) => {
  try {
    const { fieldKey, optionIndex } = req.params;
    const index = parseInt(optionIndex);

    if (!DEFAULT_OPTIONS[fieldKey]) {
      return res.status(404).json({
        success: false,
        message: 'Field not found'
      });
    }

    // Get current options
    const setting = await prisma.adminSettings.findUnique({
      where: { key: `${fieldKey}_options` }
    });

    let currentOptions = setting ? JSON.parse(setting.value) : DEFAULT_OPTIONS[fieldKey];

    if (index < 0 || index >= currentOptions.length) {
      return res.status(400).json({
        success: false,
        message: 'Invalid option index'
      });
    }

    // Remove option
    currentOptions.splice(index, 1);

    // Save to database
    await prisma.adminSettings.upsert({
      where: { key: `${fieldKey}_options` },
      update: { 
        value: JSON.stringify(currentOptions),
        updatedAt: new Date()
      },
      create: {
        key: `${fieldKey}_options`,
        value: JSON.stringify(currentOptions),
        category: 'registration_fields',
        dataType: 'json',
        description: `Options for ${fieldKey} field`
      }
    });

    res.json({
      success: true,
      options: currentOptions,
      message: 'Option deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting field option:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete field option'
    });
  }
});

/**
 * @route PUT /api/admin/registration-fields/:fieldKey/toggle
 * @desc Enable/disable a registration field
 * @access Admin
 */
router.put('/registration-fields/:fieldKey/toggle', async (req, res) => {
  try {
    const { fieldKey } = req.params;
    const { enabled } = req.body;

    if (!DEFAULT_OPTIONS[fieldKey]) {
      return res.status(404).json({
        success: false,
        message: 'Field not found'
      });
    }

    // Save field enabled status
    await prisma.adminSettings.upsert({
      where: { key: `${fieldKey}_enabled` },
      update: { 
        value: enabled.toString(),
        updatedAt: new Date()
      },
      create: {
        key: `${fieldKey}_enabled`,
        value: enabled.toString(),
        category: 'registration_fields',
        dataType: 'boolean',
        description: `Enable/disable ${fieldKey} field in registration`
      }
    });

    res.json({
      success: true,
      message: `Field ${enabled ? 'enabled' : 'disabled'} successfully`
    });
  } catch (error) {
    console.error('Error toggling field:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to toggle field'
    });
  }
});

/**
 * @route GET /api/admin/registration-fields/export
 * @desc Export current field configuration
 * @access Admin
 */
router.get('/registration-fields/export', async (req, res) => {
  try {
    const settings = await prisma.adminSettings.findMany({
      where: {
        category: 'registration_fields'
      }
    });

    const config = {};
    settings.forEach(setting => {
      config[setting.key] = setting.dataType === 'json' ? JSON.parse(setting.value) : setting.value;
    });

    res.json({
      success: true,
      config,
      exportedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error exporting field configuration:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to export field configuration'
    });
  }
});

module.exports = router;
