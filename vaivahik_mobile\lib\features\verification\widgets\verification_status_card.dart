import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../providers/verification_provider.dart';

class VerificationStatusCard extends StatelessWidget {
  const VerificationStatusCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VerificationProvider>(
      builder: (context, provider, child) {
        final isVerified = provider.isVerified;
        final status = provider.verificationStatus;
        
        return Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(16)),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isVerified
                    ? [AppColors.success, AppColors.success.withAlpha(204)]
                    : [AppColors.primary, AppColors.primary.withAlpha(204)],
              ),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white.withAlpha(51),
                        borderRadius: const BorderRadius.all(Radius.circular(12)),
                      ),
                      child: Icon(
                        isVerified ? Icons.verified : Icons.pending,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            isVerified ? 'Verified Profile' : 'Verification Pending',
                            style: AppTextStyles.h2.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getStatusDescription(isVerified, status?.profileStatus),
                            style: AppTextStyles.bodyMedium.copyWith(
                              color: Colors.white.withAlpha(230),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                
                // Progress indicator
                if (!isVerified) ...[
                  Row(
                    children: [
                      Icon(
                        Icons.timeline,
                        color: Colors.white.withAlpha(179),
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Verification Progress',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: Colors.white.withAlpha(179),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  LinearProgressIndicator(
                    value: provider.completionPercentage / 100,
                    backgroundColor: Colors.white.withAlpha(77),
                    valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${provider.completionPercentage.toInt()}% Complete',
                    style: AppTextStyles.labelSmall.copyWith(
                      color: Colors.white.withAlpha(179),
                    ),
                  ),
                ],
                
                // Action buttons
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (!isVerified) ...[
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            // Navigate to upload tab or show upload dialog
                            _showUploadDialog(context);
                          },
                          icon: const Icon(Icons.upload_file, size: 18),
                          label: const Text('Upload Documents'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.white,
                            foregroundColor: AppColors.primary,
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ] else ...[
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () {
                            _showVerificationDetails(context);
                          },
                          icon: const Icon(Icons.info_outline, size: 18),
                          label: const Text('View Details'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: const BorderSide(color: Colors.white),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _getStatusDescription(bool isVerified, String? profileStatus) {
    if (isVerified) {
      return 'Your profile has been verified and approved';
    }
    
    switch (profileStatus) {
      case 'PENDING_APPROVAL':
        return 'Your documents are under review';
      case 'INCOMPLETE':
        return 'Please upload required documents';
      case 'ACTIVE':
        return 'Profile is active but not verified';
      default:
        return 'Complete verification to build trust';
    }
  }

  void _showUploadDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Upload Documents'),
        content: const Text(
          'To verify your profile, please upload the required documents. '
          'This helps build trust with other users.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to upload screen or tab
            },
            child: const Text('Upload Now'),
          ),
        ],
      ),
    );
  }

  void _showVerificationDetails(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.verified, color: AppColors.success, size: 24),
            SizedBox(width: 8),
            Text('Verified Profile'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Your profile has been successfully verified. This means:',
            ),
            const SizedBox(height: 12),
            _buildBenefitItem('✅', 'Increased trust from other users'),
            _buildBenefitItem('🔍', 'Higher visibility in search results'),
            _buildBenefitItem('💬', 'Access to premium features'),
            _buildBenefitItem('🛡️', 'Enhanced profile security'),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitItem(String icon, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(icon, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodySmall,
            ),
          ),
        ],
      ),
    );
  }
}
