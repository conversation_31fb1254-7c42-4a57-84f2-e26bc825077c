import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'interest_model.freezed.dart';
part 'interest_model.g.dart';

// ignore_for_file: non_abstract_class_inherits_abstract_member

/// 💝 INTEREST MODELS - Complete Interest System Data Models
/// Features: Interest, Activity Stats, User Profile, Status Management

@freezed
class InterestModel with _$InterestModel {
  const factory InterestModel({
    required String id,
    required String userId,
    required String targetUserId,
    String? message,
    @Default('PENDING') String status,
    String? responseMessage,
    DateTime? respondedAt,
    required DateTime createdAt,
    DateTime? updatedAt,
    UserProfileModel? user,
    UserProfileModel? targetUser,
  }) = _InterestModel;

  factory InterestModel.fromJson(Map<String, dynamic> json) =>
      _$InterestModelFromJson(json);
}

@freezed
class UserProfileModel with _$UserProfileModel {
  const factory UserProfileModel({
    required String id,
    required String firstName,
    String? lastName,
    int? age,
    String? city,
    String? state,
    String? occupation,
    String? profilePicUrl,
    String? education,
    int? height,
    String? religion,
    String? caste,
    String? motherTongue,
    bool? isVerified,
    bool? isPremium,
    DateTime? lastSeen,
    bool? isOnline,
  }) = _UserProfileModel;

  factory UserProfileModel.fromJson(Map<String, dynamic> json) =>
      _$UserProfileModelFromJson(json);
}

@freezed
class SendInterestRequest with _$SendInterestRequest {
  const factory SendInterestRequest({
    required String targetUserId,
    String? message,
  }) = _SendInterestRequest;

  factory SendInterestRequest.fromJson(Map<String, dynamic> json) => _$SendInterestRequestFromJson(json);
}

@freezed
class RespondInterestRequest with _$RespondInterestRequest {
  const factory RespondInterestRequest({
    required String interestId,
    required String response, // 'ACCEPTED' or 'REJECTED'
    String? message,
  }) = _RespondInterestRequest;

  factory RespondInterestRequest.fromJson(Map<String, dynamic> json) => _$RespondInterestRequestFromJson(json);
}

@freezed
class ActivityStatsModel with _$ActivityStatsModel {
  const factory ActivityStatsModel({
    @Default(0) int interestsSent,
    @Default(0) int interestsReceived,
    @Default(0) int profileViews,
    @Default(0) int totalMatches,
    @Default(0) int acceptedInterests,
    @Default(0) int pendingInterests,
    @Default(0) int declinedInterests,
    @Default([]) List<int> weeklyViews,
    @Default([]) List<int> weeklyInterests,
    @Default(0.0) double successRate,
    @Default(0) int todayViews,
    @Default(0) int todayInterests,
  }) = _ActivityStatsModel;

  factory ActivityStatsModel.fromJson(Map<String, dynamic> json) =>
      _$ActivityStatsModelFromJson(json);
}

@freezed
class InterestActivityModel with _$InterestActivityModel {
  const factory InterestActivityModel({
    required String id,
    required String profileId,
    required String profileName,
    String? profilePhoto,
    required int age,
    required String location,
    required InterestType type,
    required InterestStatus status,
    required DateTime timestamp,
    String? message,
    String? responseMessage,
    bool? isRead,
    bool? isPremium,
    String? occupation,
    String? education,
  }) = _InterestActivityModel;

  factory InterestActivityModel.fromJson(Map<String, dynamic> json) =>
      _$InterestActivityModelFromJson(json);
}



@freezed
class InterestListResponse with _$InterestListResponse {
  const factory InterestListResponse({
    required bool success,
    required InterestListData data,
    String? message,
  }) = _InterestListResponse;

  factory InterestListResponse.fromJson(Map<String, dynamic> json) =>
      _$InterestListResponseFromJson(json);
}

@freezed
class InterestListData with _$InterestListData {
  const factory InterestListData({
    @Default([]) List<InterestModel> received,
    @Default([]) List<InterestModel> sent,
    @Default(0) int totalReceived,
    @Default(0) int totalSent,
    @Default(0) int pendingReceived,
    @Default(0) int pendingSent,
  }) = _InterestListData;

  factory InterestListData.fromJson(Map<String, dynamic> json) =>
      _$InterestListDataFromJson(json);
}

@freezed
class InterestDashboardResponse with _$InterestDashboardResponse {
  const factory InterestDashboardResponse({
    required bool success,
    required InterestDashboardData data,
    String? message,
  }) = _InterestDashboardResponse;

  factory InterestDashboardResponse.fromJson(Map<String, dynamic> json) =>
      _$InterestDashboardResponseFromJson(json);
}

@freezed
class InterestDashboardData with _$InterestDashboardData {
  const factory InterestDashboardData({
    required ActivityStatsModel stats,
    @Default([]) List<InterestActivityModel> recentActivities,
    @Default([]) List<InterestModel> pendingInterests,
    @Default([]) List<UserProfileModel> suggestedProfiles,
  }) = _InterestDashboardData;

  factory InterestDashboardData.fromJson(Map<String, dynamic> json) =>
      _$InterestDashboardDataFromJson(json);
}

// Enums
enum InterestType {
  @JsonValue('sent')
  sent,
  @JsonValue('received')
  received,
}

enum InterestStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('accepted')
  accepted,
  @JsonValue('declined')
  declined,
  @JsonValue('expired')
  expired,
}

// Extension methods for better UX
extension InterestStatusExtension on InterestStatus {
  String get displayName {
    switch (this) {
      case InterestStatus.pending:
        return 'Pending';
      case InterestStatus.accepted:
        return 'Accepted';
      case InterestStatus.declined:
        return 'Declined';
      case InterestStatus.expired:
        return 'Expired';
    }
  }

  Color get color {
    switch (this) {
      case InterestStatus.pending:
        return const Color(0xFFFF9800); // Orange
      case InterestStatus.accepted:
        return const Color(0xFF4CAF50); // Green
      case InterestStatus.declined:
        return const Color(0xFFF44336); // Red
      case InterestStatus.expired:
        return const Color(0xFF9E9E9E); // Grey
    }
  }

  IconData get icon {
    switch (this) {
      case InterestStatus.pending:
        return Icons.schedule;
      case InterestStatus.accepted:
        return Icons.check_circle;
      case InterestStatus.declined:
        return Icons.cancel;
      case InterestStatus.expired:
        return Icons.access_time;
    }
  }
}

extension InterestTypeExtension on InterestType {
  String get displayName {
    switch (this) {
      case InterestType.sent:
        return 'Sent';
      case InterestType.received:
        return 'Received';
    }
  }

  IconData get icon {
    switch (this) {
      case InterestType.sent:
        return Icons.send;
      case InterestType.received:
        return Icons.inbox;
    }
  }

  Color get color {
    switch (this) {
      case InterestType.sent:
        return const Color(0xFF2196F3); // Blue
      case InterestType.received:
        return const Color(0xFF4CAF50); // Green
    }
  }
}

// Helper functions
class InterestHelper {
  static String formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      return 'Just now';
    }
  }

  static String getLocationString(String? city, String? state) {
    if (city != null && state != null) {
      return '$city, $state';
    } else if (city != null) {
      return city;
    } else if (state != null) {
      return state;
    } else {
      return 'Location not specified';
    }
  }

  static String getFullName(String firstName, String? lastName) {
    if (lastName != null && lastName.isNotEmpty) {
      return '$firstName $lastName';
    }
    return firstName;
  }

  static String getAgeString(int? age) {
    if (age != null) {
      return '$age years';
    }
    return 'Age not specified';
  }
}
