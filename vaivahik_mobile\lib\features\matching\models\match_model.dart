class MatchModel {
  final String id;
  final String userId;
  final String name;
  final int age;
  final String location;
  final String education;
  final String occupation;
  final String profilePicture;
  final List<String> photos;
  final double compatibilityScore;
  final bool isOnline;
  final DateTime lastActive;
  final bool isPremium;
  final bool isVerified;
  final String height;
  final String religion;
  final String caste;
  final String maritalStatus;
  final String motherTongue;
  final String income;
  final String aboutMe;
  final bool isLiked;
  final bool isShortlisted;
  final bool hasInterest;
  final DateTime createdAt;

  const MatchModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.age,
    required this.location,
    required this.education,
    required this.occupation,
    required this.profilePicture,
    required this.photos,
    required this.compatibilityScore,
    required this.isOnline,
    required this.lastActive,
    required this.isPremium,
    required this.isVerified,
    required this.height,
    required this.religion,
    required this.caste,
    required this.maritalStatus,
    required this.motherTongue,
    required this.income,
    required this.aboutMe,
    required this.isLiked,
    required this.isShortlisted,
    required this.hasInterest,
    required this.createdAt,
  });

  factory MatchModel.fromJson(Map<String, dynamic> json) {
    return MatchModel(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      name: json['fullName']?.toString() ?? json['name']?.toString() ?? '',
      age: json['age']?.toInt() ?? 0,
      location: json['city']?.toString() ?? json['location']?.toString() ?? '',
      education: json['education']?.toString() ?? '',
      occupation: json['occupation']?.toString() ?? '',
      profilePicture: json['profilePicture']?.toString() ?? json['profilePhoto']?.toString() ?? '',
      photos: List<String>.from(json['photos'] ?? json['profilePhotos'] ?? []),
      compatibilityScore: (json['compatibilityScore']?.toDouble() ?? json['score']?.toDouble() ?? 0.0),
      isOnline: json['isOnline'] ?? false,
      lastActive: DateTime.tryParse(json['lastActive']?.toString() ?? '') ?? DateTime.now(),
      isPremium: json['isPremium'] ?? false,
      isVerified: json['isVerified'] ?? false,
      height: json['height']?.toString() ?? '',
      religion: json['religion']?.toString() ?? '',
      caste: json['caste']?.toString() ?? '',
      maritalStatus: json['maritalStatus']?.toString() ?? '',
      motherTongue: json['motherTongue']?.toString() ?? '',
      income: json['income']?.toString() ?? json['incomeRange']?.toString() ?? '',
      aboutMe: json['aboutMe']?.toString() ?? '',
      isLiked: json['isLiked'] ?? false,
      isShortlisted: json['isShortlisted'] ?? false,
      hasInterest: json['hasInterest'] ?? false,
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'age': age,
      'location': location,
      'education': education,
      'occupation': occupation,
      'profilePicture': profilePicture,
      'photos': photos,
      'compatibilityScore': compatibilityScore,
      'isOnline': isOnline,
      'lastActive': lastActive.toIso8601String(),
      'isPremium': isPremium,
      'isVerified': isVerified,
      'height': height,
      'religion': religion,
      'caste': caste,
      'maritalStatus': maritalStatus,
      'motherTongue': motherTongue,
      'income': income,
      'aboutMe': aboutMe,
      'isLiked': isLiked,
      'isShortlisted': isShortlisted,
      'hasInterest': hasInterest,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  MatchModel copyWith({
    String? id,
    String? userId,
    String? name,
    int? age,
    String? location,
    String? education,
    String? occupation,
    String? profilePicture,
    List<String>? photos,
    double? compatibilityScore,
    bool? isOnline,
    DateTime? lastActive,
    bool? isPremium,
    bool? isVerified,
    String? height,
    String? religion,
    String? caste,
    String? maritalStatus,
    String? motherTongue,
    String? income,
    String? aboutMe,
    bool? isLiked,
    bool? isShortlisted,
    bool? hasInterest,
    DateTime? createdAt,
  }) {
    return MatchModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      age: age ?? this.age,
      location: location ?? this.location,
      education: education ?? this.education,
      occupation: occupation ?? this.occupation,
      profilePicture: profilePicture ?? this.profilePicture,
      photos: photos ?? this.photos,
      compatibilityScore: compatibilityScore ?? this.compatibilityScore,
      isOnline: isOnline ?? this.isOnline,
      lastActive: lastActive ?? this.lastActive,
      isPremium: isPremium ?? this.isPremium,
      isVerified: isVerified ?? this.isVerified,
      height: height ?? this.height,
      religion: religion ?? this.religion,
      caste: caste ?? this.caste,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      motherTongue: motherTongue ?? this.motherTongue,
      income: income ?? this.income,
      aboutMe: aboutMe ?? this.aboutMe,
      isLiked: isLiked ?? this.isLiked,
      isShortlisted: isShortlisted ?? this.isShortlisted,
      hasInterest: hasInterest ?? this.hasInterest,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

enum MatchAction {
  like,
  pass,
  superLike,
  shortlist,
  interest,
  block,
  report
}

class MatchFilters {
  final int? ageMin;
  final int? ageMax;
  final String? location;
  final String? education;
  final String? occupation;
  final String? caste;
  final String? maritalStatus;
  final String? income;
  final bool? isOnline;
  final bool? hasPhoto;
  final String? sortBy;
  final String? order;

  const MatchFilters({
    this.ageMin,
    this.ageMax,
    this.location,
    this.education,
    this.occupation,
    this.caste,
    this.maritalStatus,
    this.income,
    this.isOnline,
    this.hasPhoto,
    this.sortBy,
    this.order,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    if (ageMin != null) data['ageMin'] = ageMin;
    if (ageMax != null) data['ageMax'] = ageMax;
    if (location != null) data['location'] = location;
    if (education != null) data['education'] = education;
    if (occupation != null) data['occupation'] = occupation;
    if (caste != null) data['caste'] = caste;
    if (maritalStatus != null) data['maritalStatus'] = maritalStatus;
    if (income != null) data['income'] = income;
    if (isOnline != null) data['isOnline'] = isOnline;
    if (hasPhoto != null) data['hasPhoto'] = hasPhoto;
    if (sortBy != null) data['sortBy'] = sortBy;
    if (order != null) data['order'] = order;
    return data;
  }
}

class InterestModel {
  final String id;
  final String fromUserId;
  final String toUserId;
  final String status;
  final String? message;
  final DateTime createdAt;
  final MatchModel? fromUser;
  final MatchModel? toUser;

  const InterestModel({
    required this.id,
    required this.fromUserId,
    required this.toUserId,
    required this.status,
    this.message,
    required this.createdAt,
    this.fromUser,
    this.toUser,
  });

  factory InterestModel.fromJson(Map<String, dynamic> json) {
    return InterestModel(
      id: json['id']?.toString() ?? '',
      fromUserId: json['fromUserId']?.toString() ?? '',
      toUserId: json['toUserId']?.toString() ?? '',
      status: json['status']?.toString() ?? '',
      message: json['message']?.toString(),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      fromUser: json['fromUser'] != null ? MatchModel.fromJson(json['fromUser']) : null,
      toUser: json['toUser'] != null ? MatchModel.fromJson(json['toUser']) : null,
    );
  }
}

class ShortlistModel {
  final String id;
  final String userId;
  final String profileId;
  final String? note;
  final DateTime createdAt;
  final MatchModel? profile;

  const ShortlistModel({
    required this.id,
    required this.userId,
    required this.profileId,
    this.note,
    required this.createdAt,
    this.profile,
  });

  factory ShortlistModel.fromJson(Map<String, dynamic> json) {
    return ShortlistModel(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      profileId: json['profileId']?.toString() ?? '',
      note: json['note']?.toString(),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      profile: json['profile'] != null ? MatchModel.fromJson(json['profile']) : null,
    );
  }
}
