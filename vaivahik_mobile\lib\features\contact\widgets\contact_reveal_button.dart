import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../providers/contact_provider.dart';
import 'contact_reveal_dialog.dart';

class ContactRevealButton extends StatefulWidget {
  final String targetUserId;
  final String? targetUserName;
  final String? targetUserPhoto;
  final bool isCompact;
  final VoidCallback? onSuccess;

  const ContactRevealButton({
    super.key,
    required this.targetUserId,
    this.targetUserName,
    this.targetUserPhoto,
    this.isCompact = false,
    this.onSuccess,
  });

  @override
  State<ContactRevealButton> createState() => _ContactRevealButtonState();
}

class _ContactRevealButtonState extends State<ContactRevealButton> {
  bool _canAccess = false;
  bool _isChecking = true;

  @override
  void initState() {
    super.initState();
    _checkAccess();
  }

  void _checkAccess() async {
    try {
      final provider = context.read<ContactProvider>();
      final canAccess = await provider.canAccessContact(widget.targetUserId);
      
      if (mounted) {
        setState(() {
          _canAccess = canAccess;
          _isChecking = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isChecking = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isChecking) {
      return _buildLoadingButton();
    }

    if (_canAccess) {
      return _buildAccessButton();
    } else {
      return _buildRevealButton();
    }
  }

  Widget _buildLoadingButton() {
    return Container(
      height: widget.isCompact ? 36 : 44,
      width: widget.isCompact ? 120 : double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(widget.isCompact ? 18 : 8),
      ),
      child: const Center(
        child: SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
    );
  }

  Widget _buildRevealButton() {
    return SizedBox(
      height: widget.isCompact ? 36 : 44,
      width: widget.isCompact ? 120 : double.infinity,
      child: ElevatedButton.icon(
        onPressed: _showRevealDialog,
        icon: Icon(
          Icons.visibility,
          size: widget.isCompact ? 16 : 18,
        ),
        label: Text(
          widget.isCompact ? 'Reveal' : 'Reveal Contact',
          style: (widget.isCompact ? AppTextStyles.bodySmall : AppTextStyles.bodyMedium).copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.isCompact ? 18 : 8),
          ),
        ),
      ),
    );
  }

  Widget _buildAccessButton() {
    return SizedBox(
      height: widget.isCompact ? 36 : 44,
      width: widget.isCompact ? 120 : double.infinity,
      child: OutlinedButton.icon(
        onPressed: _showRevealDialog,
        icon: Icon(
          Icons.phone,
          size: widget.isCompact ? 16 : 18,
          color: Colors.green,
        ),
        label: Text(
          widget.isCompact ? 'Call' : 'Contact Available',
          style: (widget.isCompact ? AppTextStyles.bodySmall : AppTextStyles.bodyMedium).copyWith(
            color: Colors.green,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: const BorderSide(color: Colors.green),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(widget.isCompact ? 18 : 8),
          ),
        ),
      ),
    );
  }

  void _showRevealDialog() {
    showDialog(
      context: context,
      builder: (context) => ContactRevealDialog(
        targetUserId: widget.targetUserId,
        targetUserName: widget.targetUserName,
        targetUserPhoto: widget.targetUserPhoto,
      ),
    ).then((result) {
      if (result == true && widget.onSuccess != null) {
        widget.onSuccess!();
      }
      // Refresh access status after dialog closes
      _checkAccess();
    });
  }
}
