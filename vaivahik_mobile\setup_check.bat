@echo off
echo.
echo ========================================
echo    VAIVAHIK MOBILE SETUP CHECKER
echo ========================================
echo.

echo [1/5] Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo ❌ Flutter not found! Please complete Flutter installation first.
    echo    Follow FLUTTER_SETUP_GUIDE.md for installation steps.
    pause
    exit /b 1
)
echo ✅ Flutter is installed!
echo.

echo [2/5] Running Flutter Doctor...
flutter doctor
echo.

echo [3/5] Checking project dependencies...
cd /d "%~dp0"
flutter pub get
if %errorlevel% neq 0 (
    echo ❌ Failed to get dependencies!
    pause
    exit /b 1
)
echo ✅ Dependencies installed successfully!
echo.

echo [4/5] Checking for connected devices...
flutter devices
echo.

echo [5/5] Project structure verification...
if exist "lib\main.dart" (
    echo ✅ Main app file found
) else (
    echo ❌ Main app file missing!
)

if exist "lib\features\auth" (
    echo ✅ Authentication module found
) else (
    echo ❌ Authentication module missing!
)

if exist "pubspec.yaml" (
    echo ✅ Project configuration found
) else (
    echo ❌ Project configuration missing!
)

echo.
echo ========================================
echo    SETUP CHECK COMPLETE!
echo ========================================
echo.
echo Next steps:
echo 1. If all checks passed, run: flutter run
echo 2. If any issues, check FLUTTER_SETUP_GUIDE.md
echo 3. For help, contact your development team
echo.
pause
