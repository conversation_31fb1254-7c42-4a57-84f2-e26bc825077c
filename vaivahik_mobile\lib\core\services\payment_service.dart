import 'package:flutter/material.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import '../api/api_client.dart';

// Payment models matching website's structure
class PaymentOrder {
  final String id;
  final String orderId;
  final double amount;
  final String currency;
  final String status;
  final String? planType;
  final String? planDuration;
  final String? featureType;
  final int? quantity;
  final DateTime createdAt;

  const PaymentOrder({
    required this.id,
    required this.orderId,
    required this.amount,
    required this.currency,
    required this.status,
    this.planType,
    this.planDuration,
    this.featureType,
    this.quantity,
    required this.createdAt,
  });

  factory PaymentOrder.fromJson(Map<String, dynamic> json) {
    return PaymentOrder(
      id: json['id'] ?? '',
      orderId: json['orderId'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'INR',
      status: json['status'] ?? 'CREATED',
      planType: json['planType'],
      planDuration: json['planDuration'],
      featureType: json['featureType'],
      quantity: json['quantity'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class Subscription {
  final String id;
  final String userId;
  final String planType;
  final String planDuration;
  final String status;
  final DateTime startDate;
  final DateTime endDate;
  final bool autoRenew;
  final double amount;

  const Subscription({
    required this.id,
    required this.userId,
    required this.planType,
    required this.planDuration,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.autoRenew,
    required this.amount,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      planType: json['planType'] ?? '',
      planDuration: json['planDuration'] ?? '',
      status: json['status'] ?? '',
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: DateTime.parse(json['endDate'] ?? DateTime.now().toIso8601String()),
      autoRenew: json['autoRenew'] ?? false,
      amount: (json['amount'] ?? 0).toDouble(),
    );
  }

  bool get isActive => status == 'ACTIVE' && endDate.isAfter(DateTime.now());
  int get daysRemaining => isActive ? endDate.difference(DateTime.now()).inDays : 0;
}

// Premium plans matching website's structure
class PremiumPlan {
  final String id;
  final String name;
  final String planType;
  final String duration;
  final double amount;
  final String currency;
  final List<String> features;
  final bool isPopular;
  final String? badge;
  final String? savings;
  final Color? color;

  const PremiumPlan({
    required this.id,
    required this.name,
    required this.planType,
    required this.duration,
    required this.amount,
    required this.currency,
    required this.features,
    this.isPopular = false,
    this.badge,
    this.savings,
    this.color,
  });

  factory PremiumPlan.fromJson(Map<String, dynamic> json) {
    return PremiumPlan(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      planType: json['planType'] ?? '',
      duration: json['duration'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      currency: json['currency'] ?? 'INR',
      features: List<String>.from(json['features'] ?? []),
      isPopular: json['isPopular'] ?? false,
      badge: json['badge'],
      savings: json['savings'],
    );
  }
}

class PaymentService {
  static final PaymentService _instance = PaymentService._internal();
  factory PaymentService() => _instance;
  PaymentService._internal();

  late Razorpay _razorpay;
  final ApiClient _apiClient = ApiClient();
  
  // Callback functions
  Function(String)? _onPaymentSuccess;
  Function(String)? _onPaymentError;

  void initialize() {
    _razorpay = Razorpay();
    _razorpay.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }

  void dispose() {
    _razorpay.clear();
  }

  // Get available premium plans using website's API
  Future<List<PremiumPlan>> getPremiumPlans() async {
    try {
      final response = await _apiClient.get('/payments/plans');
      
      if (response['success'] == true) {
        final plansData = response['plans'] as Map<String, dynamic>;
        final List<PremiumPlan> plans = [];
        
        // Convert website's plan structure to mobile format
        if (plansData['PREMIUM'] != null) {
          final premiumPlans = plansData['PREMIUM'] as Map<String, dynamic>;
          
          if (premiumPlans['monthly'] != null) {
            plans.add(PremiumPlan(
              id: 'premium_monthly',
              name: 'Premium Monthly',
              planType: 'PREMIUM',
              duration: 'monthly',
              amount: (premiumPlans['monthly']['amount'] ?? 99900) / 100.0,
              currency: 'INR',
              features: List<String>.from(premiumPlans['monthly']['features'] ?? []),
              isPopular: false,
            ));
          }
          
          if (premiumPlans['quarterly'] != null) {
            plans.add(PremiumPlan(
              id: 'premium_quarterly',
              name: 'Premium Quarterly',
              planType: 'PREMIUM',
              duration: 'quarterly',
              amount: (premiumPlans['quarterly']['amount'] ?? 249900) / 100.0,
              currency: 'INR',
              features: List<String>.from(premiumPlans['quarterly']['features'] ?? []),
              isPopular: true,
              badge: 'MOST POPULAR',
              savings: premiumPlans['quarterly']['savings'],
            ));
          }
          
          if (premiumPlans['annual'] != null) {
            plans.add(PremiumPlan(
              id: 'premium_annual',
              name: 'Premium Annual',
              planType: 'PREMIUM',
              duration: 'annual',
              amount: (premiumPlans['annual']['amount'] ?? 799900) / 100.0,
              currency: 'INR',
              features: List<String>.from(premiumPlans['annual']['features'] ?? []),
              isPopular: false,
              badge: 'BEST VALUE',
              savings: premiumPlans['annual']['savings'],
            ));
          }
        }
        
        return plans;
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch premium plans');
      }
    } catch (e) {
      throw Exception('Error fetching premium plans: $e');
    }
  }

  // Get current user subscription using website's API
  Future<Subscription?> getCurrentSubscription() async {
    try {
      final response = await _apiClient.get('/payments/subscription');
      
      if (response['success'] == true && response['subscription'] != null) {
        return Subscription.fromJson(response['subscription']);
      }
      return null;
    } catch (e) {
      throw Exception('Error fetching subscription: $e');
    }
  }

  // Create subscription order using website's Razorpay service
  Future<PaymentOrder> createSubscriptionOrder({
    required String planType,
    required String planDuration,
  }) async {
    try {
      final response = await _apiClient.post('/payments/create-subscription-order', {
        'planType': planType,
        'planDuration': planDuration,
      });
      
      if (response['success'] == true) {
        return PaymentOrder.fromJson({
          'id': response['paymentOrderId'],
          'orderId': response['order']['id'],
          'amount': response['order']['amount'] / 100.0,
          'currency': response['order']['currency'],
          'status': 'CREATED',
          'planType': planType,
          'planDuration': planDuration,
          'createdAt': DateTime.now().toIso8601String(),
        });
      } else {
        throw Exception(response['message'] ?? 'Failed to create subscription order');
      }
    } catch (e) {
      throw Exception('Error creating subscription order: $e');
    }
  }

  // Create feature order using website's Razorpay service
  Future<PaymentOrder> createFeatureOrder({
    required String featureType,
    int quantity = 1,
  }) async {
    try {
      final response = await _apiClient.post('/payments/create-feature-order', {
        'featureType': featureType,
        'quantity': quantity,
      });
      
      if (response['success'] == true) {
        return PaymentOrder.fromJson({
          'id': response['paymentOrderId'],
          'orderId': response['order']['id'],
          'amount': response['order']['amount'] / 100.0,
          'currency': response['order']['currency'],
          'status': 'CREATED',
          'featureType': featureType,
          'quantity': quantity,
          'createdAt': DateTime.now().toIso8601String(),
        });
      } else {
        throw Exception(response['message'] ?? 'Failed to create feature order');
      }
    } catch (e) {
      throw Exception('Error creating feature order: $e');
    }
  }

  // Purchase premium subscription using website's payment flow
  Future<void> purchaseSubscription({
    required PremiumPlan plan,
    required Map<String, dynamic> userDetails,
    required Function(Subscription) onSuccess,
    required Function(String) onError,
  }) async {
    try {
      _onPaymentSuccess = (paymentId) async {
        try {
          final subscription = await getCurrentSubscription();
          if (subscription != null && subscription.isActive) {
            onSuccess(subscription);
          } else {
            onError('Subscription activation failed');
          }
        } catch (e) {
          onError('Error fetching updated subscription: $e');
        }
      };
      _onPaymentError = onError;

      // Create payment order
      final paymentOrder = await createSubscriptionOrder(
        planType: plan.planType,
        planDuration: plan.duration,
      );

      // Get Razorpay key from backend
      final keyResponse = await _apiClient.get('/payments/razorpay-key');
      final razorpayKey = keyResponse['key'] ?? '';

      // Open Razorpay checkout
      final options = {
        'key': razorpayKey,
        'amount': (paymentOrder.amount * 100).toInt(), // Amount in paise
        'name': 'Vaivahik',
        'description': plan.name,
        'order_id': paymentOrder.orderId,
        'prefill': {
          'contact': userDetails['phone'] ?? '',
          'email': userDetails['email'] ?? '',
          'name': userDetails['name'] ?? '',
        },
        'theme': {
          'color': '#FF6B35'
        },
        'notes': {
          'planType': plan.planType,
          'planDuration': plan.duration,
        }
      };

      _razorpay.open(options);
    } catch (e) {
      onError('Error initiating subscription purchase: $e');
    }
  }

  // Purchase one-time feature using website's payment flow
  Future<void> purchaseFeature({
    required String featureType,
    required String featureName,
    required double amount,
    int quantity = 1,
    required Map<String, dynamic> userDetails,
    required Function(String) onSuccess,
    required Function(String) onError,
  }) async {
    try {
      _onPaymentSuccess = onSuccess;
      _onPaymentError = onError;

      // Create payment order
      final paymentOrder = await createFeatureOrder(
        featureType: featureType,
        quantity: quantity,
      );

      // Get Razorpay key from backend
      final keyResponse = await _apiClient.get('/payments/razorpay-key');
      final razorpayKey = keyResponse['key'] ?? '';

      // Open Razorpay checkout
      final options = {
        'key': razorpayKey,
        'amount': (paymentOrder.amount * 100).toInt(), // Amount in paise
        'name': 'Vaivahik',
        'description': featureName,
        'order_id': paymentOrder.orderId,
        'prefill': {
          'contact': userDetails['phone'] ?? '',
          'email': userDetails['email'] ?? '',
          'name': userDetails['name'] ?? '',
        },
        'theme': {
          'color': '#FF6B35'
        },
        'notes': {
          'featureType': featureType,
          'quantity': quantity.toString(),
        }
      };

      _razorpay.open(options);
    } catch (e) {
      onError('Error initiating feature purchase: $e');
    }
  }

  // Handle payment success using website's verification flow
  void _handlePaymentSuccess(dynamic response) async {
    try {
      // Verify payment on backend using website's API
      final verifyResponse = await _apiClient.post('/payments/verify-payment', {
        'orderId': response['razorpay_order_id'],
        'paymentId': response['razorpay_payment_id'],
        'signature': response['razorpay_signature'],
      });

      if (verifyResponse['success'] == true) {
        _onPaymentSuccess?.call(response['razorpay_payment_id'] ?? '');
      } else {
        _onPaymentError?.call(verifyResponse['message'] ?? 'Payment verification failed');
      }
    } catch (e) {
      _onPaymentError?.call('Error verifying payment: $e');
    }
  }

  void _handlePaymentError(dynamic response) {
    _onPaymentError?.call('Payment failed: ${response['description'] ?? 'Unknown error'}');
  }

  void _handleExternalWallet(dynamic response) {
    // Handle external wallet selection if needed
    print('External wallet selected: ${response['wallet_name']}');
  }

  // Get payment history using website's API
  Future<List<PaymentOrder>> getPaymentHistory() async {
    try {
      final response = await _apiClient.get('/payments/history');

      if (response['success'] == true) {
        final payments = response['payments'] as List;
        return payments.map((payment) => PaymentOrder.fromJson(payment)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch payment history');
      }
    } catch (e) {
      throw Exception('Error fetching payment history: $e');
    }
  }

  // Cancel subscription using website's API
  Future<bool> cancelSubscription() async {
    try {
      final response = await _apiClient.post('/payments/cancel-subscription', {});
      return response['success'] == true;
    } catch (e) {
      throw Exception('Error canceling subscription: $e');
    }
  }

  // Check if user has premium access
  Future<bool> hasPremiumAccess() async {
    try {
      final subscription = await getCurrentSubscription();
      return subscription?.isActive ?? false;
    } catch (e) {
      return false;
    }
  }

  // Get feature usage and limits
  Future<Map<String, dynamic>> getFeatureUsage() async {
    try {
      final response = await _apiClient.get('/payments/feature-usage');

      if (response['success'] == true) {
        return response['usage'] ?? {};
      } else {
        return {};
      }
    } catch (e) {
      return {};
    }
  }
}
