/// 📄 Content Management Models
/// Using existing website backend API structure
library;

class ContentModel {
  final String key;
  final String title;
  final String content;
  final String? metaTitle;
  final String? metaDescription;
  final DateTime updatedAt;

  ContentModel({
    required this.key,
    required this.title,
    required this.content,
    this.metaTitle,
    this.metaDescription,
    required this.updatedAt,
  });

  factory ContentModel.fromJson(Map<String, dynamic> json) {
    return ContentModel(
      key: json['key'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      metaTitle: json['metaTitle'],
      metaDescription: json['metaDescription'],
      updatedAt: DateTime.parse(json['updatedAt'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'title': title,
      'content': content,
      'metaTitle': metaTitle,
      'metaDescription': metaDescription,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

class SocialMediaLink {
  final String platform;
  final String url;
  final String icon;

  SocialMediaLink({
    required this.platform,
    required this.url,
    required this.icon,
  });

  factory SocialMediaLink.fromJson(Map<String, dynamic> json) {
    return SocialMediaLink(
      platform: json['platform'] ?? '',
      url: json['url'] ?? '',
      icon: json['icon'] ?? '',
    );
  }
}

class FAQItem {
  final String question;
  final String answer;
  final String category;

  FAQItem({
    required this.question,
    required this.answer,
    required this.category,
  });

  factory FAQItem.fromJson(Map<String, dynamic> json) {
    return FAQItem(
      question: json['question'] ?? '',
      answer: json['answer'] ?? '',
      category: json['category'] ?? 'general',
    );
  }
}

class ChatbotResponse {
  final String message;
  final List<String> suggestions;
  final String type; // 'text', 'faq', 'contact'

  ChatbotResponse({
    required this.message,
    this.suggestions = const [],
    this.type = 'text',
  });

  factory ChatbotResponse.fromJson(Map<String, dynamic> json) {
    return ChatbotResponse(
      message: json['message'] ?? '',
      suggestions: List<String>.from(json['suggestions'] ?? []),
      type: json['type'] ?? 'text',
    );
  }
}
