import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../providers/verification_provider.dart';

class VerificationStatsCard extends StatelessWidget {
  const VerificationStatsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<VerificationProvider>(
      builder: (context, provider, child) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Document Statistics',
                  style: AppTextStyles.h3,
                ),
                const SizedBox(height: 16),
                
                // Stats grid
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        icon: Icons.folder,
                        label: 'Total',
                        value: provider.totalDocuments.toString(),
                        color: AppColors.primary,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        icon: Icons.check_circle,
                        label: 'Approved',
                        value: provider.approvedDocuments.toString(),
                        color: AppColors.success,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildStatItem(
                        icon: Icons.pending,
                        label: 'Pending',
                        value: provider.pendingDocuments.toString(),
                        color: AppColors.warning,
                      ),
                    ),
                    Expanded(
                      child: _buildStatItem(
                        icon: Icons.cancel,
                        label: 'Rejected',
                        value: provider.rejectedDocuments.toString(),
                        color: AppColors.error,
                      ),
                    ),
                  ],
                ),
                
                // Completion percentage
                if (provider.verificationStats != null) ...[
                  const SizedBox(height: 20),
                  const Divider(),
                  const SizedBox(height: 16),
                  const Row(
                    children: [
                      Icon(
                        Icons.analytics,
                        color: AppColors.primary,
                        size: 20,
                      ),
                      SizedBox(width: 8),
                      Text(
                        'Completion Status',
                        style: AppTextStyles.labelLarge,
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  
                  // Progress bar
                  Row(
                    children: [
                      Expanded(
                        child: LinearProgressIndicator(
                          value: provider.completionPercentage / 100,
                          backgroundColor: AppColors.primary.withAlpha(51),
                          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '${provider.completionPercentage.toInt()}%',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  // Missing documents
                  if (provider.verificationStats!.missingDocuments.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.warning.withAlpha(26),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: AppColors.warning.withAlpha(77)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(
                                Icons.warning_amber,
                                color: AppColors.warning,
                                size: 16,
                              ),
                              const SizedBox(width: 6),
                              Text(
                                'Missing Documents',
                                style: AppTextStyles.labelMedium.copyWith(
                                  color: AppColors.warning,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 4,
                            children: provider.verificationStats!.missingDocuments
                                .map((docType) => Chip(
                                      label: Text(
                                        docType.label,
                                        style: AppTextStyles.labelSmall,
                                      ),
                                      avatar: Text(docType.icon),
                                      backgroundColor: Colors.white,
                                      side: BorderSide(color: AppColors.warning.withAlpha(128)),
                                    ))
                                .toList(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withAlpha(77)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.h2.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.labelSmall.copyWith(color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
