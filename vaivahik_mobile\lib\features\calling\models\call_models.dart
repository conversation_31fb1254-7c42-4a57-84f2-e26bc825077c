class CallSession {
  final String id;
  final String callerId;
  final String callerName;
  final String? callerAvatar;
  final String receiverId;
  final String receiverName;
  final String? receiverAvatar;
  final CallType type;
  final CallStatus status;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? duration;
  final String? roomId;
  final Map<String, dynamic>? metadata;
  final bool isIncoming;
  final String? declineReason;
  final CallQuality? quality;

  const CallSession({
    required this.id,
    required this.callerId,
    required this.callerName,
    this.callerAvatar,
    required this.receiverId,
    required this.receiverName,
    this.receiverAvatar,
    required this.type,
    required this.status,
    required this.startTime,
    this.endTime,
    this.duration,
    this.roomId,
    this.metadata,
    required this.isIncoming,
    this.declineReason,
    this.quality,
  });

  factory CallSession.fromJson(Map<String, dynamic> json) {
    return CallSession(
      id: json['id']?.toString() ?? '',
      callerId: json['callerId']?.toString() ?? '',
      callerName: json['callerName']?.toString() ?? '',
      callerAvatar: json['callerAvatar']?.toString(),
      receiverId: json['receiverId']?.toString() ?? '',
      receiverName: json['receiverName']?.toString() ?? '',
      receiverAvatar: json['receiverAvatar']?.toString(),
      type: CallType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => CallType.voice,
      ),
      status: CallStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => CallStatus.initiated,
      ),
      startTime: DateTime.tryParse(json['startTime']?.toString() ?? '') ?? DateTime.now(),
      endTime: json['endTime'] != null 
          ? DateTime.tryParse(json['endTime'].toString())
          : null,
      duration: json['duration'] != null 
          ? Duration(seconds: json['duration'].toInt())
          : null,
      roomId: json['roomId']?.toString(),
      metadata: json['metadata'] as Map<String, dynamic>?,
      isIncoming: json['isIncoming'] ?? false,
      declineReason: json['declineReason']?.toString(),
      quality: json['quality'] != null 
          ? CallQuality.values.firstWhere(
              (e) => e.name == json['quality'].toString(),
              orElse: () => CallQuality.good,
            )
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'callerId': callerId,
      'callerName': callerName,
      'callerAvatar': callerAvatar,
      'receiverId': receiverId,
      'receiverName': receiverName,
      'receiverAvatar': receiverAvatar,
      'type': type.name,
      'status': status.name,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'duration': duration?.inSeconds,
      'roomId': roomId,
      'metadata': metadata,
      'isIncoming': isIncoming,
      'declineReason': declineReason,
      'quality': quality?.name,
    };
  }

  CallSession copyWith({
    String? id,
    String? callerId,
    String? callerName,
    String? callerAvatar,
    String? receiverId,
    String? receiverName,
    String? receiverAvatar,
    CallType? type,
    CallStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    Duration? duration,
    String? roomId,
    Map<String, dynamic>? metadata,
    bool? isIncoming,
    String? declineReason,
    CallQuality? quality,
  }) {
    return CallSession(
      id: id ?? this.id,
      callerId: callerId ?? this.callerId,
      callerName: callerName ?? this.callerName,
      callerAvatar: callerAvatar ?? this.callerAvatar,
      receiverId: receiverId ?? this.receiverId,
      receiverName: receiverName ?? this.receiverName,
      receiverAvatar: receiverAvatar ?? this.receiverAvatar,
      type: type ?? this.type,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      duration: duration ?? this.duration,
      roomId: roomId ?? this.roomId,
      metadata: metadata ?? this.metadata,
      isIncoming: isIncoming ?? this.isIncoming,
      declineReason: declineReason ?? this.declineReason,
      quality: quality ?? this.quality,
    );
  }
}

enum CallType {
  voice,
  video
}

enum CallStatus {
  initiated,
  ringing,
  connecting,
  connected,
  ended,
  declined,
  missed,
  failed,
  busy
}

enum CallQuality {
  excellent,
  good,
  fair,
  poor
}

class CallHistory {
  final String id;
  final String userId;
  final String otherUserId;
  final String otherUserName;
  final String? otherUserAvatar;
  final CallType type;
  final CallStatus status;
  final DateTime timestamp;
  final Duration? duration;
  final bool isIncoming;
  final bool isRead;

  const CallHistory({
    required this.id,
    required this.userId,
    required this.otherUserId,
    required this.otherUserName,
    this.otherUserAvatar,
    required this.type,
    required this.status,
    required this.timestamp,
    this.duration,
    required this.isIncoming,
    required this.isRead,
  });

  factory CallHistory.fromJson(Map<String, dynamic> json) {
    return CallHistory(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      otherUserId: json['otherUserId']?.toString() ?? '',
      otherUserName: json['otherUserName']?.toString() ?? '',
      otherUserAvatar: json['otherUserAvatar']?.toString(),
      type: CallType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => CallType.voice,
      ),
      status: CallStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => CallStatus.ended,
      ),
      timestamp: DateTime.tryParse(json['timestamp']?.toString() ?? '') ?? DateTime.now(),
      duration: json['duration'] != null 
          ? Duration(seconds: json['duration'].toInt())
          : null,
      isIncoming: json['isIncoming'] ?? false,
      isRead: json['isRead'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'otherUserId': otherUserId,
      'otherUserName': otherUserName,
      'otherUserAvatar': otherUserAvatar,
      'type': type.name,
      'status': status.name,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration?.inSeconds,
      'isIncoming': isIncoming,
      'isRead': isRead,
    };
  }
}

class CallSettings {
  final bool voiceCallsEnabled;
  final bool videoCallsEnabled;
  final bool callWaitingEnabled;
  final String? ringtone;
  final double ringtoneVolume;
  final bool vibrationEnabled;
  final bool speakerphoneDefault;
  final VideoQuality defaultVideoQuality;
  final bool autoAnswerEnabled;
  final int autoAnswerDelay;
  final bool callRecordingEnabled;
  final bool showCallHistory;

  const CallSettings({
    required this.voiceCallsEnabled,
    required this.videoCallsEnabled,
    required this.callWaitingEnabled,
    this.ringtone,
    required this.ringtoneVolume,
    required this.vibrationEnabled,
    required this.speakerphoneDefault,
    required this.defaultVideoQuality,
    required this.autoAnswerEnabled,
    required this.autoAnswerDelay,
    required this.callRecordingEnabled,
    required this.showCallHistory,
  });

  factory CallSettings.fromJson(Map<String, dynamic> json) {
    return CallSettings(
      voiceCallsEnabled: json['voiceCallsEnabled'] ?? true,
      videoCallsEnabled: json['videoCallsEnabled'] ?? true,
      callWaitingEnabled: json['callWaitingEnabled'] ?? true,
      ringtone: json['ringtone']?.toString(),
      ringtoneVolume: json['ringtoneVolume']?.toDouble() ?? 0.8,
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      speakerphoneDefault: json['speakerphoneDefault'] ?? false,
      defaultVideoQuality: VideoQuality.values.firstWhere(
        (e) => e.name == json['defaultVideoQuality']?.toString(),
        orElse: () => VideoQuality.medium,
      ),
      autoAnswerEnabled: json['autoAnswerEnabled'] ?? false,
      autoAnswerDelay: json['autoAnswerDelay']?.toInt() ?? 3,
      callRecordingEnabled: json['callRecordingEnabled'] ?? false,
      showCallHistory: json['showCallHistory'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'voiceCallsEnabled': voiceCallsEnabled,
      'videoCallsEnabled': videoCallsEnabled,
      'callWaitingEnabled': callWaitingEnabled,
      'ringtone': ringtone,
      'ringtoneVolume': ringtoneVolume,
      'vibrationEnabled': vibrationEnabled,
      'speakerphoneDefault': speakerphoneDefault,
      'defaultVideoQuality': defaultVideoQuality.name,
      'autoAnswerEnabled': autoAnswerEnabled,
      'autoAnswerDelay': autoAnswerDelay,
      'callRecordingEnabled': callRecordingEnabled,
      'showCallHistory': showCallHistory,
    };
  }
}

enum VideoQuality {
  low,
  medium,
  high,
  hd
}

class CallStatistics {
  final int totalCalls;
  final int voiceCalls;
  final int videoCalls;
  final int incomingCalls;
  final int outgoingCalls;
  final int missedCalls;
  final Duration totalDuration;
  final Duration averageDuration;
  final Map<String, int> callsByDay;
  final Map<String, int> callsByHour;

  const CallStatistics({
    required this.totalCalls,
    required this.voiceCalls,
    required this.videoCalls,
    required this.incomingCalls,
    required this.outgoingCalls,
    required this.missedCalls,
    required this.totalDuration,
    required this.averageDuration,
    required this.callsByDay,
    required this.callsByHour,
  });

  factory CallStatistics.fromJson(Map<String, dynamic> json) {
    return CallStatistics(
      totalCalls: json['totalCalls']?.toInt() ?? 0,
      voiceCalls: json['voiceCalls']?.toInt() ?? 0,
      videoCalls: json['videoCalls']?.toInt() ?? 0,
      incomingCalls: json['incomingCalls']?.toInt() ?? 0,
      outgoingCalls: json['outgoingCalls']?.toInt() ?? 0,
      missedCalls: json['missedCalls']?.toInt() ?? 0,
      totalDuration: Duration(seconds: json['totalDuration']?.toInt() ?? 0),
      averageDuration: Duration(seconds: json['averageDuration']?.toInt() ?? 0),
      callsByDay: Map<String, int>.from(json['callsByDay'] ?? {}),
      callsByHour: Map<String, int>.from(json['callsByHour'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalCalls': totalCalls,
      'voiceCalls': voiceCalls,
      'videoCalls': videoCalls,
      'incomingCalls': incomingCalls,
      'outgoingCalls': outgoingCalls,
      'missedCalls': missedCalls,
      'totalDuration': totalDuration.inSeconds,
      'averageDuration': averageDuration.inSeconds,
      'callsByDay': callsByDay,
      'callsByHour': callsByHour,
    };
  }
}
