import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../app/theme.dart';
import '../providers/interests_provider.dart';

/// 💝 SEND INTEREST DIALOG - Beautiful Interest Sending Interface
/// Features: User Search, Custom Messages, Quick Send Options

class SendInterestDialog extends ConsumerStatefulWidget {
  final String? targetUserId;
  final String? targetUserName;

  const SendInterestDialog({
    super.key,
    this.targetUserId,
    this.targetUserName,
  });

  @override
  ConsumerState<SendInterestDialog> createState() => _SendInterestDialogState();
}

class _SendInterestDialogState extends ConsumerState<SendInterestDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  
  bool _isLoading = false;
  String? _selectedUserId;
  String? _selectedUserName;
  
  final List<String> _quickMessages = [
    'I found your profile interesting and would like to connect.',
    'Your profile caught my attention. I would love to know more about you.',
    'I believe we might be a good match. Would you like to connect?',
    'I am interested in getting to know you better.',
    'Your values and interests align with mine. Let\'s connect!',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _animationController.forward();
    
    // Pre-fill if target user is provided
    if (widget.targetUserId != null) {
      _selectedUserId = widget.targetUserId;
      _selectedUserName = widget.targetUserName;
    }
    
    // Set default message
    _messageController.text = _quickMessages.first;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _messageController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.targetUserId == null) _buildUserSearch(),
                    if (_selectedUserId != null) _buildSelectedUser(),
                    const SizedBox(height: 20),
                    _buildMessageSection(),
                    const SizedBox(height: 20),
                    _buildQuickMessages(),
                  ],
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    ).animate(controller: _animationController)
     .scale(begin: const Offset(0.8, 0.8), curve: Curves.elasticOut)
     .fadeIn();
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.favorite,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Send Interest',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Express your interest with a personal message',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
          ),
        ],
      ),
    ).animate()
     .slideY(begin: -0.5, curve: Curves.elasticOut);
  }

  Widget _buildUserSearch() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Select User',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: 'Search by name or ID...',
            prefixIcon: const Icon(Icons.search),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
          ),
          onChanged: _onSearchChanged,
        ),
        const SizedBox(height: 12),
        // Search results would go here
        _buildSearchResults(),
      ],
    ).animate()
     .slideX(begin: -0.5, delay: 200.ms, curve: Curves.elasticOut);
  }

  Widget _buildSearchResults() {
    // Placeholder for search results
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: const Text(
        'Search for users to send interest...',
        style: TextStyle(
          color: Colors.grey,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildSelectedUser() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: AppColors.primary.withValues(alpha: 0.2),
            child: const Icon(
              Icons.person,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedUserName ?? 'Selected User',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'ID: ${_selectedUserId ?? 'Unknown'}',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (widget.targetUserId == null)
            IconButton(
              onPressed: () {
                setState(() {
                  _selectedUserId = null;
                  _selectedUserName = null;
                });
              },
              icon: const Icon(Icons.close, size: 20),
            ),
        ],
      ),
    ).animate()
     .slideX(begin: 0.5, delay: 300.ms, curve: Curves.elasticOut);
  }

  Widget _buildMessageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Personal Message',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _messageController,
          maxLines: 4,
          maxLength: 200,
          decoration: InputDecoration(
            hintText: 'Write a personal message...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            counterStyle: TextStyle(color: Colors.grey[500]),
          ),
        ),
      ],
    ).animate()
     .slideY(begin: 0.5, delay: 400.ms, curve: Curves.elasticOut);
  }

  Widget _buildQuickMessages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Messages',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _quickMessages.map((message) {
            final isSelected = _messageController.text == message;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _messageController.text = message;
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? AppColors.primary.withValues(alpha: 0.1)
                      : Colors.grey[100],
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected 
                        ? AppColors.primary
                        : Colors.grey[300]!,
                  ),
                ),
                child: Text(
                  message.length > 50 
                      ? '${message.substring(0, 50)}...'
                      : message,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected 
                        ? AppColors.primary
                        : Colors.grey[700],
                    fontWeight: isSelected 
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    ).animate()
     .slideY(begin: 0.5, delay: 500.ms, curve: Curves.elasticOut);
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _canSendInterest() ? _sendInterest : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Send Interest',
                      style: TextStyle(fontWeight: FontWeight.w600),
                    ),
            ),
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 0.5, delay: 600.ms, curve: Curves.elasticOut);
  }

  bool _canSendInterest() {
    return _selectedUserId != null && 
           _messageController.text.trim().isNotEmpty &&
           !_isLoading;
  }

  void _onSearchChanged(String query) {
    // Implement user search logic
    // This would typically call an API to search for users
  }

  Future<void> _sendInterest() async {
    if (!_canSendInterest()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await ref.read(interestsProvider.notifier).sendInterest(
        targetUserId: _selectedUserId!,
        message: _messageController.text.trim(),
      );

      if (success && mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Interest sent successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to send interest. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
