/**
 * Manglik Dosha Detection and Analysis Service
 * Comprehensive Mars dosha calculation based on traditional Vedic astrology
 */

class ManglikDoshaService {
  constructor() {
    // Houses where Mars causes Manglik Dosha
    this.MANGLIK_HOUSES = [1, 2, 4, 7, 8, 12];
    
    // Dosha intensity levels
    this.DOSHA_LEVELS = {
      NONE: 0,
      LOW: 1,
      MEDIUM: 2,
      HIGH: 3,
      SEVERE: 4
    };

    // Remedial measures for different dosha levels
    this.REMEDIES = {
      LOW: [
        "Recite Hanuman Chalisa daily",
        "Fast on Tuesdays",
        "Donate red lentils on Tuesdays"
      ],
      MEDIUM: [
        "Perform Mars puja on Tuesdays",
        "Wear red coral gemstone after consultation",
        "Visit Hanuman temple regularly",
        "Donate red items to the needy"
      ],
      HIGH: [
        "Perform Mangal Shanti puja",
        "Kumbh Vivah (symbolic marriage) before actual marriage",
        "Regular Mars mantra chanting",
        "Donate blood or red items regularly"
      ],
      SEVERE: [
        "Comprehensive Mangal Dosha Nivaran puja",
        "Kumbh Vivah is essential",
        "Multiple Mars remedies simultaneously",
        "Consult qualified astrologer for personalized remedies"
      ]
    };
  }

  /**
   * Calculate Mars position in houses (simplified calculation)
   * In production, use accurate ephemeris data
   */
  calculateMarsPosition(birthDate, birthTime, birthPlace) {
    const date = new Date(birthDate);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    // Parse birth time
    const [hours, minutes] = birthTime.split(':').map(Number);
    const timeInHours = hours + minutes / 60;
    
    // Simplified Mars position calculation
    // In production, use Swiss Ephemeris or similar accurate calculation
    const daysSinceEpoch = Math.floor((date.getTime() - new Date('2000-01-01').getTime()) / (1000 * 60 * 60 * 24));
    
    // Mars orbital period is approximately 687 days
    const marsPosition = (daysSinceEpoch * 360 / 687 + timeInHours * 15) % 360;
    
    // Calculate house position (simplified - assumes equal house system)
    const ascendantDegree = this.calculateAscendant(birthDate, birthTime, birthPlace);
    const relativePosition = (marsPosition - ascendantDegree + 360) % 360;
    const houseNumber = Math.floor(relativePosition / 30) + 1;
    
    return {
      longitude: marsPosition,
      house: houseNumber,
      degree: relativePosition % 30
    };
  }

  /**
   * Calculate Ascendant (Lagna) - simplified calculation
   */
  calculateAscendant(birthDate, birthTime, birthPlace) {
    const date = new Date(birthDate);
    const [hours, minutes] = birthTime.split(':').map(Number);
    const timeInHours = hours + minutes / 60;
    
    // Simplified ascendant calculation
    // In production, use accurate sidereal time calculation
    const dayOfYear = Math.floor((date - new Date(date.getFullYear(), 0, 0)) / (1000 * 60 * 60 * 24));
    const siderealTime = (dayOfYear * 0.9856 + timeInHours * 15) % 360;
    
    return siderealTime;
  }

  /**
   * Detect Manglik Dosha based on Mars position
   */
  detectManglikDosha(birthDate, birthTime, birthPlace) {
    const marsPosition = this.calculateMarsPosition(birthDate, birthTime, birthPlace);
    const isManglik = this.MANGLIK_HOUSES.includes(marsPosition.house);
    
    if (!isManglik) {
      return {
        isManglik: false,
        doshaLevel: this.DOSHA_LEVELS.NONE,
        doshaLevelName: 'None',
        marsHouse: marsPosition.house,
        intensity: 0,
        description: 'No Manglik Dosha detected',
        remedies: [],
        compatibility: 'Compatible with both Manglik and Non-Manglik'
      };
    }

    // Calculate dosha intensity based on house and degree
    let intensity = this.calculateDoshaIntensity(marsPosition);
    let doshaLevel, doshaLevelName;

    if (intensity <= 25) {
      doshaLevel = this.DOSHA_LEVELS.LOW;
      doshaLevelName = 'Low';
    } else if (intensity <= 50) {
      doshaLevel = this.DOSHA_LEVELS.MEDIUM;
      doshaLevelName = 'Medium';
    } else if (intensity <= 75) {
      doshaLevel = this.DOSHA_LEVELS.HIGH;
      doshaLevelName = 'High';
    } else {
      doshaLevel = this.DOSHA_LEVELS.SEVERE;
      doshaLevelName = 'Severe';
    }

    return {
      isManglik: true,
      doshaLevel,
      doshaLevelName,
      marsHouse: marsPosition.house,
      marsDegree: marsPosition.degree,
      intensity,
      description: this.getDoshaDescription(marsPosition.house, doshaLevelName),
      remedies: this.REMEDIES[doshaLevelName.toUpperCase()] || [],
      compatibility: this.getCompatibilityAdvice(doshaLevelName),
      effects: this.getDoshaEffects(marsPosition.house)
    };
  }

  /**
   * Calculate dosha intensity based on Mars position
   */
  calculateDoshaIntensity(marsPosition) {
    let intensity = 50; // Base intensity

    // House-specific intensity modifiers
    const houseIntensity = {
      1: 80,  // Ascendant - High intensity
      2: 60,  // 2nd house - Medium-High
      4: 70,  // 4th house - High
      7: 90,  // 7th house - Very High (marriage house)
      8: 85,  // 8th house - Very High
      12: 65  // 12th house - Medium-High
    };

    intensity = houseIntensity[marsPosition.house] || 50;

    // Degree-based modification
    if (marsPosition.degree < 5 || marsPosition.degree > 25) {
      intensity += 10; // Early or late degrees increase intensity
    }

    return Math.min(100, intensity);
  }

  /**
   * Get dosha description based on house position
   */
  getDoshaDescription(house, level) {
    const descriptions = {
      1: `${level} Manglik Dosha in 1st house (Ascendant) - affects personality and health`,
      2: `${level} Manglik Dosha in 2nd house - affects family and speech`,
      4: `${level} Manglik Dosha in 4th house - affects home and mother`,
      7: `${level} Manglik Dosha in 7th house - directly affects marriage and spouse`,
      8: `${level} Manglik Dosha in 8th house - affects longevity and transformation`,
      12: `${level} Manglik Dosha in 12th house - affects expenses and foreign connections`
    };

    return descriptions[house] || `${level} Manglik Dosha detected`;
  }

  /**
   * Get compatibility advice based on dosha level
   */
  getCompatibilityAdvice(level) {
    const advice = {
      'Low': 'Can marry Non-Manglik with minor remedies',
      'Medium': 'Should preferably marry another Manglik or perform remedies',
      'High': 'Must marry another Manglik or perform comprehensive remedies',
      'Severe': 'Essential to marry another Manglik and perform all prescribed remedies'
    };

    return advice[level] || 'Consult qualified astrologer';
  }

  /**
   * Get specific effects of dosha based on house
   */
  getDoshaEffects(house) {
    const effects = {
      1: ['Aggressive nature', 'Health issues', 'Delayed marriage'],
      2: ['Family disputes', 'Speech problems', 'Financial issues'],
      4: ['Property disputes', 'Mother\'s health', 'Domestic problems'],
      7: ['Marital discord', 'Spouse health issues', 'Relationship problems'],
      8: ['Accidents prone', 'Sudden changes', 'Health complications'],
      12: ['Excessive expenses', 'Foreign settlement', 'Hidden enemies']
    };

    return effects[house] || ['General Mars-related challenges'];
  }

  /**
   * Check Manglik compatibility between two people
   */
  checkManglikCompatibility(person1Dosha, person2Dosha) {
    // Both non-Manglik - perfect compatibility
    if (!person1Dosha.isManglik && !person2Dosha.isManglik) {
      return {
        compatible: true,
        compatibility: 'Excellent',
        score: 100,
        advice: 'Perfect Manglik compatibility - no issues',
        remediesRequired: false
      };
    }

    // Both Manglik - good compatibility
    if (person1Dosha.isManglik && person2Dosha.isManglik) {
      const avgIntensity = (person1Dosha.intensity + person2Dosha.intensity) / 2;
      return {
        compatible: true,
        compatibility: avgIntensity > 75 ? 'Good' : 'Very Good',
        score: avgIntensity > 75 ? 80 : 90,
        advice: 'Both are Manglik - dosha gets neutralized',
        remediesRequired: avgIntensity > 75,
        recommendedRemedies: avgIntensity > 75 ? ['Joint Mars puja', 'Regular temple visits'] : []
      };
    }

    // One Manglik, one non-Manglik
    const manglikPerson = person1Dosha.isManglik ? person1Dosha : person2Dosha;
    const compatibility = this.calculateMixedCompatibility(manglikPerson);

    return {
      compatible: compatibility.score >= 60,
      compatibility: compatibility.level,
      score: compatibility.score,
      advice: compatibility.advice,
      remediesRequired: true,
      recommendedRemedies: compatibility.remedies,
      riskFactors: compatibility.risks
    };
  }

  /**
   * Calculate compatibility when one person is Manglik
   */
  calculateMixedCompatibility(manglikDosha) {
    let score = 100 - manglikDosha.intensity;
    let level, advice, remedies, risks;

    if (score >= 80) {
      level = 'Good';
      advice = 'Marriage possible with minor remedies';
      remedies = ['Basic Mars remedies', 'Regular prayers'];
      risks = ['Minor marital adjustments needed'];
    } else if (score >= 60) {
      level = 'Fair';
      advice = 'Marriage possible with comprehensive remedies';
      remedies = ['Comprehensive Mars puja', 'Gemstone therapy', 'Regular fasting'];
      risks = ['Potential marital challenges', 'Health concerns for non-Manglik spouse'];
    } else {
      level = 'Poor';
      advice = 'Marriage not recommended without extensive remedies';
      remedies = ['Kumbh Vivah essential', 'Multiple pujas required', 'Continuous remedial measures'];
      risks = ['Serious marital discord', 'Health risks', 'Relationship instability'];
    }

    return { score, level, advice, remedies, risks };
  }

  /**
   * Generate comprehensive Manglik analysis report
   */
  generateManglikReport(person1Data, person2Data) {
    const person1Dosha = this.detectManglikDosha(
      person1Data.birthDate, 
      person1Data.birthTime, 
      person1Data.birthPlace
    );
    
    const person2Dosha = this.detectManglikDosha(
      person2Data.birthDate, 
      person2Data.birthTime, 
      person2Data.birthPlace
    );

    const compatibility = this.checkManglikCompatibility(person1Dosha, person2Dosha);

    return {
      person1: {
        name: person1Data.name || 'Person 1',
        manglikAnalysis: person1Dosha
      },
      person2: {
        name: person2Data.name || 'Person 2',
        manglikAnalysis: person2Dosha
      },
      compatibility,
      overallRecommendation: this.getOverallRecommendation(compatibility),
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Get overall recommendation based on compatibility
   */
  getOverallRecommendation(compatibility) {
    if (compatibility.score >= 90) {
      return 'Highly recommended match with excellent Manglik compatibility';
    } else if (compatibility.score >= 75) {
      return 'Good match - proceed with recommended remedies';
    } else if (compatibility.score >= 60) {
      return 'Proceed with caution - comprehensive remedies essential';
    } else {
      return 'Not recommended - seek alternative matches or extensive remedial measures';
    }
  }
}

module.exports = ManglikDoshaService;
