import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/api/api_client.dart';
import '../models/referral_model.dart';

/// 🎁 Referral System Providers
/// Using existing website backend APIs

// API Client provider
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

// Referral Data Provider
final referralDataProvider = StateNotifierProvider<ReferralDataNotifier, AsyncValue<ReferralData>>(
  (ref) => ReferralDataNotifier(ref.read(apiClientProvider)),
);

class ReferralDataNotifier extends StateNotifier<AsyncValue<ReferralData>> {
  final ApiClient _apiClient;

  ReferralDataNotifier(this._apiClient) : super(const AsyncValue.loading()) {
    loadReferralData();
  }

  Future<void> loadReferralData() async {
    try {
      state = const AsyncValue.loading();
      
      // Use existing website API endpoint
      final response = await _apiClient.get('/user/referral-code');
      
      if (response['success'] == true) {
        final referralData = ReferralData.fromJson(response);
        state = AsyncValue.data(referralData);
      } else {
        throw Exception(response['message'] ?? 'Failed to load referral data');
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<bool> sendReferralInvite(ReferralInvite invite) async {
    try {
      final response = await _apiClient.post('/user/refer', invite.toJson());
      
      if (response['success'] == true) {
        // Refresh referral data after sending invite
        await loadReferralData();
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to send referral invite');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> redeemReferralCode(String referralCode) async {
    try {
      final response = await _apiClient.post('/user/redeem-referral', {
        'referralCode': referralCode,
      });
      
      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to redeem referral code');
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<void> refresh() async {
    await loadReferralData();
  }
}

// Referral Rewards Provider
final referralRewardsProvider = StateNotifierProvider<ReferralRewardsNotifier, AsyncValue<List<ReferralReward>>>(
  (ref) => ReferralRewardsNotifier(ref.read(apiClientProvider)),
);

class ReferralRewardsNotifier extends StateNotifier<AsyncValue<List<ReferralReward>>> {
  final ApiClient _apiClient;

  ReferralRewardsNotifier(this._apiClient) : super(const AsyncValue.loading()) {
    loadReferralRewards();
  }

  Future<void> loadReferralRewards() async {
    try {
      state = const AsyncValue.loading();
      
      // This would be a new endpoint to get user's referral rewards
      final response = await _apiClient.get('/user/referral-rewards');
      
      if (response['success'] == true && response['rewards'] != null) {
        final rewards = (response['rewards'] as List)
            .map((json) => ReferralReward.fromJson(json))
            .toList();
        state = AsyncValue.data(rewards);
      } else {
        // Fallback to empty list if endpoint doesn't exist yet
        state = const AsyncValue.data([]);
      }
    } catch (e, _) {
      // Fallback to empty list on error
      state = const AsyncValue.data([]);
    }
  }

  Future<void> refresh() async {
    await loadReferralRewards();
  }
}

// Share Methods Provider
final shareMethodsProvider = Provider<List<ShareMethod>>((ref) {
  return [
    ShareMethod(
      id: 'whatsapp',
      name: 'WhatsApp',
      icon: '💬',
      color: 0xFF25D366,
      description: 'Share via WhatsApp',
    ),
    ShareMethod(
      id: 'sms',
      name: 'SMS',
      icon: '📱',
      color: 0xFF007AFF,
      description: 'Send via SMS',
    ),
    ShareMethod(
      id: 'email',
      name: 'Email',
      icon: '📧',
      color: 0xFFEA4335,
      description: 'Send via Email',
    ),
    ShareMethod(
      id: 'copy',
      name: 'Copy Link',
      icon: '🔗',
      color: 0xFF6C757D,
      description: 'Copy referral link',
    ),
    ShareMethod(
      id: 'more',
      name: 'More',
      icon: '📤',
      color: 0xFF17A2B8,
      description: 'More sharing options',
    ),
  ];
});

class ShareMethod {
  final String id;
  final String name;
  final String icon;
  final int color;
  final String description;

  ShareMethod({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.description,
  });
}

// Referral Statistics Provider
final referralStatsProvider = Provider<ReferralStatsCalculator>((ref) {
  return ReferralStatsCalculator();
});

class ReferralStatsCalculator {
  double calculateSuccessRate(ReferralStats stats) {
    if (stats.totalReferrals == 0) return 0.0;
    return (stats.successfulReferrals / stats.totalReferrals) * 100;
  }

  String getRewardTypeDisplayName(String rewardType) {
    switch (rewardType.toLowerCase()) {
      case 'cash':
        return 'Cash Reward';
      case 'subscription_days':
        return 'Premium Days';
      case 'premium_features':
        return 'Premium Features';
      default:
        return 'Reward';
    }
  }

  String getRewardTypeIcon(String rewardType) {
    switch (rewardType.toLowerCase()) {
      case 'cash':
        return '💰';
      case 'subscription_days':
        return '⭐';
      case 'premium_features':
        return '🎁';
      default:
        return '🎉';
    }
  }

  String formatRewardAmount(String rewardType, double amount) {
    switch (rewardType.toLowerCase()) {
      case 'cash':
        return '₹${amount.toStringAsFixed(0)}';
      case 'subscription_days':
        return '${amount.toInt()} days';
      case 'premium_features':
        return 'Premium Access';
      default:
        return amount.toString();
    }
  }

  String getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return '#FFA726'; // Orange
      case 'completed':
        return '#66BB6A'; // Green
      case 'rewarded':
        return '#42A5F5'; // Blue
      default:
        return '#9E9E9E'; // Grey
    }
  }

  String getStatusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return '⏳';
      case 'completed':
        return '✅';
      case 'rewarded':
        return '🎉';
      default:
        return '❓';
    }
  }

  List<String> generateShareMessages(ReferralData referralData) {
    final program = referralData.program;
    final rewardAmount = formatRewardAmount(
      program.refereeRewardType,
      program.refereeRewardAmount,
    );

    return [
      'Join Vaivahik and get $rewardAmount! Use my referral code: ${referralData.referralCode}\n\n${referralData.referralLink}',
      'Hey! I found an amazing matrimony app - Vaivahik. Join using my code ${referralData.referralCode} and get $rewardAmount as welcome bonus!\n\n${referralData.referralLink}',
      'Looking for your perfect match? Join Vaivahik with my referral code ${referralData.referralCode} and get $rewardAmount!\n\n${referralData.referralLink}',
      'Vaivahik helped me find great matches! Join with code ${referralData.referralCode} and get $rewardAmount. Both of us will benefit!\n\n${referralData.referralLink}',
    ];
  }

  String getDefaultShareMessage(ReferralData referralData) {
    final messages = generateShareMessages(referralData);
    return messages.first;
  }
}

// Referral Code Validation Provider
final referralCodeValidatorProvider = Provider<ReferralCodeValidator>((ref) {
  return ReferralCodeValidator();
});

class ReferralCodeValidator {
  bool isValidReferralCode(String code) {
    // Basic validation - should be 8-12 characters, alphanumeric
    if (code.isEmpty || code.length < 6 || code.length > 12) {
      return false;
    }
    
    // Check if contains only alphanumeric characters
    final regex = RegExp(r'^[A-Z0-9]+$');
    return regex.hasMatch(code.toUpperCase());
  }

  String? validateReferralCode(String code) {
    if (code.isEmpty) {
      return 'Please enter a referral code';
    }
    
    if (!isValidReferralCode(code)) {
      return 'Invalid referral code format';
    }
    
    return null;
  }

  String formatReferralCode(String code) {
    // Format code to uppercase and remove spaces
    return code.toUpperCase().replaceAll(' ', '');
  }
}
