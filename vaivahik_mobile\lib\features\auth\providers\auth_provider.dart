import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_model.dart';
import '../../../core/services/storage_service.dart';
import '../../../core/api/api_client.dart';

// Auth State
class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final UserModel? user;
  final String? error;

  const AuthState({
    this.isAuthenticated = false,
    this.isLoading = false,
    this.user,
    this.error,
  });

  AuthState copyWith({
    bool? isAuthenticated,
    bool? isLoading,
    UserModel? user,
    String? error,
  }) {
    return AuthState(
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      isLoading: isLoading ?? this.isLoading,
      user: user ?? this.user,
      error: error ?? this.error,
    );
  }
}

// Auth Notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final ApiClient _apiClient = ApiClient();

  AuthNotifier() : super(const AuthState()) {
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final token = await StorageService.getSecureString(StorageService.keyAuthToken);
      final isLoggedIn = StorageService.getBool(StorageService.keyIsLoggedIn) ?? false;
      
      if (token != null && isLoggedIn) {
        try {
          // Validate token with backend
          final response = await _apiClient.get('/auth/me');

          if (response['success'] == true) {
            final userData = response['data']['user'];
            final user = UserModel.fromJson(userData);

            state = state.copyWith(
              isAuthenticated: true,
              isLoading: false,
              user: user,
            );
          } else {
            // Token is invalid, clear auth data
            await logout();
          }
        } catch (e) {
          // Token validation failed, clear auth data
          await logout();
        }
      } else {
        state = state.copyWith(
          isAuthenticated: false,
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Real API call to backend
      final response = await _apiClient.post('/auth/login', {
        'email': email,
        'password': password,
      });

      if (response['success'] == true) {
        final userData = response['data']['user'];
        final token = response['data']['token'];

        final user = UserModel.fromJson(userData);

        // Store auth data
        await StorageService.setSecureString(StorageService.keyAuthToken, token);
        await StorageService.setBool(StorageService.keyIsLoggedIn, true);
        await StorageService.setString(StorageService.keyUserEmail, email);
        await StorageService.setString(StorageService.keyUserId, user.id);
        await StorageService.setString(StorageService.keyUserData, jsonEncode(user.toJson()));

        state = state.copyWith(
          isAuthenticated: true,
          isLoading: false,
          user: user,
        );

        return true;
      } else {
        throw Exception(response['message'] ?? 'Login failed');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString().replaceAll('Exception: ', ''),
      );
      return false;
    }
  }

  Future<bool> register({
    required String email,
    required String password,
    required String name,
    required String phone,
  }) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Real API call to backend
      final response = await _apiClient.post('/auth/register', {
        'email': email,
        'password': password,
        'name': name,
        'phone': phone,
      });

      if (response['success'] == true) {
        final userData = response['data']['user'];
        final token = response['data']['token'];

        final user = UserModel.fromJson(userData);

        // Store auth data
        await StorageService.setSecureString(StorageService.keyAuthToken, token);
        await StorageService.setBool(StorageService.keyIsLoggedIn, true);
        await StorageService.setString(StorageService.keyUserEmail, email);
        await StorageService.setString(StorageService.keyUserId, user.id);
        await StorageService.setString(StorageService.keyUserData, jsonEncode(user.toJson()));

        state = state.copyWith(
          isAuthenticated: true,
          isLoading: false,
          user: user,
        );

        return true;
      } else {
        throw Exception(response['message'] ?? 'Registration failed');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  Future<void> logout() async {
    state = state.copyWith(isLoading: true);
    
    try {
      // Clear stored auth data
      await StorageService.deleteSecureString(StorageService.keyAuthToken);
      await StorageService.setBool(StorageService.keyIsLoggedIn, false);
      await StorageService.remove(StorageService.keyUserEmail);
      await StorageService.remove(StorageService.keyUserPhone);
      
      state = state.copyWith(
        isAuthenticated: false,
        isLoading: false,
        user: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<bool> verifyOtp(String otp) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Real API call to backend
      final response = await _apiClient.post('/auth/verify-otp', {
        'otp': otp,
      });

      if (response['success'] == true) {
        state = state.copyWith(
          isLoading: false,
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: response['message'] ?? 'Invalid OTP',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  Future<bool> forgotPassword(String email) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // Real API call to backend
      final response = await _apiClient.post('/auth/forgot-password', {
        'email': email,
      });

      if (response['success'] == true) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to send reset email');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

// Helper providers
final isAuthenticatedProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isAuthenticated;
});

final currentUserProvider = Provider<UserModel?>((ref) {
  return ref.watch(authProvider).user;
});

final authLoadingProvider = Provider<bool>((ref) {
  return ref.watch(authProvider).isLoading;
});

final authErrorProvider = Provider<String?>((ref) {
  return ref.watch(authProvider).error;
});
