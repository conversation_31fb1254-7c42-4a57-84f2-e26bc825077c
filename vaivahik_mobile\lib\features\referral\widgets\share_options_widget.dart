import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/theme/app_theme.dart';
import '../models/referral_model.dart';
import '../providers/referral_provider.dart';

/// 📤 Share Options Widget - Multiple sharing methods
/// WhatsApp, SMS, Email, Copy Link, and More options

class ShareOptionsWidget extends ConsumerWidget {
  final ReferralData referralData;

  const ShareOptionsWidget({
    super.key,
    required this.referralData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final shareMethods = ref.watch(shareMethodsProvider);
    final statsCalculator = ref.read(referralStatsProvider);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Row(
            children: [
              Icon(
                Icons.share,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              SizedBox(width: 12),
              Text(
                'Share with Friends',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ).animate().fadeIn(),
          
          const SizedBox(height: 8),
          
          Text(
            'Choose how you want to share your referral',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 100)),
          
          const SizedBox(height: 20),
          
          // Share Methods Grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 1,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: shareMethods.length,
            itemBuilder: (context, index) {
              final method = shareMethods[index];
              return _ShareMethodCard(
                method: method,
                referralData: referralData,
                onTap: () => _handleShare(context, method, referralData, statsCalculator),
              ).animate().fadeIn(delay: Duration(milliseconds: index * 100)).scale();
            },
          ),
        ],
      ),
    );
  }

  Future<void> _handleShare(
    BuildContext context,
    ShareMethod method,
    ReferralData referralData,
    ReferralStatsCalculator statsCalculator,
  ) async {
    final message = statsCalculator.getDefaultShareMessage(referralData);

    switch (method.id) {
      case 'whatsapp':
        await _shareViaWhatsApp(message);
        break;
      case 'sms':
        await _shareViaSMS(message);
        break;
      case 'email':
        await _shareViaEmail(context, referralData, message);
        break;
      case 'copy':
        await _copyToClipboard(context, referralData.referralLink);
        break;
      case 'more':
        await _showMoreOptions(context, referralData, message);
        break;
    }
  }

  Future<void> _shareViaWhatsApp(String message) async {
    final encodedMessage = Uri.encodeComponent(message);
    final url = 'https://wa.me/?text=$encodedMessage';
    
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error sharing via WhatsApp: $e');
    }
  }

  Future<void> _shareViaSMS(String message) async {
    final encodedMessage = Uri.encodeComponent(message);
    final url = 'sms:?body=$encodedMessage';
    
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      }
    } catch (e) {
      debugPrint('Error sharing via SMS: $e');
    }
  }

  Future<void> _shareViaEmail(BuildContext context, ReferralData referralData, String message) async {
    final subject = Uri.encodeComponent('Join Vaivahik - Get ${referralData.program.refereeRewardAmount} Reward!');
    final body = Uri.encodeComponent(message);
    final url = 'mailto:?subject=$subject&body=$body';
    
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url));
      }
    } catch (e) {
      debugPrint('Error sharing via Email: $e');
    }
  }

  Future<void> _copyToClipboard(BuildContext context, String text) async {
    try {
      await Clipboard.setData(ClipboardData(text: text));
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Referral link copied to clipboard!'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to copy link'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showMoreOptions(BuildContext context, ReferralData referralData, String message) async {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _MoreShareOptionsSheet(
        referralData: referralData,
        message: message,
      ),
    );
  }
}

class _ShareMethodCard extends StatelessWidget {
  final ShareMethod method;
  final ReferralData referralData;
  final VoidCallback onTap;

  const _ShareMethodCard({
    required this.method,
    required this.referralData,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Color(method.color).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Color(method.color).withValues(alpha: 0.2),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              method.icon,
              style: const TextStyle(fontSize: 28),
            ),
            const SizedBox(height: 8),
            Text(
              method.name,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(method.color),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class _MoreShareOptionsSheet extends StatelessWidget {
  final ReferralData referralData;
  final String message;

  const _MoreShareOptionsSheet({
    required this.referralData,
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                const Text(
                  'More Sharing Options',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Share your referral code and link',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          
          // Options
          _ShareOption(
            icon: Icons.facebook,
            title: 'Facebook',
            subtitle: 'Share on Facebook',
            color: const Color(0xFF1877F2),
            onTap: () => _shareOnFacebook(context),
          ),
          
          _ShareOption(
            icon: Icons.alternate_email,
            title: 'Twitter',
            subtitle: 'Share on Twitter',
            color: const Color(0xFF1DA1F2),
            onTap: () => _shareOnTwitter(context),
          ),
          
          _ShareOption(
            icon: Icons.link,
            title: 'Copy Link',
            subtitle: 'Copy referral link',
            color: Colors.grey[600]!,
            onTap: () => _copyLink(context),
          ),
          
          _ShareOption(
            icon: Icons.qr_code,
            title: 'QR Code',
            subtitle: 'Generate QR code',
            color: Colors.purple,
            onTap: () => _showQRCode(context),
          ),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  void _shareOnFacebook(BuildContext context) {
    final encodedUrl = Uri.encodeComponent(referralData.referralLink);
    final url = 'https://www.facebook.com/sharer/sharer.php?u=$encodedUrl';
    launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    Navigator.pop(context);
  }

  void _shareOnTwitter(BuildContext context) {
    final text = Uri.encodeComponent('Join Vaivahik with my referral code ${referralData.referralCode}');
    final encodedUrl = Uri.encodeComponent(referralData.referralLink);
    final url = 'https://twitter.com/intent/tweet?text=$text&url=$encodedUrl';
    launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    Navigator.pop(context);
  }

  void _copyLink(BuildContext context) {
    Clipboard.setData(ClipboardData(text: referralData.referralLink));
    Navigator.pop(context);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Link copied to clipboard!')),
    );
  }

  void _showQRCode(BuildContext context) {
    Navigator.pop(context);
    // Show QR code dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('QR Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'QR Code\nWould appear here',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.grey),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text('Scan this QR code to get the referral link'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class _ShareOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _ShareOption({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: color,
          size: 20,
        ),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
      onTap: onTap,
    );
  }
}
