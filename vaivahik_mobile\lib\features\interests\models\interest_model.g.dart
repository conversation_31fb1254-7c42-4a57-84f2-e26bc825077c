// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interest_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_InterestModel _$InterestModelFromJson(Map<String, dynamic> json) =>
    _InterestModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      targetUserId: json['targetUserId'] as String,
      message: json['message'] as String?,
      status: json['status'] as String? ?? 'PENDING',
      responseMessage: json['responseMessage'] as String?,
      respondedAt: json['respondedAt'] == null
          ? null
          : DateTime.parse(json['respondedAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      user: json['user'] == null
          ? null
          : UserProfileModel.fromJson(json['user'] as Map<String, dynamic>),
      targetUser: json['targetUser'] == null
          ? null
          : UserProfileModel.fromJson(
              json['targetUser'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InterestModelToJson(_InterestModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'targetUserId': instance.targetUserId,
      'message': instance.message,
      'status': instance.status,
      'responseMessage': instance.responseMessage,
      'respondedAt': instance.respondedAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'user': instance.user,
      'targetUser': instance.targetUser,
    };

_UserProfileModel _$UserProfileModelFromJson(Map<String, dynamic> json) =>
    _UserProfileModel(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String?,
      age: (json['age'] as num?)?.toInt(),
      city: json['city'] as String?,
      state: json['state'] as String?,
      occupation: json['occupation'] as String?,
      profilePicUrl: json['profilePicUrl'] as String?,
      education: json['education'] as String?,
      height: (json['height'] as num?)?.toInt(),
      religion: json['religion'] as String?,
      caste: json['caste'] as String?,
      motherTongue: json['motherTongue'] as String?,
      isVerified: json['isVerified'] as bool?,
      isPremium: json['isPremium'] as bool?,
      lastSeen: json['lastSeen'] == null
          ? null
          : DateTime.parse(json['lastSeen'] as String),
      isOnline: json['isOnline'] as bool?,
    );

Map<String, dynamic> _$UserProfileModelToJson(_UserProfileModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'age': instance.age,
      'city': instance.city,
      'state': instance.state,
      'occupation': instance.occupation,
      'profilePicUrl': instance.profilePicUrl,
      'education': instance.education,
      'height': instance.height,
      'religion': instance.religion,
      'caste': instance.caste,
      'motherTongue': instance.motherTongue,
      'isVerified': instance.isVerified,
      'isPremium': instance.isPremium,
      'lastSeen': instance.lastSeen?.toIso8601String(),
      'isOnline': instance.isOnline,
    };

_SendInterestRequest _$SendInterestRequestFromJson(Map<String, dynamic> json) =>
    _SendInterestRequest(
      targetUserId: json['targetUserId'] as String,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SendInterestRequestToJson(
        _SendInterestRequest instance) =>
    <String, dynamic>{
      'targetUserId': instance.targetUserId,
      'message': instance.message,
    };

_RespondInterestRequest _$RespondInterestRequestFromJson(
        Map<String, dynamic> json) =>
    _RespondInterestRequest(
      interestId: json['interestId'] as String,
      response: json['response'] as String,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$RespondInterestRequestToJson(
        _RespondInterestRequest instance) =>
    <String, dynamic>{
      'interestId': instance.interestId,
      'response': instance.response,
      'message': instance.message,
    };

_ActivityStatsModel _$ActivityStatsModelFromJson(Map<String, dynamic> json) =>
    _ActivityStatsModel(
      interestsSent: (json['interestsSent'] as num?)?.toInt() ?? 0,
      interestsReceived: (json['interestsReceived'] as num?)?.toInt() ?? 0,
      profileViews: (json['profileViews'] as num?)?.toInt() ?? 0,
      totalMatches: (json['totalMatches'] as num?)?.toInt() ?? 0,
      acceptedInterests: (json['acceptedInterests'] as num?)?.toInt() ?? 0,
      pendingInterests: (json['pendingInterests'] as num?)?.toInt() ?? 0,
      declinedInterests: (json['declinedInterests'] as num?)?.toInt() ?? 0,
      weeklyViews: (json['weeklyViews'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      weeklyInterests: (json['weeklyInterests'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      successRate: (json['successRate'] as num?)?.toDouble() ?? 0.0,
      todayViews: (json['todayViews'] as num?)?.toInt() ?? 0,
      todayInterests: (json['todayInterests'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ActivityStatsModelToJson(_ActivityStatsModel instance) =>
    <String, dynamic>{
      'interestsSent': instance.interestsSent,
      'interestsReceived': instance.interestsReceived,
      'profileViews': instance.profileViews,
      'totalMatches': instance.totalMatches,
      'acceptedInterests': instance.acceptedInterests,
      'pendingInterests': instance.pendingInterests,
      'declinedInterests': instance.declinedInterests,
      'weeklyViews': instance.weeklyViews,
      'weeklyInterests': instance.weeklyInterests,
      'successRate': instance.successRate,
      'todayViews': instance.todayViews,
      'todayInterests': instance.todayInterests,
    };

_InterestActivityModel _$InterestActivityModelFromJson(
        Map<String, dynamic> json) =>
    _InterestActivityModel(
      id: json['id'] as String,
      profileId: json['profileId'] as String,
      profileName: json['profileName'] as String,
      profilePhoto: json['profilePhoto'] as String?,
      age: (json['age'] as num).toInt(),
      location: json['location'] as String,
      type: $enumDecode(_$InterestTypeEnumMap, json['type']),
      status: $enumDecode(_$InterestStatusEnumMap, json['status']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      message: json['message'] as String?,
      responseMessage: json['responseMessage'] as String?,
      isRead: json['isRead'] as bool?,
      isPremium: json['isPremium'] as bool?,
      occupation: json['occupation'] as String?,
      education: json['education'] as String?,
    );

Map<String, dynamic> _$InterestActivityModelToJson(
        _InterestActivityModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'profileId': instance.profileId,
      'profileName': instance.profileName,
      'profilePhoto': instance.profilePhoto,
      'age': instance.age,
      'location': instance.location,
      'type': _$InterestTypeEnumMap[instance.type]!,
      'status': _$InterestStatusEnumMap[instance.status]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'message': instance.message,
      'responseMessage': instance.responseMessage,
      'isRead': instance.isRead,
      'isPremium': instance.isPremium,
      'occupation': instance.occupation,
      'education': instance.education,
    };

const _$InterestTypeEnumMap = {
  InterestType.sent: 'sent',
  InterestType.received: 'received',
};

const _$InterestStatusEnumMap = {
  InterestStatus.pending: 'pending',
  InterestStatus.accepted: 'accepted',
  InterestStatus.declined: 'declined',
  InterestStatus.expired: 'expired',
};

_InterestListResponse _$InterestListResponseFromJson(
        Map<String, dynamic> json) =>
    _InterestListResponse(
      success: json['success'] as bool,
      data: InterestListData.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$InterestListResponseToJson(
        _InterestListResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
      'message': instance.message,
    };

_InterestListData _$InterestListDataFromJson(Map<String, dynamic> json) =>
    _InterestListData(
      received: (json['received'] as List<dynamic>?)
              ?.map((e) => InterestModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      sent: (json['sent'] as List<dynamic>?)
              ?.map((e) => InterestModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      totalReceived: (json['totalReceived'] as num?)?.toInt() ?? 0,
      totalSent: (json['totalSent'] as num?)?.toInt() ?? 0,
      pendingReceived: (json['pendingReceived'] as num?)?.toInt() ?? 0,
      pendingSent: (json['pendingSent'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$InterestListDataToJson(_InterestListData instance) =>
    <String, dynamic>{
      'received': instance.received,
      'sent': instance.sent,
      'totalReceived': instance.totalReceived,
      'totalSent': instance.totalSent,
      'pendingReceived': instance.pendingReceived,
      'pendingSent': instance.pendingSent,
    };

_InterestDashboardResponse _$InterestDashboardResponseFromJson(
        Map<String, dynamic> json) =>
    _InterestDashboardResponse(
      success: json['success'] as bool,
      data:
          InterestDashboardData.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String?,
    );

Map<String, dynamic> _$InterestDashboardResponseToJson(
        _InterestDashboardResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'data': instance.data,
      'message': instance.message,
    };

_InterestDashboardData _$InterestDashboardDataFromJson(
        Map<String, dynamic> json) =>
    _InterestDashboardData(
      stats: ActivityStatsModel.fromJson(json['stats'] as Map<String, dynamic>),
      recentActivities: (json['recentActivities'] as List<dynamic>?)
              ?.map((e) =>
                  InterestActivityModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      pendingInterests: (json['pendingInterests'] as List<dynamic>?)
              ?.map((e) => InterestModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      suggestedProfiles: (json['suggestedProfiles'] as List<dynamic>?)
              ?.map((e) => UserProfileModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$InterestDashboardDataToJson(
        _InterestDashboardData instance) =>
    <String, dynamic>{
      'stats': instance.stats,
      'recentActivities': instance.recentActivities,
      'pendingInterests': instance.pendingInterests,
      'suggestedProfiles': instance.suggestedProfiles,
    };
