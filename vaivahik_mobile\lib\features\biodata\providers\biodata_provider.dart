import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/biodata_models.dart';
import '../services/biodata_service.dart';

// Service provider
final biodataServiceProvider = Provider<BiodataService>((ref) {
  return BiodataService();
});

// Templates providers
final biodataTemplatesProvider = FutureProvider.family<List<BiodataTemplate>, String?>((ref, gender) async {
  final service = ref.read(biodataServiceProvider);
  return service.getBiodataTemplates(gender: gender);
});

final maleTemplatesProvider = FutureProvider<List<BiodataTemplate>>((ref) async {
  final service = ref.read(biodataServiceProvider);
  final templates = await service.getBiodataTemplates(gender: 'male');
  return templates.where((template) => template.genderOrientation == 'male').toList();
});

final femaleTemplatesProvider = FutureProvider<List<BiodataTemplate>>((ref) async {
  final service = ref.read(biodataServiceProvider);
  final templates = await service.getBiodataTemplates(gender: 'female');
  return templates.where((template) => template.genderOrientation == 'female').toList();
});

final biodataTemplateByIdProvider = FutureProvider.family<BiodataTemplate, String>((ref, templateId) async {
  final service = ref.read(biodataServiceProvider);
  return service.getBiodataTemplateById(templateId);
});

// Settings provider
final biodataSettingsProvider = StateNotifierProvider<BiodataSettingsNotifier, AsyncValue<BiodataSettings>>((ref) {
  return BiodataSettingsNotifier(ref.read(biodataServiceProvider));
});

class BiodataSettingsNotifier extends StateNotifier<AsyncValue<BiodataSettings>> {
  final BiodataService _service;

  BiodataSettingsNotifier(this._service) : super(const AsyncValue.loading()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    try {
      final settings = await _service.getBiodataSettings();
      state = AsyncValue.data(settings);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> updateSettings(BiodataSettings settings) async {
    try {
      state = const AsyncValue.loading();
      final updatedSettings = await _service.updateBiodataSettings(settings);
      state = AsyncValue.data(updatedSettings);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  Future<void> refreshSettings() async {
    await _loadSettings();
  }
}

// Generation provider
final biodataGenerationProvider = StateNotifierProvider<BiodataGenerationNotifier, BiodataGenerationState>((ref) {
  return BiodataGenerationNotifier(ref.read(biodataServiceProvider));
});

class BiodataGenerationNotifier extends StateNotifier<BiodataGenerationState> {
  final BiodataService _service;

  BiodataGenerationNotifier(this._service) : super(const BiodataGenerationState());

  Future<void> generateBiodata({
    required String templateId,
    Map<String, dynamic>? customData,
    bool preview = false,
  }) async {
    state = state.copyWith(isGenerating: true, error: null);

    try {
      final result = await _service.generateBiodata(
        templateId: templateId,
        customData: customData,
        preview: preview,
      );

      if (result.success) {
        state = state.copyWith(
          isGenerating: false,
          lastGenerationResult: result,
          error: null,
        );
      } else {
        state = state.copyWith(
          isGenerating: false,
          error: result.message ?? 'Generation failed',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  Future<void> purchaseTemplate(String templateId) async {
    state = state.copyWith(isPurchasing: true, error: null);

    try {
      final result = await _service.purchaseBiodataTemplate(templateId);

      if (result.success) {
        state = state.copyWith(
          isPurchasing: false,
          lastPurchaseResult: result,
          error: null,
        );
      } else {
        state = state.copyWith(
          isPurchasing: false,
          error: result.message ?? 'Purchase failed',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isPurchasing: false,
        error: e.toString(),
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  void reset() {
    state = const BiodataGenerationState();
  }
}

class BiodataGenerationState {
  final bool isGenerating;
  final bool isPurchasing;
  final BiodataGenerationResult? lastGenerationResult;
  final PurchaseResult? lastPurchaseResult;
  final String? error;

  const BiodataGenerationState({
    this.isGenerating = false,
    this.isPurchasing = false,
    this.lastGenerationResult,
    this.lastPurchaseResult,
    this.error,
  });

  BiodataGenerationState copyWith({
    bool? isGenerating,
    bool? isPurchasing,
    BiodataGenerationResult? lastGenerationResult,
    PurchaseResult? lastPurchaseResult,
    String? error,
  }) {
    return BiodataGenerationState(
      isGenerating: isGenerating ?? this.isGenerating,
      isPurchasing: isPurchasing ?? this.isPurchasing,
      lastGenerationResult: lastGenerationResult ?? this.lastGenerationResult,
      lastPurchaseResult: lastPurchaseResult ?? this.lastPurchaseResult,
      error: error,
    );
  }
}

// History provider
final biodataHistoryProvider = FutureProvider<List<GeneratedBiodata>>((ref) async {
  final service = ref.read(biodataServiceProvider);
  return service.getBiodataHistory();
});

// Purchased templates provider
final purchasedTemplatesProvider = FutureProvider<List<BiodataTemplate>>((ref) async {
  final service = ref.read(biodataServiceProvider);
  return service.getPurchasedTemplates();
});

// Analytics provider
final biodataAnalyticsProvider = FutureProvider<BiodataAnalytics>((ref) async {
  final service = ref.read(biodataServiceProvider);
  return service.getBiodataAnalytics();
});

// Selected template provider
final selectedTemplateProvider = StateProvider<BiodataTemplate?>((ref) => null);

// Selected gender provider
final selectedGenderProvider = StateProvider<String>((ref) => 'male');

// Template customization provider
final templateCustomizationProvider = StateNotifierProvider<TemplateCustomizationNotifier, Map<String, dynamic>>((ref) {
  return TemplateCustomizationNotifier();
});

class TemplateCustomizationNotifier extends StateNotifier<Map<String, dynamic>> {
  TemplateCustomizationNotifier() : super({});

  void updateCustomization(String key, dynamic value) {
    state = {...state, key: value};
  }

  void updateMultiple(Map<String, dynamic> updates) {
    state = {...state, ...updates};
  }

  void reset() {
    state = {};
  }

  void loadFromTemplate(BiodataTemplate template) {
    state = Map<String, dynamic>.from(template.defaultSettings);
  }
}

// Template filter provider
final templateFilterProvider = StateNotifierProvider<TemplateFilterNotifier, TemplateFilter>((ref) {
  return TemplateFilterNotifier();
});

class TemplateFilterNotifier extends StateNotifier<TemplateFilter> {
  TemplateFilterNotifier() : super(const TemplateFilter());

  void updateFilter({
    String? category,
    BiodataType? type,
    bool? isPremium,
    double? maxPrice,
    String? sortBy,
  }) {
    state = state.copyWith(
      category: category,
      type: type,
      isPremium: isPremium,
      maxPrice: maxPrice,
      sortBy: sortBy,
    );
  }

  void reset() {
    state = const TemplateFilter();
  }
}

class TemplateFilter {
  final String? category;
  final BiodataType? type;
  final bool? isPremium;
  final double? maxPrice;
  final String sortBy;

  const TemplateFilter({
    this.category,
    this.type,
    this.isPremium,
    this.maxPrice,
    this.sortBy = 'name',
  });

  TemplateFilter copyWith({
    String? category,
    BiodataType? type,
    bool? isPremium,
    double? maxPrice,
    String? sortBy,
  }) {
    return TemplateFilter(
      category: category ?? this.category,
      type: type ?? this.type,
      isPremium: isPremium ?? this.isPremium,
      maxPrice: maxPrice ?? this.maxPrice,
      sortBy: sortBy ?? this.sortBy,
    );
  }
}
