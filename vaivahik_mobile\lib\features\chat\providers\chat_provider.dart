import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/api/api_client.dart';
import '../services/chat_service.dart';
import '../models/chat_models.dart';

// API Client provider (if not already defined elsewhere)
final chatApiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

// Chat service provider
final chatServiceProvider = Provider<ChatService>((ref) {
  final apiClient = ref.read(chatApiClientProvider);
  return ChatService(apiClient);
});

// Chat rooms state
class ChatRoomsState {
  final List<ChatRoom> rooms;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int currentPage;

  const ChatRoomsState({
    this.rooms = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 1,
  });

  ChatRoomsState copyWith({
    List<ChatRoom>? rooms,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentPage,
  }) {
    return ChatRoomsState(
      rooms: rooms ?? this.rooms,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

// Chat rooms provider
class ChatRoomsNotifier extends StateNotifier<ChatRoomsState> {
  final ChatService _chatService;

  ChatRoomsNotifier(this._chatService) : super(const ChatRoomsState());

  Future<void> loadChatRooms({bool refresh = false}) async {
    if (refresh) {
      state = const ChatRoomsState(isLoading: true);
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final rooms = await _chatService.getChatRooms(page: page);

      if (refresh) {
        state = ChatRoomsState(
          rooms: rooms,
          isLoading: false,
          hasMore: rooms.length >= 20,
          currentPage: 1,
        );
      } else {
        state = state.copyWith(
          rooms: [...state.rooms, ...rooms],
          isLoading: false,
          hasMore: rooms.length >= 20,
          currentPage: state.currentPage + 1,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> refreshChatRooms() async {
    await loadChatRooms(refresh: true);
  }

  void updateChatRoom(ChatRoom updatedRoom) {
    final updatedRooms = state.rooms.map((room) {
      return room.id == updatedRoom.id ? updatedRoom : room;
    }).toList();
    state = state.copyWith(rooms: updatedRooms);
  }

  void removeChatRoom(String roomId) {
    final updatedRooms = state.rooms.where((room) => room.id != roomId).toList();
    state = state.copyWith(rooms: updatedRooms);
  }
}

final chatRoomsProvider = StateNotifierProvider<ChatRoomsNotifier, ChatRoomsState>((ref) {
  final chatService = ref.read(chatServiceProvider);
  return ChatRoomsNotifier(chatService);
});

// Messages state for a specific chat room
class MessagesState {
  final List<ChatMessage> messages;
  final bool isLoading;
  final String? error;
  final bool hasMore;
  final int currentPage;
  final bool isSending;

  const MessagesState({
    this.messages = const [],
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.currentPage = 1,
    this.isSending = false,
  });

  MessagesState copyWith({
    List<ChatMessage>? messages,
    bool? isLoading,
    String? error,
    bool? hasMore,
    int? currentPage,
    bool? isSending,
  }) {
    return MessagesState(
      messages: messages ?? this.messages,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      hasMore: hasMore ?? this.hasMore,
      currentPage: currentPage ?? this.currentPage,
      isSending: isSending ?? this.isSending,
    );
  }
}

// Messages provider for a specific chat room
class MessagesNotifier extends StateNotifier<MessagesState> {
  final ChatService _chatService;
  final String chatRoomId;

  MessagesNotifier(this._chatService, this.chatRoomId) : super(const MessagesState());

  Future<void> loadMessages({bool refresh = false}) async {
    if (refresh) {
      state = const MessagesState(isLoading: true);
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final messages = await _chatService.getMessages(chatRoomId, page: page);

      if (refresh) {
        state = MessagesState(
          messages: messages.reversed.toList(), // Reverse to show latest at bottom
          isLoading: false,
          hasMore: messages.length >= 50,
          currentPage: 1,
        );
      } else {
        // Insert older messages at the beginning
        state = state.copyWith(
          messages: [...messages.reversed, ...state.messages],
          isLoading: false,
          hasMore: messages.length >= 50,
          currentPage: state.currentPage + 1,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> sendMessage(String content, {String? replyToId}) async {
    state = state.copyWith(isSending: true, error: null);
    
    try {
      final message = await _chatService.sendMessage(chatRoomId, content, replyToId: replyToId);
      
      // Add the new message to the end of the list
      state = state.copyWith(
        messages: [...state.messages, message],
        isSending: false,
      );
    } catch (e) {
      state = state.copyWith(
        isSending: false,
        error: e.toString(),
      );
    }
  }

  Future<void> sendMediaMessage(File file, MessageType type, {String? caption, String? replyToId}) async {
    state = state.copyWith(isSending: true, error: null);
    
    try {
      final message = await _chatService.sendMediaMessage(chatRoomId, file, type, caption: caption, replyToId: replyToId);
      
      // Add the new message to the end of the list
      state = state.copyWith(
        messages: [...state.messages, message],
        isSending: false,
      );
    } catch (e) {
      state = state.copyWith(
        isSending: false,
        error: e.toString(),
      );
    }
  }

  Future<void> markMessagesAsRead(List<String> messageIds) async {
    try {
      await _chatService.markMessagesAsRead(chatRoomId, messageIds);
      
      // Update message status to read
      final updatedMessages = state.messages.map((message) {
        if (messageIds.contains(message.id)) {
          return message.copyWith(status: MessageStatus.read);
        }
        return message;
      }).toList();
      
      state = state.copyWith(messages: updatedMessages);
    } catch (e) {
      // Ignore read receipt errors
    }
  }

  Future<void> deleteMessage(String messageId, {bool deleteForEveryone = false}) async {
    try {
      final success = await _chatService.deleteMessage(chatRoomId, messageId, deleteForEveryone: deleteForEveryone);
      
      if (success) {
        if (deleteForEveryone) {
          // Remove message from list
          final updatedMessages = state.messages.where((message) => message.id != messageId).toList();
          state = state.copyWith(messages: updatedMessages);
        } else {
          // Mark message as deleted
          final updatedMessages = state.messages.map((message) {
            if (message.id == messageId) {
              return message.copyWith(isDeleted: true, deletedAt: DateTime.now());
            }
            return message;
          }).toList();
          state = state.copyWith(messages: updatedMessages);
        }
      }
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> editMessage(String messageId, String newContent) async {
    try {
      final editedMessage = await _chatService.editMessage(chatRoomId, messageId, newContent);
      
      // Update the message in the list
      final updatedMessages = state.messages.map((message) {
        if (message.id == messageId) {
          return editedMessage;
        }
        return message;
      }).toList();
      
      state = state.copyWith(messages: updatedMessages);
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void addMessage(ChatMessage message) {
    state = state.copyWith(
      messages: [...state.messages, message],
    );
  }

  void updateMessage(ChatMessage updatedMessage) {
    final updatedMessages = state.messages.map((message) {
      return message.id == updatedMessage.id ? updatedMessage : message;
    }).toList();
    state = state.copyWith(messages: updatedMessages);
  }
}

// Messages provider factory
final messagesProvider = StateNotifierProvider.family<MessagesNotifier, MessagesState, String>((ref, chatRoomId) {
  final chatService = ref.read(chatServiceProvider);
  return MessagesNotifier(chatService, chatRoomId);
});

// Chat settings provider
final chatSettingsProvider = FutureProvider<ChatSettings>((ref) async {
  final chatService = ref.read(chatServiceProvider);
  return await chatService.getChatSettings();
});

// Typing indicator state
class TypingState {
  final Map<String, List<TypingIndicator>> typingUsers;

  const TypingState({
    this.typingUsers = const {},
  });

  TypingState copyWith({
    Map<String, List<TypingIndicator>>? typingUsers,
  }) {
    return TypingState(
      typingUsers: typingUsers ?? this.typingUsers,
    );
  }
}

// Typing indicator provider
class TypingNotifier extends StateNotifier<TypingState> {
  final ChatService _chatService;

  TypingNotifier(this._chatService) : super(const TypingState());

  void addTypingUser(String chatRoomId, TypingIndicator indicator) {
    final currentTyping = state.typingUsers[chatRoomId] ?? [];
    final updatedTyping = [...currentTyping.where((t) => t.userId != indicator.userId), indicator];
    
    state = state.copyWith(
      typingUsers: {
        ...state.typingUsers,
        chatRoomId: updatedTyping,
      },
    );
  }

  void removeTypingUser(String chatRoomId, String userId) {
    final currentTyping = state.typingUsers[chatRoomId] ?? [];
    final updatedTyping = currentTyping.where((t) => t.userId != userId).toList();
    
    if (updatedTyping.isEmpty) {
      final updatedMap = Map<String, List<TypingIndicator>>.from(state.typingUsers);
      updatedMap.remove(chatRoomId);
      state = state.copyWith(typingUsers: updatedMap);
    } else {
      state = state.copyWith(
        typingUsers: {
          ...state.typingUsers,
          chatRoomId: updatedTyping,
        },
      );
    }
  }

  Future<void> sendTypingIndicator(String chatRoomId, bool isTyping) async {
    await _chatService.sendTypingIndicator(chatRoomId, isTyping);
  }
}

final typingProvider = StateNotifierProvider<TypingNotifier, TypingState>((ref) {
  final chatService = ref.read(chatServiceProvider);
  return TypingNotifier(chatService);
});

// Get or create chat room provider
final getOrCreateChatRoomProvider = FutureProvider.family<ChatRoom, String>((ref, otherUserId) async {
  final chatService = ref.read(chatServiceProvider);
  return await chatService.getOrCreateChatRoom(otherUserId);
});

// Chat statistics provider
final chatStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final chatService = ref.read(chatServiceProvider);
  return await chatService.getChatStatistics();
});
