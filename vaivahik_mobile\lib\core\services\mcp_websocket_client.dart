// MCP WebSocket Client Template for Flutter
// Complete template for integrating MCP server with authentication

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';

/// MCP WebSocket Client for Flutter applications
class MCPWebSocketClient {
  /// Server URL for WebSocket connection
  final String serverUrl;
  
  /// Configuration options for the client
  final Map<String, dynamic> options;
  
  WebSocketChannel? _channel;
  bool _isConnected = false;
  int _reconnectAttempts = 0;
  int _messageId = 0;
  String? _token;
  
  final Map<int, Completer<dynamic>> _pendingRequests = {};
  final Map<String, List<void Function(dynamic)>> _eventListeners = {};
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  
  // Default options
  static const Map<String, dynamic> _defaultOptions = {
    'autoReconnect': true,
    'maxReconnectAttempts': 5,
    'reconnectDelay': 1000,
    'heartbeatInterval': 30000,
    'timeout': 30000,
  };

  /// Creates a new MCP WebSocket client
  MCPWebSocketClient(this.serverUrl, [Map<String, dynamic>? options])
      : options = {..._defaultOptions, ...?options};

  /// Connect to MCP server with authentication
  Future<void> connect(String? token) async {
    try {
      _token = token;
      final wsUrl = token != null 
          ? '$serverUrl?token=${Uri.encodeComponent(token)}'
          : serverUrl;
      
      _channel = IOWebSocketChannel.connect(Uri.parse(wsUrl));
      
      // Listen to connection state
      _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnect,
      );
      
      _isConnected = true;
      _reconnectAttempts = 0;
      _startHeartbeat();
      _emit('connected', null);
      
      debugPrint('Connected to MCP server');
    } catch (error) {
      debugPrint('Failed to connect to MCP server: $error');
      _emit('error', error);
      rethrow;
    }
  }

  /// Disconnect from MCP server
  void disconnect() {
    options['autoReconnect'] = false;
    _stopHeartbeat();
    _stopReconnectTimer();
    
    _channel?.sink.close(WebSocketStatus.normalClosure, 'Client disconnect');
    _channel = null;
    _isConnected = false;
    
    _emit('disconnected', {'code': 1000, 'reason': 'Client disconnect'});
  }

  /// Send message to MCP server
  void _send(Map<String, dynamic> message) {
    if (!_isConnected || _channel == null) {
      throw Exception('Not connected to MCP server');
    }
    
    _channel!.sink.add(jsonEncode(message));
  }

  /// Call MCP tool with promise-based response
  Future<dynamic> callTool(String toolName, [Map<String, dynamic>? args, Map<String, dynamic>? options]) async {
    final messageId = ++_messageId;
    final timeout = options?['timeout'] ?? this.options['timeout'] as int;
    
    final message = {
      'jsonrpc': '2.0',
      'id': messageId,
      'method': 'tools/call',
      'params': {
        'name': toolName,
        'arguments': args ?? {},
      }
    };

    final completer = Completer<dynamic>();
    _pendingRequests[messageId] = completer;

    // Set timeout
    Timer(Duration(milliseconds: timeout), () {
      if (_pendingRequests.containsKey(messageId)) {
        _pendingRequests.remove(messageId);
        completer.completeError(TimeoutException('Tool call timeout: $toolName', Duration(milliseconds: timeout)));
      }
    });

    _send(message);
    return completer.future;
  }

  /// Handle incoming messages
  void _handleMessage(dynamic data) {
    try {
      final message = jsonDecode(data as String) as Map<String, dynamic>;
      
      // Handle responses to pending requests
      if (message['id'] != null && _pendingRequests.containsKey(message['id'])) {
        final completer = _pendingRequests.remove(message['id'])!;
        
        if (message['error'] != null) {
          final errorMessage = message['error']['message'] as String? ?? 'Tool call failed';
          completer.completeError(Exception(errorMessage));
        } else {
          completer.complete(message['result']);
        }
        return;
      }

      // Handle notifications and other messages
      if (message['method'] != null) {
        _emit('notification', message);
      }

      _emit('message', message);
    } catch (error) {
      debugPrint('Error handling message: $error');
      _emit('error', error);
    }
  }

  /// Handle connection errors
  void _handleError(dynamic error) {
    debugPrint('MCP WebSocket error: $error');
    _isConnected = false;
    _emit('error', error);
  }

  /// Handle disconnection
  void _handleDisconnect() {
    debugPrint('Disconnected from MCP server');
    _isConnected = false;
    _stopHeartbeat();
    _emit('disconnected', {'code': 1006, 'reason': 'Connection lost'});
    
    final autoReconnect = options['autoReconnect'] as bool;
    final maxReconnectAttempts = options['maxReconnectAttempts'] as int;
    
    if (autoReconnect && _reconnectAttempts < maxReconnectAttempts) {
      _attemptReconnect();
    }
  }

  /// Start heartbeat to keep connection alive
  void _startHeartbeat() {
    final heartbeatInterval = options['heartbeatInterval'] as int;
    _heartbeatTimer = Timer.periodic(
      Duration(milliseconds: heartbeatInterval),
      (timer) {
        if (_isConnected) {
          _send({'type': 'ping', 'timestamp': DateTime.now().millisecondsSinceEpoch});
        }
      },
    );
  }

  /// Stop heartbeat
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// Stop reconnect timer
  void _stopReconnectTimer() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// Attempt to reconnect
  void _attemptReconnect() {
    _reconnectAttempts++;
    final reconnectDelay = options['reconnectDelay'] as int;
    final maxReconnectAttempts = options['maxReconnectAttempts'] as int;
    final delay = reconnectDelay * (1 << (_reconnectAttempts - 1));
    
    debugPrint('Attempting to reconnect ($_reconnectAttempts/$maxReconnectAttempts) in ${delay}ms');
    
    _reconnectTimer = Timer(Duration(milliseconds: delay), () {
      if (_reconnectAttempts <= maxReconnectAttempts) {
        connect(_token).catchError((error) {
          // Reconnection failed, will try again if attempts remain
          debugPrint('Reconnection failed: $error');
        });
      }
    });
  }

  /// Add event listener
  void on(String event, void Function(dynamic) listener) {
    _eventListeners.putIfAbsent(event, () => []).add(listener);
  }

  /// Remove event listener
  void off(String event, void Function(dynamic) listener) {
    _eventListeners[event]?.remove(listener);
  }

  /// Emit event to listeners
  void _emit(String event, dynamic data) {
    _eventListeners[event]?.forEach((listener) {
      try {
        listener(data);
      } catch (error) {
        debugPrint('Error in event listener: $error');
      }
    });
  }

  /// Get connection status
  bool get isConnected => _isConnected;

  /// Get current token
  String? get token => _token;

  /// Dispose resources
  void dispose() {
    disconnect();
    _pendingRequests.clear();
    _eventListeners.clear();
  }
}

/// Flutter Widget for MCP Integration
class MCPClientProvider extends StatefulWidget {
  /// Server URL for WebSocket connection
  final String serverUrl;

  /// Authentication token
  final String? token;

  /// Configuration options
  final Map<String, dynamic>? options;

  /// Child widget
  final Widget child;

  /// Creates a new MCP client provider
  const MCPClientProvider({
    super.key,
    required this.serverUrl,
    this.token,
    this.options,
    required this.child,
  });

  @override
  State<MCPClientProvider> createState() => _MCPClientProviderState();

  /// Get MCP client from context
  static MCPWebSocketClient? of(BuildContext context) {
    return context.dependOnInheritedWidgetOfExactType<_MCPClientInherited>()?.client;
  }
}

class _MCPClientProviderState extends State<MCPClientProvider> {
  late MCPWebSocketClient _client;
  bool _isConnected = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _client = MCPWebSocketClient(widget.serverUrl, widget.options);

    _client.on('connected', (_) {
      if (mounted) setState(() => _isConnected = true);
    });

    _client.on('disconnected', (_) {
      if (mounted) setState(() => _isConnected = false);
    });

    _client.on('error', (error) {
      if (mounted) setState(() => _error = error.toString());
    });

    if (widget.token != null) {
      _client.connect(widget.token);
    }
  }

  @override
  void didUpdateWidget(MCPClientProvider oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.token != oldWidget.token && widget.token != null) {
      _client.connect(widget.token);
    }
  }

  @override
  void dispose() {
    _client.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _MCPClientInherited(
      client: _client,
      isConnected: _isConnected,
      error: _error,
      child: widget.child,
    );
  }
}

class _MCPClientInherited extends InheritedWidget {
  /// MCP WebSocket client instance
  final MCPWebSocketClient client;

  /// Connection status
  final bool isConnected;

  /// Error message if any
  final String? error;

  /// Creates inherited widget for MCP client
  const _MCPClientInherited({
    required this.client,
    required this.isConnected,
    this.error,
    required super.child,
  });

  @override
  bool updateShouldNotify(_MCPClientInherited oldWidget) {
    return isConnected != oldWidget.isConnected || error != oldWidget.error;
  }
}
