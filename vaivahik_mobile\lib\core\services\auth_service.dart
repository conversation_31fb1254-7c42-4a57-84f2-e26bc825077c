import '../api/api_client.dart';
import '../models/user_model.dart';

class AuthService {
  final ApiClient _apiClient = ApiClient();

  Future<AuthResult> requestOtp(String phone) async {
    try {
      final response = await _apiClient.post('/users/request-otp', {
        'phone': phone,
      });

      if (response['success'] == true) {
        return AuthResult(
          success: true,
          message: response['message'] ?? 'OTP sent successfully',
        );
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'Failed to send OTP',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<AuthResult> login(String phone, String password) async {
    // For backward compatibility, redirect to OTP flow
    return await requestOtp(phone);
  }

  Future<AuthResult> register(Map<String, dynamic> userData) async {
    try {
      final response = await _apiClient.post('/users/register', userData);

      if (response['success'] == true) {
        return AuthResult.fromJson(response);
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'Registration failed',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<AuthResult> verifyOtp(String phone, String otp) async {
    try {
      final response = await _apiClient.post('/users/verify-otp', {
        'phone': phone,
        'otp': otp,
      });

      if (response['success'] == true) {
        return AuthResult.fromJson(response);
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'OTP verification failed',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<AuthResult> resendOtp(String phone) async {
    try {
      final response = await _apiClient.post('/users/resend-otp', {
        'phone': phone,
      });

      if (response['success'] == true) {
        return const AuthResult(
          success: true,
          message: 'OTP sent successfully',
        );
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'Failed to send OTP',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<UserModel?> getCurrentUser() async {
    try {
      final response = await _apiClient.get('/users/profile');

      if (response['success'] == true) {
        return UserModel.fromJson(response['user']);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<AuthResult> forgotPassword(String phone) async {
    try {
      final response = await _apiClient.post('/users/request-otp', {
        'phone': phone,
      });

      if (response['success'] == true) {
        return const AuthResult(
          success: true,
          message: 'Password reset OTP sent successfully',
        );
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'Failed to send reset OTP',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<AuthResult> resetPassword(String phone, String otp, String newPassword) async {
    try {
      final response = await _apiClient.post('/users/verify-otp', {
        'phone': phone,
        'otp': otp,
        'newPassword': newPassword,
      });

      if (response['success'] == true) {
        return const AuthResult(
          success: true,
          message: 'Password reset successfully',
        );
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'Password reset failed',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<AuthResult> changePassword(String currentPassword, String newPassword) async {
    try {
      final response = await _apiClient.put('/users/profile', {
        'currentPassword': currentPassword,
        'newPassword': newPassword,
      });

      if (response['success'] == true) {
        return const AuthResult(
          success: true,
          message: 'Password changed successfully',
        );
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'Password change failed',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<AuthResult> refreshToken() async {
    try {
      final response = await _apiClient.post('/users/refresh-token', {});

      if (response['success'] == true) {
        return AuthResult.fromJson(response);
      } else {
        return AuthResult(
          success: false,
          message: response['message'] ?? 'Token refresh failed',
        );
      }
    } catch (e) {
      return AuthResult(
        success: false,
        message: e.toString(),
      );
    }
  }

  Future<void> logout() async {
    try {
      await _apiClient.post('/users/logout', {});
    } catch (e) {
      // Ignore logout errors
    }
  }
}
