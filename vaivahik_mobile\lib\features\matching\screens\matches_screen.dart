import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class MatchesScreen extends ConsumerWidget {
  const MatchesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Matches'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // Open filters screen
              Navigator.of(context).pushNamed('/matches-filter');
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite, size: 80, color: Colors.pink),
            <PERSON><PERSON><PERSON><PERSON>(height: 16),
            Text(
              'Matches Screen',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON>ox(height: 8),
            Text('Find your perfect match here...'),
          ],
        ),
      ),
    );
  }
}


