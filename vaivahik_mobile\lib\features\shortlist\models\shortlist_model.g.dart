// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shortlist_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ShortlistItem _$ShortlistItemFromJson(Map<String, dynamic> json) =>
    _ShortlistItem(
      id: json['id'] as String,
      userId: json['userId'] as String,
      targetUserId: json['targetUserId'] as String,
      note: json['note'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      interestSent: json['interestSent'] as bool,
      interestStatus: json['interestStatus'] as String?,
      profile:
          ShortlistProfile.fromJson(json['profile'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ShortlistItemToJson(_ShortlistItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'targetUserId': instance.targetUserId,
      'note': instance.note,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'interestSent': instance.interestSent,
      'interestStatus': instance.interestStatus,
      'profile': instance.profile,
    };

_ShortlistProfile _$ShortlistProfileFromJson(Map<String, dynamic> json) =>
    _ShortlistProfile(
      id: json['id'] as String,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String?,
      age: (json['age'] as num?)?.toInt(),
      location: json['location'] as String?,
      education: json['education'] as String?,
      occupation: json['occupation'] as String?,
      profilePicture: json['profilePicture'] as String?,
      compatibility: (json['compatibility'] as num?)?.toInt(),
      religion: json['religion'] as String?,
      caste: json['caste'] as String?,
      motherTongue: json['motherTongue'] as String?,
      maritalStatus: json['maritalStatus'] as String?,
      height: (json['height'] as num?)?.toDouble(),
      income: json['income'] as String?,
      isVerified: json['isVerified'] as bool?,
      isOnline: json['isOnline'] as bool?,
      lastSeen: json['lastSeen'] == null
          ? null
          : DateTime.parse(json['lastSeen'] as String),
    );

Map<String, dynamic> _$ShortlistProfileToJson(_ShortlistProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'age': instance.age,
      'location': instance.location,
      'education': instance.education,
      'occupation': instance.occupation,
      'profilePicture': instance.profilePicture,
      'compatibility': instance.compatibility,
      'religion': instance.religion,
      'caste': instance.caste,
      'motherTongue': instance.motherTongue,
      'maritalStatus': instance.maritalStatus,
      'height': instance.height,
      'income': instance.income,
      'isVerified': instance.isVerified,
      'isOnline': instance.isOnline,
      'lastSeen': instance.lastSeen?.toIso8601String(),
    };

_ShortlistStats _$ShortlistStatsFromJson(Map<String, dynamic> json) =>
    _ShortlistStats(
      totalShortlisted: (json['totalShortlisted'] as num).toInt(),
      interestsSent: (json['interestsSent'] as num).toInt(),
      interestsReceived: (json['interestsReceived'] as num).toInt(),
      mutualInterests: (json['mutualInterests'] as num).toInt(),
      recentlyAdded: (json['recentlyAdded'] as num).toInt(),
      thisWeek: (json['thisWeek'] as num).toInt(),
      thisMonth: (json['thisMonth'] as num).toInt(),
    );

Map<String, dynamic> _$ShortlistStatsToJson(_ShortlistStats instance) =>
    <String, dynamic>{
      'totalShortlisted': instance.totalShortlisted,
      'interestsSent': instance.interestsSent,
      'interestsReceived': instance.interestsReceived,
      'mutualInterests': instance.mutualInterests,
      'recentlyAdded': instance.recentlyAdded,
      'thisWeek': instance.thisWeek,
      'thisMonth': instance.thisMonth,
    };

_ShortlistFilters _$ShortlistFiltersFromJson(Map<String, dynamic> json) =>
    _ShortlistFilters(
      searchQuery: json['searchQuery'] as String?,
      interestSent: json['interestSent'] as bool?,
      interestStatus: json['interestStatus'] as String?,
      location: json['location'] as String?,
      education: json['education'] as String?,
      occupation: json['occupation'] as String?,
      minAge: (json['minAge'] as num?)?.toInt(),
      maxAge: (json['maxAge'] as num?)?.toInt(),
      minHeight: (json['minHeight'] as num?)?.toDouble(),
      maxHeight: (json['maxHeight'] as num?)?.toDouble(),
      religion: json['religion'] as String?,
      caste: json['caste'] as String?,
      maritalStatus: json['maritalStatus'] as String?,
      isVerified: json['isVerified'] as bool?,
      isOnline: json['isOnline'] as bool?,
      sortBy: json['sortBy'] as String?,
      sortAscending: json['sortAscending'] as bool?,
    );

Map<String, dynamic> _$ShortlistFiltersToJson(_ShortlistFilters instance) =>
    <String, dynamic>{
      'searchQuery': instance.searchQuery,
      'interestSent': instance.interestSent,
      'interestStatus': instance.interestStatus,
      'location': instance.location,
      'education': instance.education,
      'occupation': instance.occupation,
      'minAge': instance.minAge,
      'maxAge': instance.maxAge,
      'minHeight': instance.minHeight,
      'maxHeight': instance.maxHeight,
      'religion': instance.religion,
      'caste': instance.caste,
      'maritalStatus': instance.maritalStatus,
      'isVerified': instance.isVerified,
      'isOnline': instance.isOnline,
      'sortBy': instance.sortBy,
      'sortAscending': instance.sortAscending,
    };

_AddToShortlistRequest _$AddToShortlistRequestFromJson(
        Map<String, dynamic> json) =>
    _AddToShortlistRequest(
      profileId: json['profileId'] as String,
      note: json['note'] as String?,
    );

Map<String, dynamic> _$AddToShortlistRequestToJson(
        _AddToShortlistRequest instance) =>
    <String, dynamic>{
      'profileId': instance.profileId,
      'note': instance.note,
    };

_UpdateShortlistNoteRequest _$UpdateShortlistNoteRequestFromJson(
        Map<String, dynamic> json) =>
    _UpdateShortlistNoteRequest(
      shortlistId: json['shortlistId'] as String,
      note: json['note'] as String?,
    );

Map<String, dynamic> _$UpdateShortlistNoteRequestToJson(
        _UpdateShortlistNoteRequest instance) =>
    <String, dynamic>{
      'shortlistId': instance.shortlistId,
      'note': instance.note,
    };

_RemoveFromShortlistRequest _$RemoveFromShortlistRequestFromJson(
        Map<String, dynamic> json) =>
    _RemoveFromShortlistRequest(
      shortlistId: json['shortlistId'] as String,
    );

Map<String, dynamic> _$RemoveFromShortlistRequestToJson(
        _RemoveFromShortlistRequest instance) =>
    <String, dynamic>{
      'shortlistId': instance.shortlistId,
    };

_ShortlistResponse _$ShortlistResponseFromJson(Map<String, dynamic> json) =>
    _ShortlistResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => ShortlistItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      stats: json['stats'] == null
          ? null
          : ShortlistStats.fromJson(json['stats'] as Map<String, dynamic>),
      total: (json['total'] as num?)?.toInt(),
      page: (json['page'] as num?)?.toInt(),
      limit: (json['limit'] as num?)?.toInt(),
      hasMore: json['hasMore'] as bool?,
    );

Map<String, dynamic> _$ShortlistResponseToJson(_ShortlistResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
      'stats': instance.stats,
      'total': instance.total,
      'page': instance.page,
      'limit': instance.limit,
      'hasMore': instance.hasMore,
    };

_ShortlistActionResult _$ShortlistActionResultFromJson(
        Map<String, dynamic> json) =>
    _ShortlistActionResult(
      success: json['success'] as bool,
      message: json['message'] as String?,
      shortlistId: json['shortlistId'] as String?,
      item: json['item'] == null
          ? null
          : ShortlistItem.fromJson(json['item'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ShortlistActionResultToJson(
        _ShortlistActionResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'shortlistId': instance.shortlistId,
      'item': instance.item,
    };

_ShortlistUIState _$ShortlistUIStateFromJson(Map<String, dynamic> json) =>
    _ShortlistUIState(
      items: (json['items'] as List<dynamic>?)
              ?.map((e) => ShortlistItem.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      isLoading: json['isLoading'] as bool? ?? false,
      isRefreshing: json['isRefreshing'] as bool? ?? false,
      hasMore: json['hasMore'] as bool? ?? false,
      currentPage: (json['currentPage'] as num?)?.toInt() ?? 1,
      error: json['error'] as String?,
      filters: json['filters'] == null
          ? null
          : ShortlistFilters.fromJson(json['filters'] as Map<String, dynamic>),
      stats: json['stats'] == null
          ? null
          : ShortlistStats.fromJson(json['stats'] as Map<String, dynamic>),
      selectedItemId: json['selectedItemId'] as String?,
      showFilters: json['showFilters'] as bool? ?? false,
      sortBy: json['sortBy'] as String? ?? 'recent',
      sortAscending: json['sortAscending'] as bool? ?? false,
    );

Map<String, dynamic> _$ShortlistUIStateToJson(_ShortlistUIState instance) =>
    <String, dynamic>{
      'items': instance.items,
      'isLoading': instance.isLoading,
      'isRefreshing': instance.isRefreshing,
      'hasMore': instance.hasMore,
      'currentPage': instance.currentPage,
      'error': instance.error,
      'filters': instance.filters,
      'stats': instance.stats,
      'selectedItemId': instance.selectedItemId,
      'showFilters': instance.showFilters,
      'sortBy': instance.sortBy,
      'sortAscending': instance.sortAscending,
    };
