import 'package:go_router/go_router.dart';
import '../screens/content_screen.dart';
import '../screens/faq_screen.dart';

/// 📄 Content Management Routes
/// Dynamic content pages, FAQ, and support

final contentRoutes = [
  // Privacy Policy
  GoRoute(
    path: '/privacy-policy',
    name: 'privacy-policy',
    builder: (context, state) => const ContentScreen(
      contentType: 'privacy-policy',
    ),
  ),
  
  // Terms & Conditions
  GoRoute(
    path: '/terms-conditions',
    name: 'terms-conditions',
    builder: (context, state) => const ContentScreen(
      contentType: 'terms-of-service',
    ),
  ),
  
  // Refund Policy
  GoRoute(
    path: '/refund-policy',
    name: 'refund-policy',
    builder: (context, state) => const ContentScreen(
      contentType: 'refund-policy',
    ),
  ),
  
  // About Us
  GoRoute(
    path: '/about-us',
    name: 'about-us',
    builder: (context, state) => const ContentScreen(
      contentType: 'about-us',
    ),
  ),
  
  // Contact Us
  GoRoute(
    path: '/contact-us',
    name: 'contact-us',
    builder: (context, state) => const ContactSupportScreen(),
  ),
  
  // FAQ & Support
  GoRoute(
    path: '/faq',
    name: 'faq',
    builder: (context, state) => const FAQScreen(),
  ),
  
  // Generic Content Page
  GoRoute(
    path: '/content/:contentType',
    name: 'content',
    builder: (context, state) {
      final contentType = state.pathParameters['contentType'] ?? 'about-us';
      return ContentScreen(contentType: contentType);
    },
  ),
];
