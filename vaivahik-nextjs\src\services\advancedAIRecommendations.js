// Advanced AI Recommendations Service for Vaivahik
import { apiClient } from './apiClient';

class AdvancedAIRecommendations {
  constructor() {
    this.userBehaviorData = new Map();
    this.preferenceLearning = new Map();
    this.contentPersonalization = new Map();
    this.realTimeInsights = new Map();
  }

  // Enhanced Profile Matching with Behavioral Analysis
  async getAdvancedMatches(userId, options = {}) {
    try {
      const userBehavior = await this.getUserBehaviorProfile(userId);
      const preferences = await this.getLearnedPreferences(userId);
      
      const matchRequest = {
        userId,
        behaviorProfile: userBehavior,
        learnedPreferences: preferences,
        realTimeContext: await this.getRealTimeContext(userId),
        matchingAlgorithm: 'advanced_2tower_with_behavior',
        ...options
      };

      const response = await apiClient.post('/api/ai/advanced-matches', matchRequest);
      
      // Learn from user's response to these matches
      this.trackMatchRecommendations(userId, response.data.matches);
      
      return response.data;
    } catch (error) {
      console.error('Advanced matching failed:', error);
      return this.getFallbackMatches(userId);
    }
  }

  // Behavioral Analysis and Learning
  async getUserBehaviorProfile(userId) {
    if (this.userBehaviorData.has(userId)) {
      return this.userBehaviorData.get(userId);
    }

    try {
      const response = await apiClient.get(`/api/ai/user-behavior/${userId}`);
      const behaviorProfile = {
        searchPatterns: response.data.searchPatterns || {},
        interactionHistory: response.data.interactions || [],
        timeSpentOnProfiles: response.data.timeSpent || {},
        messagePatterns: response.data.messagePatterns || {},
        photoPreferences: response.data.photoPreferences || {},
        profileCompletionBehavior: response.data.profileBehavior || {},
        sessionPatterns: response.data.sessionPatterns || {},
        deviceUsagePatterns: response.data.devicePatterns || {}
      };

      this.userBehaviorData.set(userId, behaviorProfile);
      return behaviorProfile;
    } catch (error) {
      console.error('Failed to get user behavior profile:', error);
      return this.getDefaultBehaviorProfile();
    }
  }

  // Real-time Preference Learning
  async getLearnedPreferences(userId) {
    if (this.preferenceLearning.has(userId)) {
      return this.preferenceLearning.get(userId);
    }

    try {
      const response = await apiClient.get(`/api/ai/learned-preferences/${userId}`);
      const preferences = {
        implicitPreferences: response.data.implicit || {},
        explicitPreferences: response.data.explicit || {},
        evolvedPreferences: response.data.evolved || {},
        contextualPreferences: response.data.contextual || {},
        temporalPreferences: response.data.temporal || {}
      };

      this.preferenceLearning.set(userId, preferences);
      return preferences;
    } catch (error) {
      console.error('Failed to get learned preferences:', error);
      return this.getDefaultPreferences();
    }
  }

  // Personalized Content Recommendations
  async getPersonalizedContent(userId, contentType = 'all') {
    try {
      const userProfile = await this.getUserBehaviorProfile(userId);
      const preferences = await this.getLearnedPreferences(userId);
      
      const contentRequest = {
        userId,
        contentType,
        userProfile,
        preferences,
        context: await this.getRealTimeContext(userId)
      };

      const response = await apiClient.post('/api/ai/personalized-content', contentRequest);
      
      return {
        profileSuggestions: response.data.profileSuggestions || [],
        searchRecommendations: response.data.searchRecommendations || [],
        conversationStarters: response.data.conversationStarters || [],
        profileImprovements: response.data.profileImprovements || [],
        photoRecommendations: response.data.photoRecommendations || [],
        premiumFeatureSuggestions: response.data.premiumSuggestions || []
      };
    } catch (error) {
      console.error('Failed to get personalized content:', error);
      return this.getDefaultPersonalizedContent();
    }
  }

  // Real-time Context Analysis
  async getRealTimeContext(userId) {
    try {
      const now = new Date();
      const context = {
        timeOfDay: this.getTimeOfDay(now),
        dayOfWeek: now.getDay(),
        season: this.getSeason(now),
        userActivity: await this.getCurrentUserActivity(userId),
        deviceType: this.getDeviceType(),
        location: await this.getUserLocation(userId),
        mood: await this.inferUserMood(userId),
        urgency: await this.inferUrgency(userId)
      };

      this.realTimeInsights.set(userId, context);
      return context;
    } catch (error) {
      console.error('Failed to get real-time context:', error);
      return this.getDefaultContext();
    }
  }

  // AI-Powered Conversation Starters
  async getConversationStarters(userId, targetUserId) {
    try {
      const userProfile = await this.getUserBehaviorProfile(userId);
      const targetProfile = await this.getUserBehaviorProfile(targetUserId);
      
      const response = await apiClient.post('/api/ai/conversation-starters', {
        userId,
        targetUserId,
        userProfile,
        targetProfile,
        commonInterests: await this.findCommonInterests(userId, targetUserId),
        conversationHistory: await this.getConversationHistory(userId, targetUserId)
      });

      return response.data.starters || this.getDefaultConversationStarters();
    } catch (error) {
      console.error('Failed to get conversation starters:', error);
      return this.getDefaultConversationStarters();
    }
  }

  // Smart Profile Optimization Suggestions
  async getProfileOptimizationSuggestions(userId) {
    try {
      const userProfile = await apiClient.get(`/api/user/profile/${userId}`);
      const behaviorData = await this.getUserBehaviorProfile(userId);
      const marketAnalysis = await this.getMarketAnalysis(userProfile.data);

      const response = await apiClient.post('/api/ai/profile-optimization', {
        userId,
        currentProfile: userProfile.data,
        behaviorData,
        marketAnalysis,
        competitorAnalysis: await this.getCompetitorAnalysis(userProfile.data)
      });

      return {
        photoSuggestions: response.data.photoSuggestions || [],
        bioImprovements: response.data.bioImprovements || [],
        interestRecommendations: response.data.interestRecommendations || [],
        profileCompleteness: response.data.completeness || {},
        visibilityTips: response.data.visibilityTips || [],
        engagementTips: response.data.engagementTips || []
      };
    } catch (error) {
      console.error('Failed to get profile optimization suggestions:', error);
      return this.getDefaultOptimizationSuggestions();
    }
  }

  // Predictive Analytics
  async getPredictiveInsights(userId) {
    try {
      const response = await apiClient.get(`/api/ai/predictive-insights/${userId}`);
      
      return {
        matchProbability: response.data.matchProbability || {},
        churnRisk: response.data.churnRisk || {},
        subscriptionLikelihood: response.data.subscriptionLikelihood || {},
        optimalActivityTimes: response.data.optimalTimes || {},
        successFactors: response.data.successFactors || {},
        improvementAreas: response.data.improvementAreas || []
      };
    } catch (error) {
      console.error('Failed to get predictive insights:', error);
      return this.getDefaultPredictiveInsights();
    }
  }

  // Real-time Learning from User Actions
  async learnFromUserAction(userId, action, context = {}) {
    try {
      const learningData = {
        userId,
        action: {
          type: action.type,
          target: action.target,
          result: action.result,
          timestamp: new Date(),
          context: {
            ...context,
            realTimeContext: await this.getRealTimeContext(userId)
          }
        }
      };

      // Send to learning pipeline
      await apiClient.post('/api/ai/learn-from-action', learningData);
      
      // Update local cache
      this.updateLocalLearning(userId, learningData);
      
    } catch (error) {
      console.error('Failed to learn from user action:', error);
    }
  }

  // Smart Notification Timing
  async getOptimalNotificationTime(userId, notificationType) {
    try {
      const behaviorProfile = await this.getUserBehaviorProfile(userId);
      const response = await apiClient.post('/api/ai/optimal-notification-time', {
        userId,
        notificationType,
        behaviorProfile,
        currentContext: await this.getRealTimeContext(userId)
      });

      return response.data.optimalTime || this.getDefaultNotificationTime();
    } catch (error) {
      console.error('Failed to get optimal notification time:', error);
      return this.getDefaultNotificationTime();
    }
  }

  // Helper Methods
  getTimeOfDay(date) {
    const hour = date.getHours();
    if (hour < 6) return 'night';
    if (hour < 12) return 'morning';
    if (hour < 18) return 'afternoon';
    return 'evening';
  }

  getSeason(date) {
    const month = date.getMonth();
    if (month >= 2 && month <= 4) return 'spring';
    if (month >= 5 && month <= 7) return 'summer';
    if (month >= 8 && month <= 10) return 'autumn';
    return 'winter';
  }

  getDeviceType() {
    if (typeof window === 'undefined') return 'server';
    const userAgent = window.navigator.userAgent;
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) return 'tablet';
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) return 'mobile';
    return 'desktop';
  }

  async findCommonInterests(userId1, userId2) {
    try {
      const response = await apiClient.post('/api/ai/common-interests', {
        userId1,
        userId2
      });
      return response.data.commonInterests || [];
    } catch (error) {
      return [];
    }
  }

  // Fallback Methods
  getDefaultBehaviorProfile() {
    return {
      searchPatterns: {},
      interactionHistory: [],
      timeSpentOnProfiles: {},
      messagePatterns: {},
      photoPreferences: {},
      profileCompletionBehavior: {},
      sessionPatterns: {},
      deviceUsagePatterns: {}
    };
  }

  getDefaultPreferences() {
    return {
      implicitPreferences: {},
      explicitPreferences: {},
      evolvedPreferences: {},
      contextualPreferences: {},
      temporalPreferences: {}
    };
  }

  getDefaultPersonalizedContent() {
    return {
      profileSuggestions: [],
      searchRecommendations: [],
      conversationStarters: [
        "Hi! I noticed we have similar interests. How are you doing?",
        "Hello! Your profile caught my attention. Would love to know more about you.",
        "Hi there! I'd love to connect and learn more about your interests."
      ],
      profileImprovements: [],
      photoRecommendations: [],
      premiumFeatureSuggestions: []
    };
  }

  getDefaultConversationStarters() {
    return [
      "Hi! How are you doing today?",
      "Hello! I'd love to get to know you better.",
      "Hi there! Your profile seems interesting.",
      "Hello! Would you like to chat?",
      "Hi! I think we might have a lot in common."
    ];
  }

  getDefaultOptimizationSuggestions() {
    return {
      photoSuggestions: [],
      bioImprovements: [],
      interestRecommendations: [],
      profileCompleteness: { score: 70, suggestions: [] },
      visibilityTips: [],
      engagementTips: []
    };
  }

  getDefaultPredictiveInsights() {
    return {
      matchProbability: { score: 50, factors: [] },
      churnRisk: { level: 'medium', factors: [] },
      subscriptionLikelihood: { score: 30, factors: [] },
      optimalActivityTimes: [],
      successFactors: [],
      improvementAreas: []
    };
  }

  updateLocalLearning(userId, learningData) {
    // Update local caches with new learning data
    if (this.userBehaviorData.has(userId)) {
      const profile = this.userBehaviorData.get(userId);
      // Update profile with new learning
      this.userBehaviorData.set(userId, { ...profile, lastUpdated: new Date() });
    }
  }
}

// Create singleton instance
const advancedAI = new AdvancedAIRecommendations();

export default advancedAI;
