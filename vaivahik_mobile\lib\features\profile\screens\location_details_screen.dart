import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/widgets/premium_widgets.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../app/theme.dart';

class LocationDetailsScreen extends StatefulWidget {
  const LocationDetailsScreen({super.key});

  @override
  State<LocationDetailsScreen> createState() => _LocationDetailsScreenState();
}

class _LocationDetailsScreenState extends State<LocationDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _cityController = TextEditingController();
  final _nativePlaceController = TextEditingController();
  
  String? selectedState;
  String? selectedCountry;
  
  bool isLoading = false;

  final List<String> stateOptions = [
    'Maharashtra',
    'Karnataka',
    'Gujarat',
    'Rajasthan',
    'Uttar Pradesh',
    'Madhya Pradesh',
    'Tamil Nadu',
    'Kerala',
    'Andhra Pradesh',
    'Telangana',
    'West Bengal',
    'Bihar',
    'Odisha',
    'Punjab',
    'Haryana',
    'Delhi',
    'Other',
  ];
  
  final List<String> countryOptions = [
    'India',
    'United States',
    'Canada',
    'United Kingdom',
    'Australia',
    'Germany',
    'Singapore',
    'UAE',
    'Other',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Location Details',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              GlassmorphicCard(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.location_on_outlined,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Location Information',
                              style: TextStyle(
                                color: AppTheme.textColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Where are you located?',
                              style: TextStyle(
                                color: AppTheme.textColor.withValues(alpha: 0.7),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
              
              const SizedBox(height: 24),
              
              // Current Location Section
              _buildFormSection(
                title: 'Current Location',
                icon: Icons.my_location,
                children: [
                  CustomTextField(
                    controller: _cityController,
                    label: 'City',
                    hint: 'Enter your current city',
                    prefixIcon: const Icon(Icons.location_city),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your city';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildDropdownField(
                    label: 'State',
                    hint: 'Select your state',
                    value: selectedState,
                    items: stateOptions,
                    onChanged: (value) => setState(() => selectedState = value),
                    prefixIcon: Icons.map,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildDropdownField(
                    label: 'Country',
                    hint: 'Select your country',
                    value: selectedCountry,
                    items: countryOptions,
                    onChanged: (value) => setState(() => selectedCountry = value),
                    prefixIcon: Icons.public,
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Native Place Section
              _buildFormSection(
                title: 'Native Place',
                icon: Icons.home,
                children: [
                  CustomTextField(
                    controller: _nativePlaceController,
                    label: 'Native Place',
                    hint: 'Enter your native place',
                    prefixIcon: const Icon(Icons.home),
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Save Button
              PremiumGradientButton(
                text: isLoading ? 'Saving...' : 'Save & Continue',
                onPressed: isLoading ? null : _saveLocationDetails,
                width: double.infinity,
              ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return GlassmorphicCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData prefixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.cardColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            hint: Text(
              hint,
              style: TextStyle(
                color: AppTheme.textColor.withValues(alpha: 0.5),
              ),
            ),
            decoration: InputDecoration(
              prefixIcon: Icon(prefixIcon, color: AppTheme.primaryColor),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            dropdownColor: AppTheme.cardColor,
            style: TextStyle(color: AppTheme.textColor),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Future<void> _saveLocationDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      // Implement API call to save location details
      await _saveLocationDetailsToAPI();
      await Future.delayed(const Duration(seconds: 2)); // Mock API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location details saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _saveLocationDetailsToAPI() async {
    // Simulate API call to save location details information
    print('Saving location details information to API...');
    print('State: $selectedState');
    print('City: ${_cityController.text}');
    print('Native Place: ${_nativePlaceController.text}');
    print('Country: $selectedCountry');
    // In a real implementation, this would make HTTP request to backend
    await Future.delayed(const Duration(milliseconds: 500));
    print('Location details information saved successfully');
  }

  @override
  void dispose() {
    _cityController.dispose();
    _nativePlaceController.dispose();
    super.dispose();
  }
}
