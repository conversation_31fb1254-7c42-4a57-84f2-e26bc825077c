// Comprehensive Testing Script for All Implemented Features
const puppeteer = require('puppeteer');
const axios = require('axios');

const FRONTEND_URL = 'http://localhost:3000';
const BACKEND_URL = 'http://localhost:8000/api';

class ComprehensiveTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = {
            passed: 0,
            failed: 0,
            errors: []
        };
    }

    async initialize() {
        console.log('🚀 Initializing comprehensive testing suite...');
        this.browser = await puppeteer.launch({ 
            headless: false,
            slowMo: 50,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
        await this.page.setViewport({ width: 1280, height: 720 });
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async runTest(testName, testFunction) {
        try {
            console.log(`📝 Running: ${testName}...`);
            await testFunction();
            console.log(`✅ ${testName} - PASSED`);
            this.testResults.passed++;
        } catch (error) {
            console.error(`❌ ${testName} - FAILED: ${error.message}`);
            this.testResults.failed++;
            this.testResults.errors.push({ test: testName, error: error.message });
        }
    }

    async runAllTests() {
        try {
            await this.initialize();
            
            console.log('🧪 Starting Comprehensive Feature Testing...\n');
            
            // Backend API Tests
            await this.runTest('Backend Health Check', () => this.testBackendHealth());
            await this.runTest('User Account Deletion API', () => this.testAccountDeletionAPI());
            await this.runTest('Content Management API', () => this.testContentManagementAPI());
            await this.runTest('Premium Plans API', () => this.testPremiumPlansAPI());
            
            // Frontend UI Tests
            await this.runTest('Landing Page Load', () => this.testLandingPageLoad());
            await this.runTest('Join Vaivahik Button Design', () => this.testJoinVaivahikButton());
            await this.runTest('Hero Section Content', () => this.testHeroSection());
            await this.runTest('Dynamic Footer Links', () => this.testDynamicFooter());
            await this.runTest('Social Media Integration', () => this.testSocialMediaLinks());
            await this.runTest('Premium Plans Display', () => this.testPremiumPlansDisplay());
            await this.runTest('Settings Page Navigation', () => this.testSettingsNavigation());
            await this.runTest('Account Management Section', () => this.testAccountManagementSection());
            await this.runTest('Privacy Control Access', () => this.testPrivacyControlAccess());
            
            // Responsive Design Tests
            await this.runTest('Mobile Responsive Design', () => this.testMobileResponsive());
            await this.runTest('Tablet Responsive Design', () => this.testTabletResponsive());
            
            // Performance Tests
            await this.runTest('Page Load Performance', () => this.testPageLoadPerformance());
            
            this.printTestResults();
            
        } catch (error) {
            console.error('❌ Test suite failed:', error);
        } finally {
            await this.cleanup();
        }
    }

    // Backend API Tests
    async testBackendHealth() {
        const response = await axios.get(`${BACKEND_URL}/health`);
        if (response.status !== 200) {
            throw new Error('Backend health check failed');
        }
    }

    async testAccountDeletionAPI() {
        // Test unauthorized access
        try {
            await axios.delete(`${BACKEND_URL}/user/account`);
            throw new Error('Unauthorized deletion should have failed');
        } catch (error) {
            if (!error.response || error.response.status !== 401) {
                throw new Error('Expected 401 unauthorized error');
            }
        }
    }

    async testContentManagementAPI() {
        const response = await axios.get(`${BACKEND_URL}/content/pages`);
        if (response.status !== 200) {
            throw new Error('Content management API failed');
        }
    }

    async testPremiumPlansAPI() {
        const response = await axios.get(`${BACKEND_URL}/admin/premium-plans`);
        // Should work even without auth for public plans
        if (response.status !== 200 && response.status !== 401) {
            throw new Error('Premium plans API failed');
        }
    }

    // Frontend UI Tests
    async testLandingPageLoad() {
        await this.page.goto(FRONTEND_URL);
        await this.page.waitForSelector('header', { timeout: 10000 });
        
        const title = await this.page.title();
        if (!title.includes('Vaivahik')) {
            throw new Error('Landing page title incorrect');
        }
    }

    async testJoinVaivahikButton() {
        await this.page.goto(FRONTEND_URL);
        
        // Check header button exists and is styled correctly
        const headerButton = await this.page.$('nav a[href="/register"]');
        if (!headerButton) {
            throw new Error('Join Vaivahik button not found in header');
        }
        
        // Check button styling
        const buttonStyles = await this.page.evaluate((btn) => {
            const styles = window.getComputedStyle(btn);
            return {
                background: styles.background,
                borderRadius: styles.borderRadius,
                padding: styles.padding
            };
        }, headerButton);
        
        if (!buttonStyles.borderRadius || buttonStyles.borderRadius === '0px') {
            throw new Error('Button should have rounded corners');
        }
    }

    async testHeroSection() {
        await this.page.goto(FRONTEND_URL);
        
        // Check hero section exists
        const heroSection = await this.page.$('#hero');
        if (!heroSection) {
            throw new Error('Hero section not found');
        }
        
        // Check that Join Vaivahik button is NOT in hero section
        const heroJoinButton = await this.page.$('#hero a[href="/register"]:has-text("Join Vaivahik")');
        if (heroJoinButton) {
            throw new Error('Join Vaivahik button should not be in hero section');
        }
        
        // Check hero buttons exist
        const heroButtons = await this.page.$$('#hero .heroButtons a');
        if (heroButtons.length < 2) {
            throw new Error('Hero section should have at least 2 buttons');
        }
    }

    async testDynamicFooter() {
        await this.page.goto(FRONTEND_URL);
        
        // Wait for footer to load
        await this.page.waitForSelector('footer', { timeout: 5000 });
        
        // Check if footer links are present
        const footerLinks = await this.page.$$('footer a');
        if (footerLinks.length === 0) {
            throw new Error('Footer should contain links');
        }
    }

    async testSocialMediaLinks() {
        await this.page.goto(FRONTEND_URL);
        
        // Check for social media icons
        const socialIcons = await this.page.$$('footer .social-icons a, footer [class*="social"] a');
        if (socialIcons.length === 0) {
            console.warn('Warning: No social media links found');
        }
    }

    async testPremiumPlansDisplay() {
        await this.page.goto(FRONTEND_URL);
        
        // Scroll to pricing section
        await this.page.evaluate(() => {
            const pricingSection = document.querySelector('#pricing');
            if (pricingSection) {
                pricingSection.scrollIntoView();
            }
        });
        
        await this.page.waitForTimeout(2000);
        
        // Check if pricing cards are displayed
        const pricingCards = await this.page.$$('.pricingCard, [class*="pricing"] [class*="card"]');
        if (pricingCards.length === 0) {
            throw new Error('Premium plans should be displayed');
        }
    }

    async testSettingsNavigation() {
        await this.page.goto(`${FRONTEND_URL}/settings`);
        
        // Check if settings page loads
        const settingsPage = await this.page.$('[data-testid="settings-page"], .settings-page, main');
        if (!settingsPage) {
            throw new Error('Settings page should load');
        }
    }

    async testAccountManagementSection() {
        await this.page.goto(`${FRONTEND_URL}/settings`);
        
        // Look for Account Management option
        const accountManagement = await this.page.$('text=Account Management');
        if (!accountManagement) {
            throw new Error('Account Management section should be available');
        }
        
        // Click on it
        await accountManagement.click();
        
        // Check for delete account button
        await this.page.waitForSelector('button:has-text("Delete Account")', { timeout: 3000 });
    }

    async testPrivacyControlAccess() {
        await this.page.goto(`${FRONTEND_URL}/settings`);
        
        // Look for Privacy option
        const privacyOption = await this.page.$('text=Privacy');
        if (!privacyOption) {
            throw new Error('Privacy control should be accessible');
        }
    }

    // Responsive Design Tests
    async testMobileResponsive() {
        await this.page.setViewport({ width: 375, height: 667 });
        await this.page.goto(FRONTEND_URL);
        
        // Check if mobile navigation works
        const hamburger = await this.page.$('.hamburger, [class*="hamburger"]');
        if (hamburger) {
            await hamburger.click();
            await this.page.waitForTimeout(500);
        }
        
        // Check if Join Vaivahik button is visible on mobile
        const mobileJoinButton = await this.page.$('nav a[href="/register"]');
        if (!mobileJoinButton) {
            throw new Error('Join Vaivahik button should be visible on mobile');
        }
    }

    async testTabletResponsive() {
        await this.page.setViewport({ width: 768, height: 1024 });
        await this.page.goto(FRONTEND_URL);
        
        // Check basic layout
        const header = await this.page.$('header');
        if (!header) {
            throw new Error('Header should be visible on tablet');
        }
    }

    async testPageLoadPerformance() {
        const startTime = Date.now();
        await this.page.goto(FRONTEND_URL);
        await this.page.waitForSelector('header');
        const loadTime = Date.now() - startTime;
        
        if (loadTime > 10000) { // 10 seconds
            throw new Error(`Page load time too slow: ${loadTime}ms`);
        }
        
        console.log(`📊 Page load time: ${loadTime}ms`);
    }

    printTestResults() {
        console.log('\n🎯 TEST RESULTS SUMMARY');
        console.log('========================');
        console.log(`✅ Passed: ${this.testResults.passed}`);
        console.log(`❌ Failed: ${this.testResults.failed}`);
        console.log(`📊 Total: ${this.testResults.passed + this.testResults.failed}`);
        
        if (this.testResults.errors.length > 0) {
            console.log('\n❌ FAILED TESTS:');
            this.testResults.errors.forEach(error => {
                console.log(`   • ${error.test}: ${error.error}`);
            });
        }
        
        if (this.testResults.failed === 0) {
            console.log('\n🎉 ALL TESTS PASSED!');
        } else {
            console.log(`\n⚠️  ${this.testResults.failed} tests need attention.`);
        }
    }
}

// Run comprehensive tests
async function runComprehensiveTests() {
    const tester = new ComprehensiveTester();
    await tester.runAllTests();
}

if (require.main === module) {
    runComprehensiveTests().catch(console.error);
}

module.exports = { ComprehensiveTester };
