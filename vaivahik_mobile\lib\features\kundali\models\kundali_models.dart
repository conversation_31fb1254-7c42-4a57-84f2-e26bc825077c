import 'package:freezed_annotation/freezed_annotation.dart';

part 'kundali_models.freezed.dart';
part 'kundali_models.g.dart';

@freezed
class KundaliData with _$KundaliData {
  const factory KundaliData({
    required String id,
    required String userId,
    required String name,
    required DateTime birthDate,
    required String birthTime,
    required String birthPlace,
    required double latitude,
    required double longitude,
    required String timezone,
    required Map<String, dynamic> planetaryPositions,
    required Map<String, dynamic> houses,
    required Map<String, dynamic> aspects,
    required List<String> doshas,
    String? mangalDosha,
    String? kalSarpaDosha,
    String? pitruDosha,
    required DateTime createdAt,
    required DateTime updatedAt,
    // Additional premium features
    Map<String, dynamic>? yogas,
    Map<String, dynamic>? transits,
    List<String>? predictions,
    String? nakshatra,
    String? rashi,
    double? moonLongitude,
    String? ascendant,
    bool? isPremium,
  }) = _KundaliData;

  factory KundaliData.fromJson(Map<String, dynamic> json) => _$KundaliDataFromJson(json);
}

@freezed
class KundaliMatching with _$KundaliMatching {
  const factory KundaliMatching({
    required String id,
    required String user1Id,
    required String user2Id,
    required KundaliData user1Kundali,
    required KundaliData user2Kundali,
    required int totalScore,
    required int maxScore,
    required double compatibilityPercentage,
    required Map<String, GunaScore> gunaScores,
    required List<String> strengths,
    required List<String> challenges,
    required List<String> recommendations,
    required MatchingStatus status,
    required bool isPremiumFeature,
    required DateTime createdAt,
    String? astrologerNotes,
  }) = _KundaliMatching;

  factory KundaliMatching.fromJson(Map<String, dynamic> json) => _$KundaliMatchingFromJson(json);
}

@freezed
class GunaScore with _$GunaScore {
  const factory GunaScore({
    required String gunaName,
    required int obtainedScore,
    required int maxScore,
    required String description,
    required CompatibilityLevel level,
    required double percentage,
  }) = _GunaScore;

  factory GunaScore.fromJson(Map<String, dynamic> json) => _$GunaScoreFromJson(json);
}

enum MatchingStatus {
  pending,
  processing,
  completed,
  failed
}

enum CompatibilityLevel {
  excellent,
  good,
  average,
  poor
}

@freezed
class AstrologyReport with _$AstrologyReport {
  const factory AstrologyReport({
    required String id,
    required String userId,
    required String type,
    required String title,
    required String content,
    required Map<String, dynamic> data,
    required DateTime generatedAt,
    String? astrologerId,
    String? astrologerName,
    required bool isPremium,
    double? price,
  }) = _AstrologyReport;

  factory AstrologyReport.fromJson(Map<String, dynamic> json) => _$AstrologyReportFromJson(json);
}

@freezed
class KundaliSettings with _$KundaliSettings {
  const factory KundaliSettings({
    required bool showKundali,
    required bool allowKundaliMatching,
    required bool requireKundaliForMatching,
    required bool showDoshas,
    required bool showPlanetaryPositions,
    required bool showHouses,
    required bool showAspects,
    required String preferredLanguage,
    required bool enableNotifications,
  }) = _KundaliSettings;

  factory KundaliSettings.fromJson(Map<String, dynamic> json) => _$KundaliSettingsFromJson(json);
}
