import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../theme/app_theme.dart';
import '../../features/notifications/providers/notification_provider.dart';



class MainNavigationScreen extends ConsumerStatefulWidget {
  final Widget child;

  const MainNavigationScreen({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends ConsumerState<MainNavigationScreen> {
  int _currentIndex = 0;

  final List<NavigationItem> _navigationItems = [
    const NavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
      route: '/home',
    ),
    const NavigationItem(
      icon: Icons.favorite_outline,
      activeIcon: Icons.favorite,
      label: 'Matches',
      route: '/matches',
    ),
    const NavigationItem(
      icon: Icons.chat_bubble_outline,
      activeIcon: Icons.chat_bubble,
      label: 'Chat',
      route: '/chat',
    ),
    const NavigationItem(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Profile',
      route: '/profile',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _updateCurrentIndex();
  }

  void _updateCurrentIndex() {
    final location = GoRouterState.of(context).matchedLocation;
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        setState(() {
          _currentIndex = i;
        });
        break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final unreadNotifications = ref.watch(unreadNotificationsCountProvider);
    const unreadMessages = 0; // Chat provider will be implemented later

    return Scaffold(
      body: widget.child,
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: _onItemTapped,
          type: BottomNavigationBarType.fixed,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          selectedItemColor: AppTheme.primaryColor,
          unselectedItemColor: Colors.grey[600],
          selectedFontSize: 12,
          unselectedFontSize: 12,
          elevation: 0,
          items: _navigationItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = _currentIndex == index;
            
            // Add badge for notifications and messages
            Widget icon = Icon(
              isSelected ? item.activeIcon : item.icon,
              size: 24,
            );
            
            if (index == 2 && unreadMessages > 0) {
              // Chat tab with unread messages
              icon = Badge(
                label: Text(
                  unreadMessages > 99 ? '99+' : unreadMessages.toString(),
                  style: const TextStyle(fontSize: 10, color: Colors.white),
                ),
                backgroundColor: Colors.red,
                child: icon,
              );
            } else if (index == 3 && unreadNotifications > 0) {
              // Notifications tab with unread notifications
              icon = Badge(
                label: Text(
                  unreadNotifications > 99 ? '99+' : unreadNotifications.toString(),
                  style: const TextStyle(fontSize: 10, color: Colors.white),
                ),
                backgroundColor: Colors.red,
                child: icon,
              );
            }
            
            return BottomNavigationBarItem(
              icon: icon,
              label: item.label,
            );
          }).toList(),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  void _onItemTapped(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  Widget? _buildFloatingActionButton() {
    // Show different FABs based on current screen
    switch (_currentIndex) {
      case 0: // Home
        return FloatingActionButton(
          onPressed: () => context.push('/search'),
          backgroundColor: AppTheme.primaryColor,
          child: const Icon(Icons.search, color: Colors.white),
        );
      case 1: // Matches
        return FloatingActionButton(
          onPressed: () => context.push('/search'),
          backgroundColor: AppTheme.primaryColor,
          child: const Icon(Icons.filter_list, color: Colors.white),
        );
      case 2: // Chat
        return null; // No FAB for chat
      case 3: // Profile
        return FloatingActionButton(
          onPressed: () => context.push('/profile/edit'),
          backgroundColor: AppTheme.primaryColor,
          child: const Icon(Icons.edit, color: Colors.white),
        );
      default:
        return null;
    }
  }
}

class NavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final String route;

  const NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.route,
  });
}

// Custom App Bar for consistent design
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool centerTitle;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double elevation;

  const CustomAppBar({
    super.key,
    required this.title,
    this.actions,
    this.leading,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation = 0,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      actions: actions,
      leading: leading,
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
      foregroundColor: foregroundColor ?? Theme.of(context).textTheme.titleLarge?.color,
      elevation: elevation,
      surfaceTintColor: Colors.transparent,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

// Custom Drawer for additional navigation
class CustomDrawer extends ConsumerWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const DrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.secondaryColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Colors.white,
                  child: Icon(
                    Icons.person,
                    size: 40,
                    color: AppTheme.primaryColor,
                  ),
                ),
                SizedBox(height: 10),
                Text(
                  'Vaivahik',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.analytics_outlined,
            title: 'Analytics',
            onTap: () => context.push('/analytics'),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.description_outlined,
            title: 'Biodata',
            onTap: () => context.push('/biodata'),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.star_outline,
            title: 'Kundali',
            onTap: () => context.push('/kundali'),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.workspace_premium_outlined,
            title: 'Premium',
            onTap: () => context.push('/premium'),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.history,
            title: 'Call History',
            onTap: () => context.push('/call-history'),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.notifications_outlined,
            title: 'Notifications',
            onTap: () => context.push('/notifications'),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.privacy_tip_outlined,
            title: 'Privacy & Security',
            onTap: () => context.push('/privacy'),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.settings_outlined,
            title: 'Settings',
            onTap: () => context.push('/settings'),
          ),
          const Divider(),
          _buildDrawerItem(
            context,
            icon: Icons.help_outline,
            title: 'Help & Support',
            onTap: () {
              // Help & support will be implemented later
            },
          ),
          _buildDrawerItem(
            context,
            icon: Icons.info_outline,
            title: 'About',
            onTap: () {
              // About page will be implemented later
            },
          ),
          _buildDrawerItem(
            context,
            icon: Icons.logout,
            title: 'Logout',
            onTap: () {
              // Logout functionality will be implemented later
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
    );
  }
}

// Providers for badge counts
final unreadNotificationCountProvider = StateProvider<int>((ref) => 0);
final unreadMessageCountProvider = StateProvider<int>((ref) => 0);
