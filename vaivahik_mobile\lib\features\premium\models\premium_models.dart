class PremiumPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final String currency;
  final int durationDays;
  final PlanType type;
  final List<String> features;
  final List<String> benefits;
  final bool isPopular;
  final bool isActive;
  final double? discountPercentage;
  final double? originalPrice;
  final DateTime? validUntil;
  final Map<String, dynamic>? metadata;

  const PremiumPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.currency,
    required this.durationDays,
    required this.type,
    required this.features,
    required this.benefits,
    required this.isPopular,
    required this.isActive,
    this.discountPercentage,
    this.originalPrice,
    this.validUntil,
    this.metadata,
  });

  factory PremiumPlan.fromJson(Map<String, dynamic> json) {
    return PremiumPlan(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      price: json['price']?.toDouble() ?? 0.0,
      currency: json['currency']?.toString() ?? 'INR',
      durationDays: json['durationDays']?.toInt() ?? 30,
      type: PlanType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => PlanType.monthly,
      ),
      features: List<String>.from(json['features'] ?? []),
      benefits: List<String>.from(json['benefits'] ?? []),
      isPopular: json['isPopular'] ?? false,
      isActive: json['isActive'] ?? true,
      discountPercentage: json['discountPercentage']?.toDouble(),
      originalPrice: json['originalPrice']?.toDouble(),
      validUntil: json['validUntil'] != null 
          ? DateTime.tryParse(json['validUntil'].toString())
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'durationDays': durationDays,
      'type': type.name,
      'features': features,
      'benefits': benefits,
      'isPopular': isPopular,
      'isActive': isActive,
      'discountPercentage': discountPercentage,
      'originalPrice': originalPrice,
      'validUntil': validUntil?.toIso8601String(),
      'metadata': metadata,
    };
  }
}

enum PlanType {
  weekly,
  monthly,
  quarterly,
  yearly,
  lifetime
}

class UserSubscription {
  final String id;
  final String userId;
  final String planId;
  final PremiumPlan plan;
  final SubscriptionStatus status;
  final DateTime startDate;
  final DateTime endDate;
  final bool autoRenew;
  final PaymentMethod paymentMethod;
  final String? transactionId;
  final double amountPaid;
  final String currency;
  final DateTime createdAt;
  final DateTime? cancelledAt;
  final String? cancellationReason;
  final Map<String, dynamic>? metadata;

  const UserSubscription({
    required this.id,
    required this.userId,
    required this.planId,
    required this.plan,
    required this.status,
    required this.startDate,
    required this.endDate,
    required this.autoRenew,
    required this.paymentMethod,
    this.transactionId,
    required this.amountPaid,
    required this.currency,
    required this.createdAt,
    this.cancelledAt,
    this.cancellationReason,
    this.metadata,
  });

  factory UserSubscription.fromJson(Map<String, dynamic> json) {
    return UserSubscription(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      planId: json['planId']?.toString() ?? '',
      plan: PremiumPlan.fromJson(json['plan'] ?? {}),
      status: SubscriptionStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => SubscriptionStatus.inactive,
      ),
      startDate: DateTime.tryParse(json['startDate']?.toString() ?? '') ?? DateTime.now(),
      endDate: DateTime.tryParse(json['endDate']?.toString() ?? '') ?? DateTime.now(),
      autoRenew: json['autoRenew'] ?? false,
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.name == json['paymentMethod']?.toString(),
        orElse: () => PaymentMethod.razorpay,
      ),
      transactionId: json['transactionId']?.toString(),
      amountPaid: json['amountPaid']?.toDouble() ?? 0.0,
      currency: json['currency']?.toString() ?? 'INR',
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      cancelledAt: json['cancelledAt'] != null 
          ? DateTime.tryParse(json['cancelledAt'].toString())
          : null,
      cancellationReason: json['cancellationReason']?.toString(),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'planId': planId,
      'plan': plan.toJson(),
      'status': status.name,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'autoRenew': autoRenew,
      'paymentMethod': paymentMethod.name,
      'transactionId': transactionId,
      'amountPaid': amountPaid,
      'currency': currency,
      'createdAt': createdAt.toIso8601String(),
      'cancelledAt': cancelledAt?.toIso8601String(),
      'cancellationReason': cancellationReason,
      'metadata': metadata,
    };
  }

  bool get isActive => status == SubscriptionStatus.active && endDate.isAfter(DateTime.now());
  bool get isExpired => endDate.isBefore(DateTime.now());
  int get daysRemaining => isActive ? endDate.difference(DateTime.now()).inDays : 0;
}

enum SubscriptionStatus {
  active,
  inactive,
  expired,
  cancelled,
  pending,
  failed
}

enum PaymentMethod {
  razorpay,
  paytm,
  googlepay,
  phonepe,
  upi,
  netbanking,
  card,
  wallet
}

class PremiumFeature {
  final String id;
  final String name;
  final String description;
  final String icon;
  final bool isEnabled;
  final List<String> requiredPlans;
  final int? usageLimit;
  final int? usageCount;
  final DateTime? resetDate;

  const PremiumFeature({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.isEnabled,
    required this.requiredPlans,
    this.usageLimit,
    this.usageCount,
    this.resetDate,
  });

  factory PremiumFeature.fromJson(Map<String, dynamic> json) {
    return PremiumFeature(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      icon: json['icon']?.toString() ?? '',
      isEnabled: json['isEnabled'] ?? true,
      requiredPlans: List<String>.from(json['requiredPlans'] ?? []),
      usageLimit: json['usageLimit']?.toInt(),
      usageCount: json['usageCount']?.toInt(),
      resetDate: json['resetDate'] != null 
          ? DateTime.tryParse(json['resetDate'].toString())
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon,
      'isEnabled': isEnabled,
      'requiredPlans': requiredPlans,
      'usageLimit': usageLimit,
      'usageCount': usageCount,
      'resetDate': resetDate?.toIso8601String(),
    };
  }

  bool get hasUsageLimit => usageLimit != null;
  bool get isUsageLimitReached => hasUsageLimit && (usageCount ?? 0) >= usageLimit!;
  int get remainingUsage => hasUsageLimit ? (usageLimit! - (usageCount ?? 0)).clamp(0, usageLimit!) : -1;
}

class PaymentTransaction {
  final String id;
  final String userId;
  final String planId;
  final double amount;
  final String currency;
  final PaymentMethod method;
  final TransactionStatus status;
  final String? gatewayTransactionId;
  final String? gatewayOrderId;
  final DateTime createdAt;
  final DateTime? completedAt;
  final String? failureReason;
  final Map<String, dynamic>? gatewayResponse;

  const PaymentTransaction({
    required this.id,
    required this.userId,
    required this.planId,
    required this.amount,
    required this.currency,
    required this.method,
    required this.status,
    this.gatewayTransactionId,
    this.gatewayOrderId,
    required this.createdAt,
    this.completedAt,
    this.failureReason,
    this.gatewayResponse,
  });

  factory PaymentTransaction.fromJson(Map<String, dynamic> json) {
    return PaymentTransaction(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      planId: json['planId']?.toString() ?? '',
      amount: json['amount']?.toDouble() ?? 0.0,
      currency: json['currency']?.toString() ?? 'INR',
      method: PaymentMethod.values.firstWhere(
        (e) => e.name == json['method']?.toString(),
        orElse: () => PaymentMethod.razorpay,
      ),
      status: TransactionStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => TransactionStatus.pending,
      ),
      gatewayTransactionId: json['gatewayTransactionId']?.toString(),
      gatewayOrderId: json['gatewayOrderId']?.toString(),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      completedAt: json['completedAt'] != null 
          ? DateTime.tryParse(json['completedAt'].toString())
          : null,
      failureReason: json['failureReason']?.toString(),
      gatewayResponse: json['gatewayResponse'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'planId': planId,
      'amount': amount,
      'currency': currency,
      'method': method.name,
      'status': status.name,
      'gatewayTransactionId': gatewayTransactionId,
      'gatewayOrderId': gatewayOrderId,
      'createdAt': createdAt.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'failureReason': failureReason,
      'gatewayResponse': gatewayResponse,
    };
  }
}

enum TransactionStatus {
  pending,
  processing,
  completed,
  failed,
  cancelled,
  refunded
}

class PremiumSettings {
  final bool autoRenew;
  final bool emailNotifications;
  final bool smsNotifications;
  final bool pushNotifications;
  final PaymentMethod preferredPaymentMethod;
  final String? billingEmail;
  final Map<String, bool> featurePreferences;

  const PremiumSettings({
    required this.autoRenew,
    required this.emailNotifications,
    required this.smsNotifications,
    required this.pushNotifications,
    required this.preferredPaymentMethod,
    this.billingEmail,
    required this.featurePreferences,
  });

  factory PremiumSettings.fromJson(Map<String, dynamic> json) {
    return PremiumSettings(
      autoRenew: json['autoRenew'] ?? true,
      emailNotifications: json['emailNotifications'] ?? true,
      smsNotifications: json['smsNotifications'] ?? true,
      pushNotifications: json['pushNotifications'] ?? true,
      preferredPaymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.name == json['preferredPaymentMethod']?.toString(),
        orElse: () => PaymentMethod.razorpay,
      ),
      billingEmail: json['billingEmail']?.toString(),
      featurePreferences: Map<String, bool>.from(json['featurePreferences'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoRenew': autoRenew,
      'emailNotifications': emailNotifications,
      'smsNotifications': smsNotifications,
      'pushNotifications': pushNotifications,
      'preferredPaymentMethod': preferredPaymentMethod.name,
      'billingEmail': billingEmail,
      'featurePreferences': featurePreferences,
    };
  }
}
