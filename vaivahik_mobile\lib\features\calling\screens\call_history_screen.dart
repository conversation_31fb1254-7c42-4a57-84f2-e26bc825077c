import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/api/api_client.dart';
import '../services/calling_service.dart';
import '../models/call_models.dart';

// API Client provider
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

class CallHistoryScreen extends ConsumerStatefulWidget {
  const CallHistoryScreen({super.key});

  @override
  ConsumerState<CallHistoryScreen> createState() => _CallHistoryScreenState();
}

class _CallHistoryScreenState extends ConsumerState<CallHistoryScreen> {
  List<CallHistory> _callHistory = [];
  bool _loading = true;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _loadCallHistory();
  }

  Future<void> _loadCallHistory() async {
    try {
      setState(() {
        _loading = true;
        _error = '';
      });

      final apiClient = ref.read(apiClientProvider);
      final callingService = CallingService(apiClient);
      final history = await callingService.getCallHistory();

      setState(() {
        _callHistory = history;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _loading = false;
      });
    }
  }

  Future<void> _clearHistory() async {
    try {
      // For now, just clear the local list
      // In a real app, you'd call an API to clear server-side history
      setState(() {
        _callHistory.clear();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Call history cleared')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error clearing history: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Call History'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_callHistory.isNotEmpty)
            IconButton(
              icon: const Icon(Icons.clear_all),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Clear Call History'),
                    content: const Text('Are you sure you want to clear all call history?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          _clearHistory();
                        },
                        child: const Text('Clear'),
                      ),
                    ],
                  ),
                );
              },
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_loading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 80, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'Error loading call history',
              style: AppTextStyles.h4,
            ),
            const SizedBox(height: 8),
            Text(
              _error,
              style: AppTextStyles.bodyMedium.copyWith(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCallHistory,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_callHistory.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.call, size: 80, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No Call History',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Your call history will appear here...'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadCallHistory,
      child: ListView.builder(
        itemCount: _callHistory.length,
        itemBuilder: (context, index) {
          final call = _callHistory[index];
          return _buildCallHistoryItem(call);
        },
      ),
    );
  }

  Widget _buildCallHistoryItem(CallHistory call) {
    final isOutgoing = !call.isIncoming;
    final contactName = call.otherUserName;
    final contactId = call.otherUserId;

    IconData callIcon;
    Color iconColor;

    switch (call.status) {
      case CallStatus.ended:
        callIcon = isOutgoing ? Icons.call_made : Icons.call_received;
        iconColor = Colors.green;
        break;
      case CallStatus.missed:
        callIcon = isOutgoing ? Icons.call_made : Icons.call_received;
        iconColor = Colors.red;
        break;
      case CallStatus.declined:
        callIcon = Icons.call_end;
        iconColor = Colors.red;
        break;
      case CallStatus.failed:
        callIcon = Icons.error_outline;
        iconColor = Colors.orange;
        break;
      case CallStatus.busy:
        callIcon = Icons.phone_disabled;
        iconColor = Colors.orange;
        break;
      default:
        callIcon = Icons.call;
        iconColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: iconColor.withAlpha(26),
          child: Icon(callIcon, color: iconColor),
        ),
        title: Text(
          contactName,
          style: AppTextStyles.bodyLarge.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _getCallStatusText(call.status, isOutgoing),
              style: AppTextStyles.bodySmall.copyWith(color: iconColor),
            ),
            Text(
              DateFormat('MMM dd, yyyy • hh:mm a').format(call.timestamp),
              style: AppTextStyles.bodySmall.copyWith(color: AppColors.textSecondary),
            ),
            if (call.duration != null && call.duration!.inSeconds > 0)
              Text(
                'Duration: ${_formatDuration(call.duration!.inSeconds)}',
                style: AppTextStyles.bodySmall.copyWith(color: AppColors.textSecondary),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (call.type == CallType.video)
              const Icon(Icons.videocam, size: 16, color: AppColors.textSecondary),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.call, color: AppColors.primary),
              onPressed: () => _makeCall(contactId, contactName),
            ),
          ],
        ),
        onTap: () => _showCallDetails(call),
      ),
    );
  }

  String _getCallStatusText(CallStatus status, bool isOutgoing) {
    switch (status) {
      case CallStatus.ended:
        return isOutgoing ? 'Outgoing call' : 'Incoming call';
      case CallStatus.missed:
        return isOutgoing ? 'Call not answered' : 'Missed call';
      case CallStatus.declined:
        return 'Call declined';
      case CallStatus.failed:
        return 'Call failed';
      case CallStatus.busy:
        return 'Line busy';
      default:
        return 'Unknown';
    }
  }

  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final secs = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m ${secs}s';
    } else if (minutes > 0) {
      return '${minutes}m ${secs}s';
    } else {
      return '${secs}s';
    }
  }

  Future<void> _makeCall(String contactId, String contactName) async {
    try {
      final apiClient = ref.read(apiClientProvider);
      final callingService = CallingService(apiClient);
      await callingService.initiateVoiceCall(contactId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Calling $contactName...')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error making call: $e')),
        );
      }
    }
  }

  void _showCallDetails(CallHistory call) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info_outline, color: AppColors.primary),
            SizedBox(width: 8),
            Text('Call Details'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Contact', call.otherUserName),
            _buildDetailRow('Type', call.type == CallType.video ? 'Video Call' : 'Voice Call'),
            _buildDetailRow('Status', _getCallStatusText(call.status, !call.isIncoming)),
            _buildDetailRow('Date & Time', DateFormat('MMM dd, yyyy • hh:mm a').format(call.timestamp)),
            if (call.duration != null && call.duration!.inSeconds > 0)
              _buildDetailRow('Duration', _formatDuration(call.duration!.inSeconds)),
            _buildDetailRow('Direction', call.isIncoming ? 'Incoming' : 'Outgoing'),
            _buildDetailRow('Read Status', call.isRead ? 'Read' : 'Unread'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodySmall,
            ),
          ),
        ],
      ),
    );
  }
}
