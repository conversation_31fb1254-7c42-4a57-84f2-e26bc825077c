import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'shortlist_model.freezed.dart';
part 'shortlist_model.g.dart';

// ignore_for_file: non_abstract_class_inherits_abstract_member

/// 📋 SHORTLIST MODEL - Complete Shortlist Data Structure
/// Features: Profile Data, Notes, Interest Status, Timestamps
@freezed
class ShortlistItem with _$ShortlistItem {
  const factory ShortlistItem({
    required String id,
    required String userId,
    required String targetUserId,
    String? note,
    required DateTime createdAt,
    DateTime? updatedAt,
    required bool interestSent,
    String? interestStatus,
    required ShortlistProfile profile,
  }) = _ShortlistItem;

  factory ShortlistItem.fromJson(Map<String, dynamic> json) => _$ShortlistItemFromJson(json);
}

/// 👤 SHORTLIST PROFILE - Profile Information in Shortlist
@freezed
class ShortlistProfile with _$ShortlistProfile {
  const factory ShortlistProfile({
    required String id,
    required String firstName,
    String? lastName,
    int? age,
    String? location,
    String? education,
    String? occupation,
    String? profilePicture,
    int? compatibility,
    String? religion,
    String? caste,
    String? motherTongue,
    String? maritalStatus,
    double? height,
    String? income,
    bool? isVerified,
    bool? isOnline,
    DateTime? lastSeen,
  }) = _ShortlistProfile;

  factory ShortlistProfile.fromJson(Map<String, dynamic> json) => _$ShortlistProfileFromJson(json);
}

/// 📊 SHORTLIST STATS - Statistics for Shortlist Dashboard
@freezed
class ShortlistStats with _$ShortlistStats {
  const factory ShortlistStats({
    required int totalShortlisted,
    required int interestsSent,
    required int interestsReceived,
    required int mutualInterests,
    required int recentlyAdded,
    required int thisWeek,
    required int thisMonth,
  }) = _ShortlistStats;

  factory ShortlistStats.fromJson(Map<String, dynamic> json) => _$ShortlistStatsFromJson(json);
}

/// 🔍 SHORTLIST FILTERS - Filter Options for Shortlist
@freezed
class ShortlistFilters with _$ShortlistFilters {
  const factory ShortlistFilters({
    String? searchQuery,
    bool? interestSent,
    String? interestStatus,
    String? location,
    String? education,
    String? occupation,
    int? minAge,
    int? maxAge,
    double? minHeight,
    double? maxHeight,
    String? religion,
    String? caste,
    String? maritalStatus,
    bool? isVerified,
    bool? isOnline,
    String? sortBy, // 'recent', 'name', 'age', 'compatibility'
    bool? sortAscending,
  }) = _ShortlistFilters;

  factory ShortlistFilters.fromJson(Map<String, dynamic> json) => _$ShortlistFiltersFromJson(json);
}

/// 📝 ADD TO SHORTLIST REQUEST
@freezed
class AddToShortlistRequest with _$AddToShortlistRequest {
  const factory AddToShortlistRequest({
    required String profileId,
    String? note,
  }) = _AddToShortlistRequest;

  factory AddToShortlistRequest.fromJson(Map<String, dynamic> json) => _$AddToShortlistRequestFromJson(json);
}

/// ✏️ UPDATE SHORTLIST NOTE REQUEST
@freezed
class UpdateShortlistNoteRequest with _$UpdateShortlistNoteRequest {
  const factory UpdateShortlistNoteRequest({
    required String shortlistId,
    String? note,
  }) = _UpdateShortlistNoteRequest;

  factory UpdateShortlistNoteRequest.fromJson(Map<String, dynamic> json) => _$UpdateShortlistNoteRequestFromJson(json);
}

/// 🗑️ REMOVE FROM SHORTLIST REQUEST
@freezed
class RemoveFromShortlistRequest with _$RemoveFromShortlistRequest {
  const factory RemoveFromShortlistRequest({
    required String shortlistId,
  }) = _RemoveFromShortlistRequest;

  factory RemoveFromShortlistRequest.fromJson(Map<String, dynamic> json) => _$RemoveFromShortlistRequestFromJson(json);
}

/// 📋 SHORTLIST RESPONSE - API Response Wrapper
@freezed
class ShortlistResponse with _$ShortlistResponse {
  const factory ShortlistResponse({
    required bool success,
    String? message,
    List<ShortlistItem>? data,
    ShortlistStats? stats,
    int? total,
    int? page,
    int? limit,
    bool? hasMore,
  }) = _ShortlistResponse;

  factory ShortlistResponse.fromJson(Map<String, dynamic> json) => _$ShortlistResponseFromJson(json);
}

/// 🎯 SHORTLIST ACTION RESULT
@freezed
class ShortlistActionResult with _$ShortlistActionResult {
  const factory ShortlistActionResult({
    required bool success,
    String? message,
    String? shortlistId,
    ShortlistItem? item,
  }) = _ShortlistActionResult;

  factory ShortlistActionResult.fromJson(Map<String, dynamic> json) => _$ShortlistActionResultFromJson(json);
}

/// 📱 SHORTLIST UI STATE
@freezed
class ShortlistUIState with _$ShortlistUIState {
  const factory ShortlistUIState({
    @Default([]) List<ShortlistItem> items,
    @Default(false) bool isLoading,
    @Default(false) bool isRefreshing,
    @Default(false) bool hasMore,
    @Default(1) int currentPage,
    String? error,
    ShortlistFilters? filters,
    ShortlistStats? stats,
    String? selectedItemId,
    @Default(false) bool showFilters,
    @Default('recent') String sortBy,
    @Default(false) bool sortAscending,
  }) = _ShortlistUIState;

  factory ShortlistUIState.fromJson(Map<String, dynamic> json) => _$ShortlistUIStateFromJson(json);
}

/// 🎨 SHORTLIST CARD STYLE ENUM
enum ShortlistCardStyle {
  compact,
  detailed,
  grid,
  list,
}

/// 📊 SHORTLIST SORT OPTIONS
enum ShortlistSortOption {
  recent('Recent', 'recent'),
  name('Name', 'name'),
  age('Age', 'age'),
  compatibility('Compatibility', 'compatibility'),
  location('Location', 'location');

  const ShortlistSortOption(this.label, this.value);
  final String label;
  final String value;
}

/// 🔍 SHORTLIST VIEW MODE
enum ShortlistViewMode {
  all,
  interestSent,
  noInterest,
  mutualInterest,
}

/// 📋 SHORTLIST EXTENSIONS
extension ShortlistItemExtensions on ShortlistItem {
  String get fullName => '${profile.firstName} ${profile.lastName ?? ''}'.trim();
  
  bool get hasMutualInterest => interestSent && interestStatus == 'ACCEPTED';
  
  bool get isPendingInterest => interestSent && interestStatus == 'PENDING';
  
  String get displayLocation {
    if (profile.location?.isNotEmpty == true) {
      return profile.location!;
    }
    return 'Location not specified';
  }
  
  String get displayAge {
    if (profile.age != null) {
      return '${profile.age} years';
    }
    return 'Age not specified';
  }
  
  String get displayEducation {
    if (profile.education?.isNotEmpty == true) {
      return profile.education!;
    }
    return 'Education not specified';
  }
  
  String get displayOccupation {
    if (profile.occupation?.isNotEmpty == true) {
      return profile.occupation!;
    }
    return 'Occupation not specified';
  }
  
  Color get interestStatusColor {
    switch (interestStatus) {
      case 'PENDING':
        return Colors.orange;
      case 'ACCEPTED':
        return Colors.green;
      case 'DECLINED':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  IconData get interestStatusIcon {
    switch (interestStatus) {
      case 'PENDING':
        return Icons.schedule;
      case 'ACCEPTED':
        return Icons.check_circle;
      case 'DECLINED':
        return Icons.cancel;
      default:
        return Icons.favorite_border;
    }
  }
  
  String get interestStatusText {
    switch (interestStatus) {
      case 'PENDING':
        return 'Interest Pending';
      case 'ACCEPTED':
        return 'Interest Accepted';
      case 'DECLINED':
        return 'Interest Declined';
      default:
        return interestSent ? 'Interest Sent' : 'No Interest Sent';
    }
  }
}

extension ShortlistStatsExtensions on ShortlistStats {
  double get interestSuccessRate {
    if (interestsSent == 0) return 0.0;
    return (interestsReceived / interestsSent) * 100;
  }
  
  double get mutualInterestRate {
    if (interestsSent == 0) return 0.0;
    return (mutualInterests / interestsSent) * 100;
  }
  
  bool get hasActivity => totalShortlisted > 0 || interestsSent > 0;
}
