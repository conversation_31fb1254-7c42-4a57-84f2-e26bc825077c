/**
 * Comprehensive Kundali System Test Suite
 * Tests authentic Vedic astrology calculations and compatibility matching
 */

const VedicAstrologyService = require('../src/services/vedicAstrology.service');
const ManglikDoshaService = require('../src/services/manglikDosha.service');
const ComprehensiveKundaliService = require('../src/services/comprehensiveKundali.service');

describe('Kundali System Tests', () => {
  let vedicService;
  let manglikService;
  let kundaliService;

  beforeAll(() => {
    vedicService = new VedicAstrologyService();
    manglikService = new ManglikDoshaService();
    kundaliService = new ComprehensiveKundaliService();
  });

  describe('Vedic Astrology Service', () => {
    test('should calculate correct Nakshatra from moon longitude', () => {
      // Test case: Moon at 45 degrees should be in Rohini (2nd Nakshatra)
      const nakshatra = vedicService.calculateNakshatra(45);
      expect(nakshatra.name).toBe('Roh<PERSON>');
      expect(nakshatra.lord).toBe('Moon');
      expect(nakshatra.pada).toBe(2);
    });

    test('should calculate correct Rashi from longitude', () => {
      // Test case: 45 degrees should be in Taurus (2nd Rashi)
      const rashi = vedicService.calculateRashi(45);
      expect(rashi.name).toBe('Taurus');
      expect(rashi.lord).toBe('Venus');
    });

    test('should generate birth chart with all required elements', () => {
      const birthData = {
        birthDate: '1990-05-15',
        birthTime: '14:30',
        birthPlace: 'Mumbai, India'
      };

      const chart = vedicService.calculateBirthChart(
        birthData.birthDate,
        birthData.birthTime,
        birthData.birthPlace
      );

      expect(chart).toHaveProperty('moonSign');
      expect(chart).toHaveProperty('nakshatra');
      expect(chart).toHaveProperty('ascendant');
      expect(chart).toHaveProperty('planetaryPositions');
      expect(chart.planetaryPositions).toHaveProperty('sun');
      expect(chart.planetaryPositions).toHaveProperty('moon');
      expect(chart.planetaryPositions).toHaveProperty('mars');
    });

    test('should calculate Ashtakoot Guna correctly', () => {
      const chart1 = {
        moonSign: { name: 'Aries', number: 1 },
        nakshatra: { name: 'Ashwini', number: 1, pada: 1 }
      };

      const chart2 = {
        moonSign: { name: 'Leo', number: 5 },
        nakshatra: { name: 'Magha', number: 10, pada: 2 }
      };

      const gunaResult = vedicService.calculateAshtakootGuna(chart1, chart2);

      expect(gunaResult).toHaveProperty('scores');
      expect(gunaResult).toHaveProperty('totalScore');
      expect(gunaResult).toHaveProperty('maxScore', 36);
      expect(gunaResult).toHaveProperty('percentage');
      expect(gunaResult.totalScore).toBeGreaterThanOrEqual(0);
      expect(gunaResult.totalScore).toBeLessThanOrEqual(36);
    });
  });

  describe('Manglik Dosha Service', () => {
    test('should detect Manglik dosha correctly', () => {
      const birthData = {
        birthDate: '1990-05-15',
        birthTime: '14:30',
        birthPlace: 'Mumbai, India'
      };

      const doshaResult = manglikService.detectManglikDosha(
        birthData.birthDate,
        birthData.birthTime,
        birthData.birthPlace
      );

      expect(doshaResult).toHaveProperty('isManglik');
      expect(doshaResult).toHaveProperty('intensity');
      expect(doshaResult).toHaveProperty('affectedHouses');
      expect(doshaResult).toHaveProperty('remedies');
      expect(['None', 'Low', 'Medium', 'High']).toContain(doshaResult.intensity);
    });

    test('should provide appropriate remedies for different dosha levels', () => {
      const highDoshaResult = {
        isManglik: true,
        intensity: 'High',
        affectedHouses: [1, 7, 8]
      };

      const remedies = manglikService.getRemedies(highDoshaResult);
      expect(remedies).toBeInstanceOf(Array);
      expect(remedies.length).toBeGreaterThan(0);
      expect(remedies[0]).toHaveProperty('type');
      expect(remedies[0]).toHaveProperty('description');
    });

    test('should calculate Manglik compatibility correctly', () => {
      const person1Dosha = { isManglik: true, intensity: 'Medium' };
      const person2Dosha = { isManglik: false, intensity: 'None' };

      const compatibility = manglikService.calculateManglikCompatibility(person1Dosha, person2Dosha);

      expect(compatibility).toHaveProperty('isCompatible');
      expect(compatibility).toHaveProperty('remediesRequired');
      expect(compatibility).toHaveProperty('recommendation');
    });
  });

  describe('Comprehensive Kundali Service', () => {
    const testUser1 = {
      id: 'user1',
      name: 'Test User 1',
      birthDate: '1990-05-15',
      birthTime: '14:30',
      birthPlace: 'Mumbai, India',
      timezone: 'IST'
    };

    const testUser2 = {
      id: 'user2',
      name: 'Test User 2',
      birthDate: '1992-08-20',
      birthTime: '10:15',
      birthPlace: 'Delhi, India',
      timezone: 'IST'
    };

    test('should validate birth data correctly', () => {
      const validData = {
        birthDate: '1990-05-15',
        birthTime: '14:30',
        birthPlace: 'Mumbai, India'
      };

      const invalidData = {
        birthDate: 'invalid-date',
        birthTime: '25:70',
        birthPlace: ''
      };

      const validResult = kundaliService.validateBirthData(validData);
      const invalidResult = kundaliService.validateBirthData(invalidData);

      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);

      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors.length).toBeGreaterThan(0);
    });

    test('should generate complete kundali match', async () => {
      const options = {
        includeCharts: true,
        includeRemedies: true,
        includeAuspiciousDates: false,
        includeMLScore: true,
        includeDetailedAnalysis: true
      };

      const result = await kundaliService.generateCompleteKundaliMatch(
        testUser1,
        testUser2,
        options
      );

      expect(result).toHaveProperty('overallCompatibility');
      expect(result).toHaveProperty('ashtakootAnalysis');
      expect(result).toHaveProperty('manglikAnalysis');
      expect(result).toHaveProperty('detailedBreakdown');
      expect(result).toHaveProperty('remedies');
      expect(result).toHaveProperty('recommendation');

      expect(result.overallCompatibility).toHaveProperty('score');
      expect(result.overallCompatibility).toHaveProperty('level');
      expect(result.overallCompatibility.score).toBeGreaterThanOrEqual(0);
      expect(result.overallCompatibility.score).toBeLessThanOrEqual(100);
    });

    test('should handle timezone-aware calculations', () => {
      const user1IST = { ...testUser1, timezone: 'IST' };
      const user1UTC = { ...testUser1, timezone: 'UTC' };

      const chart1 = kundaliService.calculatePlanetaryPositionsWithTimezone(
        user1IST.birthDate,
        user1IST.birthTime,
        user1IST.birthPlace,
        user1IST.timezone
      );

      const chart2 = kundaliService.calculatePlanetaryPositionsWithTimezone(
        user1UTC.birthDate,
        user1UTC.birthTime,
        user1UTC.birthPlace,
        user1UTC.timezone
      );

      // Charts should be different due to timezone difference
      expect(chart1).not.toEqual(chart2);
    });
  });

  describe('Integration Tests', () => {
    test('should handle real birth data accurately', async () => {
      // Real test case data
      const realUser1 = {
        id: 'real1',
        name: 'Arjun Sharma',
        birthDate: '1988-03-21',
        birthTime: '06:45',
        birthPlace: 'Pune, Maharashtra, India',
        timezone: 'IST'
      };

      const realUser2 = {
        id: 'real2',
        name: 'Priya Patel',
        birthDate: '1990-11-15',
        birthTime: '18:20',
        birthPlace: 'Ahmedabad, Gujarat, India',
        timezone: 'IST'
      };

      const options = {
        includeCharts: true,
        includeRemedies: true,
        includeDetailedAnalysis: true
      };

      const result = await kundaliService.generateCompleteKundaliMatch(
        realUser1,
        realUser2,
        options
      );

      // Verify comprehensive result structure
      expect(result.overallCompatibility.score).toBeGreaterThan(0);
      expect(result.ashtakootAnalysis.totalScore).toBeLessThanOrEqual(36);
      expect(result.manglikAnalysis).toHaveProperty('user1');
      expect(result.manglikAnalysis).toHaveProperty('user2');
      expect(result.manglikAnalysis).toHaveProperty('compatibility');
      expect(result.recommendation).toBeTruthy();
    });

    test('should provide consistent results for same input', async () => {
      const options = { includeDetailedAnalysis: true };

      const result1 = await kundaliService.generateCompleteKundaliMatch(
        testUser1,
        testUser2,
        options
      );

      const result2 = await kundaliService.generateCompleteKundaliMatch(
        testUser1,
        testUser2,
        options
      );

      // Core calculations should be consistent
      expect(result1.ashtakootAnalysis.totalScore).toBe(result2.ashtakootAnalysis.totalScore);
      expect(result1.overallCompatibility.level).toBe(result2.overallCompatibility.level);
    });
  });

  describe('Performance Tests', () => {
    test('should complete kundali matching within acceptable time', async () => {
      const startTime = Date.now();

      await kundaliService.generateCompleteKundaliMatch(
        testUser1,
        testUser2,
        { includeDetailedAnalysis: true }
      );

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Should complete within 5 seconds
      expect(executionTime).toBeLessThan(5000);
    });
  });
});
