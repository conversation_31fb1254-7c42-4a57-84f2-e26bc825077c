import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(
        children: [
          // Profile Section
          const ListTile(
            leading: Icon(Icons.person),
            title: Text('Profile Settings'),
            subtitle: Text('Manage your profile information'),
            trailing: Icon(Icons.arrow_forward_ios),
          ),
          const Divider(),
          
          // Privacy Section
          const ListTile(
            leading: Icon(Icons.privacy_tip),
            title: Text('Privacy Settings'),
            subtitle: Text('Control who can see your profile'),
            trailing: Icon(Icons.arrow_forward_ios),
          ),
          const Divider(),
          
          // Notifications Section
          const ListTile(
            leading: Icon(Icons.notifications),
            title: Text('Notification Settings'),
            subtitle: Text('Manage your notification preferences'),
            trailing: Icon(Icons.arrow_forward_ios),
          ),
          const Divider(),
          
          // Account Section
          const ListTile(
            leading: Icon(Icons.account_circle),
            title: Text('Account Settings'),
            subtitle: Text('Manage your account'),
            trailing: Icon(Icons.arrow_forward_ios),
          ),
          const Divider(),
          
          // Premium Section
          ListTile(
            leading: const Icon(Icons.star, color: AppTheme.premiumGold),
            title: const Text('Premium Plans'),
            subtitle: const Text('Upgrade to premium'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap: () {
              Navigator.of(context).pushNamed('/premium');
            },
          ),
          const Divider(),
          
          // Help & Support Section
          const ListTile(
            leading: Icon(Icons.help),
            title: Text('Help & Support'),
            subtitle: Text('Get help and contact support'),
            trailing: Icon(Icons.arrow_forward_ios),
          ),
          const Divider(),
          
          // About Section
          const ListTile(
            leading: Icon(Icons.info),
            title: Text('About'),
            subtitle: Text('App version and information'),
            trailing: Icon(Icons.arrow_forward_ios),
          ),
          const Divider(),
          
          // Logout Section
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('Logout', style: TextStyle(color: Colors.red)),
            onTap: () {
              _showLogoutDialog(context, ref);
            },
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Are you sure you want to logout?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                // Show confirmation dialog
                final shouldLogout = await showDialog<bool>(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Logout'),
                    content: const Text('Are you sure you want to logout?'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(false),
                        child: const Text('Cancel'),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(true),
                        child: const Text('Logout'),
                      ),
                    ],
                  ),
                );

                if (shouldLogout == true) {
                  // Clear user session data
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.clear();

                  // Navigate to login screen
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    Navigator.of(context).pushNamedAndRemoveUntil('/login', (route) => false);

                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Logged out successfully')),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }
}
