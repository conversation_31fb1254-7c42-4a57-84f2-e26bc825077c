class UserAnalytics {
  final String userId;
  final int profileViews;
  final int profileViewsToday;
  final int profileViewsThisWeek;
  final int profileViewsThisMonth;
  final int interestsSent;
  final int interestsReceived;
  final int interestsAccepted;
  final int interestsDeclined;
  final int matchesFound;
  final int shortlistCount;
  final int shortlistedByCount;
  final int messagesExchanged;
  final int callsMade;
  final int callsReceived;
  final double averageResponseTime;
  final DateTime lastActive;
  final int totalLoginDays;
  final Map<String, int> featureUsage;
  final Map<String, double> engagementMetrics;

  const UserAnalytics({
    required this.userId,
    required this.profileViews,
    required this.profileViewsToday,
    required this.profileViewsThisWeek,
    required this.profileViewsThisMonth,
    required this.interestsSent,
    required this.interestsReceived,
    required this.interestsAccepted,
    required this.interestsDeclined,
    required this.matchesFound,
    required this.shortlistCount,
    required this.shortlistedByCount,
    required this.messagesExchanged,
    required this.callsMade,
    required this.callsReceived,
    required this.averageResponseTime,
    required this.lastActive,
    required this.totalLoginDays,
    required this.featureUsage,
    required this.engagementMetrics,
  });

  factory UserAnalytics.fromJson(Map<String, dynamic> json) {
    return UserAnalytics(
      userId: json['userId']?.toString() ?? '',
      profileViews: json['profileViews']?.toInt() ?? 0,
      profileViewsToday: json['profileViewsToday']?.toInt() ?? 0,
      profileViewsThisWeek: json['profileViewsThisWeek']?.toInt() ?? 0,
      profileViewsThisMonth: json['profileViewsThisMonth']?.toInt() ?? 0,
      interestsSent: json['interestsSent']?.toInt() ?? 0,
      interestsReceived: json['interestsReceived']?.toInt() ?? 0,
      interestsAccepted: json['interestsAccepted']?.toInt() ?? 0,
      interestsDeclined: json['interestsDeclined']?.toInt() ?? 0,
      matchesFound: json['matchesFound']?.toInt() ?? 0,
      shortlistCount: json['shortlistCount']?.toInt() ?? 0,
      shortlistedByCount: json['shortlistedByCount']?.toInt() ?? 0,
      messagesExchanged: json['messagesExchanged']?.toInt() ?? 0,
      callsMade: json['callsMade']?.toInt() ?? 0,
      callsReceived: json['callsReceived']?.toInt() ?? 0,
      averageResponseTime: json['averageResponseTime']?.toDouble() ?? 0.0,
      lastActive: DateTime.tryParse(json['lastActive']?.toString() ?? '') ?? DateTime.now(),
      totalLoginDays: json['totalLoginDays']?.toInt() ?? 0,
      featureUsage: Map<String, int>.from(json['featureUsage'] ?? {}),
      engagementMetrics: Map<String, double>.from(json['engagementMetrics'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'profileViews': profileViews,
      'profileViewsToday': profileViewsToday,
      'profileViewsThisWeek': profileViewsThisWeek,
      'profileViewsThisMonth': profileViewsThisMonth,
      'interestsSent': interestsSent,
      'interestsReceived': interestsReceived,
      'interestsAccepted': interestsAccepted,
      'interestsDeclined': interestsDeclined,
      'matchesFound': matchesFound,
      'shortlistCount': shortlistCount,
      'shortlistedByCount': shortlistedByCount,
      'messagesExchanged': messagesExchanged,
      'callsMade': callsMade,
      'callsReceived': callsReceived,
      'averageResponseTime': averageResponseTime,
      'lastActive': lastActive.toIso8601String(),
      'totalLoginDays': totalLoginDays,
      'featureUsage': featureUsage,
      'engagementMetrics': engagementMetrics,
    };
  }

  double get interestSuccessRate {
    if (interestsSent == 0) return 0.0;
    return (interestsAccepted / interestsSent) * 100;
  }

  double get profilePopularityScore {
    return (profileViews * 0.3) + 
           (interestsReceived * 0.4) + 
           (shortlistedByCount * 0.3);
  }
}

class ProfileViewAnalytics {
  final String userId;
  final List<ProfileView> recentViews;
  final Map<String, int> viewsByAge;
  final Map<String, int> viewsByLocation;
  final Map<String, int> viewsByEducation;
  final Map<String, int> viewsByOccupation;
  final Map<String, int> viewsByDay;
  final Map<String, int> viewsByHour;
  final int uniqueViewers;
  final int repeatViewers;
  final double averageViewDuration;

  const ProfileViewAnalytics({
    required this.userId,
    required this.recentViews,
    required this.viewsByAge,
    required this.viewsByLocation,
    required this.viewsByEducation,
    required this.viewsByOccupation,
    required this.viewsByDay,
    required this.viewsByHour,
    required this.uniqueViewers,
    required this.repeatViewers,
    required this.averageViewDuration,
  });

  factory ProfileViewAnalytics.fromJson(Map<String, dynamic> json) {
    List<ProfileView> views = [];
    if (json['recentViews'] != null) {
      views = (json['recentViews'] as List)
          .map((view) => ProfileView.fromJson(view))
          .toList();
    }

    return ProfileViewAnalytics(
      userId: json['userId']?.toString() ?? '',
      recentViews: views,
      viewsByAge: Map<String, int>.from(json['viewsByAge'] ?? {}),
      viewsByLocation: Map<String, int>.from(json['viewsByLocation'] ?? {}),
      viewsByEducation: Map<String, int>.from(json['viewsByEducation'] ?? {}),
      viewsByOccupation: Map<String, int>.from(json['viewsByOccupation'] ?? {}),
      viewsByDay: Map<String, int>.from(json['viewsByDay'] ?? {}),
      viewsByHour: Map<String, int>.from(json['viewsByHour'] ?? {}),
      uniqueViewers: json['uniqueViewers']?.toInt() ?? 0,
      repeatViewers: json['repeatViewers']?.toInt() ?? 0,
      averageViewDuration: json['averageViewDuration']?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'recentViews': recentViews.map((view) => view.toJson()).toList(),
      'viewsByAge': viewsByAge,
      'viewsByLocation': viewsByLocation,
      'viewsByEducation': viewsByEducation,
      'viewsByOccupation': viewsByOccupation,
      'viewsByDay': viewsByDay,
      'viewsByHour': viewsByHour,
      'uniqueViewers': uniqueViewers,
      'repeatViewers': repeatViewers,
      'averageViewDuration': averageViewDuration,
    };
  }
}

class ProfileView {
  final String id;
  final String viewerId;
  final String viewerName;
  final String? viewerPhoto;
  final DateTime viewedAt;
  final int duration;
  final String? viewerLocation;
  final int viewerAge;
  final bool isAnonymous;

  const ProfileView({
    required this.id,
    required this.viewerId,
    required this.viewerName,
    this.viewerPhoto,
    required this.viewedAt,
    required this.duration,
    this.viewerLocation,
    required this.viewerAge,
    required this.isAnonymous,
  });

  factory ProfileView.fromJson(Map<String, dynamic> json) {
    return ProfileView(
      id: json['id']?.toString() ?? '',
      viewerId: json['viewerId']?.toString() ?? '',
      viewerName: json['viewerName']?.toString() ?? '',
      viewerPhoto: json['viewerPhoto']?.toString(),
      viewedAt: DateTime.tryParse(json['viewedAt']?.toString() ?? '') ?? DateTime.now(),
      duration: json['duration']?.toInt() ?? 0,
      viewerLocation: json['viewerLocation']?.toString(),
      viewerAge: json['viewerAge']?.toInt() ?? 0,
      isAnonymous: json['isAnonymous'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'viewerId': viewerId,
      'viewerName': viewerName,
      'viewerPhoto': viewerPhoto,
      'viewedAt': viewedAt.toIso8601String(),
      'duration': duration,
      'viewerLocation': viewerLocation,
      'viewerAge': viewerAge,
      'isAnonymous': isAnonymous,
    };
  }
}

class InterestAnalytics {
  final String userId;
  final List<InterestActivity> recentActivity;
  final Map<String, int> interestsByAge;
  final Map<String, int> interestsByLocation;
  final Map<String, int> interestsByEducation;
  final Map<String, int> interestsByOccupation;
  final Map<String, int> responseTimeDistribution;
  final double averageResponseTime;
  final int totalSent;
  final int totalReceived;
  final int totalAccepted;
  final int totalDeclined;
  final int totalPending;

  const InterestAnalytics({
    required this.userId,
    required this.recentActivity,
    required this.interestsByAge,
    required this.interestsByLocation,
    required this.interestsByEducation,
    required this.interestsByOccupation,
    required this.responseTimeDistribution,
    required this.averageResponseTime,
    required this.totalSent,
    required this.totalReceived,
    required this.totalAccepted,
    required this.totalDeclined,
    required this.totalPending,
  });

  factory InterestAnalytics.fromJson(Map<String, dynamic> json) {
    List<InterestActivity> activities = [];
    if (json['recentActivity'] != null) {
      activities = (json['recentActivity'] as List)
          .map((activity) => InterestActivity.fromJson(activity))
          .toList();
    }

    return InterestAnalytics(
      userId: json['userId']?.toString() ?? '',
      recentActivity: activities,
      interestsByAge: Map<String, int>.from(json['interestsByAge'] ?? {}),
      interestsByLocation: Map<String, int>.from(json['interestsByLocation'] ?? {}),
      interestsByEducation: Map<String, int>.from(json['interestsByEducation'] ?? {}),
      interestsByOccupation: Map<String, int>.from(json['interestsByOccupation'] ?? {}),
      responseTimeDistribution: Map<String, int>.from(json['responseTimeDistribution'] ?? {}),
      averageResponseTime: json['averageResponseTime']?.toDouble() ?? 0.0,
      totalSent: json['totalSent']?.toInt() ?? 0,
      totalReceived: json['totalReceived']?.toInt() ?? 0,
      totalAccepted: json['totalAccepted']?.toInt() ?? 0,
      totalDeclined: json['totalDeclined']?.toInt() ?? 0,
      totalPending: json['totalPending']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'recentActivity': recentActivity.map((activity) => activity.toJson()).toList(),
      'interestsByAge': interestsByAge,
      'interestsByLocation': interestsByLocation,
      'interestsByEducation': interestsByEducation,
      'interestsByOccupation': interestsByOccupation,
      'responseTimeDistribution': responseTimeDistribution,
      'averageResponseTime': averageResponseTime,
      'totalSent': totalSent,
      'totalReceived': totalReceived,
      'totalAccepted': totalAccepted,
      'totalDeclined': totalDeclined,
      'totalPending': totalPending,
    };
  }

  double get successRate {
    if (totalSent == 0) return 0.0;
    return (totalAccepted / totalSent) * 100;
  }

  double get responseRate {
    if (totalReceived == 0) return 0.0;
    return ((totalAccepted + totalDeclined) / totalReceived) * 100;
  }
}

class InterestActivity {
  final String id;
  final String otherUserId;
  final String otherUserName;
  final String? otherUserPhoto;
  final InterestType type;
  final InterestStatus status;
  final DateTime createdAt;
  final DateTime? respondedAt;
  final String? message;

  const InterestActivity({
    required this.id,
    required this.otherUserId,
    required this.otherUserName,
    this.otherUserPhoto,
    required this.type,
    required this.status,
    required this.createdAt,
    this.respondedAt,
    this.message,
  });

  factory InterestActivity.fromJson(Map<String, dynamic> json) {
    return InterestActivity(
      id: json['id']?.toString() ?? '',
      otherUserId: json['otherUserId']?.toString() ?? '',
      otherUserName: json['otherUserName']?.toString() ?? '',
      otherUserPhoto: json['otherUserPhoto']?.toString(),
      type: InterestType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => InterestType.sent,
      ),
      status: InterestStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => InterestStatus.pending,
      ),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      respondedAt: json['respondedAt'] != null 
          ? DateTime.tryParse(json['respondedAt'].toString())
          : null,
      message: json['message']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'otherUserId': otherUserId,
      'otherUserName': otherUserName,
      'otherUserPhoto': otherUserPhoto,
      'type': type.name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
      'message': message,
    };
  }
}

enum InterestType {
  sent,
  received
}

enum InterestStatus {
  pending,
  accepted,
  declined,
  expired
}

class AppUsageAnalytics {
  final String userId;
  final int totalSessions;
  final double averageSessionDuration;
  final int totalScreenTime;
  final Map<String, int> screenTimeByFeature;
  final Map<String, int> sessionsByDay;
  final Map<String, int> sessionsByHour;
  final List<String> mostUsedFeatures;
  final List<String> leastUsedFeatures;
  final DateTime firstLogin;
  final DateTime lastLogin;
  final int consecutiveLoginDays;
  final Map<String, double> conversionRates;

  const AppUsageAnalytics({
    required this.userId,
    required this.totalSessions,
    required this.averageSessionDuration,
    required this.totalScreenTime,
    required this.screenTimeByFeature,
    required this.sessionsByDay,
    required this.sessionsByHour,
    required this.mostUsedFeatures,
    required this.leastUsedFeatures,
    required this.firstLogin,
    required this.lastLogin,
    required this.consecutiveLoginDays,
    required this.conversionRates,
  });

  factory AppUsageAnalytics.fromJson(Map<String, dynamic> json) {
    return AppUsageAnalytics(
      userId: json['userId']?.toString() ?? '',
      totalSessions: json['totalSessions']?.toInt() ?? 0,
      averageSessionDuration: json['averageSessionDuration']?.toDouble() ?? 0.0,
      totalScreenTime: json['totalScreenTime']?.toInt() ?? 0,
      screenTimeByFeature: Map<String, int>.from(json['screenTimeByFeature'] ?? {}),
      sessionsByDay: Map<String, int>.from(json['sessionsByDay'] ?? {}),
      sessionsByHour: Map<String, int>.from(json['sessionsByHour'] ?? {}),
      mostUsedFeatures: List<String>.from(json['mostUsedFeatures'] ?? []),
      leastUsedFeatures: List<String>.from(json['leastUsedFeatures'] ?? []),
      firstLogin: DateTime.tryParse(json['firstLogin']?.toString() ?? '') ?? DateTime.now(),
      lastLogin: DateTime.tryParse(json['lastLogin']?.toString() ?? '') ?? DateTime.now(),
      consecutiveLoginDays: json['consecutiveLoginDays']?.toInt() ?? 0,
      conversionRates: Map<String, double>.from(json['conversionRates'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'totalSessions': totalSessions,
      'averageSessionDuration': averageSessionDuration,
      'totalScreenTime': totalScreenTime,
      'screenTimeByFeature': screenTimeByFeature,
      'sessionsByDay': sessionsByDay,
      'sessionsByHour': sessionsByHour,
      'mostUsedFeatures': mostUsedFeatures,
      'leastUsedFeatures': leastUsedFeatures,
      'firstLogin': firstLogin.toIso8601String(),
      'lastLogin': lastLogin.toIso8601String(),
      'consecutiveLoginDays': consecutiveLoginDays,
      'conversionRates': conversionRates,
    };
  }

  double get engagementScore {
    return (totalSessions * 0.3) +
           (averageSessionDuration * 0.4) +
           (consecutiveLoginDays * 0.3);
  }
}

// Profile insights model for detailed profile analytics
class ProfileInsights {
  final String userId;
  final double profileCompleteness;
  final double profileStrength;
  final double visibilityScore;
  final double attractivenessScore;
  final double responseRate;
  final List<int> profileViewTrend;
  final List<int> interestTrend;
  final Map<String, double> topViewingSources;
  final Map<String, double> viewerDemographics;
  final List<String> improvementSuggestions;

  const ProfileInsights({
    required this.userId,
    required this.profileCompleteness,
    required this.profileStrength,
    required this.visibilityScore,
    required this.attractivenessScore,
    required this.responseRate,
    required this.profileViewTrend,
    required this.interestTrend,
    required this.topViewingSources,
    required this.viewerDemographics,
    required this.improvementSuggestions,
  });

  factory ProfileInsights.fromJson(Map<String, dynamic> json) {
    return ProfileInsights(
      userId: json['userId']?.toString() ?? '',
      profileCompleteness: json['profileCompleteness']?.toDouble() ?? 0.0,
      profileStrength: json['profileStrength']?.toDouble() ?? 0.0,
      visibilityScore: json['visibilityScore']?.toDouble() ?? 0.0,
      attractivenessScore: json['attractivenessScore']?.toDouble() ?? 0.0,
      responseRate: json['responseRate']?.toDouble() ?? 0.0,
      profileViewTrend: List<int>.from(json['profileViewTrend'] ?? []),
      interestTrend: List<int>.from(json['interestTrend'] ?? []),
      topViewingSources: Map<String, double>.from(json['topViewingSources'] ?? {}),
      viewerDemographics: Map<String, double>.from(json['viewerDemographics'] ?? {}),
      improvementSuggestions: List<String>.from(json['improvementSuggestions'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'profileCompleteness': profileCompleteness,
      'profileStrength': profileStrength,
      'visibilityScore': visibilityScore,
      'attractivenessScore': attractivenessScore,
      'responseRate': responseRate,
      'profileViewTrend': profileViewTrend,
      'interestTrend': interestTrend,
      'topViewingSources': topViewingSources,
      'viewerDemographics': viewerDemographics,
      'improvementSuggestions': improvementSuggestions,
    };
  }

  double get overallScore {
    return (profileCompleteness * 0.25) +
           (profileStrength * 0.25) +
           (visibilityScore * 0.25) +
           (attractivenessScore * 0.25);
  }

  String get scoreGrade {
    final score = overallScore;
    if (score >= 90) return 'A+';
    if (score >= 80) return 'A';
    if (score >= 70) return 'B+';
    if (score >= 60) return 'B';
    if (score >= 50) return 'C+';
    if (score >= 40) return 'C';
    return 'D';
  }
}
