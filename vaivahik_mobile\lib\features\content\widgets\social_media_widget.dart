import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../app/theme.dart';
import '../models/content_model.dart';
import '../providers/content_provider.dart';

/// 📱 Social Media Widget - Dynamic Social Links
/// Using existing website backend API: /api/social-media-links

class SocialMediaWidget extends ConsumerWidget {
  final bool showTitle;
  final MainAxisAlignment alignment;
  final double iconSize;

  const SocialMediaWidget({
    super.key,
    this.showTitle = true,
    this.alignment = MainAxisAlignment.center,
    this.iconSize = 24,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final socialLinksAsync = ref.watch(socialMediaLinksProvider);

    return socialLinksAsync.when(
      loading: () => const SizedBox(
        height: 50,
        child: Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => _buildFallbackSocialLinks(),
      data: (links) => _buildSocialLinks(links),
    );
  }

  Widget _buildSocialLinks(List<SocialMediaLink> links) {
    if (links.isEmpty) {
      return _buildFallbackSocialLinks();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (showTitle) ...[
          Text(
            'Follow Us',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ).animate().fadeIn(),
          const SizedBox(height: 12),
        ],
        Row(
          mainAxisAlignment: alignment,
          children: links.asMap().entries.map((entry) {
            final index = entry.key;
            final link = entry.value;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: _SocialButton(
                platform: link.platform,
                url: link.url,
                icon: link.icon,
                iconSize: iconSize,
              ).animate().fadeIn(delay: Duration(milliseconds: index * 100))
               .scale(),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildFallbackSocialLinks() {
    final fallbackLinks = [
      {'platform': 'facebook', 'url': 'https://facebook.com/vaivahik', 'icon': 'facebook'},
      {'platform': 'instagram', 'url': 'https://instagram.com/vaivahik', 'icon': 'instagram'},
      {'platform': 'twitter', 'url': 'https://twitter.com/vaivahik', 'icon': 'twitter'},
      {'platform': 'youtube', 'url': 'https://youtube.com/vaivahik', 'icon': 'youtube'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (showTitle) ...[
          Text(
            'Follow Us',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ).animate().fadeIn(),
          const SizedBox(height: 12),
        ],
        Row(
          mainAxisAlignment: alignment,
          children: fallbackLinks.asMap().entries.map((entry) {
            final index = entry.key;
            final link = entry.value;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: _SocialButton(
                platform: link['platform']!,
                url: link['url']!,
                icon: link['icon']!,
                iconSize: iconSize,
              ).animate().fadeIn(delay: Duration(milliseconds: index * 100))
               .scale(),
            );
          }).toList(),
        ),
      ],
    );
  }
}

class _SocialButton extends StatelessWidget {
  final String platform;
  final String url;
  final String icon;
  final double iconSize;

  const _SocialButton({
    required this.platform,
    required this.url,
    required this.icon,
    required this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => _launchUrl(url),
      child: Container(
        width: iconSize + 16,
        height: iconSize + 16,
        decoration: BoxDecoration(
          color: _getPlatformColor(platform),
          borderRadius: BorderRadius.circular((iconSize + 16) / 2),
          boxShadow: [
            BoxShadow(
              color: _getPlatformColor(platform).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          _getPlatformIcon(platform),
          color: Colors.white,
          size: iconSize,
        ),
      ),
    );
  }

  Color _getPlatformColor(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return const Color(0xFF1877F2);
      case 'instagram':
        return const Color(0xFFE4405F);
      case 'twitter':
        return const Color(0xFF1DA1F2);
      case 'youtube':
        return const Color(0xFFFF0000);
      case 'linkedin':
        return const Color(0xFF0A66C2);
      case 'whatsapp':
        return const Color(0xFF25D366);
      case 'telegram':
        return const Color(0xFF0088CC);
      default:
        return AppColors.primary;
    }
  }

  IconData _getPlatformIcon(String platform) {
    switch (platform.toLowerCase()) {
      case 'facebook':
        return Icons.facebook;
      case 'instagram':
        return Icons.camera_alt; // Instagram icon not available in Material Icons
      case 'twitter':
        return Icons.alternate_email; // Twitter icon approximation
      case 'youtube':
        return Icons.play_arrow;
      case 'linkedin':
        return Icons.business;
      case 'whatsapp':
        return Icons.chat;
      case 'telegram':
        return Icons.send;
      default:
        return Icons.link;
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}

/// 📱 Compact Social Media Row
class CompactSocialMediaWidget extends ConsumerWidget {
  const CompactSocialMediaWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const SocialMediaWidget(
      showTitle: false,
      alignment: MainAxisAlignment.center,
      iconSize: 20,
    );
  }
}

/// 📱 Footer Social Media Widget
class FooterSocialMediaWidget extends ConsumerWidget {
  const FooterSocialMediaWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final socialLinksAsync = ref.watch(socialMediaLinksProvider);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Text(
            'Connect With Us',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ).animate().fadeIn(),
          const SizedBox(height: 16),
          socialLinksAsync.when(
            loading: () => const CircularProgressIndicator(),
            error: (error, stack) => const SocialMediaWidget(
              showTitle: false,
              iconSize: 28,
            ),
            data: (links) => const SocialMediaWidget(
              showTitle: false,
              iconSize: 28,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Stay updated with the latest features and success stories',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ).animate().fadeIn(delay: const Duration(milliseconds: 300)),
        ],
      ),
    );
  }
}

/// 📱 Social Share Widget
class SocialShareWidget extends StatelessWidget {
  final String title;
  final String url;

  const SocialShareWidget({
    super.key,
    required this.title,
    required this.url,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Share this with friends',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _ShareButton(
                icon: Icons.chat,
                label: 'WhatsApp',
                color: const Color(0xFF25D366),
                onTap: () => _shareToWhatsApp(title, url),
              ),
              _ShareButton(
                icon: Icons.facebook,
                label: 'Facebook',
                color: const Color(0xFF1877F2),
                onTap: () => _shareToFacebook(url),
              ),
              _ShareButton(
                icon: Icons.alternate_email,
                label: 'Twitter',
                color: const Color(0xFF1DA1F2),
                onTap: () => _shareToTwitter(title, url),
              ),
              _ShareButton(
                icon: Icons.share,
                label: 'More',
                color: Colors.grey[600]!,
                onTap: () => _shareGeneric(title, url),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _shareToWhatsApp(String title, String url) {
    final text = Uri.encodeComponent('$title\n\n$url');
    launchUrl(Uri.parse('https://wa.me/?text=$text'));
  }

  void _shareToFacebook(String url) {
    final encodedUrl = Uri.encodeComponent(url);
    launchUrl(Uri.parse('https://www.facebook.com/sharer/sharer.php?u=$encodedUrl'));
  }

  void _shareToTwitter(String title, String url) {
    final text = Uri.encodeComponent(title);
    final encodedUrl = Uri.encodeComponent(url);
    launchUrl(Uri.parse('https://twitter.com/intent/tweet?text=$text&url=$encodedUrl'));
  }

  void _shareGeneric(String title, String url) {
    // Use platform share dialog
    // This would typically use the share_plus package
    debugPrint('Share: $title - $url');
  }
}

class _ShareButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const _ShareButton({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(24),
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }
}
