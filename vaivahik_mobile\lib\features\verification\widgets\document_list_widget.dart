import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../models/verification_models.dart';
import '../providers/verification_provider.dart';
import 'document_card.dart';

class DocumentListWidget extends StatefulWidget {
  const DocumentListWidget({super.key});

  @override
  State<DocumentListWidget> createState() => _DocumentListWidgetState();
}

class _DocumentListWidgetState extends State<DocumentListWidget> {
  DocumentStatus? _selectedFilter;

  @override
  Widget build(BuildContext context) {
    return Consumer<VerificationProvider>(
      builder: (context, provider, child) {
        final documents = _getFilteredDocuments(provider.documents);

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Filter chips
            _buildFilterChips(),
            const SizedBox(height: 16),
            
            // Document count
            Text(
              '${documents.length} Document${documents.length != 1 ? 's' : ''}',
              style: AppTextStyles.labelLarge,
            ),
            const SizedBox(height: 12),
            
            // Documents list
            if (documents.isEmpty) ...[
              const SizedBox(height: 40),
              EmptyStateWidget(
                icon: Icons.folder_open,
                title: _selectedFilter == null
                    ? 'No Documents Uploaded'
                    : 'No ${_selectedFilter!.label} Documents',
                message: _selectedFilter == null
                    ? 'Upload your first document to get started with verification'
                    : 'No documents found with ${_selectedFilter!.label.toLowerCase()} status',
                actionText: _selectedFilter == null ? 'Upload Document' : 'Clear Filter',
                onAction: _selectedFilter == null
                    ? () => _showUploadDialog(context)
                    : () => setState(() => _selectedFilter = null),
              ),
            ] else ...[
              ...documents.map((document) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: DocumentCard(
                  document: document,
                  onDelete: () => _confirmDelete(context, document),
                  onView: () => _viewDocument(context, document),
                ),
              )),
            ],
          ],
        );
      },
    );
  }

  Widget _buildFilterChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // All filter
          FilterChip(
            label: const Text('All'),
            selected: _selectedFilter == null,
            onSelected: (selected) {
              setState(() {
                _selectedFilter = null;
              });
            },
            selectedColor: AppColors.primary.withAlpha(51),
            checkmarkColor: AppColors.primary,
          ),
          const SizedBox(width: 8),
          
          // Status filters
          ...DocumentStatus.values.map((status) => Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(status.label),
              avatar: Text(status.icon, style: const TextStyle(fontSize: 12)),
              selected: _selectedFilter == status,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = selected ? status : null;
                });
              },
              selectedColor: _getStatusColor(status).withAlpha(51),
              checkmarkColor: _getStatusColor(status),
            ),
          )),
        ],
      ),
    );
  }

  List<VerificationDocument> _getFilteredDocuments(List<VerificationDocument> documents) {
    if (_selectedFilter == null) {
      return documents;
    }
    return documents.where((doc) => doc.status == _selectedFilter).toList();
  }

  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.approved:
        return AppColors.success;
      case DocumentStatus.rejected:
        return AppColors.error;
      case DocumentStatus.pendingReview:
        return AppColors.warning;
    }
  }

  void _confirmDelete(BuildContext context, VerificationDocument document) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Document'),
        content: Text(
          'Are you sure you want to delete this ${document.type.label}? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final provider = context.read<VerificationProvider>();
              final success = await provider.deleteDocument(document.id);
              
              if (success && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('${document.type.label} deleted successfully'),
                    backgroundColor: AppColors.success,
                  ),
                );
              } else if (context.mounted && provider.error != null) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(provider.error!),
                    backgroundColor: AppColors.error,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _viewDocument(BuildContext context, VerificationDocument document) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    document.type.icon,
                    style: const TextStyle(fontSize: 24),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      document.type.label,
                      style: AppTextStyles.h3,
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              _buildDetailRow('Status', document.status.label, document.status.icon),
              _buildDetailRow('Filename', document.filename, '📄'),
              _buildDetailRow('File Size', _formatFileSize(document.filesize), '📏'),
              _buildDetailRow('Uploaded', _formatDate(document.uploadedAt), '📅'),
              
              if (document.reviewedAt != null)
                _buildDetailRow('Reviewed', _formatDate(document.reviewedAt!), '👤'),
              
              if (document.adminNotes != null && document.adminNotes!.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 12),
                const Text(
                  'Admin Notes',
                  style: AppTextStyles.labelLarge,
                ),
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.background,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.primary.withAlpha(77)),
                  ),
                  child: Text(
                    document.adminNotes!,
                    style: AppTextStyles.bodySmall,
                  ),
                ),
              ],
              
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, String icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Text(icon, style: const TextStyle(fontSize: 16)),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: AppTextStyles.labelMedium,
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd/MM/yyyy HH:mm').format(date);
  }

  void _showUploadDialog(BuildContext context) {
    // This would typically navigate to upload screen or show upload dialog
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigate to Upload tab to add documents'),
      ),
    );
  }
}
