import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Container,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  Button,
  Box,
  Alert,
  Chip,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Settings as SettingsIcon,
  List as ListIcon,
  DragIndicator as DragIcon
} from '@mui/icons-material';
import EnhancedAdminLayout from '../../components/admin/EnhancedAdminLayout';
import { toast } from 'react-toastify';

// Predefined dropdown field types that can be managed
const DROPDOWN_FIELDS = [
  { key: 'profileFor', name: 'Profile For', description: 'Who is creating the profile' },
  { key: 'maritalStatus', name: 'Marital Status', description: 'Current marital status' },
  { key: 'bloodGroup', name: 'Blood Group', description: 'Blood group options' },
  { key: 'education', name: 'Education Level', description: 'Highest education levels' },
  { key: 'occupation', name: 'Occupation Type', description: 'Types of occupations' },
  { key: 'income', name: 'Income Range', description: 'Annual income ranges' },
  { key: 'familyType', name: 'Family Type', description: 'Types of family structure' },
  { key: 'familyValues', name: 'Family Values', description: 'Family value orientations' },
  { key: 'subCaste', name: 'Sub-caste', description: 'Sub-caste options for Maratha community' }
];

export default function RegistrationFields() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [fields, setFields] = useState([]);
  const [selectedField, setSelectedField] = useState(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [openOptionsDialog, setOpenOptionsDialog] = useState(false);
  const [fieldOptions, setFieldOptions] = useState([]);
  const [newOption, setNewOption] = useState('');

  useEffect(() => {
    fetchRegistrationFields();
  }, []);

  const fetchRegistrationFields = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/registration-fields', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFields(data.fields || []);
      } else {
        toast.error('Failed to fetch registration fields');
      }
    } catch (error) {
      console.error('Error fetching registration fields:', error);
      toast.error('Error loading registration fields');
    } finally {
      setLoading(false);
    }
  };

  const handleManageOptions = async (fieldKey) => {
    try {
      const response = await fetch(`/api/admin/registration-fields/${fieldKey}/options`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFieldOptions(data.options || []);
        setSelectedField(fieldKey);
        setOpenOptionsDialog(true);
      } else {
        toast.error('Failed to fetch field options');
      }
    } catch (error) {
      console.error('Error fetching field options:', error);
      toast.error('Error loading field options');
    }
  };

  const handleAddOption = async () => {
    if (!newOption.trim()) return;

    try {
      const response = await fetch(`/api/admin/registration-fields/${selectedField}/options`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({ option: newOption.trim() })
      });

      if (response.ok) {
        const data = await response.json();
        setFieldOptions(data.options);
        setNewOption('');
        toast.success('Option added successfully!');
      } else {
        toast.error('Failed to add option');
      }
    } catch (error) {
      console.error('Error adding option:', error);
      toast.error('Error adding option');
    }
  };

  const handleDeleteOption = async (optionIndex) => {
    try {
      const response = await fetch(`/api/admin/registration-fields/${selectedField}/options/${optionIndex}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFieldOptions(data.options);
        toast.success('Option deleted successfully!');
      } else {
        toast.error('Failed to delete option');
      }
    } catch (error) {
      console.error('Error deleting option:', error);
      toast.error('Error deleting option');
    }
  };

  const handleToggleField = async (fieldKey, enabled) => {
    try {
      const response = await fetch(`/api/admin/registration-fields/${fieldKey}/toggle`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({ enabled })
      });

      if (response.ok) {
        await fetchRegistrationFields();
        toast.success(`Field ${enabled ? 'enabled' : 'disabled'} successfully!`);
      } else {
        toast.error('Failed to update field status');
      }
    } catch (error) {
      console.error('Error updating field:', error);
      toast.error('Error updating field');
    }
  };

  if (loading) {
    return (
      <EnhancedAdminLayout>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
            <CircularProgress />
          </Box>
        </Container>
      </EnhancedAdminLayout>
    );
  }

  return (
    <EnhancedAdminLayout>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Header */}
        <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <SettingsIcon sx={{ fontSize: 40, color: 'primary.main' }} />
            <Box>
              <Typography variant="h4" gutterBottom>
                Registration Fields Management
              </Typography>
              <Typography variant="body1" color="text.secondary">
                Manage dropdown options and field settings for the registration form
              </Typography>
            </Box>
          </Box>
        </Paper>

        {/* Fields Table */}
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Dropdown Fields
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Field Name</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Options Count</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {DROPDOWN_FIELDS.map((field) => {
                    const fieldData = fields.find(f => f.key === field.key) || {};
                    const optionsCount = fieldData.options?.length || 0;
                    const isEnabled = fieldData.enabled !== false;

                    return (
                      <TableRow key={field.key}>
                        <TableCell>
                          <Typography variant="subtitle2">{field.name}</Typography>
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" color="text.secondary">
                            {field.description}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={isEnabled}
                                onChange={(e) => handleToggleField(field.key, e.target.checked)}
                                size="small"
                              />
                            }
                            label={isEnabled ? 'Enabled' : 'Disabled'}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={`${optionsCount} options`} 
                            size="small" 
                            color={optionsCount > 0 ? 'primary' : 'default'}
                          />
                        </TableCell>
                        <TableCell>
                          <IconButton
                            onClick={() => handleManageOptions(field.key)}
                            color="primary"
                            size="small"
                          >
                            <ListIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>

        {/* Options Management Dialog */}
        <Dialog 
          open={openOptionsDialog} 
          onClose={() => setOpenOptionsDialog(false)} 
          maxWidth="md" 
          fullWidth
        >
          <DialogTitle>
            Manage Options: {DROPDOWN_FIELDS.find(f => f.key === selectedField)?.name}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 3 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={8}>
                  <TextField
                    fullWidth
                    label="New Option"
                    value={newOption}
                    onChange={(e) => setNewOption(e.target.value)}
                    placeholder="Enter new option"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddOption()}
                  />
                </Grid>
                <Grid item xs={4}>
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={handleAddOption}
                    startIcon={<AddIcon />}
                  >
                    Add
                  </Button>
                </Grid>
              </Grid>
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              Current Options ({fieldOptions.length}):
            </Typography>
            <List>
              {fieldOptions.map((option, index) => (
                <ListItem key={index} divider>
                  <ListItemText primary={option} />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleDeleteOption(index)}
                      color="error"
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
            
            {fieldOptions.length === 0 && (
              <Alert severity="info">
                No options configured for this field. Add some options above.
              </Alert>
            )}
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setOpenOptionsDialog(false)}>
              Close
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </EnhancedAdminLayout>
  );
}
