import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

// Import the comprehensive kundali service
const ComprehensiveKundaliService = require('../../../../../../../vaivahik-backend/src/services/comprehensiveKundali.service');
const kundaliService = new ComprehensiveKundaliService();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Check if kundali matching is enabled via admin panel
    const isEnabled = await kundaliService.isKundaliMatchingEnabled();
    if (!isEnabled) {
      return res.status(403).json({
        success: false,
        message: 'Kundali matching feature is currently disabled'
      });
    }

    // Check if it's free promotion period
    const isFreePromotion = await kundaliService.isKundaliMatchingFree();

    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'Authentication required' });
    }

    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      return res.status(401).json({ success: false, message: 'Invalid token' });
    }

    // Check if user has premium access (unless free promotion)
    const currentUser = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: { profile: true, subscription: true }
    });

    if (!currentUser) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Check premium access (unless it's a free promotion)
    const isPremiumRequired = !isFreePromotion &&
      (!currentUser.subscription || currentUser.subscription.status !== 'ACTIVE');

    if (isPremiumRequired) {
      return res.status(403).json({
        success: false,
        message: 'Premium subscription required for kundli matching',
        upgradeRequired: true,
        promotionAvailable: isFreePromotion
      });
    }

    const { user1, user2, options = {} } = req.body;

    // Validate required data
    if (!user1 || !user2) {
      return res.status(400).json({
        success: false,
        message: 'Both user profiles are required'
      });
    }

    // Validate birth data using comprehensive service
    const user1Validation = kundaliService.validateBirthData(user1);
    const user2Validation = kundaliService.validateBirthData(user2);

    if (!user1Validation.isValid || !user2Validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid birth data',
        errors: {
          user1: user1Validation.errors,
          user2: user2Validation.errors
        }
      });
    }

    // Prepare comprehensive user data
    const user1Data = {
      id: currentUser.id,
      name: currentUser.profile?.firstName + ' ' + (currentUser.profile?.lastName || ''),
      birthDate: user1.birthDate,
      birthTime: user1.birthTime,
      birthPlace: user1.birthPlace,
      timezone: user1.timezone || 'IST'
    };

    const user2Data = {
      id: user2.id,
      name: user2.name || 'User 2',
      birthDate: user2.birthDate,
      birthTime: user2.birthTime,
      birthPlace: user2.birthPlace,
      timezone: user2.timezone || 'IST'
    };

    // Enhanced options
    const enhancedOptions = {
      includeCharts: options.includeCharts || false,
      includeRemedies: options.includeRemedies || true,
      includeAuspiciousDates: options.includeAuspiciousDates || false,
      includeMLScore: options.includeMLScore || true,
      includeDetailedAnalysis: options.includeDetailedAnalysis || true
    };

    // Generate comprehensive kundali match using new service
    const kundliMatch = await kundaliService.generateCompleteKundaliMatch(
      user1Data,
      user2Data,
      enhancedOptions
    );

    // Log the kundli matching activity
    await prisma.userActivity.create({
      data: {
        userId: decoded.userId,
        activityType: 'KUNDLI_MATCH',
        details: JSON.stringify({
          targetUserId: user2Data.id,
          score: kundliMatch.overallCompatibility.score,
          compatibility: kundliMatch.overallCompatibility.level
        })
      }
    });

    res.status(200).json({
      success: true,
      data: kundliMatch,
      message: 'Kundali matching completed successfully',
      isFreePromotion,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in comprehensive kundli matching:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}

async function calculateGunaScores(user1, user2) {
  // This is a simplified implementation
  // In a real application, you would use actual astrological calculations
  // based on birth charts, nakshatras, etc.
  
  const scores = {};
  
  // Varna (Caste) - 1 point
  scores.varna = calculateVarnaScore(user1, user2);
  
  // Vashya (Dominance) - 2 points
  scores.vashya = calculateVashyaScore(user1, user2);
  
  // Tara (Birth Star) - 3 points
  scores.tara = calculateTaraScore(user1, user2);
  
  // Yoni (Sexual compatibility) - 4 points
  scores.yoni = calculateYoniScore(user1, user2);
  
  // Graha Maitri (Planetary friendship) - 5 points
  scores.graha_maitri = calculateGrahaMaitriScore(user1, user2);
  
  // Gana (Temperament) - 6 points
  scores.gana = calculateGanaScore(user1, user2);
  
  // Bhakoot (Love and affection) - 7 points
  scores.bhakoot = calculateBhakootScore(user1, user2);
  
  // Nadi (Health and progeny) - 8 points
  scores.nadi = calculateNadiScore(user1, user2);
  
  return scores;
}

// Individual Guna calculation functions (simplified)
function calculateVarnaScore(user1, user2) {
  // Simplified based on caste compatibility
  const caste1 = user1.profile?.caste || '';
  const caste2 = user2.profile?.caste || '';
  
  if (caste1 === caste2) return 1;
  return Math.random() > 0.5 ? 1 : 0; // Simplified random for demo
}

function calculateVashyaScore(user1, user2) {
  // Simplified calculation
  return Math.random() > 0.3 ? 2 : Math.random() > 0.5 ? 1 : 0;
}

function calculateTaraScore(user1, user2) {
  // Simplified calculation
  return Math.random() > 0.2 ? 3 : Math.random() > 0.4 ? 2 : Math.random() > 0.6 ? 1 : 0;
}

function calculateYoniScore(user1, user2) {
  // Simplified calculation
  return Math.random() > 0.2 ? 4 : Math.random() > 0.4 ? 3 : Math.random() > 0.6 ? 2 : 1;
}

function calculateGrahaMaitriScore(user1, user2) {
  // Simplified calculation
  return Math.random() > 0.1 ? 5 : Math.random() > 0.3 ? 4 : Math.random() > 0.5 ? 3 : 2;
}

function calculateGanaScore(user1, user2) {
  // Simplified calculation
  return Math.random() > 0.1 ? 6 : Math.random() > 0.3 ? 4 : Math.random() > 0.5 ? 1 : 0;
}

function calculateBhakootScore(user1, user2) {
  // Simplified calculation
  return Math.random() > 0.1 ? 7 : Math.random() > 0.3 ? 5 : Math.random() > 0.5 ? 2 : 0;
}

function calculateNadiScore(user1, user2) {
  // Simplified calculation - Nadi is very important
  return Math.random() > 0.1 ? 8 : Math.random() > 0.3 ? 6 : Math.random() > 0.5 ? 3 : 0;
}

function getCompatibilityLevel(score) {
  if (score >= 28) return 'Excellent';
  if (score >= 24) return 'Very Good';
  if (score >= 18) return 'Good';
  if (score >= 12) return 'Average';
  return 'Poor';
}

function getRecommendation(score, compatibility) {
  if (score >= 28) return 'Highly recommended for marriage';
  if (score >= 24) return 'Very favorable for marriage';
  if (score >= 18) return 'Favorable for marriage with minor considerations';
  if (score >= 12) return 'Marriage possible with proper remedies';
  return 'Marriage not recommended without significant remedies';
}

function getConfidenceLevel(score) {
  if (score >= 28) return 'Very High';
  if (score >= 24) return 'High';
  if (score >= 18) return 'Medium';
  return 'Low';
}

async function calculateMLCompatibilityScore(user1, user2) {
  // Simplified ML compatibility score
  // In a real implementation, this would call your ML service
  const baseScore = Math.random() * 30 + 70; // 70-100%
  return Math.round(baseScore * 100) / 100;
}

function generateDetailedAnalysis(gunaScores, overallScore) {
  const analysis = [];
  
  Object.entries(gunaScores).forEach(([guna, score]) => {
    const maxScore = GUNA_WEIGHTS[guna];
    const percentage = Math.round((score / maxScore) * 100);
    
    analysis.push({
      guna: guna.replace('_', ' ').toUpperCase(),
      score,
      maxScore,
      percentage,
      status: percentage >= 80 ? 'Excellent' : percentage >= 60 ? 'Good' : percentage >= 40 ? 'Average' : 'Poor',
      description: getGunaDescription(guna, score, maxScore)
    });
  });
  
  return analysis;
}

function getGunaDescription(guna, score, maxScore) {
  const descriptions = {
    varna: 'Indicates spiritual compatibility and ego levels',
    vashya: 'Shows mutual attraction and control in relationship',
    tara: 'Represents health, well-being and destiny',
    yoni: 'Indicates sexual compatibility and intimate bonding',
    graha_maitri: 'Shows mental compatibility and friendship',
    gana: 'Represents temperament and behavioral compatibility',
    bhakoot: 'Indicates love, affection and emotional bonding',
    nadi: 'Most important - represents health of progeny and genetic compatibility'
  };
  
  const percentage = Math.round((score / maxScore) * 100);
  return `${descriptions[guna]}. Score: ${percentage}%`;
}

function generateRemedies(gunaScores, overallScore) {
  const remedies = [];
  
  Object.entries(gunaScores).forEach(([guna, score]) => {
    const maxScore = GUNA_WEIGHTS[guna];
    const percentage = (score / maxScore) * 100;
    
    if (percentage < 60) {
      remedies.push({
        guna: guna.replace('_', ' ').toUpperCase(),
        issue: `Low ${guna} compatibility`,
        remedy: getRemedyForGuna(guna),
        priority: percentage < 30 ? 'High' : 'Medium'
      });
    }
  });
  
  return remedies;
}

function getRemedyForGuna(guna) {
  const remedies = {
    varna: 'Perform charity and spiritual practices together',
    vashya: 'Practice mutual respect and understanding',
    tara: 'Wear gemstones recommended by astrologer',
    yoni: 'Perform compatibility rituals before marriage',
    graha_maitri: 'Chant mantras for planetary harmony',
    gana: 'Practice patience and compromise',
    bhakoot: 'Perform love and harmony rituals',
    nadi: 'Perform special pujas and seek astrological guidance'
  };
  
  return remedies[guna] || 'Consult with qualified astrologer';
}

function generateAuspiciousDates(user1, user2) {
  // Generate some sample auspicious dates
  const dates = [];
  const currentDate = new Date();
  
  for (let i = 1; i <= 6; i++) {
    const futureDate = new Date(currentDate);
    futureDate.setMonth(currentDate.getMonth() + i);
    futureDate.setDate(Math.floor(Math.random() * 28) + 1);
    
    dates.push({
      date: futureDate.toISOString().split('T')[0],
      occasion: i <= 3 ? 'Marriage' : 'Engagement',
      auspiciousness: Math.random() > 0.5 ? 'Highly Auspicious' : 'Auspicious'
    });
  }
  
  return dates;
}

function generatePlanetaryAnalysis(user1, user2) {
  // Simplified planetary analysis
  return {
    sunCompatibility: Math.round(Math.random() * 40 + 60),
    moonCompatibility: Math.round(Math.random() * 40 + 60),
    marsCompatibility: Math.round(Math.random() * 40 + 60),
    mercuryCompatibility: Math.round(Math.random() * 40 + 60),
    jupiterCompatibility: Math.round(Math.random() * 40 + 60),
    venusCompatibility: Math.round(Math.random() * 40 + 60),
    saturnCompatibility: Math.round(Math.random() * 40 + 60)
  };
}

function generateDoshAnalysis(user1, user2) {
  // Simplified dosha analysis
  return {
    mangalDosha: {
      user1: Math.random() > 0.7,
      user2: Math.random() > 0.7,
      severity: Math.random() > 0.5 ? 'Low' : 'Medium',
      remedies: ['Perform Mars puja', 'Wear red coral', 'Fast on Tuesdays']
    },
    kaalsarpDosha: {
      user1: Math.random() > 0.8,
      user2: Math.random() > 0.8,
      severity: Math.random() > 0.5 ? 'Low' : 'Medium',
      remedies: ['Perform Rahu-Ketu puja', 'Visit Trimbakeshwar', 'Chant Maha Mrityunjaya mantra']
    },
    pitruDosha: {
      user1: Math.random() > 0.9,
      user2: Math.random() > 0.9,
      severity: 'Low',
      remedies: ['Perform Shraddha rituals', 'Feed Brahmins', 'Donate to charity']
    }
  };
}
