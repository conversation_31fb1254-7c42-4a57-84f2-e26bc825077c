import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import '../../core/api/api_client.dart';
import '../../app/theme.dart';

/// 🎁 REFER & EARN SERVICE - Mobile Implementation
/// Features: Referral code generation, sharing, reward tracking, statistics
/// Reuses existing website backend APIs for consistency

class ReferralService {
  static const String baseUrl = '/api/referrals';

  /// Get user's referral data
  static Future<Map<String, dynamic>> getReferralData() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('$baseUrl/my-referrals');
      
      if (response['success'] == true) {
        return response['data'] ?? {};
      }
      return {};
    } catch (e) {
      print('Error fetching referral data: $e');
      return {};
    }
  }

  /// Generate or get existing referral code
  static Future<String?> getReferralCode() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.post('$baseUrl/generate-code', {});
      
      if (response['success'] == true) {
        return response['referralCode'];
      }
      return null;
    } catch (e) {
      print('Error generating referral code: $e');
      return null;
    }
  }

  /// Get referral statistics
  static Future<Map<String, dynamic>> getReferralStats() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('$baseUrl/stats');
      
      if (response['success'] == true) {
        return response['stats'] ?? {};
      }
      return {
        'totalReferrals': 0,
        'successfulReferrals': 0,
        'totalRewards': 0,
        'pendingRewards': 0,
      };
    } catch (e) {
      print('Error fetching referral stats: $e');
      return {
        'totalReferrals': 0,
        'successfulReferrals': 0,
        'totalRewards': 0,
        'pendingRewards': 0,
      };
    }
  }

  /// Redeem referral code
  static Future<bool> redeemReferralCode(String code) async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.post('$baseUrl/redeem', {
        'referralCode': code,
      });
      
      return response['success'] == true;
    } catch (e) {
      print('Error redeeming referral code: $e');
      return false;
    }
  }

  /// Share referral code via native sharing
  static Future<void> shareReferralCode(String referralCode, String referralLink) async {
    try {
      final shareText = '''
🎉 Join Vaivahik Matrimony - India's Most Trusted Matrimony Platform!

I'm inviting you to find your perfect life partner on Vaivahik.

✨ Use my referral code: $referralCode
🔗 Or click this link: $referralLink

🎁 Special benefits when you sign up:
• Premium features for free
• Priority matching
• Enhanced profile visibility

Download the app and start your journey to forever! 💕

#VaivahikMatrimony #FindYourMatch #MarriageGoals
      ''';

      await Share.share(
        shareText,
        subject: 'Join Vaivahik Matrimony - Special Referral Offer',
      );
    } catch (e) {
      print('Error sharing referral code: $e');
    }
  }
}

/// Referral Provider
final referralProvider = StateNotifierProvider<ReferralNotifier, ReferralState>((ref) {
  return ReferralNotifier();
});

/// Referral State
class ReferralState {
  final String? referralCode;
  final String? referralLink;
  final Map<String, dynamic> stats;
  final List<Map<String, dynamic>> referralHistory;
  final bool isLoading;
  final String? error;

  const ReferralState({
    this.referralCode,
    this.referralLink,
    this.stats = const {},
    this.referralHistory = const [],
    this.isLoading = false,
    this.error,
  });

  ReferralState copyWith({
    String? referralCode,
    String? referralLink,
    Map<String, dynamic>? stats,
    List<Map<String, dynamic>>? referralHistory,
    bool? isLoading,
    String? error,
  }) {
    return ReferralState(
      referralCode: referralCode ?? this.referralCode,
      referralLink: referralLink ?? this.referralLink,
      stats: stats ?? this.stats,
      referralHistory: referralHistory ?? this.referralHistory,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Referral Notifier
class ReferralNotifier extends StateNotifier<ReferralState> {
  ReferralNotifier() : super(const ReferralState()) {
    loadReferralData();
  }

  Future<void> loadReferralData() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final data = await ReferralService.getReferralData();
      final stats = await ReferralService.getReferralStats();
      
      state = state.copyWith(
        referralCode: data['referralCode'],
        referralLink: data['referralLink'],
        stats: stats,
        referralHistory: List<Map<String, dynamic>>.from(data['referrals'] ?? []),
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load referral data: $e',
      );
    }
  }

  Future<void> generateReferralCode() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final code = await ReferralService.getReferralCode();
      if (code != null) {
        await loadReferralData(); // Reload all data
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to generate referral code',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error generating referral code: $e',
      );
    }
  }

  Future<void> shareReferralCode() async {
    if (state.referralCode != null && state.referralLink != null) {
      await ReferralService.shareReferralCode(
        state.referralCode!,
        state.referralLink!,
      );
    }
  }

  Future<bool> redeemCode(String code) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final success = await ReferralService.redeemReferralCode(code);
      if (success) {
        await loadReferralData(); // Reload data after successful redemption
      }
      
      state = state.copyWith(isLoading: false);
      return success;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Error redeeming referral code: $e',
      );
      return false;
    }
  }
}

/// Referral Screen
class ReferralScreen extends ConsumerWidget {
  const ReferralScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final referralState = ref.watch(referralProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Refer & Earn'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => ref.read(referralProvider.notifier).loadReferralData(),
          ),
        ],
      ),
      body: referralState.isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildReferralCard(context, ref, referralState),
                  const SizedBox(height: 24),
                  _buildStatsCards(referralState),
                  const SizedBox(height: 24),
                  _buildHowItWorks(),
                  const SizedBox(height: 24),
                  _buildReferralHistory(referralState),
                ],
              ),
            ),
    );
  }

  Widget _buildReferralCard(BuildContext context, WidgetRef ref, ReferralState state) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.card_giftcard, color: Colors.white, size: 28),
              SizedBox(width: 12),
              Text(
                'Your Referral Code',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (state.referralCode != null) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      state.referralCode!,
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        letterSpacing: 2,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () {
                      // Copy to clipboard
                      // Clipboard.setData(ClipboardData(text: state.referralCode!));
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Referral code copied to clipboard!'),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    },
                    icon: const Icon(Icons.copy, color: Colors.white),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => ref.read(referralProvider.notifier).shareReferralCode(),
                icon: const Icon(Icons.share),
                label: const Text('Share with Friends'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ] else ...[
            const Text(
              'Generate your unique referral code to start earning rewards!',
              style: TextStyle(color: Colors.white70),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => ref.read(referralProvider.notifier).generateReferralCode(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: const Text('Generate Referral Code'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatsCards(ReferralState state) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Referrals',
            state.stats['totalReferrals']?.toString() ?? '0',
            Icons.people,
            AppColors.primary,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'Rewards Earned',
            '₹${state.stats['totalRewards']?.toString() ?? '0'}',
            Icons.monetization_on,
            AppColors.success,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 12),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHowItWorks() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'How It Works',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildStep('1', 'Share your referral code with friends'),
          _buildStep('2', 'They sign up using your code'),
          _buildStep('3', 'Both of you get premium benefits'),
          _buildStep('4', 'Earn rewards for successful referrals'),
        ],
      ),
    );
  }

  Widget _buildStep(String number, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              description,
              style: const TextStyle(
                fontSize: 16,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReferralHistory(ReferralState state) {
    if (state.referralHistory.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: AppShadows.cardShadow,
        ),
        child: const Column(
          children: [
            Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No Referrals Yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Start sharing your referral code to see your referrals here!',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Referral History',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...state.referralHistory.map((referral) => _buildReferralItem(referral)),
        ],
      ),
    );
  }

  Widget _buildReferralItem(Map<String, dynamic> referral) {
    final status = referral['status'] ?? 'pending';
    final statusColor = status == 'completed' ? AppColors.success : AppColors.warning;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(6),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  referral['refereeEmail'] ?? 'Unknown',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  'Status: ${status.toUpperCase()}',
                  style: TextStyle(
                    fontSize: 14,
                    color: statusColor,
                  ),
                ),
              ],
            ),
          ),
          if (referral['rewardAmount'] != null)
            Text(
              '₹${referral['rewardAmount']}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.success,
              ),
            ),
        ],
      ),
    );
  }
}
