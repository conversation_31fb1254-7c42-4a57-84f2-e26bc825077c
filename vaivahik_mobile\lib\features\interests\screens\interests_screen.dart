import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../app/theme.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../providers/interests_provider.dart';
import '../models/interest_model.dart';
import '../widgets/interest_card.dart';
import '../widgets/interest_stats_card.dart';
import '../widgets/send_interest_dialog.dart';

/// 💝 INTERESTS SCREEN - Main Interest Management Interface
/// Features: Send/Receive Interests, Real-time Updates, Beautiful UI

class InterestsScreen extends ConsumerStatefulWidget {
  const InterestsScreen({super.key});

  @override
  ConsumerState<InterestsScreen> createState() => _InterestsScreenState();
}

class _InterestsScreenState extends ConsumerState<InterestsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;
  final ScrollController _receivedScrollController = ScrollController();
  final ScrollController _sentScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _animationController.forward();

    // Load interests on init
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(interestsProvider.notifier).loadInterests(refresh: true);
    });

    // Setup scroll listeners for pagination
    _receivedScrollController.addListener(_onReceivedScroll);
    _sentScrollController.addListener(_onSentScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    _receivedScrollController.dispose();
    _sentScrollController.dispose();
    super.dispose();
  }

  void _onReceivedScroll() {
    if (_receivedScrollController.position.pixels >=
        _receivedScrollController.position.maxScrollExtent * 0.8) {
      final state = ref.read(interestsProvider);
      if (!state.isLoading && state.hasMore) {
        ref.read(interestsProvider.notifier).loadInterests();
      }
    }
  }

  void _onSentScroll() {
    if (_sentScrollController.position.pixels >=
        _sentScrollController.position.maxScrollExtent * 0.8) {
      final state = ref.read(interestsProvider);
      if (!state.isLoading && state.hasMore) {
        ref.read(interestsProvider.notifier).loadInterests();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final interestsState = ref.watch(interestsProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildStatsCard(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildReceivedTab(interestsState),
                _buildSentTab(interestsState),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Interests',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () => context.push('/interests/dashboard'),
          icon: const Icon(Icons.analytics_outlined),
          tooltip: 'Analytics Dashboard',
        ),
        IconButton(
          onPressed: _refreshInterests,
          icon: const Icon(Icons.refresh),
          tooltip: 'Refresh',
        ),
      ],
    );
  }

  Widget _buildStatsCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: const InterestStatsCard(),
    ).animate(controller: _animationController)
     .slideY(begin: 0.5, delay: 200.ms, curve: Curves.elasticOut)
     .fadeIn(delay: 200.ms);
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppGradients.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard_outlined, size: 20),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.inbox, size: 20),
            text: 'Received',
          ),
          Tab(
            icon: Icon(Icons.send, size: 20),
            text: 'Sent',
          ),
        ],
      ),
    ).animate(controller: _animationController)
     .slideX(begin: 1, delay: 400.ms, curve: Curves.elasticOut)
     .fadeIn(delay: 400.ms);
  }

  Widget _buildOverviewTab() {
    final receivedInterests = ref.watch(receivedInterestsProvider);
    final sentInterests = ref.watch(sentInterestsProvider);
    final pendingReceived = ref.watch(pendingReceivedInterestsProvider);
    final pendingSent = ref.watch(pendingSentInterestsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickStats(receivedInterests, sentInterests, pendingReceived, pendingSent),
          const SizedBox(height: 20),
          _buildRecentActivity(),
          const SizedBox(height: 20),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildQuickStats(
    List<InterestModel> received,
    List<InterestModel> sent,
    List<InterestModel> pendingReceived,
    List<InterestModel> pendingSent,
  ) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total Received',
            received.length.toString(),
            Icons.inbox,
            Colors.green,
            '${pendingReceived.length} pending',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Total Sent',
            sent.length.toString(),
            Icons.send,
            Colors.blue,
            '${pendingSent.length} pending',
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    final receivedInterests = ref.watch(receivedInterestsProvider);
    final sentInterests = ref.watch(sentInterestsProvider);

    // Combine and sort by date
    final allInterests = [...receivedInterests, ...sentInterests];
    allInterests.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    final recentInterests = allInterests.take(5).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Recent Activity',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        if (recentInterests.isEmpty)
          const EmptyStateWidget(
            icon: Icons.timeline,
            title: 'No Recent Activity',
            message: 'Your recent interests will appear here',
          )
        else
          ...recentInterests.map((interest) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: InterestCard(
                  interest: interest,
                  type: receivedInterests.contains(interest)
                      ? InterestType.received
                      : InterestType.sent,
                  onTap: () => _viewProfile(interest),
                  onRespond: receivedInterests.contains(interest) &&
                          interest.status == 'PENDING'
                      ? (response, message) => _respondToInterest(
                            interest.id,
                            response,
                            message,
                          )
                      : null,
                ),
              )),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                'Find Matches',
                Icons.search,
                Colors.blue,
                () => context.push('/search'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionButton(
                'View Analytics',
                Icons.analytics,
                Colors.purple,
                () => context.push('/interests/dashboard'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color, color.withValues(alpha: 0.8)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white, size: 28),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReceivedTab(InterestsState state) {
    if (state.isLoading && state.received.isEmpty) {
      return const LoadingWidget();
    }

    if (state.error != null && state.received.isEmpty) {
      return CustomErrorWidget(
        message: state.error!,
        onRetry: () => ref.read(interestsProvider.notifier).loadInterests(refresh: true),
      );
    }

    if (state.received.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.inbox,
        title: 'No Interests Received',
        message: 'Interests from other users will appear here',
      );
    }

    return _buildInterestsList(
      state.received,
      InterestType.received,
      _receivedScrollController,
      state.isLoading,
    );
  }

  Widget _buildSentTab(InterestsState state) {
    if (state.isLoading && state.sent.isEmpty) {
      return const LoadingWidget();
    }

    if (state.error != null && state.sent.isEmpty) {
      return CustomErrorWidget(
        message: state.error!,
        onRetry: () => ref.read(interestsProvider.notifier).loadInterests(refresh: true),
      );
    }

    if (state.sent.isEmpty) {
      return const EmptyStateWidget(
        icon: Icons.send,
        title: 'No Interests Sent',
        message: 'Start exploring profiles and send interests',
      );
    }

    return _buildInterestsList(
      state.sent,
      InterestType.sent,
      _sentScrollController,
      state.isLoading,
    );
  }

  Widget _buildInterestsList(
    List<InterestModel> interests,
    InterestType type,
    ScrollController scrollController,
    bool isLoading,
  ) {
    return RefreshIndicator(
      onRefresh: _refreshInterests,
      child: ListView.builder(
        controller: scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: interests.length + (isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= interests.length) {
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final interest = interests[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: InterestCard(
              interest: interest,
              type: type,
              onTap: () => _viewProfile(interest),
              onRespond: type == InterestType.received && interest.status == 'PENDING'
                  ? (response, message) => _respondToInterest(
                        interest.id,
                        response,
                        message,
                      )
                  : null,
              onDelete: type == InterestType.sent && interest.status == 'PENDING'
                  ? () => _deleteInterest(interest.id)
                  : null,
            ).animate()
             .slideX(begin: 1, delay: (index * 50).ms)
             .fadeIn(delay: (index * 50).ms),
          );
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: _showSendInterestDialog,
      backgroundColor: AppColors.primary,
      icon: const Icon(Icons.favorite, color: Colors.white),
      label: const Text(
        'Send Interest',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    ).animate(controller: _animationController)
     .scale(delay: 800.ms, curve: Curves.elasticOut);
  }

  // Action methods
  Future<void> _refreshInterests() async {
    await ref.read(interestsProvider.notifier).refresh();
  }

  void _viewProfile(InterestModel interest) {
    final targetUserId = interest.userId == interest.targetUserId
        ? interest.targetUserId
        : interest.userId;
    context.push('/profile/$targetUserId');
  }

  Future<void> _respondToInterest(
    String interestId,
    String response,
    String? message,
  ) async {
    final success = await ref.read(interestsProvider.notifier).respondToInterest(
          interestId: interestId,
          response: response,
          message: message,
        );

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            response == 'ACCEPTED'
                ? 'Interest accepted successfully!'
                : 'Interest declined',
          ),
          backgroundColor: response == 'ACCEPTED' ? Colors.green : Colors.orange,
        ),
      );
    }
  }

  Future<void> _deleteInterest(String interestId) async {
    final success = await ref.read(interestsProvider.notifier).deleteInterest(interestId);

    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Interest withdrawn successfully'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _showSendInterestDialog() {
    showDialog(
      context: context,
      builder: (context) => const SendInterestDialog(),
    );
  }
}
