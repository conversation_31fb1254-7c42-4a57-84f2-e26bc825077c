// Script to seed content management data
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const contentPages = [
  {
    key: 'privacy-policy',
    title: 'Privacy Policy',
    content: `
# Privacy Policy

## Information We Collect
We collect information you provide directly to us, such as when you create an account, fill out your profile, or contact us.

## How We Use Your Information
- To provide and maintain our matrimony services
- To match you with compatible profiles
- To communicate with you about our services
- To improve our platform and user experience

## Information Sharing
We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.

## Data Security
We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.

## Contact Us
If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>
    `,
    metaTitle: 'Privacy Policy - Vaivahik Matrimony',
    metaDescription: 'Learn about how Vaivahik protects your privacy and handles your personal information.',
    isPublished: true
  },
  {
    key: 'terms-of-service',
    title: 'Terms of Service',
    content: `
# Terms of Service

## Acceptance of Terms
By accessing and using Vaivahik, you accept and agree to be bound by the terms and provision of this agreement.

## Use License
Permission is granted to temporarily use Vaivahik for personal, non-commercial matrimonial purposes.

## User Responsibilities
- Provide accurate and truthful information
- Respect other users and their privacy
- Use the platform for legitimate matrimonial purposes only
- Do not engage in harassment or inappropriate behavior

## Account Termination
We reserve the right to terminate accounts that violate our terms of service.

## Contact Information
For questions about these Terms of Service, contact <NAME_EMAIL>
    `,
    metaTitle: 'Terms of Service - Vaivahik Matrimony',
    metaDescription: 'Read the terms and conditions for using Vaivahik matrimony platform.',
    isPublished: true
  },
  {
    key: 'about-vaivahik',
    title: 'About Vaivahik',
    content: `
# About Vaivahik

## Our Mission
Vaivahik is dedicated to helping members of the Maratha community find their perfect life partner through our advanced AI-powered matching system.

## Our Story
Founded with the vision of preserving cultural values while embracing modern technology, Vaivahik combines traditional matrimonial practices with cutting-edge algorithms.

## What Makes Us Different
- **AI-Powered Matching**: Our advanced algorithms consider compatibility factors beyond basic preferences
- **Community Focused**: Specifically designed for the Maratha community
- **Privacy First**: Your data security and privacy are our top priorities
- **Verified Profiles**: We ensure authentic profiles through our verification process

## Our Values
- Authenticity
- Respect for tradition
- Innovation in matchmaking
- User privacy and security

## Contact Us
Email: <EMAIL>
Phone: +91-XXXX-XXXX-XX
    `,
    metaTitle: 'About Vaivahik - Maratha Matrimony Platform',
    metaDescription: 'Learn about Vaivahik, the premier matrimony platform for the Maratha community.',
    isPublished: true
  },
  {
    key: 'contact-us',
    title: 'Contact Us',
    content: `
# Contact Us

## Get in Touch
We're here to help you find your perfect match. Reach out to us for any questions or support.

## Contact Information
**Email**: <EMAIL>
**Phone**: +91-XXXX-XXXX-XX
**Address**: [Your Address Here]

## Business Hours
- Monday to Friday: 9:00 AM - 6:00 PM
- Saturday: 10:00 AM - 4:00 PM
- Sunday: Closed

## Support Categories
- **Technical Support**: For app and website issues
- **Profile Assistance**: Help with profile creation and verification
- **Billing Questions**: Subscription and payment inquiries
- **General Inquiries**: Any other questions or feedback

## Response Time
We typically respond to all inquiries within 24 hours during business days.
    `,
    metaTitle: 'Contact Us - Vaivahik Support',
    metaDescription: 'Get in touch with Vaivahik support team for assistance with your matrimony journey.',
    isPublished: true
  }
];

const socialMediaLinks = [
  {
    platform: 'Facebook',
    url: 'https://facebook.com/vaivahik',
    icon: 'facebook',
    isActive: true,
    sortOrder: 1
  },
  {
    platform: 'Instagram',
    url: 'https://instagram.com/vaivahik',
    icon: 'instagram',
    isActive: true,
    sortOrder: 2
  },
  {
    platform: 'Twitter',
    url: 'https://twitter.com/vaivahik',
    icon: 'twitter',
    isActive: true,
    sortOrder: 3
  },
  {
    platform: 'LinkedIn',
    url: 'https://linkedin.com/company/vaivahik',
    icon: 'linkedin',
    isActive: true,
    sortOrder: 4
  }
];

async function seedContent() {
  try {
    console.log('🌱 Seeding content management data...');

    // Seed content pages
    for (const page of contentPages) {
      await prisma.contentPage.upsert({
        where: { key: page.key },
        update: page,
        create: page
      });
      console.log(`✅ Created/Updated content page: ${page.key}`);
    }

    // Seed social media links
    for (const link of socialMediaLinks) {
      await prisma.socialMediaLink.upsert({
        where: { platform: link.platform },
        update: link,
        create: link
      });
      console.log(`✅ Created/Updated social media link: ${link.platform}`);
    }

    console.log('🎉 Content seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding content:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedContent();
