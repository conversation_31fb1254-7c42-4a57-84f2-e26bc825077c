import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../providers/contact_provider.dart';
import '../widgets/contact_stats_card.dart';
import '../widgets/contact_history_card.dart';
import '../widgets/contact_privacy_settings_card.dart';
import '../models/contact_models.dart';


class ContactManagementScreen extends StatefulWidget {
  const ContactManagementScreen({super.key});

  @override
  State<ContactManagementScreen> createState() => _ContactManagementScreenState();
}

class _ContactManagementScreenState extends State<ContactManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ContactProvider>().initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Consumer<ContactProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.accessHistory.isEmpty) {
            return const LoadingWidget(size: 50);
          }

          if (provider.error != null && provider.accessHistory.isEmpty) {
            return CustomErrorWidget(
              message: provider.error!,
              onRetry: () => provider.initialize(),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(provider),
              _buildHistoryTab(provider),
              _buildPrivacyTab(provider),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Contact Management',
        style: AppTextStyles.h2.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
        unselectedLabelStyle: AppTextStyles.bodyMedium,
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'History'),
          Tab(text: 'Privacy'),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(ContactProvider provider) {
    return RefreshIndicator(
      onRefresh: () => provider.refresh(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statistics Card
            if (provider.stats != null)
              ContactStatsCard(stats: provider.stats!),
            
            const SizedBox(height: 20),
            
            // Quick Actions
            _buildQuickActions(),
            
            const SizedBox(height: 20),
            
            // Recent History
            _buildRecentHistory(provider),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryTab(ContactProvider provider) {
    if (provider.accessHistory.isEmpty) {
      return const EmptyStateWidget(
        title: 'No Contact History',
        message: 'Your contact access history will appear here',
        icon: Icons.history,
      );
    }

    return RefreshIndicator(
      onRefresh: () => provider.loadAccessHistory(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: provider.accessHistory.length,
        itemBuilder: (context, index) {
          final history = provider.accessHistory[index];
          return ContactHistoryCard(
            history: history,
            onTap: () => _showHistoryDetails(history),
          );
        },
      ),
    );
  }

  Widget _buildPrivacyTab(ContactProvider provider) {
    return RefreshIndicator(
      onRefresh: () => provider.loadPrivacySettings(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            if (provider.privacySettings != null)
              ContactPrivacySettingsCard(
                settings: provider.privacySettings!,
                onUpdate: (settings) => provider.updatePrivacySettings(settings),
              ),
            
            const SizedBox(height: 20),
            
            _buildPrivacyInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: AppTextStyles.h3.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.history,
                    label: 'View History',
                    onTap: () => _tabController.animateTo(1),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    icon: Icons.privacy_tip,
                    label: 'Privacy Settings',
                    onTap: () => _tabController.animateTo(2),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(icon, color: AppColors.primary, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentHistory(ContactProvider provider) {
    final recentHistory = provider.accessHistory.take(3).toList();
    
    if (recentHistory.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Activity',
                  style: AppTextStyles.h3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(1),
                  child: Text(
                    'View All',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...recentHistory.map((history) => ContactHistoryCard(
              history: history,
              isCompact: true,
              onTap: () => _showHistoryDetails(history),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacyInfo() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.info_outline, color: AppColors.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Privacy Information',
                  style: AppTextStyles.h4.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Your contact information is protected by our privacy policy. You can control who can access your contact details and when.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showHistoryDetails(ContactAccessHistory history) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.info_outline, color: AppColors.primary),
            SizedBox(width: 8),
            Text('Contact Details'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Contact', _getContactName(history)),
              _buildDetailRow('Access Type', _getAccessTypeDisplay(history.accessType)),
              _buildDetailRow('Date & Time', DateFormat('MMM dd, yyyy • hh:mm a').format(history.accessedAt)),
              if (history.contactNumber != null && history.contactNumber!.isNotEmpty)
                _buildDetailRow('Contact Number', history.contactNumber!),
              if (history.accessReason != null && history.accessReason!.isNotEmpty)
                _buildDetailRow('Access Reason', history.accessReason!),
              _buildDetailRow('Premium Access', history.isPremiumAccess ? 'Yes' : 'No'),
              if (history.callDuration != null)
                _buildDetailRow('Call Duration', _formatDuration(history.callDuration!)),
              if (history.callStatus != null)
                _buildDetailRow('Call Status', history.callStatus!),
              if (history.platform != null)
                _buildDetailRow('Platform', history.platform!),
              if (history.featurePurchaseId != null)
                _buildDetailRow('Purchase ID', history.featurePurchaseId!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  String _getContactName(ContactAccessHistory history) {
    if (history.contactOwner != null) {
      return history.contactOwner!.fullName ?? 'Unknown User';
    }
    return 'Unknown User';
  }

  String _getAccessTypeDisplay(String? accessType) {
    switch (accessType?.toLowerCase()) {
      case 'phone':
        return 'Phone Number';
      case 'whatsapp':
        return 'WhatsApp';
      case 'email':
        return 'Email Address';
      case 'call':
        return 'Phone Call';
      default:
        return accessType ?? 'Contact Access';
    }
  }

  String _formatDuration(int seconds) {
    final duration = Duration(seconds: seconds);
    final minutes = duration.inMinutes;
    final remainingSeconds = duration.inSeconds % 60;

    if (minutes > 0) {
      return '${minutes}m ${remainingSeconds}s';
    } else {
      return '${remainingSeconds}s';
    }
  }
}
