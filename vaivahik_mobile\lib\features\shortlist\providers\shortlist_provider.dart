import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/shortlist_model.dart';
import '../services/shortlist_service.dart';

/// 📋 SHORTLIST STATE NOTIFIER - Complete State Management
/// Features: CRUD Operations, Filters, Pagination, Real-time Updates
class ShortlistNotifier extends StateNotifier<ShortlistUIState> {
  final ShortlistService _shortlistService;

  ShortlistNotifier(this._shortlistService) : super(const ShortlistUIState());

  /// 📋 Load Shortlisted Profiles
  Future<void> loadShortlist({
    bool refresh = false,
    ShortlistFilters? filters,
  }) async {
    if (refresh) {
      state = state.copyWith(
        isRefreshing: true,
        error: null,
        currentPage: 1,
      );
    } else if (state.isLoading || !state.hasMore) {
      return;
    } else {
      state = state.copyWith(isLoading: true, error: null);
    }

    try {
      final page = refresh ? 1 : state.currentPage;
      final response = await _shortlistService.getShortlistedProfiles(
        page: page,
        limit: 20,
        filters: filters ?? state.filters,
      );

      if (refresh) {
        state = state.copyWith(
          items: response.data ?? [],
          isLoading: false,
          isRefreshing: false,
          hasMore: (response.data?.length ?? 0) >= 20,
          currentPage: 1,
          filters: filters,
        );
      } else {
        state = state.copyWith(
          items: [...state.items, ...(response.data ?? [])],
          isLoading: false,
          hasMore: (response.data?.length ?? 0) >= 20,
          currentPage: state.currentPage + 1,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isRefreshing: false,
        error: e.toString(),
      );
    }
  }

  /// 🔄 Refresh Shortlist
  Future<void> refreshShortlist({ShortlistFilters? filters}) async {
    await loadShortlist(refresh: true, filters: filters);
  }

  /// ➕ Add Profile to Shortlist
  Future<bool> addToShortlist(String profileId, {String? note}) async {
    try {
      final request = AddToShortlistRequest(profileId: profileId, note: note);
      final result = await _shortlistService.addToShortlist(request);
      
      if (result.success) {
        // Refresh the list to get the updated data
        await refreshShortlist();
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// 🗑️ Remove Profile from Shortlist
  Future<bool> removeFromShortlist(String shortlistId) async {
    try {
      final request = RemoveFromShortlistRequest(shortlistId: shortlistId);
      final result = await _shortlistService.removeFromShortlist(request);
      
      if (result.success) {
        // Remove from local state immediately for better UX
        state = state.copyWith(
          items: state.items.where((item) => item.id != shortlistId).toList(),
        );
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// ✏️ Update Shortlist Note
  Future<bool> updateNote(String shortlistId, String? note) async {
    try {
      final request = UpdateShortlistNoteRequest(shortlistId: shortlistId, note: note);
      final result = await _shortlistService.updateShortlistNote(request);
      
      if (result.success) {
        // Update local state immediately
        state = state.copyWith(
          items: state.items.map((item) {
            if (item.id == shortlistId) {
              return item.copyWith(note: note);
            }
            return item;
          }).toList(),
        );
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// 🔍 Apply Filters
  void applyFilters(ShortlistFilters filters) {
    state = state.copyWith(filters: filters);
    refreshShortlist(filters: filters);
  }

  /// 🧹 Clear Filters
  void clearFilters() {
    state = state.copyWith(filters: null);
    refreshShortlist();
  }

  /// 📊 Update Sort Options
  void updateSort(String sortBy, bool ascending) {
    final filters = state.filters?.copyWith(
      sortBy: sortBy,
      sortAscending: ascending,
    ) ?? ShortlistFilters(sortBy: sortBy, sortAscending: ascending);
    
    state = state.copyWith(
      sortBy: sortBy,
      sortAscending: ascending,
      filters: filters,
    );
    
    refreshShortlist(filters: filters);
  }

  /// 🎯 Select Item
  void selectItem(String? itemId) {
    state = state.copyWith(selectedItemId: itemId);
  }

  /// 🔍 Toggle Filters Visibility
  void toggleFilters() {
    state = state.copyWith(showFilters: !state.showFilters);
  }

  /// 🗑️ Bulk Remove Items
  Future<bool> bulkRemove(List<String> shortlistIds) async {
    try {
      final result = await _shortlistService.bulkRemoveFromShortlist(shortlistIds);
      
      if (result.success) {
        // Remove from local state
        state = state.copyWith(
          items: state.items.where((item) => !shortlistIds.contains(item.id)).toList(),
        );
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// 💝 Send Interest to Profile
  Future<bool> sendInterest(String profileId, {String? message}) async {
    try {
      final success = await _shortlistService.sendInterestToShortlistedProfile(
        profileId,
        message: message,
      );
      
      if (success) {
        // Update local state to reflect interest sent
        state = state.copyWith(
          items: state.items.map((item) {
            if (item.profile.id == profileId) {
              return item.copyWith(
                interestSent: true,
                interestStatus: 'PENDING',
              );
            }
            return item;
          }).toList(),
        );
        return true;
      }
      return false;
    } catch (e) {
      state = state.copyWith(error: e.toString());
      return false;
    }
  }

  /// 🧹 Clear Error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 🔄 Reset State
  void reset() {
    state = const ShortlistUIState();
  }
}

/// 📊 SHORTLIST STATS NOTIFIER
class ShortlistStatsNotifier extends StateNotifier<AsyncValue<ShortlistStats>> {
  final ShortlistService _shortlistService;

  ShortlistStatsNotifier(this._shortlistService) : super(const AsyncValue.loading());

  /// 📊 Load Statistics
  Future<void> loadStats() async {
    state = const AsyncValue.loading();
    try {
      final stats = await _shortlistService.getShortlistStats();
      state = AsyncValue.data(stats);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// 🔄 Refresh Statistics
  Future<void> refreshStats() async {
    await loadStats();
  }
}

/// 🏭 PROVIDERS

/// Main shortlist provider
final shortlistProvider = StateNotifierProvider<ShortlistNotifier, ShortlistUIState>((ref) {
  final shortlistService = ref.read(shortlistServiceProvider);
  return ShortlistNotifier(shortlistService);
});

/// Shortlist statistics provider
final shortlistStatsProvider = StateNotifierProvider<ShortlistStatsNotifier, AsyncValue<ShortlistStats>>((ref) {
  final shortlistService = ref.read(shortlistServiceProvider);
  return ShortlistStatsNotifier(shortlistService);
});

/// Filtered shortlist items provider
final filteredShortlistProvider = Provider<List<ShortlistItem>>((ref) {
  final state = ref.watch(shortlistProvider);
  final filters = state.filters;
  
  if (filters == null) return state.items;
  
  return state.items.where((item) {
    // Search query filter
    if (filters.searchQuery?.isNotEmpty == true) {
      final query = filters.searchQuery!.toLowerCase();
      final fullName = item.fullName.toLowerCase();
      final location = item.profile.location?.toLowerCase() ?? '';
      final education = item.profile.education?.toLowerCase() ?? '';
      final occupation = item.profile.occupation?.toLowerCase() ?? '';
      
      if (!fullName.contains(query) && 
          !location.contains(query) && 
          !education.contains(query) && 
          !occupation.contains(query)) {
        return false;
      }
    }
    
    // Interest sent filter
    if (filters.interestSent != null && item.interestSent != filters.interestSent) {
      return false;
    }
    
    // Interest status filter
    if (filters.interestStatus?.isNotEmpty == true && 
        item.interestStatus != filters.interestStatus) {
      return false;
    }
    
    // Age filters
    if (filters.minAge != null && (item.profile.age ?? 0) < filters.minAge!) {
      return false;
    }
    if (filters.maxAge != null && (item.profile.age ?? 0) > filters.maxAge!) {
      return false;
    }
    
    // Height filters
    if (filters.minHeight != null && (item.profile.height ?? 0) < filters.minHeight!) {
      return false;
    }
    if (filters.maxHeight != null && (item.profile.height ?? 0) > filters.maxHeight!) {
      return false;
    }
    
    // Other filters
    if (filters.religion?.isNotEmpty == true && 
        item.profile.religion != filters.religion) {
      return false;
    }
    if (filters.caste?.isNotEmpty == true && 
        item.profile.caste != filters.caste) {
      return false;
    }
    if (filters.maritalStatus?.isNotEmpty == true && 
        item.profile.maritalStatus != filters.maritalStatus) {
      return false;
    }
    if (filters.isVerified != null && 
        item.profile.isVerified != filters.isVerified) {
      return false;
    }
    if (filters.isOnline != null && 
        item.profile.isOnline != filters.isOnline) {
      return false;
    }
    
    return true;
  }).toList();
});

/// Check if profile is shortlisted provider
final isProfileShortlistedProvider = FutureProvider.family<bool, String>((ref, profileId) async {
  final shortlistService = ref.read(shortlistServiceProvider);
  return await shortlistService.isProfileShortlisted(profileId);
});

/// Get shortlist item by profile ID provider
final shortlistByProfileProvider = FutureProvider.family<ShortlistItem?, String>((ref, profileId) async {
  final shortlistService = ref.read(shortlistServiceProvider);
  return await shortlistService.getShortlistByProfileId(profileId);
});
