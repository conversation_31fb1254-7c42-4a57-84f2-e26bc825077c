/**
 * Manual Test Script for Comprehensive Kundali System
 * Tests all components of the authentic Vedic astrology system
 */

const VedicAstrologyService = require('../src/services/vedicAstrology.service');
const ManglikDoshaService = require('../src/services/manglikDosha.service');
const ComprehensiveKundaliService = require('../src/services/comprehensiveKundali.service');

async function runKundaliTests() {
  console.log('🔮 Starting Comprehensive Kundali System Tests...\n');

  // Initialize services
  const vedicService = new VedicAstrologyService();
  const manglikService = new ManglikDoshaService();
  const kundaliService = new ComprehensiveKundaliService();

  let passedTests = 0;
  let totalTests = 0;

  function test(description, testFunction) {
    totalTests++;
    try {
      const result = testFunction();
      if (result) {
        console.log(`✅ PASS: ${description}`);
        passedTests++;
      } else {
        console.log(`❌ FAIL: ${description}`);
      }
    } catch (error) {
      console.log(`❌ ERROR: ${description} - ${error.message}`);
    }
  }

  async function asyncTest(description, testFunction) {
    totalTests++;
    try {
      const result = await testFunction();
      if (result) {
        console.log(`✅ PASS: ${description}`);
        passedTests++;
      } else {
        console.log(`❌ FAIL: ${description}`);
      }
    } catch (error) {
      console.log(`❌ ERROR: ${description} - ${error.message}`);
    }
  }

  // Test 1: Nakshatra Calculation
  test('Nakshatra calculation for 45 degrees', () => {
    const nakshatra = vedicService.getNakshatraFromLongitude(45);
    return nakshatra && nakshatra.name === 'Rohini' && nakshatra.lord === 'Moon';
  });

  // Test 2: Rashi Calculation
  test('Rashi calculation for 45 degrees', () => {
    const rashi = vedicService.getRashiFromLongitude(45);
    return rashi && rashi.name === 'Taurus' && rashi.lord === 'Venus';
  });

  // Test 3: Birth Chart Generation
  test('Birth chart generation with all elements', () => {
    const chart = vedicService.calculateBirthChart('1990-05-15', '14:30', 'Mumbai, India');
    return chart &&
           chart.rashi &&
           chart.nakshatra &&
           chart.moonLongitude >= 0 &&
           chart.birthDate &&
           chart.birthTime &&
           chart.birthPlace;
  });

  // Test 4: Ashtakoot Guna Calculation
  test('Ashtakoot Guna calculation', () => {
    const chart1 = {
      rashi: { name: 'Aries', index: 0 },
      nakshatra: { name: 'Ashwini', index: 0, lord: 'Ketu', gana: 'Deva', yoni: 'Horse', varna: 'Vaishya' }
    };
    const chart2 = {
      rashi: { name: 'Leo', index: 4 },
      nakshatra: { name: 'Magha', index: 9, lord: 'Ketu', gana: 'Rakshasa', yoni: 'Rat', varna: 'Shudra' }
    };

    const gunaResult = vedicService.calculateAshtakootGuna(chart1, chart2);
    return gunaResult &&
           gunaResult.scores &&
           gunaResult.totalScore >= 0 &&
           gunaResult.totalScore <= 36 &&
           gunaResult.maxScore === 36;
  });

  // Test 5: Manglik Dosha Detection
  test('Manglik dosha detection', () => {
    const doshaResult = manglikService.detectManglikDosha('1990-05-15', '14:30', 'Mumbai, India');
    return doshaResult &&
           typeof doshaResult.isManglik === 'boolean' &&
           typeof doshaResult.intensity === 'number' &&
           doshaResult.doshaLevelName &&
           Array.isArray(doshaResult.remedies);
  });

  // Test 6: Birth Data Validation
  test('Birth data validation - valid data', () => {
    const validData = {
      birthDate: '1990-05-15',
      birthTime: '14:30',
      birthPlace: 'Mumbai, India'
    };
    const result = kundaliService.validateBirthData(validData);
    return result.isValid === true && result.errors.length === 0;
  });

  // Test 7: Birth Data Validation - invalid data
  test('Birth data validation - invalid data', () => {
    const invalidData = {
      birthDate: 'invalid-date',
      birthTime: '25:70',
      birthPlace: ''
    };
    const result = kundaliService.validateBirthData(invalidData);
    return result.isValid === false && result.errors.length > 0;
  });

  // Test 8: Comprehensive Kundali Match
  await asyncTest('Complete kundali matching', async () => {
    const user1 = {
      id: 'test1',
      name: 'Test User 1',
      birthDate: '1990-05-15',
      birthTime: '14:30',
      birthPlace: 'Mumbai, India',
      timezone: 'IST'
    };

    const user2 = {
      id: 'test2',
      name: 'Test User 2',
      birthDate: '1992-08-20',
      birthTime: '10:15',
      birthPlace: 'Delhi, India',
      timezone: 'IST'
    };

    const options = {
      includeCharts: true,
      includeRemedies: true,
      includeDetailedAnalysis: true
    };

    const result = await kundaliService.generateCompleteKundaliMatch(user1, user2, options);
    
    return result &&
           result.overallCompatibility &&
           result.gunaMatching &&
           result.manglikAnalysis &&
           result.remedies &&
           result.recommendation &&
           typeof result.overallCompatibility.score === 'number' &&
           result.overallCompatibility.score >= 0 &&
           result.overallCompatibility.score <= 100;
  });

  // Test 9: Manglik Compatibility
  test('Manglik compatibility calculation', () => {
    const person1Dosha = { isManglik: true, intensity: 50 };
    const person2Dosha = { isManglik: false, intensity: 0 };

    const compatibility = manglikService.checkManglikCompatibility(person1Dosha, person2Dosha);

    return compatibility &&
           typeof compatibility.compatible === 'boolean' &&
           typeof compatibility.remediesRequired === 'boolean' &&
           compatibility.compatibility &&
           typeof compatibility.score === 'number';
  });

  // Test 10: Real Data Test
  await asyncTest('Real birth data processing', async () => {
    const realUser1 = {
      id: 'real1',
      name: 'Arjun Sharma',
      birthDate: '1988-03-21',
      birthTime: '06:45',
      birthPlace: 'Pune, Maharashtra, India',
      timezone: 'IST'
    };

    const realUser2 = {
      id: 'real2',
      name: 'Priya Patel',
      birthDate: '1990-11-15',
      birthTime: '18:20',
      birthPlace: 'Ahmedabad, Gujarat, India',
      timezone: 'IST'
    };

    const result = await kundaliService.generateCompleteKundaliMatch(
      realUser1, 
      realUser2, 
      { includeDetailedAnalysis: true }
    );

    return result &&
           result.overallCompatibility.score > 0 &&
           result.gunaMatching.totalScore <= 36 &&
           result.manglikAnalysis &&
           result.recommendation;
  });

  // Performance Test
  console.log('\n⏱️  Running Performance Test...');
  const startTime = Date.now();
  
  await asyncTest('Performance test - under 5 seconds', async () => {
    const testUser1 = {
      id: 'perf1',
      name: 'Performance Test 1',
      birthDate: '1990-05-15',
      birthTime: '14:30',
      birthPlace: 'Mumbai, India',
      timezone: 'IST'
    };

    const testUser2 = {
      id: 'perf2',
      name: 'Performance Test 2',
      birthDate: '1992-08-20',
      birthTime: '10:15',
      birthPlace: 'Delhi, India',
      timezone: 'IST'
    };

    await kundaliService.generateCompleteKundaliMatch(
      testUser1, 
      testUser2, 
      { includeDetailedAnalysis: true }
    );

    const endTime = Date.now();
    const executionTime = endTime - startTime;
    
    console.log(`   Execution time: ${executionTime}ms`);
    return executionTime < 5000; // Should complete within 5 seconds
  });

  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Passed: ${passedTests}`);
  console.log(`   Failed: ${totalTests - passedTests}`);
  console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);

  if (passedTests === totalTests) {
    console.log('\n🎉 All tests passed! Kundali system is working correctly.');
    return true;
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
    return false;
  }
}

// Run the tests
if (require.main === module) {
  runKundaliTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { runKundaliTests };
