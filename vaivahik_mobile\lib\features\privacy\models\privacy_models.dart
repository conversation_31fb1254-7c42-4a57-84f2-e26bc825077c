class PrivacySettings {
  final String userId;
  final ProfileVisibility profileVisibility;
  final ContactVisibility contactVisibility;
  final PhotoVisibility photoVisibility;
  final NameDisplayOption nameDisplay;
  final bool showOnlineStatus;
  final bool showLastSeen;
  final bool allowDirectMessages;
  final bool allowCalls;
  final bool showProfileViews;
  final bool showInterestSent;
  final bool showShortlisted;
  final List<String> blockedUsers;
  final List<String> restrictedUsers;
  final Map<String, dynamic> customSettings;
  final DateTime updatedAt;

  const PrivacySettings({
    required this.userId,
    required this.profileVisibility,
    required this.contactVisibility,
    required this.photoVisibility,
    required this.nameDisplay,
    required this.showOnlineStatus,
    required this.showLastSeen,
    required this.allowDirectMessages,
    required this.allowCalls,
    required this.showProfileViews,
    required this.showInterestSent,
    required this.showShortlisted,
    required this.blockedUsers,
    required this.restrictedUsers,
    required this.customSettings,
    required this.updatedAt,
  });

  factory PrivacySettings.fromJson(Map<String, dynamic> json) {
    return PrivacySettings(
      userId: json['userId']?.toString() ?? '',
      profileVisibility: ProfileVisibility.values.firstWhere(
        (e) => e.name == json['profileVisibility']?.toString(),
        orElse: () => ProfileVisibility.public,
      ),
      contactVisibility: ContactVisibility.values.firstWhere(
        (e) => e.name == json['contactVisibility']?.toString(),
        orElse: () => ContactVisibility.premium,
      ),
      photoVisibility: PhotoVisibility.values.firstWhere(
        (e) => e.name == json['photoVisibility']?.toString(),
        orElse: () => PhotoVisibility.registered,
      ),
      nameDisplay: NameDisplayOption.values.firstWhere(
        (e) => e.name == json['nameDisplay']?.toString(),
        orElse: () => NameDisplayOption.fullName,
      ),
      showOnlineStatus: json['showOnlineStatus'] ?? true,
      showLastSeen: json['showLastSeen'] ?? true,
      allowDirectMessages: json['allowDirectMessages'] ?? true,
      allowCalls: json['allowCalls'] ?? true,
      showProfileViews: json['showProfileViews'] ?? true,
      showInterestSent: json['showInterestSent'] ?? true,
      showShortlisted: json['showShortlisted'] ?? true,
      blockedUsers: List<String>.from(json['blockedUsers'] ?? []),
      restrictedUsers: List<String>.from(json['restrictedUsers'] ?? []),
      customSettings: Map<String, dynamic>.from(json['customSettings'] ?? {}),
      updatedAt: DateTime.tryParse(json['updatedAt']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'profileVisibility': profileVisibility.name,
      'contactVisibility': contactVisibility.name,
      'photoVisibility': photoVisibility.name,
      'nameDisplay': nameDisplay.name,
      'showOnlineStatus': showOnlineStatus,
      'showLastSeen': showLastSeen,
      'allowDirectMessages': allowDirectMessages,
      'allowCalls': allowCalls,
      'showProfileViews': showProfileViews,
      'showInterestSent': showInterestSent,
      'showShortlisted': showShortlisted,
      'blockedUsers': blockedUsers,
      'restrictedUsers': restrictedUsers,
      'customSettings': customSettings,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  PrivacySettings copyWith({
    String? userId,
    ProfileVisibility? profileVisibility,
    ContactVisibility? contactVisibility,
    PhotoVisibility? photoVisibility,
    NameDisplayOption? nameDisplay,
    bool? showOnlineStatus,
    bool? showLastSeen,
    bool? allowDirectMessages,
    bool? allowCalls,
    bool? showProfileViews,
    bool? showInterestSent,
    bool? showShortlisted,
    List<String>? blockedUsers,
    List<String>? restrictedUsers,
    Map<String, dynamic>? customSettings,
    DateTime? updatedAt,
  }) {
    return PrivacySettings(
      userId: userId ?? this.userId,
      profileVisibility: profileVisibility ?? this.profileVisibility,
      contactVisibility: contactVisibility ?? this.contactVisibility,
      photoVisibility: photoVisibility ?? this.photoVisibility,
      nameDisplay: nameDisplay ?? this.nameDisplay,
      showOnlineStatus: showOnlineStatus ?? this.showOnlineStatus,
      showLastSeen: showLastSeen ?? this.showLastSeen,
      allowDirectMessages: allowDirectMessages ?? this.allowDirectMessages,
      allowCalls: allowCalls ?? this.allowCalls,
      showProfileViews: showProfileViews ?? this.showProfileViews,
      showInterestSent: showInterestSent ?? this.showInterestSent,
      showShortlisted: showShortlisted ?? this.showShortlisted,
      blockedUsers: blockedUsers ?? this.blockedUsers,
      restrictedUsers: restrictedUsers ?? this.restrictedUsers,
      customSettings: customSettings ?? this.customSettings,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum ProfileVisibility {
  public,
  registered,
  premium,
  private
}

enum ContactVisibility {
  public,
  registered,
  premium,
  verified,
  never
}

enum PhotoVisibility {
  public,
  registered,
  premium,
  verified,
  private
}

enum NameDisplayOption {
  fullName,
  firstName,
  profileId,
  anonymous
}

class SecuritySettings {
  final bool twoFactorEnabled;
  final bool biometricEnabled;
  final bool deviceLockEnabled;
  final int sessionTimeout;
  final bool logSecurityEvents;
  final bool emailSecurityAlerts;
  final bool smsSecurityAlerts;
  final List<String> trustedDevices;
  final Map<String, DateTime> loginAttempts;
  final DateTime lastPasswordChange;
  final List<SecurityQuestion> securityQuestions;

  const SecuritySettings({
    required this.twoFactorEnabled,
    required this.biometricEnabled,
    required this.deviceLockEnabled,
    required this.sessionTimeout,
    required this.logSecurityEvents,
    required this.emailSecurityAlerts,
    required this.smsSecurityAlerts,
    required this.trustedDevices,
    required this.loginAttempts,
    required this.lastPasswordChange,
    required this.securityQuestions,
  });

  factory SecuritySettings.fromJson(Map<String, dynamic> json) {
    final loginAttemptsMap = <String, DateTime>{};
    if (json['loginAttempts'] != null) {
      (json['loginAttempts'] as Map<String, dynamic>).forEach((key, value) {
        loginAttemptsMap[key] = DateTime.tryParse(value.toString()) ?? DateTime.now();
      });
    }

    List<SecurityQuestion> questions = [];
    if (json['securityQuestions'] != null) {
      questions = (json['securityQuestions'] as List)
          .map((q) => SecurityQuestion.fromJson(q))
          .toList();
    }

    return SecuritySettings(
      twoFactorEnabled: json['twoFactorEnabled'] ?? false,
      biometricEnabled: json['biometricEnabled'] ?? false,
      deviceLockEnabled: json['deviceLockEnabled'] ?? false,
      sessionTimeout: json['sessionTimeout']?.toInt() ?? 30,
      logSecurityEvents: json['logSecurityEvents'] ?? true,
      emailSecurityAlerts: json['emailSecurityAlerts'] ?? true,
      smsSecurityAlerts: json['smsSecurityAlerts'] ?? false,
      trustedDevices: List<String>.from(json['trustedDevices'] ?? []),
      loginAttempts: loginAttemptsMap,
      lastPasswordChange: DateTime.tryParse(json['lastPasswordChange']?.toString() ?? '') ?? DateTime.now(),
      securityQuestions: questions,
    );
  }

  Map<String, dynamic> toJson() {
    final loginAttemptsJson = <String, String>{};
    loginAttempts.forEach((key, value) {
      loginAttemptsJson[key] = value.toIso8601String();
    });

    return {
      'twoFactorEnabled': twoFactorEnabled,
      'biometricEnabled': biometricEnabled,
      'deviceLockEnabled': deviceLockEnabled,
      'sessionTimeout': sessionTimeout,
      'logSecurityEvents': logSecurityEvents,
      'emailSecurityAlerts': emailSecurityAlerts,
      'smsSecurityAlerts': smsSecurityAlerts,
      'trustedDevices': trustedDevices,
      'loginAttempts': loginAttemptsJson,
      'lastPasswordChange': lastPasswordChange.toIso8601String(),
      'securityQuestions': securityQuestions.map((q) => q.toJson()).toList(),
    };
  }
}

class SecurityQuestion {
  final String id;
  final String question;
  final String hashedAnswer;
  final DateTime createdAt;

  const SecurityQuestion({
    required this.id,
    required this.question,
    required this.hashedAnswer,
    required this.createdAt,
  });

  factory SecurityQuestion.fromJson(Map<String, dynamic> json) {
    return SecurityQuestion(
      id: json['id']?.toString() ?? '',
      question: json['question']?.toString() ?? '',
      hashedAnswer: json['hashedAnswer']?.toString() ?? '',
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'hashedAnswer': hashedAnswer,
      'createdAt': createdAt.toIso8601String(),
    };
  }
}

class BlockedUser {
  final String id;
  final String blockedUserId;
  final String blockedUserName;
  final String? blockedUserPhoto;
  final BlockReason reason;
  final String? customReason;
  final DateTime blockedAt;
  final bool reportSubmitted;

  const BlockedUser({
    required this.id,
    required this.blockedUserId,
    required this.blockedUserName,
    this.blockedUserPhoto,
    required this.reason,
    this.customReason,
    required this.blockedAt,
    required this.reportSubmitted,
  });

  factory BlockedUser.fromJson(Map<String, dynamic> json) {
    return BlockedUser(
      id: json['id']?.toString() ?? '',
      blockedUserId: json['blockedUserId']?.toString() ?? '',
      blockedUserName: json['blockedUserName']?.toString() ?? '',
      blockedUserPhoto: json['blockedUserPhoto']?.toString(),
      reason: BlockReason.values.firstWhere(
        (e) => e.name == json['reason']?.toString(),
        orElse: () => BlockReason.other,
      ),
      customReason: json['customReason']?.toString(),
      blockedAt: DateTime.tryParse(json['blockedAt']?.toString() ?? '') ?? DateTime.now(),
      reportSubmitted: json['reportSubmitted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'blockedUserId': blockedUserId,
      'blockedUserName': blockedUserName,
      'blockedUserPhoto': blockedUserPhoto,
      'reason': reason.name,
      'customReason': customReason,
      'blockedAt': blockedAt.toIso8601String(),
      'reportSubmitted': reportSubmitted,
    };
  }
}

enum BlockReason {
  spam,
  harassment,
  inappropriate,
  fake,
  scam,
  other
}

class ReportUser {
  final String id;
  final String reporterId;
  final String reportedUserId;
  final ReportReason reason;
  final String description;
  final List<String> evidence;
  final ReportStatus status;
  final DateTime createdAt;
  final DateTime? resolvedAt;
  final String? adminNotes;

  const ReportUser({
    required this.id,
    required this.reporterId,
    required this.reportedUserId,
    required this.reason,
    required this.description,
    required this.evidence,
    required this.status,
    required this.createdAt,
    this.resolvedAt,
    this.adminNotes,
  });

  factory ReportUser.fromJson(Map<String, dynamic> json) {
    return ReportUser(
      id: json['id']?.toString() ?? '',
      reporterId: json['reporterId']?.toString() ?? '',
      reportedUserId: json['reportedUserId']?.toString() ?? '',
      reason: ReportReason.values.firstWhere(
        (e) => e.name == json['reason']?.toString(),
        orElse: () => ReportReason.other,
      ),
      description: json['description']?.toString() ?? '',
      evidence: List<String>.from(json['evidence'] ?? []),
      status: ReportStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => ReportStatus.pending,
      ),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      resolvedAt: json['resolvedAt'] != null 
          ? DateTime.tryParse(json['resolvedAt'].toString())
          : null,
      adminNotes: json['adminNotes']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reporterId': reporterId,
      'reportedUserId': reportedUserId,
      'reason': reason.name,
      'description': description,
      'evidence': evidence,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'resolvedAt': resolvedAt?.toIso8601String(),
      'adminNotes': adminNotes,
    };
  }
}

enum ReportReason {
  fake,
  harassment,
  inappropriate,
  spam,
  scam,
  underage,
  married,
  other
}

enum ReportStatus {
  pending,
  investigating,
  resolved,
  dismissed
}

class DataPrivacy {
  final bool shareProfileWithMatches;
  final bool shareContactWithPremium;
  final bool allowDataAnalytics;
  final bool allowMarketingEmails;
  final bool allowThirdPartySharing;
  final bool allowLocationTracking;
  final bool allowActivityTracking;
  final DataRetentionPeriod dataRetention;
  final List<String> dataExportRequests;
  final List<String> dataDeletionRequests;

  const DataPrivacy({
    required this.shareProfileWithMatches,
    required this.shareContactWithPremium,
    required this.allowDataAnalytics,
    required this.allowMarketingEmails,
    required this.allowThirdPartySharing,
    required this.allowLocationTracking,
    required this.allowActivityTracking,
    required this.dataRetention,
    required this.dataExportRequests,
    required this.dataDeletionRequests,
  });

  factory DataPrivacy.fromJson(Map<String, dynamic> json) {
    return DataPrivacy(
      shareProfileWithMatches: json['shareProfileWithMatches'] ?? true,
      shareContactWithPremium: json['shareContactWithPremium'] ?? true,
      allowDataAnalytics: json['allowDataAnalytics'] ?? true,
      allowMarketingEmails: json['allowMarketingEmails'] ?? false,
      allowThirdPartySharing: json['allowThirdPartySharing'] ?? false,
      allowLocationTracking: json['allowLocationTracking'] ?? false,
      allowActivityTracking: json['allowActivityTracking'] ?? true,
      dataRetention: DataRetentionPeriod.values.firstWhere(
        (e) => e.name == json['dataRetention']?.toString(),
        orElse: () => DataRetentionPeriod.twoYears,
      ),
      dataExportRequests: List<String>.from(json['dataExportRequests'] ?? []),
      dataDeletionRequests: List<String>.from(json['dataDeletionRequests'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'shareProfileWithMatches': shareProfileWithMatches,
      'shareContactWithPremium': shareContactWithPremium,
      'allowDataAnalytics': allowDataAnalytics,
      'allowMarketingEmails': allowMarketingEmails,
      'allowThirdPartySharing': allowThirdPartySharing,
      'allowLocationTracking': allowLocationTracking,
      'allowActivityTracking': allowActivityTracking,
      'dataRetention': dataRetention.name,
      'dataExportRequests': dataExportRequests,
      'dataDeletionRequests': dataDeletionRequests,
    };
  }
}

enum DataRetentionPeriod {
  oneYear,
  twoYears,
  fiveYears,
  indefinite
}
