-- Migration to add Kundali matching admin settings
-- This allows dynamic control of kundali features via admin panel

-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_settings (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    data_type VARCHAR(50) DEFAULT 'string',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert Kundali matching settings
INSERT INTO admin_settings (key, value, description, category, data_type) VALUES
('kundali_matching_enabled', 'true', 'Enable/disable kundali matching feature globally', 'kundali', 'boolean'),
('kundali_matching_free_promotion', 'false', 'Make kundali matching free for all users (promotional)', 'kundali', 'boolean'),
('kundali_matching_premium_only', 'true', 'Restrict kundali matching to premium users only', 'kundali', 'boolean'),
('kundali_swiss_ephemeris_enabled', 'false', 'Use Swiss Ephemeris for precise calculations', 'kundali', 'boolean'),
('kundali_ml_scoring_enabled', 'true', 'Enable ML-enhanced compatibility scoring', 'kundali', 'boolean'),
('kundali_detailed_analysis_enabled', 'true', 'Enable detailed astrological analysis', 'kundali', 'boolean'),
('kundali_remedies_enabled', 'true', 'Show remedial measures in results', 'kundali', 'boolean'),
('kundali_auspicious_dates_enabled', 'false', 'Include auspicious date suggestions', 'kundali', 'boolean'),
('kundali_pdf_reports_enabled', 'false', 'Enable PDF report generation', 'kundali', 'boolean'),
('kundali_chart_generation_enabled', 'true', 'Enable kundali chart visualization', 'kundali', 'boolean'),
('kundali_max_daily_matches', '10', 'Maximum kundali matches per user per day', 'kundali', 'number'),
('kundali_cache_results_hours', '24', 'Cache kundali results for specified hours', 'kundali', 'number'),
('kundali_minimum_score_threshold', '18', 'Minimum Ashtakoot score for positive recommendation', 'kundali', 'number'),
('kundali_manglik_strict_matching', 'false', 'Require strict Manglik compatibility', 'kundali', 'boolean'),
('kundali_timezone_awareness', 'true', 'Enable timezone-aware calculations', 'kundali', 'boolean')
ON CONFLICT (key) DO UPDATE SET
    value = EXCLUDED.value,
    description = EXCLUDED.description,
    updated_at = CURRENT_TIMESTAMP;

-- Create kundali_match_history table for tracking
CREATE TABLE IF NOT EXISTS kundali_match_history (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    target_user_id INTEGER NOT NULL,
    ashtakoot_score INTEGER,
    overall_compatibility_score DECIMAL(5,2),
    compatibility_level VARCHAR(50),
    is_manglik_compatible BOOLEAN,
    ml_score DECIMAL(5,2),
    calculation_method VARCHAR(100) DEFAULT 'vedic_standard',
    options_used JSONB,
    result_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (target_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_kundali_history_user_id ON kundali_match_history(user_id);
CREATE INDEX IF NOT EXISTS idx_kundali_history_target_user_id ON kundali_match_history(target_user_id);
CREATE INDEX IF NOT EXISTS idx_kundali_history_created_at ON kundali_match_history(created_at);
CREATE INDEX IF NOT EXISTS idx_admin_settings_category ON admin_settings(category);
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(key);

-- Create kundali_user_preferences table
CREATE TABLE IF NOT EXISTS kundali_user_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER UNIQUE NOT NULL,
    preferred_calculation_method VARCHAR(100) DEFAULT 'comprehensive',
    include_remedies BOOLEAN DEFAULT true,
    include_detailed_analysis BOOLEAN DEFAULT true,
    include_ml_scoring BOOLEAN DEFAULT true,
    include_auspicious_dates BOOLEAN DEFAULT false,
    minimum_compatibility_threshold INTEGER DEFAULT 18,
    strict_manglik_matching BOOLEAN DEFAULT false,
    notification_on_high_match BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_admin_settings_updated_at 
    BEFORE UPDATE ON admin_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_kundali_preferences_updated_at 
    BEFORE UPDATE ON kundali_user_preferences 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample kundali user preferences for existing users (optional)
-- This can be run separately if needed
-- INSERT INTO kundali_user_preferences (user_id) 
-- SELECT id FROM users WHERE id NOT IN (SELECT user_id FROM kundali_user_preferences);

COMMENT ON TABLE admin_settings IS 'Global admin settings for controlling application features';
COMMENT ON TABLE kundali_match_history IS 'History of all kundali matching calculations performed';
COMMENT ON TABLE kundali_user_preferences IS 'Individual user preferences for kundali matching';

COMMENT ON COLUMN admin_settings.key IS 'Unique setting identifier';
COMMENT ON COLUMN admin_settings.value IS 'Setting value (stored as text, cast based on data_type)';
COMMENT ON COLUMN admin_settings.data_type IS 'Data type for proper casting (string, boolean, number, json)';
COMMENT ON COLUMN kundali_match_history.calculation_method IS 'Method used for calculation (vedic_standard, swiss_ephemeris, ml_enhanced)';
COMMENT ON COLUMN kundali_match_history.options_used IS 'JSON of options used during calculation';
COMMENT ON COLUMN kundali_match_history.result_data IS 'Complete result data in JSON format';
