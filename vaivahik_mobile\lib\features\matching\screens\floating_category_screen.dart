import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../core/services/matching_service.dart';
import '../../profile/models/profile_model.dart';

/// 🌟 FLOATING CATEGORY SCREENS - World-Class Profile Browsing
/// Features: Swipe Gestures, Filter Overlays, Smooth Animations, Category-wise Browsing

class FloatingCategoryScreen extends ConsumerStatefulWidget {
  final String category;
  final String? filterValue;
  
  const FloatingCategoryScreen({
    super.key,
    required this.category,
    this.filterValue,
  });

  @override
  ConsumerState<FloatingCategoryScreen> createState() => _FloatingCategoryScreenState();
}

class _FloatingCategoryScreenState extends ConsumerState<FloatingCategoryScreen> 
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _cardController;
  late PageController _pageController;
  
  int _currentIndex = 0;
  bool _showFilters = false;
  List<ProfileModel> _profiles = [];
  bool _isLoading = true;

  final Map<String, CategoryConfig> _categoryConfigs = {
    'religion': const CategoryConfig(
      title: 'Browse by Religion',
      icon: Icons.temple_hindu,
      color: Colors.orange,
      filters: ['Hindu', 'Muslim', 'Christian', 'Sikh', 'Buddhist', 'Jain'],
    ),
    'caste': const CategoryConfig(
      title: 'Browse by Caste',
      icon: Icons.family_restroom,
      color: Colors.purple,
      filters: ['Maratha', 'Brahmin', 'Kshatriya', 'Vaishya', 'Other'],
    ),
    'profession': const CategoryConfig(
      title: 'Browse by Profession',
      icon: Icons.work,
      color: Colors.blue,
      filters: ['Engineer', 'Doctor', 'Teacher', 'Business', 'Government', 'Other'],
    ),
    'location': const CategoryConfig(
      title: 'Browse by Location',
      icon: Icons.location_city,
      color: Colors.green,
      filters: ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Other'],
    ),
    'education': const CategoryConfig(
      title: 'Browse by Education',
      icon: Icons.school,
      color: Colors.indigo,
      filters: ['Graduate', 'Post Graduate', 'Professional', 'Doctorate', 'Other'],
    ),
  };

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _pageController = PageController();
    
    _slideController.forward();
    _loadProfiles();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _cardController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadProfiles() async {
    try {
      final matchingService = MatchingService();
      MatchFilters filters = const MatchFilters();

      // Apply category-specific filters using copyWith
      switch (widget.category) {
        case 'religion':
          filters = filters.copyWith(religions: widget.filterValue != null ? [widget.filterValue!] : null);
          break;
        case 'caste':
          filters = filters.copyWith(castes: widget.filterValue != null ? [widget.filterValue!] : null);
          break;
        case 'profession':
          filters = filters.copyWith(occupations: widget.filterValue != null ? [widget.filterValue!] : null);
          break;
        case 'location':
          filters = filters.copyWith(location: widget.filterValue);
          break;
        case 'education':
          filters = filters.copyWith(educations: widget.filterValue != null ? [widget.filterValue!] : null);
          break;
      }

      final matches = await matchingService.getMatches(filters: filters);
      setState(() {
        _profiles = matches.map((match) => match.profile).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _onSwipeLeft() {
    // Pass/Skip profile
    _cardController.forward().then((_) {
      _nextProfile();
      _cardController.reset();
    });
  }

  void _onSwipeRight() {
    // Like profile
    _cardController.forward().then((_) {
      _nextProfile();
      _cardController.reset();
    });
  }

  void _nextProfile() {
    if (_currentIndex < _profiles.length - 1) {
      setState(() {
        _currentIndex++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Load more profiles or show completion
      _loadProfiles();
    }
  }

  @override
  Widget build(BuildContext context) {
    final config = _categoryConfigs[widget.category]!;
    
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.5),
      body: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Center(
            child: GestureDetector(
              onTap: () {}, // Prevent closing when tapping on content
              child: Container(
                margin: const EdgeInsets.all(20),
                height: MediaQuery.of(context).size.height * 0.85,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    _buildHeader(config),
                    Expanded(
                      child: _isLoading 
                          ? _buildLoadingState()
                          : _buildProfileCards(),
                    ),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          ),
        ).animate(controller: _slideController)
         .slideY(begin: 1, curve: Curves.elasticOut)
         .fadeIn(),
    );
  }

  Widget _buildHeader(CategoryConfig config) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [config.color, config.color.withValues(alpha: 0.8)],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              config.icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  config.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.filterValue != null)
                  Text(
                    widget.filterValue!,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => setState(() => _showFilters = !_showFilters),
            icon: const Icon(
              Icons.tune,
              color: Colors.white,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
            ),
          ),
        ],
      ),
    ).animate()
     .slideX(begin: -1, delay: 200.ms)
     .fadeIn(delay: 200.ms);
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Finding perfect matches...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileCards() {
    if (_profiles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No profiles found',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return PageView.builder(
      controller: _pageController,
      itemCount: _profiles.length,
      itemBuilder: (context, index) {
        final profile = _profiles[index];
        return _buildProfileCard(profile, index);
      },
    );
  }

  Widget _buildProfileCard(ProfileModel profile, int index) {
    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            // Profile Image
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.grey[300]!,
                    Colors.grey[400]!,
                  ],
                ),
              ),
              child: profile.hasProfilePhoto
                  ? Image.network(
                      profile.profilePhoto!,
                      fit: BoxFit.cover,
                    )
                  : Icon(
                      Icons.person,
                      size: 100,
                      color: Colors.grey[600],
                    ),
            ),
            
            // Gradient Overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withValues(alpha: 0.7),
                  ],
                ),
              ),
            ),
            
            // Profile Info
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    profile.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${profile.ageDisplay} • ${profile.heightDisplay}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    profile.locationDisplay,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  if (profile.education != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      profile.education!,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    ).animate()
     .scale(delay: (index * 100).ms, curve: Curves.elasticOut)
     .fadeIn(delay: (index * 100).ms);
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            icon: Icons.close,
            color: Colors.red,
            onTap: _onSwipeLeft,
            label: 'Pass',
          ),
          _buildActionButton(
            icon: Icons.star,
            color: Colors.blue,
            onTap: () {
              // Add to shortlist
            },
            label: 'Shortlist',
          ),
          _buildActionButton(
            icon: Icons.favorite,
            color: Colors.green,
            onTap: _onSwipeRight,
            label: 'Like',
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 1, delay: 400.ms)
     .fadeIn(delay: 400.ms);
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required String label,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class CategoryConfig {
  final String title;
  final IconData icon;
  final Color color;
  final List<String> filters;

  const CategoryConfig({
    required this.title,
    required this.icon,
    required this.color,
    required this.filters,
  });
}
