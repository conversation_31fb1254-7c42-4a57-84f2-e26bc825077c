/**
 * Simple Kundali System Test
 */

console.log('🔮 Testing Kundali System...\n');

try {
  // Test 1: Load Vedic Astrology Service
  console.log('1. Loading Vedic Astrology Service...');
  const VedicAstrologyService = require('../src/services/vedicAstrology.service');
  const vedicService = new VedicAstrologyService();
  console.log('   ✅ Vedic Astrology Service loaded successfully');

  // Test 2: Test Nakshatra calculation
  console.log('2. Testing Nakshatra calculation...');
  const nakshatra = vedicService.getNakshatraFromLongitude(45);
  console.log(`   Result: ${nakshatra.name} (Lord: ${nakshatra.lord})`);
  console.log('   ✅ Nakshatra calculation working');

  // Test 3: Test Rashi calculation
  console.log('3. Testing Rashi calculation...');
  const rashi = vedicService.getRashiFromLongitude(45);
  console.log(`   Result: ${rashi.name} (Lord: ${rashi.lord})`);
  console.log('   ✅ Rashi calculation working');

  // Test 4: Load Manglik Dosha Service
  console.log('4. Loading Manglik Dosha Service...');
  const ManglikDoshaService = require('../src/services/manglikDosha.service');
  const manglikService = new ManglikDoshaService();
  console.log('   ✅ Manglik Dosha Service loaded successfully');

  // Test 5: Load Comprehensive Kundali Service
  console.log('5. Loading Comprehensive Kundali Service...');
  const ComprehensiveKundaliService = require('../src/services/comprehensiveKundali.service');
  const kundaliService = new ComprehensiveKundaliService();
  console.log('   ✅ Comprehensive Kundali Service loaded successfully');

  // Test 6: Test birth data validation
  console.log('6. Testing birth data validation...');
  const validData = {
    birthDate: '1990-05-15',
    birthTime: '14:30',
    birthPlace: 'Mumbai, India'
  };
  const validation = kundaliService.validateBirthData(validData);
  console.log(`   Valid data result: ${validation.isValid}`);
  console.log('   ✅ Birth data validation working');

  // Test 7: Test birth chart generation
  console.log('7. Testing birth chart generation...');
  const chart = vedicService.calculateBirthChart('1990-05-15', '14:30', 'Mumbai, India');
  console.log(`   Chart generated with Moon Sign: ${chart.moonSign.name}`);
  console.log(`   Nakshatra: ${chart.nakshatra.name}`);
  console.log('   ✅ Birth chart generation working');

  // Test 8: Test Ashtakoot calculation
  console.log('8. Testing Ashtakoot calculation...');
  const chart1 = {
    rashi: { name: 'Aries', index: 0 },
    nakshatra: { name: 'Ashwini', index: 0, lord: 'Ketu', gana: 'Deva', yoni: 'Horse', varna: 'Vaishya' }
  };
  const chart2 = {
    rashi: { name: 'Leo', index: 4 },
    nakshatra: { name: 'Magha', index: 9, lord: 'Ketu', gana: 'Rakshasa', yoni: 'Rat', varna: 'Shudra' }
  };

  const gunaResult = vedicService.calculateAshtakootGuna(chart1, chart2);
  console.log(`   Total Score: ${gunaResult.totalScore}/${gunaResult.maxScore} (${gunaResult.percentage}%)`);
  console.log('   ✅ Ashtakoot calculation working');

  // Test 9: Test Manglik detection
  console.log('9. Testing Manglik dosha detection...');
  const doshaResult = manglikService.detectManglikDosha('1990-05-15', '14:30', 'Mumbai, India');
  console.log(`   Manglik Status: ${doshaResult.isManglik ? 'Yes' : 'No'}`);
  console.log(`   Intensity: ${doshaResult.intensity}`);
  console.log('   ✅ Manglik dosha detection working');

  console.log('\n🎉 All basic tests passed! Kundali system is functional.');
  console.log('\n📋 System Status:');
  console.log('   ✅ Vedic Astrology Service: Working');
  console.log('   ✅ Manglik Dosha Service: Working');
  console.log('   ✅ Comprehensive Kundali Service: Working');
  console.log('   ✅ Birth Chart Generation: Working');
  console.log('   ✅ Ashtakoot Calculations: Working');
  console.log('   ✅ Manglik Detection: Working');

} catch (error) {
  console.error('❌ Error during testing:', error.message);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
