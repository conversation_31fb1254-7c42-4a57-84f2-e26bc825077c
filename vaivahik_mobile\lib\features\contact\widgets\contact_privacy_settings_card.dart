import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../models/contact_models.dart';

class ContactPrivacySettingsCard extends StatefulWidget {
  final ContactPrivacySettings settings;
  final Function(ContactPrivacySettings) onUpdate;

  const ContactPrivacySettingsCard({
    super.key,
    required this.settings,
    required this.onUpdate,
  });

  @override
  State<ContactPrivacySettingsCard> createState() => _ContactPrivacySettingsCardState();
}

class _ContactPrivacySettingsCardState extends State<ContactPrivacySettingsCard> {
  late ContactPrivacySettings _currentSettings;
  bool _isUpdating = false;

  @override
  void initState() {
    super.initState();
    _currentSettings = widget.settings;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.privacy_tip, color: AppColors.primary, size: 24),
                const SizedBox(width: 12),
                Text(
                  'Privacy Settings',
                  style: AppTextStyles.h3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            
            // Allow Direct Calls
            _buildSwitchTile(
              title: 'Allow Direct Calls',
              subtitle: 'Others can call you when contact is revealed',
              value: _currentSettings.allowDirectCalls,
              onChanged: (value) {
                setState(() {
                  _currentSettings = _currentSettings.copyWith(allowDirectCalls: value);
                });
                _updateSettings();
              },
            ),
            
            const SizedBox(height: 16),
            
            // Contact Reveal Preference
            _buildDropdownTile(
              title: 'Contact Reveal Preference',
              subtitle: 'Who can access your contact information',
              value: _currentSettings.contactRevealPreference,
              items: ContactRevealPreference.values.map((pref) => 
                DropdownMenuItem(
                  value: pref.value,
                  child: Row(
                    children: [
                      Text(pref.icon, style: const TextStyle(fontSize: 16)),
                      const SizedBox(width: 8),
                      Text(pref.label),
                    ],
                  ),
                ),
              ).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _currentSettings = _currentSettings.copyWith(contactRevealPreference: value);
                  });
                  _updateSettings();
                }
              },
            ),
            
            const SizedBox(height: 16),
            
            // Require Mutual Interest
            _buildSwitchTile(
              title: 'Require Mutual Interest',
              subtitle: 'Both users must show interest before contact reveal',
              value: _currentSettings.requireMutualInterest,
              onChanged: (value) {
                setState(() {
                  _currentSettings = _currentSettings.copyWith(requireMutualInterest: value);
                });
                _updateSettings();
              },
            ),
            
            const SizedBox(height: 16),
            
            // Call Availability
            _buildDropdownTile(
              title: 'Call Availability',
              subtitle: 'When you prefer to receive calls',
              value: _currentSettings.callAvailability,
              items: CallAvailability.values.map((availability) => 
                DropdownMenuItem(
                  value: availability.value,
                  child: Row(
                    children: [
                      Text(availability.icon, style: const TextStyle(fontSize: 16)),
                      const SizedBox(width: 8),
                      Text(availability.label),
                    ],
                  ),
                ),
              ).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _currentSettings = _currentSettings.copyWith(callAvailability: value);
                  });
                  _updateSettings();
                }
              },
            ),
            
            if (_isUpdating) ...[
              const SizedBox(height: 16),
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: AppTextStyles.bodySmall.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: value,
            items: items,
            onChanged: onChanged,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppColors.primary),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }

  void _updateSettings() async {
    setState(() {
      _isUpdating = true;
    });

    try {
      await widget.onUpdate(_currentSettings);
    } catch (e) {
      // Handle error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUpdating = false;
        });
      }
    }
  }
}
