/**
 * Birthday Service
 * 
 * Handles birthday detection, AI-powered birthday wishes generation,
 * and birthday celebration features for the matrimony platform.
 */

const { PrismaClient } = require('@prisma/client');
const notificationService = require('./notification.service');
const emailService = require('./email.service');

class BirthdayService {
  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * Check if today is user's birthday
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - True if today is user's birthday
   */
  async isTodayUserBirthday(userId) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true }
      });

      if (!user?.profile?.dateOfBirth) {
        return false;
      }

      const today = new Date();
      const birthDate = new Date(user.profile.dateOfBirth);

      return (
        today.getMonth() === birthDate.getMonth() &&
        today.getDate() === birthDate.getDate()
      );
    } catch (error) {
      console.error('Error checking birthday:', error);
      return false;
    }
  }

  /**
   * Generate AI-powered birthday wishes
   * @param {Object} user - User object with profile
   * @returns {Promise<Object>} - Birthday wishes data
   */
  async generateBirthdayWishes(user) {
    try {
      const age = this.calculateAge(user.profile.dateOfBirth);
      const name = user.profile.fullName || 'Dear User';
      const gender = user.profile.gender || 'USER';

      // AI-generated birthday wishes templates
      const wishesTemplates = [
        {
          title: `🎉 Happy Birthday, ${name}! 🎂`,
          message: `Wishing you a year filled with love, laughter, and beautiful connections. May this new chapter of your life bring you closer to finding your perfect match!`,
          emoji: '🎈',
          color: '#FF6B6B'
        },
        {
          title: `🌟 Another Year of Possibilities! 🎊`,
          message: `Happy ${age}th Birthday! May this year open new doors to meaningful relationships and bring you the happiness you deserve.`,
          emoji: '✨',
          color: '#4ECDC4'
        },
        {
          title: `🎂 Celebrating You Today! 🎉`,
          message: `On your special day, we celebrate the wonderful person you are. Here's to finding love, joy, and your perfect life partner!`,
          emoji: '💝',
          color: '#45B7D1'
        },
        {
          title: `🎈 Birthday Blessings! 🌸`,
          message: `May your birthday be as special as you are! Wishing you love, prosperity, and the joy of finding your soulmate.`,
          emoji: '🌺',
          color: '#96CEB4'
        }
      ];

      // Select random wish
      const randomWish = wishesTemplates[Math.floor(Math.random() * wishesTemplates.length)];

      // Add personalized elements based on profile
      const personalizedElements = this.getPersonalizedElements(user);

      return {
        ...randomWish,
        age,
        personalizedElements,
        specialOffers: this.getBirthdaySpecialOffers(),
        zodiacMessage: this.getZodiacBirthdayMessage(user.profile.dateOfBirth)
      };
    } catch (error) {
      console.error('Error generating birthday wishes:', error);
      return this.getDefaultBirthdayWish(user);
    }
  }

  /**
   * Get personalized elements based on user profile
   * @param {Object} user - User object
   * @returns {Object} - Personalized elements
   */
  getPersonalizedElements(user) {
    const elements = {
      achievements: [],
      suggestions: [],
      milestones: []
    };

    // Add achievements based on profile completion
    if (user.profile.photos && user.profile.photos.length > 0) {
      elements.achievements.push('Profile photos uploaded');
    }
    
    if (user.isVerified) {
      elements.achievements.push('Verified profile');
    }

    // Add suggestions
    elements.suggestions.push('Complete your profile for better matches');
    elements.suggestions.push('Browse new profiles today');
    elements.suggestions.push('Send interests to potential matches');

    return elements;
  }

  /**
   * Get birthday special offers
   * @returns {Array} - Array of special offers
   */
  getBirthdaySpecialOffers() {
    return [
      {
        title: '🎁 Birthday Special: 50% Off Premium',
        description: 'Unlock unlimited messaging and advanced features',
        code: 'BIRTHDAY50',
        validUntil: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
      },
      {
        title: '💎 Free Profile Boost',
        description: 'Get your profile featured for 24 hours',
        code: 'BIRTHDAYBOOST',
        validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      }
    ];
  }

  /**
   * Get zodiac-based birthday message
   * @param {Date} dateOfBirth - Date of birth
   * @returns {string} - Zodiac birthday message
   */
  getZodiacBirthdayMessage(dateOfBirth) {
    const zodiacSigns = {
      'Aries': 'Your adventurous spirit will lead you to exciting connections!',
      'Taurus': 'Your loyalty and stability make you an ideal life partner!',
      'Gemini': 'Your charm and communication skills will attract the right person!',
      'Cancer': 'Your caring nature will find its perfect match!',
      'Leo': 'Your confidence and warmth will shine bright in love!',
      'Virgo': 'Your attention to detail will help you find true compatibility!',
      'Libra': 'Your sense of balance will create harmonious relationships!',
      'Scorpio': 'Your passion and intensity will create deep connections!',
      'Sagittarius': 'Your optimism will attract positive relationships!',
      'Capricorn': 'Your determination will lead you to lasting love!',
      'Aquarius': 'Your uniqueness will attract someone special!',
      'Pisces': 'Your intuition will guide you to your soulmate!'
    };

    const zodiac = this.getZodiacSign(dateOfBirth);
    return zodiacSigns[zodiac] || 'The stars align for love in your life!';
  }

  /**
   * Get zodiac sign from date of birth
   * @param {Date} dateOfBirth - Date of birth
   * @returns {string} - Zodiac sign
   */
  getZodiacSign(dateOfBirth) {
    const month = dateOfBirth.getMonth() + 1;
    const day = dateOfBirth.getDate();

    if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'Aries';
    if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'Taurus';
    if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'Gemini';
    if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'Cancer';
    if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'Leo';
    if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'Virgo';
    if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'Libra';
    if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'Scorpio';
    if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'Sagittarius';
    if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'Capricorn';
    if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'Aquarius';
    return 'Pisces';
  }

  /**
   * Calculate age from date of birth
   * @param {Date} dateOfBirth - Date of birth
   * @returns {number} - Age in years
   */
  calculateAge(dateOfBirth) {
    const today = new Date();
    const birth = new Date(dateOfBirth);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }

    return age;
  }

  /**
   * Get default birthday wish
   * @param {Object} user - User object
   * @returns {Object} - Default birthday wish
   */
  getDefaultBirthdayWish(user) {
    const name = user.profile?.fullName || 'Dear User';
    return {
      title: `🎉 Happy Birthday, ${name}! 🎂`,
      message: 'Wishing you a wonderful birthday filled with joy, love, and beautiful moments!',
      emoji: '🎈',
      color: '#FF6B6B',
      age: null,
      personalizedElements: { achievements: [], suggestions: [], milestones: [] },
      specialOffers: this.getBirthdaySpecialOffers(),
      zodiacMessage: 'May this year bring you happiness and love!'
    };
  }

  /**
   * Send birthday notifications
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Success status
   */
  async sendBirthdayNotifications(userId) {
    try {
      const user = await this.prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true, photos: true }
      });

      if (!user) return false;

      const birthdayWishes = await this.generateBirthdayWishes(user);

      // Send in-app notification
      await notificationService.createNotification({
        userId: userId,
        title: birthdayWishes.title,
        message: birthdayWishes.message,
        type: 'BIRTHDAY_WISHES',
        data: birthdayWishes
      });

      // Send email if available
      if (user.email) {
        await emailService.sendEmail({
          to: user.email,
          subject: birthdayWishes.title,
          template: 'birthday-wishes',
          data: {
            name: user.profile?.fullName || 'User',
            wishes: birthdayWishes,
            loginUrl: `${process.env.FRONTEND_URL}/dashboard`
          }
        });
      }

      return true;
    } catch (error) {
      console.error('Error sending birthday notifications:', error);
      return false;
    }
  }

  /**
   * Get users with birthdays today
   * @returns {Promise<Array>} - Array of users with birthdays today
   */
  async getTodaysBirthdayUsers() {
    try {
      const today = new Date();
      const month = today.getMonth() + 1;
      const day = today.getDate();

      const users = await this.prisma.user.findMany({
        where: {
          profile: {
            dateOfBirth: {
              not: null
            }
          }
        },
        include: {
          profile: true
        }
      });

      return users.filter(user => {
        if (!user.profile?.dateOfBirth) return false;
        const birthDate = new Date(user.profile.dateOfBirth);
        return birthDate.getMonth() + 1 === month && birthDate.getDate() === day;
      });
    } catch (error) {
      console.error('Error getting birthday users:', error);
      return [];
    }
  }
}

module.exports = new BirthdayService();
