import 'dart:async';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/api/api_client.dart';
import '../models/contact_models.dart';

class ContactService {
  final ApiClient _apiClient;
  
  ContactService() : _apiClient = ApiClient();

  /// Request contact reveal for a specific user
  Future<ContactRevealResponse> requestContactReveal(String targetUserId, {String? reason}) async {
    try {
      final request = ContactRevealRequest(
        targetUserId: targetUserId,
        reason: reason,
        platform: 'mobile',
      );

      final response = await _apiClient.post('/contact/reveal', request.toJson());

      if (response['success'] == true) {
        return ContactRevealResponse.fromJson(response);
      } else {
        return ContactRevealResponse(
          success: false,
          error: response['error'] ?? 'UNKNOWN_ERROR',
          message: response['message'] ?? 'Failed to reveal contact',
          upgradeRequired: response['upgradeRequired'] ?? false,
        );
      }
    } catch (e) {
      return ContactRevealResponse(
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Unable to process contact reveal request: $e',
      );
    }
  }

  /// Get contact privacy settings
  Future<ContactPrivacySettings?> getContactPrivacySettings() async {
    try {
      final response = await _apiClient.get('/contact/privacy-settings');

      if (response['success'] == true && response['settings'] != null) {
        return ContactPrivacySettings.fromJson(response['settings']);
      }
      return null;
    } catch (e) {
      print('Error fetching contact privacy settings: $e');
      return null;
    }
  }

  /// Update contact privacy settings
  Future<bool> updateContactPrivacySettings(ContactPrivacySettings settings) async {
    try {
      final response = await _apiClient.put('/contact/privacy-settings', settings.toJson());
      return response['success'] == true;
    } catch (e) {
      print('Error updating contact privacy settings: $e');
      return false;
    }
  }

  /// Get available contact reveal options
  Future<List<ContactRevealOption>> getContactRevealOptions() async {
    try {
      final response = await _apiClient.get('/contact/options');

      if (response['success'] == true && response['contactRevealPreferences'] != null) {
        final List<dynamic> options = response['contactRevealPreferences'];
        return options.map((option) => ContactRevealOption.fromJson(option)).toList();
      }
      return [];
    } catch (e) {
      print('Error fetching contact reveal options: $e');
      return [];
    }
  }

  /// Get contact access history
  Future<List<ContactAccessHistory>> getContactAccessHistory({int limit = 50}) async {
    try {
      final response = await _apiClient.get('/contact/history?limit=$limit');

      if (response['success'] == true && response['history'] != null) {
        final List<dynamic> history = response['history'];
        return history.map((item) => ContactAccessHistory.fromJson(item)).toList();
      }
      return [];
    } catch (e) {
      print('Error fetching contact access history: $e');
      return [];
    }
  }

  /// Get contact reveal statistics
  Future<ContactRevealStats?> getContactRevealStats() async {
    try {
      final response = await _apiClient.get('/contact/stats');

      if (response['success'] == true && response['stats'] != null) {
        return ContactRevealStats.fromJson(response['stats']);
      }
      return null;
    } catch (e) {
      print('Error fetching contact reveal stats: $e');
      return null;
    }
  }

  /// Make a phone call using native dialer
  Future<bool> makePhoneCall(String phoneNumber, {String? contactName}) async {
    try {
      // Clean phone number (remove spaces, dashes, etc.)
      final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
      
      final Uri phoneUri = Uri(scheme: 'tel', path: cleanNumber);
      
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
        
        // Log the call attempt
        await _logCallAttempt(phoneNumber, contactName);
        
        return true;
      } else {
        throw Exception('Cannot launch phone dialer');
      }
    } catch (e) {
      print('Error making phone call: $e');
      return false;
    }
  }

  /// Log call attempt for analytics
  Future<void> _logCallAttempt(String phoneNumber, String? contactName) async {
    try {
      await _apiClient.post('/contact/log-call', {
        'phoneNumber': phoneNumber,
        'contactName': contactName,
        'platform': 'mobile',
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error logging call attempt: $e');
    }
  }

  /// Check if user can access contact for a specific profile
  Future<bool> canAccessContact(String targetUserId) async {
    try {
      final response = await _apiClient.get('/contact/can-access/$targetUserId');
      return response['canAccess'] == true;
    } catch (e) {
      print('Error checking contact access: $e');
      return false;
    }
  }

  /// Get security message for contact reveal
  Future<String> getSecurityMessage() async {
    try {
      final response = await _apiClient.get('/contact/security-message');
      return response['message'] ?? 'Contact information is protected by our privacy policy.';
    } catch (e) {
      return 'Contact information is protected by our privacy policy.';
    }
  }

  /// Generate WhatsApp URL for contact
  String generateWhatsAppUrl(String phoneNumber, {String? message}) {
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    final encodedMessage = message != null ? Uri.encodeComponent(message) : '';
    return 'https://wa.me/$cleanNumber${encodedMessage.isNotEmpty ? '?text=$encodedMessage' : ''}';
  }

  /// Launch WhatsApp chat
  Future<bool> launchWhatsApp(String phoneNumber, {String? message}) async {
    try {
      final whatsappUrl = generateWhatsAppUrl(phoneNumber, message: message);
      final Uri uri = Uri.parse(whatsappUrl);
      
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        return true;
      }
      return false;
    } catch (e) {
      print('Error launching WhatsApp: $e');
      return false;
    }
  }

  /// Dispose resources
  void dispose() {
    // Clean up any resources if needed
  }
}
