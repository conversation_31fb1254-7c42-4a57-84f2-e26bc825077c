/**
 * Birthday Service
 * 
 * Frontend service for handling birthday-related API calls and functionality.
 */

import { API_BASE_URL } from '../config/api';

class BirthdayService {
  /**
   * Check if today is user's birthday and get birthday wishes
   * @returns {Promise<Object>} Birthday check response
   */
  async checkBirthday() {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_BASE_URL}/users/birthday/check`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error checking birthday:', error);
      throw error;
    }
  }

  /**
   * Get birthday wishes for user (can be called anytime for testing)
   * @returns {Promise<Object>} Birthday wishes response
   */
  async getBirthdayWishes() {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_BASE_URL}/users/birthday/wishes`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting birthday wishes:', error);
      throw error;
    }
  }

  /**
   * Send birthday notification to user
   * @returns {Promise<Object>} Notification response
   */
  async sendBirthdayNotification() {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_BASE_URL}/users/birthday/notify`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error sending birthday notification:', error);
      throw error;
    }
  }

  /**
   * Check if birthday wishes should be shown
   * This checks localStorage to avoid showing wishes multiple times per day
   * @returns {boolean} Whether to show birthday wishes
   */
  shouldShowBirthdayWishes() {
    const today = new Date().toDateString();
    const lastShown = localStorage.getItem('birthdayWishesShown');
    
    return lastShown !== today;
  }

  /**
   * Mark birthday wishes as shown for today
   */
  markBirthdayWishesShown() {
    const today = new Date().toDateString();
    localStorage.setItem('birthdayWishesShown', today);
  }

  /**
   * Get birthday celebration animation preferences
   * @returns {Object} Animation preferences
   */
  getAnimationPreferences() {
    const preferences = localStorage.getItem('birthdayAnimationPreferences');
    return preferences ? JSON.parse(preferences) : {
      enableConfetti: true,
      enableSound: false, // Default to false to avoid auto-play issues
      animationDuration: 3000
    };
  }

  /**
   * Save birthday celebration animation preferences
   * @param {Object} preferences Animation preferences
   */
  saveAnimationPreferences(preferences) {
    localStorage.setItem('birthdayAnimationPreferences', JSON.stringify(preferences));
  }

  /**
   * Format birthday wishes for display
   * @param {Object} birthdayData Raw birthday data from API
   * @returns {Object} Formatted birthday data
   */
  formatBirthdayWishes(birthdayData) {
    if (!birthdayData?.birthdayWishes) {
      return null;
    }

    const wishes = birthdayData.birthdayWishes;
    
    return {
      ...wishes,
      formattedAge: wishes.age ? `${wishes.age} years young` : null,
      hasSpecialOffers: wishes.specialOffers && wishes.specialOffers.length > 0,
      validOffers: wishes.specialOffers?.filter(offer => 
        new Date(offer.validUntil) > new Date()
      ) || []
    };
  }

  /**
   * Get birthday statistics for admin
   * @returns {Promise<Object>} Birthday statistics
   */
  async getBirthdayStats() {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_BASE_URL}/admin/birthday/stats`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting birthday stats:', error);
      throw error;
    }
  }

  /**
   * Get users with birthdays today (Admin only)
   * @returns {Promise<Object>} Today's birthday users
   */
  async getTodaysBirthdays() {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_BASE_URL}/admin/birthday/today`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting today\'s birthdays:', error);
      throw error;
    }
  }

  /**
   * Send birthday notifications to all users with birthdays today (Admin only)
   * @returns {Promise<Object>} Notification results
   */
  async notifyAllBirthdayUsers() {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_BASE_URL}/admin/birthday/notify-all`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error notifying all birthday users:', error);
      throw error;
    }
  }
}

export default new BirthdayService();
