import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/feature_access_service.dart';

import 'auth_provider.dart';

// Feature access state
class FeatureAccessState {
  final FeatureUsage? usage;
  final FeatureLimits? limits;
  final bool isLoading;
  final String? error;

  const FeatureAccessState({
    this.usage,
    this.limits,
    this.isLoading = false,
    this.error,
  });

  FeatureAccessState copyWith({
    FeatureUsage? usage,
    FeatureLimits? limits,
    bool? isLoading,
    String? error,
  }) {
    return FeatureAccessState(
      usage: usage ?? this.usage,
      limits: limits ?? this.limits,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Feature access notifier
class FeatureAccessNotifier extends StateNotifier<FeatureAccessState> {
  final FeatureAccessService _featureAccessService;
  final Ref _ref;

  FeatureAccessNotifier(this._featureAccessService, this._ref) 
      : super(const FeatureAccessState()) {
    _loadFeatureData();
  }

  Future<void> _loadFeatureData() async {
    final authState = _ref.read(authProvider);
    final user = authState.user;
    
    if (user == null) return;

    state = state.copyWith(isLoading: true);

    try {
      final usage = await _featureAccessService.getFeatureUsage();
      final limits = _featureAccessService.getFeatureLimits(user);

      state = state.copyWith(
        usage: usage,
        limits: limits,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  // Check feature access
  Future<FeatureAccessResult> checkAccess(FeatureType feature) async {
    final authState = _ref.read(authProvider);
    final user = authState.user;
    
    if (user == null) {
      return FeatureAccessResult.denied(
        reason: 'Please login to access this feature',
        requiredLevel: AccessLevel.basic,
      );
    }

    return await _featureAccessService.checkFeatureAccess(feature, user);
  }

  // Track feature usage
  Future<void> trackUsage(FeatureType feature) async {
    await _featureAccessService.trackFeatureUsage(feature);
    // Refresh usage data
    await _loadFeatureData();
  }

  // Refresh feature data
  Future<void> refresh() async {
    await _loadFeatureData();
  }

  // Get upgrade message for a feature
  String getUpgradeMessage(FeatureType feature) {
    return _featureAccessService.getUpgradeMessage(feature);
  }

  // Check if user has premium access
  bool get hasPremiumAccess {
    final authState = _ref.read(authProvider);
    final user = authState.user;
    return user?.isPremium ?? false;
  }

  // Check if user is verified
  bool get isVerified {
    final authState = _ref.read(authProvider);
    final user = authState.user;
    return user?.isVerified ?? false;
  }

  // Get remaining usage for a feature
  int getRemainingUsage(FeatureType feature) {
    final usage = state.usage;
    final limits = state.limits;
    
    if (usage == null || limits == null) return 0;

    switch (feature) {
      case FeatureType.profileView:
        if (limits.profileViewsPerDay == -1) return -1; // Unlimited
        return (limits.profileViewsPerDay - usage.profileViewsToday).clamp(0, limits.profileViewsPerDay);
        
      case FeatureType.sendInterest:
        if (limits.interestsPerDay == -1) return -1; // Unlimited
        return (limits.interestsPerDay - usage.interestsToday).clamp(0, limits.interestsPerDay);
        
      case FeatureType.chatWithMatches:
        if (limits.chatMessagesPerDay == -1) return -1; // Unlimited
        return (limits.chatMessagesPerDay - usage.chatMessagesToday).clamp(0, limits.chatMessagesPerDay);
        
      default:
        return -1; // No limit for other features
    }
  }

  // Get usage percentage for a feature
  double getUsagePercentage(FeatureType feature) {
    final usage = state.usage;
    final limits = state.limits;
    
    if (usage == null || limits == null) return 0.0;

    switch (feature) {
      case FeatureType.profileView:
        if (limits.profileViewsPerDay == -1) return 0.0; // Unlimited
        return (usage.profileViewsToday / limits.profileViewsPerDay).clamp(0.0, 1.0);
        
      case FeatureType.sendInterest:
        if (limits.interestsPerDay == -1) return 0.0; // Unlimited
        return (usage.interestsToday / limits.interestsPerDay).clamp(0.0, 1.0);
        
      case FeatureType.chatWithMatches:
        if (limits.chatMessagesPerDay == -1) return 0.0; // Unlimited
        return (usage.chatMessagesToday / limits.chatMessagesPerDay).clamp(0.0, 1.0);
        
      default:
        return 0.0;
    }
  }
}

// Providers
final featureAccessServiceProvider = Provider<FeatureAccessService>((ref) {
  return FeatureAccessService();
});

final featureAccessProvider = StateNotifierProvider<FeatureAccessNotifier, FeatureAccessState>((ref) {
  final service = ref.watch(featureAccessServiceProvider);
  return FeatureAccessNotifier(service, ref);
});

// Convenience providers for common checks
final hasPremiumAccessProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.isPremium ?? false;
});

final isVerifiedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user?.isVerified ?? false;
});

// Feature access check provider
final featureAccessCheckProvider = FutureProvider.family<FeatureAccessResult, FeatureType>((ref, feature) async {
  final notifier = ref.read(featureAccessProvider.notifier);
  return await notifier.checkAccess(feature);
});
