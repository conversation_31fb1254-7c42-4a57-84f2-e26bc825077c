import 'package:flutter/foundation.dart';
import '../models/contact_models.dart';
import '../services/contact_service.dart';

class ContactProvider extends ChangeNotifier {
  final ContactService _contactService = ContactService();

  // State variables
  bool _isLoading = false;
  String? _error;
  ContactPrivacySettings? _privacySettings;
  List<ContactRevealOption> _revealOptions = [];
  List<ContactAccessHistory> _accessHistory = [];
  ContactRevealStats? _stats;
  final Map<String, ContactRevealResponse> _revealCache = {};

  // Getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  ContactPrivacySettings? get privacySettings => _privacySettings;
  List<ContactRevealOption> get revealOptions => _revealOptions;
  List<ContactAccessHistory> get accessHistory => _accessHistory;
  ContactRevealStats? get stats => _stats;

  /// Initialize contact provider
  Future<void> initialize() async {
    await Future.wait([
      loadPrivacySettings(),
      loadRevealOptions(),
      loadAccessHistory(),
      loadStats(),
    ]);
  }

  /// Request contact reveal for a user
  Future<ContactRevealResponse> requestContactReveal(String targetUserId, {String? reason}) async {
    _setLoading(true);
    _clearError();

    try {
      // Check cache first
      final cacheKey = '$targetUserId-$reason';
      if (_revealCache.containsKey(cacheKey)) {
        final cachedResponse = _revealCache[cacheKey]!;
        if (cachedResponse.success) {
          return cachedResponse;
        }
      }

      final response = await _contactService.requestContactReveal(targetUserId, reason: reason);
      
      // Cache successful responses
      if (response.success) {
        _revealCache[cacheKey] = response;
      }

      // Refresh history after successful reveal
      if (response.success) {
        await loadAccessHistory();
      }

      return response;
    } catch (e) {
      _setError('Failed to reveal contact: $e');
      return const ContactRevealResponse(
        success: false,
        error: 'NETWORK_ERROR',
        message: 'Unable to process request',
      );
    } finally {
      _setLoading(false);
    }
  }

  /// Load contact privacy settings
  Future<void> loadPrivacySettings() async {
    try {
      _privacySettings = await _contactService.getContactPrivacySettings();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load privacy settings: $e');
    }
  }

  /// Update contact privacy settings
  Future<bool> updatePrivacySettings(ContactPrivacySettings settings) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await _contactService.updateContactPrivacySettings(settings);
      if (success) {
        _privacySettings = settings;
        notifyListeners();
      } else {
        _setError('Failed to update privacy settings');
      }
      return success;
    } catch (e) {
      _setError('Failed to update privacy settings: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Load contact reveal options
  Future<void> loadRevealOptions() async {
    try {
      _revealOptions = await _contactService.getContactRevealOptions();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load reveal options: $e');
    }
  }

  /// Load contact access history
  Future<void> loadAccessHistory({int limit = 50}) async {
    try {
      _accessHistory = await _contactService.getContactAccessHistory(limit: limit);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load access history: $e');
    }
  }

  /// Load contact reveal statistics
  Future<void> loadStats() async {
    try {
      _stats = await _contactService.getContactRevealStats();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load statistics: $e');
    }
  }

  /// Make phone call
  Future<bool> makePhoneCall(String phoneNumber, {String? contactName}) async {
    try {
      final success = await _contactService.makePhoneCall(phoneNumber, contactName: contactName);
      if (success) {
        // Refresh history after call
        await loadAccessHistory();
      }
      return success;
    } catch (e) {
      _setError('Failed to make phone call: $e');
      return false;
    }
  }

  /// Launch WhatsApp
  Future<bool> launchWhatsApp(String phoneNumber, {String? message}) async {
    try {
      return await _contactService.launchWhatsApp(phoneNumber, message: message);
    } catch (e) {
      _setError('Failed to launch WhatsApp: $e');
      return false;
    }
  }

  /// Check if user can access contact
  Future<bool> canAccessContact(String targetUserId) async {
    try {
      return await _contactService.canAccessContact(targetUserId);
    } catch (e) {
      _setError('Failed to check contact access: $e');
      return false;
    }
  }

  /// Get security message
  Future<String> getSecurityMessage() async {
    try {
      return await _contactService.getSecurityMessage();
    } catch (e) {
      return 'Contact information is protected by our privacy policy.';
    }
  }

  /// Clear reveal cache
  void clearRevealCache() {
    _revealCache.clear();
    notifyListeners();
  }

  /// Refresh all data
  Future<void> refresh() async {
    clearRevealCache();
    await initialize();
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _contactService.dispose();
    super.dispose();
  }
}
