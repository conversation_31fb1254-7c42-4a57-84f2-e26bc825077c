import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../models/referral_model.dart';
import '../providers/referral_provider.dart';

/// 📊 Referral Stats Widget - Shows referral statistics
/// Displays total referrals, success rate, and earnings

class ReferralStatsWidget extends ConsumerWidget {
  final ReferralData referralData;

  const ReferralStatsWidget({
    super.key,
    required this.referralData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final statsCalculator = ref.read(referralStatsProvider);
    final stats = referralData.stats;
    final successRate = statsCalculator.calculateSuccessRate(stats);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          const Row(
            children: [
              Icon(
                Icons.analytics,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              SizedBox(width: 12),
              Text(
                'Your Referral Stats',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ).animate().fadeIn(),
          
          const SizedBox(height: 20),
          
          // Stats Grid
          Row(
            children: [
              Expanded(
                child: _StatCard(
                  title: 'Total Referrals',
                  value: stats.totalReferrals.toString(),
                  icon: Icons.people,
                  color: const Color(0xFF667eea),
                ).animate().fadeIn(delay: const Duration(milliseconds: 100)),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _StatCard(
                  title: 'Successful',
                  value: stats.successfulReferrals.toString(),
                  icon: Icons.check_circle,
                  color: const Color(0xFF4CAF50),
                ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _StatCard(
                  title: 'Success Rate',
                  value: '${successRate.toStringAsFixed(1)}%',
                  icon: Icons.trending_up,
                  color: const Color(0xFFFF9800),
                ).animate().fadeIn(delay: const Duration(milliseconds: 300)),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _StatCard(
                  title: 'Total Earned',
                  value: '₹${referralData.totalRewardsEarned.toStringAsFixed(0)}',
                  icon: Icons.monetization_on,
                  color: const Color(0xFF9C27B0),
                ).animate().fadeIn(delay: const Duration(milliseconds: 400)),
              ),
            ],
          ),
          
          // Progress Indicator
          if (referralData.program.maxReferralsPerUser != null) ...[
            const SizedBox(height: 20),
            _buildProgressSection(referralData),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressSection(ReferralData referralData) {
    final maxReferrals = referralData.program.maxReferralsPerUser!;
    final currentReferrals = referralData.stats.successfulReferrals;
    final progress = currentReferrals / maxReferrals;
    final remaining = maxReferrals - currentReferrals;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Referral Progress',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            Text(
              '$currentReferrals / $maxReferrals',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[200],
          valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          minHeight: 6,
        ),
        const SizedBox(height: 8),
        if (remaining > 0)
          Text(
            '$remaining more referrals to reach the limit',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          )
        else
          const Text(
            'You\'ve reached the maximum referrals!',
            style: TextStyle(
              fontSize: 12,
              color: Colors.green,
              fontWeight: FontWeight.w500,
            ),
          ),
      ],
    ).animate().fadeIn(delay: const Duration(milliseconds: 500));
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// 🏆 Referral Achievements Widget
class ReferralAchievementsWidget extends ConsumerWidget {
  final ReferralData referralData;

  const ReferralAchievementsWidget({
    super.key,
    required this.referralData,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final achievements = _getAchievements(referralData);

    if (achievements.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.emoji_events,
                color: Colors.amber[600],
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Achievements',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...achievements.asMap().entries.map((entry) {
            final index = entry.key;
            final achievement = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _AchievementItem(
                achievement: achievement,
              ).animate().fadeIn(delay: Duration(milliseconds: index * 100)),
            );
          }),
        ],
      ),
    );
  }

  List<Achievement> _getAchievements(ReferralData referralData) {
    final achievements = <Achievement>[];
    final stats = referralData.stats;

    // First referral achievement
    if (stats.successfulReferrals >= 1) {
      achievements.add(Achievement(
        title: 'First Referral',
        description: 'Made your first successful referral',
        icon: '🎉',
        color: Colors.green,
      ));
    }

    // Multiple referrals achievements
    if (stats.successfulReferrals >= 5) {
      achievements.add(Achievement(
        title: 'Referral Champion',
        description: 'Made 5 successful referrals',
        icon: '🏆',
        color: Colors.amber,
      ));
    }

    if (stats.successfulReferrals >= 10) {
      achievements.add(Achievement(
        title: 'Referral Master',
        description: 'Made 10 successful referrals',
        icon: '👑',
        color: Colors.purple,
      ));
    }

    // Earnings achievements
    if (referralData.totalRewardsEarned >= 1000) {
      achievements.add(Achievement(
        title: 'Big Earner',
        description: 'Earned ₹1000+ from referrals',
        icon: '💰',
        color: Colors.green,
      ));
    }

    return achievements;
  }
}

class Achievement {
  final String title;
  final String description;
  final String icon;
  final Color color;

  Achievement({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

class _AchievementItem extends StatelessWidget {
  final Achievement achievement;

  const _AchievementItem({
    required this.achievement,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: achievement.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: achievement.color.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Text(
            achievement.icon,
            style: const TextStyle(fontSize: 24),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  achievement.title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: achievement.color,
                  ),
                ),
                Text(
                  achievement.description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
