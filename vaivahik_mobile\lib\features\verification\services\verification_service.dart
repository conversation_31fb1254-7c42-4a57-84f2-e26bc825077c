import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import '../../../core/api/api_client.dart';
import '../models/verification_models.dart';

class VerificationService {
  final ApiClient _apiClient;

  VerificationService() : _apiClient = ApiClient();

  // Get user's verification status and documents
  Future<VerificationStatus> getVerificationStatus() async {
    try {
      final response = await _apiClient.get('/users/verification/documents');
      
      // Parse documents
      final List<dynamic> documentsJson = response['documents'] ?? [];
      final documents = documentsJson
          .map((doc) => VerificationDocument.fromJson(doc))
          .toList();

      // Calculate statistics
      final totalDocuments = documents.length;
      final approvedDocuments = documents.where((doc) => doc.status == DocumentStatus.approved).length;
      final pendingDocuments = documents.where((doc) => doc.status == DocumentStatus.pendingReview).length;
      final rejectedDocuments = documents.where((doc) => doc.status == DocumentStatus.rejected).length;

      // Get last upload and review dates
      DateTime? lastUploadedAt;
      DateTime? lastReviewedAt;
      
      if (documents.isNotEmpty) {
        documents.sort((a, b) => b.uploadedAt.compareTo(a.uploadedAt));
        lastUploadedAt = documents.first.uploadedAt;
        
        final reviewedDocs = documents.where((doc) => doc.reviewedAt != null).toList();
        if (reviewedDocs.isNotEmpty) {
          reviewedDocs.sort((a, b) => b.reviewedAt!.compareTo(a.reviewedAt!));
          lastReviewedAt = reviewedDocs.first.reviewedAt;
        }
      }

      return VerificationStatus(
        isVerified: response['isVerified'] ?? false,
        profileStatus: response['profileStatus'] ?? 'INCOMPLETE',
        documents: documents,
        totalDocuments: totalDocuments,
        approvedDocuments: approvedDocuments,
        pendingDocuments: pendingDocuments,
        rejectedDocuments: rejectedDocuments,
        lastUploadedAt: lastUploadedAt,
        lastReviewedAt: lastReviewedAt,
      );
    } catch (e) {
      throw Exception('Failed to get verification status: $e');
    }
  }

  // Upload a verification document
  Future<DocumentUploadResponse> uploadDocument({
    required DocumentType documentType,
    required File file,
  }) async {
    try {
      // Prepare form data
      final formData = {
        'documentType': documentType.value,
        'document': await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
          contentType: _getContentType(file.path),
        ),
      };

      final response = await _apiClient.postMultipart(
        '/users/verification/documents',
        formData,
      );

      if (response['document'] != null) {
        final document = VerificationDocument.fromJson(response['document']);
        return DocumentUploadResponse(
          success: true,
          message: response['message'] ?? 'Document uploaded successfully',
          document: document,
        );
      } else {
        return DocumentUploadResponse(
          success: false,
          message: response['message'] ?? 'Upload failed',
          error: 'No document data received',
        );
      }
    } catch (e) {
      return DocumentUploadResponse(
        success: false,
        message: 'Upload failed',
        error: e.toString(),
      );
    }
  }

  // Delete a verification document
  Future<DocumentDeleteResponse> deleteDocument(String documentId) async {
    try {
      final response = await _apiClient.delete('/users/verification/documents/$documentId');
      
      return DocumentDeleteResponse(
        success: true,
        message: response['message'] ?? 'Document deleted successfully',
      );
    } catch (e) {
      return DocumentDeleteResponse(
        success: false,
        message: 'Delete failed',
        error: e.toString(),
      );
    }
  }

  // Get verification statistics
  Future<VerificationStats> getVerificationStats() async {
    try {
      final status = await getVerificationStatus();
      
      // Define required documents (can be customized based on business logic)
      final requiredDocuments = [
        DocumentType.aadharCard,
        DocumentType.panCard,
      ];

      // Find missing documents
      final uploadedTypes = status.documents.map((doc) => doc.type).toSet();
      final missingDocuments = requiredDocuments
          .where((type) => !uploadedTypes.contains(type))
          .toList();

      // Calculate completion percentage
      final completionPercentage = requiredDocuments.isEmpty 
          ? 100.0 
          : (status.approvedDocuments / requiredDocuments.length * 100).clamp(0.0, 100.0);

      return VerificationStats(
        totalUploaded: status.totalDocuments,
        approved: status.approvedDocuments,
        pending: status.pendingDocuments,
        rejected: status.rejectedDocuments,
        completionPercentage: completionPercentage,
        missingDocuments: missingDocuments,
        requiredDocuments: requiredDocuments,
      );
    } catch (e) {
      throw Exception('Failed to get verification stats: $e');
    }
  }

  // Get document by ID
  Future<VerificationDocument?> getDocumentById(String documentId) async {
    try {
      final status = await getVerificationStatus();
      return status.documents.firstWhere(
        (doc) => doc.id == documentId,
        orElse: () => throw Exception('Document not found'),
      );
    } catch (e) {
      return null;
    }
  }

  // Check if document type is already uploaded
  Future<bool> isDocumentTypeUploaded(DocumentType documentType) async {
    try {
      final status = await getVerificationStatus();
      return status.documents.any((doc) => doc.type == documentType);
    } catch (e) {
      return false;
    }
  }

  // Get content type based on file extension
  MediaType _getContentType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return MediaType('image', 'jpeg');
      case 'png':
        return MediaType('image', 'png');
      case 'webp':
        return MediaType('image', 'webp');
      case 'pdf':
        return MediaType('application', 'pdf');
      default:
        return MediaType('application', 'octet-stream');
    }
  }

  // Validate file before upload
  bool validateFile(File file) {
    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.lengthSync() > maxSize) {
      return false;
    }

    // Check file extension
    final extension = file.path.split('.').last.toLowerCase();
    const allowedExtensions = ['jpg', 'jpeg', 'png', 'webp', 'pdf'];
    
    return allowedExtensions.contains(extension);
  }

  // Get file size in human readable format
  String getFileSizeString(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}
