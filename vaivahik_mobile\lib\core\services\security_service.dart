import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../api/api_client.dart';

/// Security audit and monitoring service
class SecurityService {
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;
  SecurityService._internal();

  final ApiClient _apiClient = ApiClient();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  
  Timer? _securityTimer;
  final List<Map<String, dynamic>> _securityEvents = [];
  final Map<String, int> _securityCounters = {};
  
  // Security configuration
  static const int _maxSecurityEvents = 50;
  static const int _securityCheckInterval = 300; // 5 minutes
  static const int _maxFailedAttempts = 5;
  static const int _sessionTimeoutMinutes = 30;
  
  bool _isInitialized = false;
  bool _isEnabled = true;
  String? _deviceFingerprint;

  /// Initialize security monitoring
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      _isEnabled = prefs.getBool('security_monitoring_enabled') ?? true;
      
      if (_isEnabled) {
        await _generateDeviceFingerprint();
        _startSecurityMonitoring();
        await _performInitialSecurityCheck();
        debugPrint('SecurityService: Initialized successfully');
      }
      
      _isInitialized = true;
    } catch (e) {
      debugPrint('SecurityService: Failed to initialize - $e');
    }
  }

  /// Generate unique device fingerprint
  Future<void> _generateDeviceFingerprint() async {
    try {
      final deviceData = <String, dynamic>{};
      
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        deviceData.addAll({
          'platform': 'android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'version': androidInfo.version.release,
          'sdk': androidInfo.version.sdkInt,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
        });
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        deviceData.addAll({
          'platform': 'ios',
          'model': iosInfo.model,
          'name': iosInfo.name,
          'version': iosInfo.systemVersion,
          'identifier': iosInfo.identifierForVendor,
        });
      }
      
      // Create fingerprint hash
      final jsonString = jsonEncode(deviceData);
      final bytes = utf8.encode(jsonString);
      final digest = sha256.convert(bytes);
      _deviceFingerprint = digest.toString();
      
      // Store device fingerprint
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('device_fingerprint', _deviceFingerprint!);
      
    } catch (e) {
      debugPrint('SecurityService: Failed to generate device fingerprint - $e');
    }
  }

  /// Start periodic security monitoring
  void _startSecurityMonitoring() {
    _securityTimer?.cancel();
    _securityTimer = Timer.periodic(
      const Duration(seconds: _securityCheckInterval),
      (_) => _performSecurityCheck(),
    );
  }

  /// Perform initial security check
  Future<void> _performInitialSecurityCheck() async {
    try {
      // Check for rooted/jailbroken device
      final isCompromised = await _checkDeviceIntegrity();
      if (isCompromised) {
        _recordSecurityEvent('device_compromised', {
          'type': 'root_jailbreak_detected',
          'risk_level': 'HIGH',
        });
      }
      
      // Check app integrity
      await _checkAppIntegrity();
      
      // Validate stored credentials
      await _validateStoredCredentials();
      
      // Check for suspicious patterns
      await _checkSuspiciousPatterns();
      
    } catch (e) {
      debugPrint('SecurityService: Initial security check failed - $e');
    }
  }

  /// Perform periodic security check
  Future<void> _performSecurityCheck() async {
    if (!_isEnabled) return;
    
    try {
      // Check session validity
      await _checkSessionSecurity();
      
      // Monitor app usage patterns
      await _monitorUsagePatterns();
      
      // Check for security threats
      await _checkSecurityThreats();
      
      // Send security report if needed
      await _sendSecurityReport();
      
    } catch (e) {
      debugPrint('SecurityService: Security check failed - $e');
    }
  }

  /// Check device integrity (root/jailbreak detection)
  Future<bool> _checkDeviceIntegrity() async {
    try {
      // This is a simplified check - in production you would use
      // more sophisticated root/jailbreak detection methods
      
      if (Platform.isAndroid) {
        // Check for common root indicators
        final rootPaths = [
          '/system/app/Superuser.apk',
          '/sbin/su',
          '/system/bin/su',
          '/system/xbin/su',
          '/data/local/xbin/su',
          '/data/local/bin/su',
          '/system/sd/xbin/su',
          '/system/bin/failsafe/su',
          '/data/local/su',
        ];
        
        for (final path in rootPaths) {
          if (await File(path).exists()) {
            return true;
          }
        }
      }
      
      // Additional checks would be implemented here
      return false;
    } catch (e) {
      debugPrint('SecurityService: Device integrity check failed - $e');
      return false;
    }
  }

  /// Check app integrity
  Future<void> _checkAppIntegrity() async {
    try {
      // Verify app signature and integrity
      // This would typically involve checking the app's signature
      // against a known good signature
      
      _recordSecurityEvent('app_integrity_check', {
        'status': 'verified',
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('SecurityService: App integrity check failed - $e');
      _recordSecurityEvent('app_integrity_check', {
        'status': 'failed',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  /// Validate stored credentials
  Future<void> _validateStoredCredentials() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Check for suspicious credential patterns
      final storedKeys = prefs.getKeys();
      final suspiciousKeys = storedKeys.where((key) => 
        key.contains('password') || 
        key.contains('secret') || 
        key.contains('private')
      ).toList();
      
      if (suspiciousKeys.isNotEmpty) {
        _recordSecurityEvent('suspicious_stored_data', {
          'suspicious_keys': suspiciousKeys,
          'risk_level': 'MEDIUM',
        });
      }
    } catch (e) {
      debugPrint('SecurityService: Credential validation failed - $e');
    }
  }

  /// Check for suspicious patterns
  Future<void> _checkSuspiciousPatterns() async {
    try {
      // Check for rapid successive actions
      final rapidActions = _securityCounters['rapid_actions'] ?? 0;
      if (rapidActions > 50) {
        _recordSecurityEvent('suspicious_behavior', {
          'type': 'rapid_actions',
          'count': rapidActions,
          'risk_level': 'MEDIUM',
        });
      }
      
      // Check for unusual access patterns
      final failedAttempts = _securityCounters['failed_attempts'] ?? 0;
      if (failedAttempts > _maxFailedAttempts) {
        _recordSecurityEvent('security_threat', {
          'type': 'excessive_failed_attempts',
          'count': failedAttempts,
          'risk_level': 'HIGH',
        });
      }
    } catch (e) {
      debugPrint('SecurityService: Suspicious pattern check failed - $e');
    }
  }

  /// Check session security
  Future<void> _checkSessionSecurity() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastActivity = prefs.getInt('last_activity_timestamp');
      
      if (lastActivity != null) {
        final timeSinceLastActivity = DateTime.now().millisecondsSinceEpoch - lastActivity;
        final minutesSinceLastActivity = timeSinceLastActivity / (1000 * 60);
        
        if (minutesSinceLastActivity > _sessionTimeoutMinutes) {
          _recordSecurityEvent('session_timeout', {
            'minutes_inactive': minutesSinceLastActivity,
            'action': 'force_logout_required',
          });
        }
      }
    } catch (e) {
      debugPrint('SecurityService: Session security check failed - $e');
    }
  }

  /// Monitor usage patterns
  Future<void> _monitorUsagePatterns() async {
    try {
      // Monitor for unusual usage patterns
      final currentHour = DateTime.now().hour;
      
      // Check for unusual access times (e.g., 2-5 AM)
      if (currentHour >= 2 && currentHour <= 5) {
        _securityCounters['unusual_hours'] = (_securityCounters['unusual_hours'] ?? 0) + 1;
        
        if ((_securityCounters['unusual_hours'] ?? 0) > 5) {
          _recordSecurityEvent('unusual_usage_pattern', {
            'type': 'unusual_hours',
            'hour': currentHour,
            'count': _securityCounters['unusual_hours'],
          });
        }
      }
    } catch (e) {
      debugPrint('SecurityService: Usage pattern monitoring failed - $e');
    }
  }

  /// Check for security threats
  Future<void> _checkSecurityThreats() async {
    try {
      // Check for network security
      await _checkNetworkSecurity();
      
      // Check for data tampering
      await _checkDataIntegrity();
      
    } catch (e) {
      debugPrint('SecurityService: Security threat check failed - $e');
    }
  }

  /// Check network security
  Future<void> _checkNetworkSecurity() async {
    try {
      // Check if using secure connection
      // This would typically involve checking SSL/TLS configuration
      
      _recordSecurityEvent('network_security_check', {
        'status': 'secure',
        'timestamp': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      debugPrint('SecurityService: Network security check failed - $e');
    }
  }

  /// Check data integrity
  Future<void> _checkDataIntegrity() async {
    try {
      // Verify critical data hasn't been tampered with
      final prefs = await SharedPreferences.getInstance();
      final storedFingerprint = prefs.getString('device_fingerprint');
      
      if (storedFingerprint != _deviceFingerprint) {
        _recordSecurityEvent('data_integrity_violation', {
          'type': 'device_fingerprint_mismatch',
          'risk_level': 'HIGH',
        });
      }
    } catch (e) {
      debugPrint('SecurityService: Data integrity check failed - $e');
    }
  }

  /// Record authentication attempt
  void recordAuthAttempt({
    required bool success,
    required String method,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isEnabled) return;
    
    try {
      if (success) {
        _securityCounters['successful_auth'] = (_securityCounters['successful_auth'] ?? 0) + 1;
        _securityCounters['failed_attempts'] = 0; // Reset failed attempts
      } else {
        _securityCounters['failed_attempts'] = (_securityCounters['failed_attempts'] ?? 0) + 1;
        
        if ((_securityCounters['failed_attempts'] ?? 0) >= _maxFailedAttempts) {
          _recordSecurityEvent('security_alert', {
            'type': 'excessive_failed_auth',
            'method': method,
            'count': _securityCounters['failed_attempts'],
            'risk_level': 'HIGH',
            ...?additionalData,
          });
        }
      }
      
      _recordSecurityEvent('auth_attempt', {
        'success': success,
        'method': method,
        'timestamp': DateTime.now().toIso8601String(),
        ...?additionalData,
      });
    } catch (e) {
      debugPrint('SecurityService: Failed to record auth attempt - $e');
    }
  }

  /// Record security event
  void _recordSecurityEvent(String type, Map<String, dynamic> data) {
    try {
      _securityEvents.add({
        'type': type,
        'timestamp': DateTime.now().toIso8601String(),
        'device_fingerprint': _deviceFingerprint,
        'data': data,
      });
      
      // Keep only recent events
      if (_securityEvents.length > _maxSecurityEvents) {
        _securityEvents.removeAt(0);
      }
      
      // Log high-risk events immediately
      if (data['risk_level'] == 'HIGH') {
        debugPrint('SecurityService: HIGH RISK EVENT - $type: $data');
      }
    } catch (e) {
      debugPrint('SecurityService: Failed to record security event - $e');
    }
  }

  /// Send security report to backend
  Future<void> _sendSecurityReport() async {
    if (!_isEnabled || _securityEvents.isEmpty) return;
    
    try {
      final report = {
        'timestamp': DateTime.now().toIso8601String(),
        'device_fingerprint': _deviceFingerprint,
        'events': List<Map<String, dynamic>>.from(_securityEvents),
        'counters': Map<String, dynamic>.from(_securityCounters),
        'device_info': await _getDeviceInfo(),
      };
      
      await _apiClient.post('/security/audit', report);
      
      // Clear sent events
      _securityEvents.clear();
      
      debugPrint('SecurityService: Security report sent successfully');
    } catch (e) {
      debugPrint('SecurityService: Failed to send security report - $e');
    }
  }

  /// Get device information for security reporting
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        return {
          'platform': 'android',
          'model': androidInfo.model,
          'version': androidInfo.version.release,
          'is_physical_device': androidInfo.isPhysicalDevice,
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        return {
          'platform': 'ios',
          'model': iosInfo.model,
          'version': iosInfo.systemVersion,
          'is_physical_device': iosInfo.isPhysicalDevice,
        };
      }
    } catch (e) {
      debugPrint('SecurityService: Failed to get device info - $e');
    }
    
    return {'platform': 'unknown'};
  }

  /// Get security summary
  Map<String, dynamic> getSecuritySummary() {
    return {
      'device_fingerprint': _deviceFingerprint,
      'events_count': _securityEvents.length,
      'counters': Map<String, dynamic>.from(_securityCounters),
      'last_check': DateTime.now().toIso8601String(),
      'is_enabled': _isEnabled,
    };
  }

  /// Enable/disable security monitoring
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('security_monitoring_enabled', enabled);
    
    if (enabled && _isInitialized) {
      _startSecurityMonitoring();
    } else {
      _securityTimer?.cancel();
    }
  }

  /// Clear security data
  void clearSecurityData() {
    _securityEvents.clear();
    _securityCounters.clear();
  }

  /// Dispose resources
  void dispose() {
    _securityTimer?.cancel();
    clearSecurityData();
  }
}
