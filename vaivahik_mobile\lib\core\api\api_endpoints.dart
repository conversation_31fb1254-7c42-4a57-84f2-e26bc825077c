class ApiEndpoints {
  // Base URL
  static const String baseUrl = 'http://localhost:8000/api';
  
  // Authentication endpoints
  static const String login = '/auth/login';
  static const String register = '/auth/register';
  static const String logout = '/auth/logout';
  static const String refreshToken = '/auth/refresh';
  static const String forgotPassword = '/auth/forgot-password';
  static const String resetPassword = '/auth/reset-password';
  static const String verifyOtp = '/auth/verify-otp';
  static const String resendOtp = '/auth/resend-otp';
  static const String changePassword = '/auth/change-password';
  
  // User profile endpoints
  static const String profile = '/user/profile';
  static const String updateProfile = '/user/profile';
  static const String uploadPhoto = '/user/upload-photo';
  static const String deletePhoto = '/user/delete-photo';
  static const String getUser = '/user'; // /user/:id
  static const String searchUsers = '/user/search';
  static const String verifyProfile = '/user/verify';
  static const String deactivateAccount = '/user/deactivate';
  static const String deleteAccount = '/user/delete';
  
  // Matching endpoints
  static const String matches = '/matches';
  static const String matchSuggestions = '/matches/suggestions';
  static const String sendInterest = '/matches/interest';
  static const String respondInterest = '/matches/interest/respond';
  static const String interests = '/matches/interests';
  static const String shortlist = '/user/shortlist';
  static const String addToShortlist = '/user/shortlist/add';
  static const String removeFromShortlist = '/user/shortlist/remove';
  static const String likeProfile = '/matches/like';
  static const String unlikeProfile = '/matches/unlike';
  static const String blockProfile = '/matches/block';
  static const String unblockProfile = '/matches/unblock';
  static const String reportProfile = '/matches/report';
  
  // Chat endpoints
  static const String chatRooms = '/chat/rooms';
  static const String chatMessages = '/chat/messages'; // /chat/messages/:roomId
  static const String sendMessage = '/chat/send';
  static const String uploadChatMedia = '/chat/upload';
  static const String markAsRead = '/chat/read';
  static const String deleteMessage = '/chat/delete';
  static const String editMessage = '/chat/edit';
  static const String searchMessages = '/chat/search';
  static const String exportChat = '/chat/export';
  static const String typingIndicator = '/chat/typing';
  
  // Calling endpoints
  static const String initiateCall = '/calls/initiate';
  static const String acceptCall = '/calls/accept'; // /calls/:id/accept
  static const String declineCall = '/calls/decline'; // /calls/:id/decline
  static const String endCall = '/calls/end'; // /calls/:id/end
  static const String callHistory = '/calls/history';
  static const String activeCall = '/calls/active';
  static const String callQuality = '/calls/quality'; // /calls/:id/quality
  static const String callSettings = '/calls/settings';
  static const String callStatistics = '/calls/statistics';
  static const String blockCalls = '/calls/block';
  static const String unblockCalls = '/calls/unblock';
  static const String reportCall = '/calls/report'; // /calls/:id/report
  static const String getUserPhone = '/users/phone'; // /users/:id/phone
  
  // Kundali endpoints
  static const String generateKundali = '/kundali/generate';
  static const String userKundali = '/kundali/user'; // /kundali/user/:id
  static const String myKundali = '/kundali/my';
  static const String updateKundali = '/kundali/update'; // /kundali/:id
  static const String kundaliMatching = '/kundali/match';
  static const String getKundaliMatching = '/kundali/matching'; // /kundali/matching/:id
  static const String myKundaliMatchings = '/kundali/matchings';
  static const String astrologyReport = '/kundali/report';
  static const String astrologyReports = '/kundali/reports';
  static const String kundaliSettings = '/kundali/settings';
  static const String kundaliAvailability = '/kundali/availability';
  static const String compatibilityScore = '/kundali/compatibility'; // /kundali/compatibility/:id
  static const String doshaAnalysis = '/kundali/doshas'; // /kundali/:id/doshas
  static const String planetaryPositions = '/kundali/planets'; // /kundali/:id/planets
  static const String houseAnalysis = '/kundali/houses'; // /kundali/:id/houses
  static const String kundaliPdf = '/kundali/pdf'; // /kundali/:id/pdf
  static const String matchingReportPdf = '/kundali/matching/pdf'; // /kundali/matching/:id/pdf
  static const String validateBirthDetails = '/kundali/validate';
  static const String birthPlaceSuggestions = '/kundali/places';
  
  // Premium endpoints
  static const String premiumPlans = '/premium/plans';
  static const String userSubscription = '/premium/subscription';
  static const String purchasePlan = '/premium/purchase';
  static const String cancelSubscription = '/premium/cancel';
  static const String renewSubscription = '/premium/renew';
  static const String premiumFeatures = '/premium/features';
  static const String featureUsage = '/premium/usage';
  static const String paymentHistory = '/premium/payments';
  static const String premiumSettings = '/premium/settings';
  static const String createPaymentOrder = '/premium/payment/create';
  static const String verifyPayment = '/premium/payment/verify';
  static const String refundPayment = '/premium/payment/refund';
  
  // Notification endpoints
  static const String notifications = '/notifications';
  static const String markNotificationRead = '/notifications/read'; // /notifications/:id/read
  static const String markAllRead = '/notifications/read-all';
  static const String deleteNotification = '/notifications/delete'; // /notifications/:id
  static const String notificationSettings = '/notifications/settings';
  static const String updateFcmToken = '/notifications/fcm-token';
  static const String testNotification = '/notifications/test';
  static const String notificationTemplates = '/notifications/templates';
  static const String notificationStatistics = '/notifications/statistics';
  
  // Privacy & Security endpoints
  static const String privacySettings = '/privacy/settings';
  static const String securitySettings = '/security/settings';
  static const String blockedUsers = '/privacy/blocked';
  static const String blockUser = '/privacy/block';
  static const String unblockUser = '/privacy/unblock';
  static const String reportUser = '/privacy/report';
  static const String myReports = '/privacy/reports';
  static const String dataPrivacy = '/privacy/data';
  static const String exportData = '/privacy/export';
  static const String deleteData = '/privacy/delete';
  static const String securityQuestions = '/security/questions';
  static const String enable2FA = '/security/2fa/enable';
  static const String disable2FA = '/security/2fa/disable';
  static const String verify2FA = '/security/2fa/verify';
  static const String trustedDevices = '/security/devices';
  static const String removeTrustedDevice = '/security/devices/remove';
  static const String securityLogs = '/security/logs';
  
  // Analytics endpoints
  static const String userAnalytics = '/analytics/user';
  static const String profileViews = '/analytics/profile-views';
  static const String interestAnalytics = '/analytics/interests';
  static const String matchAnalytics = '/analytics/matches';
  static const String chatAnalytics = '/analytics/chat';
  static const String appUsage = '/analytics/usage';
  static const String featureUsageAnalytics = '/analytics/features';
  static const String conversionAnalytics = '/analytics/conversion';
  
  // Admin endpoints (if needed)
  static const String adminUsers = '/admin/users';
  static const String adminReports = '/admin/reports';
  static const String adminAnalytics = '/admin/analytics';
  static const String adminSettings = '/admin/settings';
  static const String adminNotifications = '/admin/notifications';
  
  // Utility endpoints
  static const String uploadFile = '/upload';
  static const String deleteFile = '/delete-file';
  static const String getPresignedUrl = '/presigned-url';
  static const String healthCheck = '/health';
  static const String appConfig = '/config';
  static const String appVersion = '/version';
  
  // Location endpoints
  static const String nearbyUsers = '/location/nearby';
  static const String updateLocation = '/location/update';
  static const String locationSettings = '/location/settings';
  static const String citySuggestions = '/location/cities';
  static const String stateSuggestions = '/location/states';
  static const String countrySuggestions = '/location/countries';
  
  // Verification endpoints
  static const String verificationStatus = '/verification/status';
  static const String submitVerification = '/verification/submit';
  static const String verificationDocuments = '/verification/documents';
  static const String uploadVerificationDoc = '/verification/upload';
  
  // Feedback endpoints
  static const String submitFeedback = '/feedback/submit';
  static const String getFeedback = '/feedback';
  static const String ratingsReviews = '/feedback/ratings';
  static const String appRating = '/feedback/app-rating';
  
  // Support endpoints
  static const String supportTickets = '/support/tickets';
  static const String createTicket = '/support/create';
  static const String updateTicket = '/support/update';
  static const String supportFaq = '/support/faq';
  static const String supportCategories = '/support/categories';
  
  // Biodata endpoints
  static const String biodataTemplates = '/biodata/templates';
  static const String generateBiodata = '/biodata/generate';
  static const String biodataPdf = '/biodata/pdf';
  static const String biodataSettings = '/biodata/settings';
  
  // Registration form endpoints
  static const String registrationFields = '/registration/fields';
  static const String registrationSteps = '/registration/steps';
  static const String saveRegistrationStep = '/registration/save-step';
  static const String completeRegistration = '/registration/complete';
  
  // Search & Filter endpoints
  static const String advancedSearch = '/search/advanced';
  static const String savedSearches = '/search/saved';
  static const String saveSearch = '/search/save';
  static const String deleteSearch = '/search/delete';
  static const String searchFilters = '/search/filters';
  static const String popularSearches = '/search/popular';
  
  // Social features endpoints
  static const String socialPosts = '/social/posts';
  static const String createPost = '/social/create';
  static const String likePost = '/social/like';
  static const String commentPost = '/social/comment';
  static const String sharePost = '/social/share';
  static const String followUser = '/social/follow';
  static const String unfollowUser = '/social/unfollow';
  static const String followers = '/social/followers';
  static const String following = '/social/following';
  
  // Event endpoints
  static const String events = '/events';
  static const String createEvent = '/events/create';
  static const String joinEvent = '/events/join';
  static const String leaveEvent = '/events/leave';
  static const String eventAttendees = '/events/attendees';
  static const String myEvents = '/events/my';
  
  // Success stories endpoints
  static const String successStories = '/success-stories';
  static const String submitStory = '/success-stories/submit';
  static const String featuredStories = '/success-stories/featured';
  
  // Helper methods for dynamic endpoints
  static String getUserById(String id) => '/user/$id';
  static String getChatMessages(String roomId) => '/chat/messages/$roomId';
  static String acceptCallById(String id) => '/calls/$id/accept';
  static String declineCallById(String id) => '/calls/$id/decline';
  static String endCallById(String id) => '/calls/$id/end';
  static String getKundaliById(String id) => '/kundali/$id';
  static String getKundaliMatchingById(String id) => '/kundali/matching/$id';
  static String markNotificationReadById(String id) => '/notifications/$id/read';
  static String deleteNotificationById(String id) => '/notifications/$id';
  static String getCompatibilityScore(String userId) => '/kundali/compatibility/$userId';
  static String getDoshaAnalysis(String kundaliId) => '/kundali/$kundaliId/doshas';
  static String getPlanetaryPositions(String kundaliId) => '/kundali/$kundaliId/planets';
  static String getHouseAnalysis(String kundaliId) => '/kundali/$kundaliId/houses';
  static String getKundaliPdf(String kundaliId) => '/kundali/$kundaliId/pdf';
  static String getMatchingReportPdf(String matchingId) => '/kundali/matching/$matchingId/pdf';
  static String reportCallById(String id) => '/calls/$id/report';
  static String getUserPhoneById(String userId) => '/users/$userId/phone';
  static String updateCallQuality(String callId) => '/calls/$callId/quality';
}
