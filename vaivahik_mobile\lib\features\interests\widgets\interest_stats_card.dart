import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/theme.dart';
import '../providers/interests_provider.dart';


/// 💝 INTEREST STATS CARD - Beautiful Statistics Display
/// Features: Real-time Stats, Animated Counters, Gradient Design

class InterestStatsCard extends ConsumerWidget {
  const InterestStatsCard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final receivedInterests = ref.watch(receivedInterestsProvider);
    final sentInterests = ref.watch(sentInterestsProvider);
    final pendingReceived = ref.watch(pendingReceivedInterestsProvider);
    final pendingSent = ref.watch(pendingSentInterestsProvider);
    final acceptedInterests = ref.watch(acceptedInterestsProvider);

    return Container(
      decoration: BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background pattern
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.transparent,
                  ],
                ),
              ),
            ),
          ),
          
          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildHeader(),
                const SizedBox(height: 20),
                _buildStatsGrid(
                  receivedInterests.length,
                  sentInterests.length,
                  pendingReceived.length,
                  pendingSent.length,
                  acceptedInterests.length,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.favorite,
            color: Colors.white,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Interest Summary',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Your matrimony activity overview',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    ).animate()
     .slideX(begin: -0.5, curve: Curves.elasticOut)
     .fadeIn();
  }

  Widget _buildStatsGrid(
    int totalReceived,
    int totalSent,
    int pendingReceived,
    int pendingSent,
    int totalAccepted,
  ) {
    return Column(
      children: [
        // Top row - Main stats
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'Received',
                totalReceived.toString(),
                Icons.inbox,
                Colors.green,
                '$pendingReceived pending',
                0,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatItem(
                'Sent',
                totalSent.toString(),
                Icons.send,
                Colors.blue,
                '$pendingSent pending',
                100,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // Bottom row - Secondary stats
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                'Accepted',
                totalAccepted.toString(),
                Icons.check_circle,
                Colors.amber,
                'Mutual interests',
                200,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatItem(
                'Success Rate',
                _calculateSuccessRate(totalSent, totalAccepted),
                Icons.trending_up,
                Colors.pink,
                'Match percentage',
                300,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(
    String title,
    String value,
    IconData icon,
    Color accentColor,
    String subtitle,
    int animationDelay,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: accentColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: accentColor,
              size: 20,
            ),
          ),
          const SizedBox(height: 8),
          AnimatedCounter(
            value: value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 0.5, delay: animationDelay.ms, curve: Curves.elasticOut)
     .fadeIn(delay: animationDelay.ms);
  }

  String _calculateSuccessRate(int totalSent, int totalAccepted) {
    if (totalSent == 0) return '0%';
    final rate = (totalAccepted / totalSent * 100).round();
    return '$rate%';
  }
}

/// Animated Counter Widget for smooth number transitions
class AnimatedCounter extends StatefulWidget {
  final String value;
  final TextStyle style;
  final Duration duration;

  const AnimatedCounter({
    super.key,
    required this.value,
    required this.style,
    this.duration = const Duration(milliseconds: 1000),
  });

  @override
  State<AnimatedCounter> createState() => _AnimatedCounterState();
}

class _AnimatedCounterState extends State<AnimatedCounter>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  int _currentValue = 0;
  int _targetValue = 0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));

    _updateValue();
  }

  @override
  void didUpdateWidget(AnimatedCounter oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _updateValue();
    }
  }

  void _updateValue() {
    final newValue = int.tryParse(widget.value.replaceAll(RegExp(r'[^0-9]'), '')) ?? 0;
    if (newValue != _targetValue) {
      _currentValue = _targetValue;
      _targetValue = newValue;
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        final displayValue = (_currentValue + 
            (_targetValue - _currentValue) * _animation.value).round();
        
        String displayText = widget.value;
        if (widget.value.contains('%')) {
          displayText = '$displayValue%';
        } else {
          displayText = displayValue.toString();
        }
        
        return Text(
          displayText,
          style: widget.style,
        );
      },
    );
  }
}

/// Enhanced Stats Card with additional features
class EnhancedInterestStatsCard extends ConsumerWidget {
  final bool showTrends;
  final VoidCallback? onTap;

  const EnhancedInterestStatsCard({
    super.key,
    this.showTrends = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
              AppColors.accent,
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: AppColors.primary.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Animated background pattern
            Positioned.fill(
              child: CustomPaint(
                painter: PatternPainter(),
              ),
            ),
            
            // Content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  const InterestStatsCard(),
                  if (showTrends) ...[
                    const SizedBox(height: 16),
                    _buildTrendIndicators(),
                  ],
                ],
              ),
            ),
            
            // Tap indicator
            if (onTap != null)
              const Positioned(
                top: 16,
                right: 16,
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white70,
                  size: 16,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrendIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildTrendItem('↗️', '+12%', 'This week'),
        _buildTrendItem('📈', '+5', 'New today'),
        _buildTrendItem('🔥', '85%', 'Response rate'),
      ],
    );
  }

  Widget _buildTrendItem(String emoji, String value, String label) {
    return Column(
      children: [
        Text(
          emoji,
          style: const TextStyle(fontSize: 20),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
}

/// Custom painter for background pattern
class PatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.05)
      ..strokeWidth = 1;

    // Draw subtle pattern
    for (int i = 0; i < size.width; i += 20) {
      for (int j = 0; j < size.height; j += 20) {
        canvas.drawCircle(
          Offset(i.toDouble(), j.toDouble()),
          1,
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
