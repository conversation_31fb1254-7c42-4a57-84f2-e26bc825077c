// A/B Testing Framework for Vaivahik Platform
import { v4 as uuidv4 } from 'uuid';

class ABTestingFramework {
  constructor() {
    this.tests = new Map();
    this.userAssignments = new Map();
    this.results = new Map();
    this.isInitialized = false;
  }

  // Initialize A/B testing framework
  async initialize() {
    if (this.isInitialized) return;
    
    try {
      // Load existing tests from backend
      await this.loadActiveTests();
      
      // Load user assignments from localStorage
      this.loadUserAssignments();
      
      this.isInitialized = true;
      console.log('A/B Testing Framework initialized');
    } catch (error) {
      console.error('Failed to initialize A/B Testing Framework:', error);
    }
  }

  // Create a new A/B test
  createTest(testConfig) {
    const test = {
      id: testConfig.id || uuidv4(),
      name: testConfig.name,
      description: testConfig.description,
      variants: testConfig.variants, // Array of variant objects
      trafficAllocation: testConfig.trafficAllocation || 100, // Percentage of users to include
      startDate: testConfig.startDate || new Date(),
      endDate: testConfig.endDate,
      status: 'active',
      targetAudience: testConfig.targetAudience || 'all',
      conversionGoals: testConfig.conversionGoals || [],
      createdAt: new Date(),
      createdBy: testConfig.createdBy
    };

    this.tests.set(test.id, test);
    this.saveTestToBackend(test);
    
    return test;
  }

  // Get variant for a user
  getVariant(testId, userId) {
    const test = this.tests.get(testId);
    if (!test || test.status !== 'active') {
      return null;
    }

    // Check if user is already assigned
    const assignmentKey = `${testId}_${userId}`;
    if (this.userAssignments.has(assignmentKey)) {
      return this.userAssignments.get(assignmentKey);
    }

    // Check if user should be included in test
    if (!this.shouldIncludeUser(test, userId)) {
      return null;
    }

    // Assign user to variant
    const variant = this.assignUserToVariant(test, userId);
    this.userAssignments.set(assignmentKey, variant);
    this.saveUserAssignments();

    // Track assignment
    this.trackEvent(testId, userId, 'assignment', { variant: variant.id });

    return variant;
  }

  // Assign user to variant based on traffic allocation
  assignUserToVariant(test, userId) {
    const hash = this.hashUserId(userId + test.id);
    const bucket = hash % 100;

    let cumulativeWeight = 0;
    for (const variant of test.variants) {
      cumulativeWeight += variant.weight || (100 / test.variants.length);
      if (bucket < cumulativeWeight) {
        return variant;
      }
    }

    // Fallback to control
    return test.variants[0];
  }

  // Check if user should be included in test
  shouldIncludeUser(test, userId) {
    // Check traffic allocation
    const hash = this.hashUserId(userId);
    if ((hash % 100) >= test.trafficAllocation) {
      return false;
    }

    // Check target audience
    if (test.targetAudience !== 'all') {
      // Add audience targeting logic here
      return this.matchesTargetAudience(test.targetAudience, userId);
    }

    return true;
  }

  // Track conversion event
  trackConversion(testId, userId, goalId, value = 1) {
    const variant = this.getVariant(testId, userId);
    if (!variant) return;

    this.trackEvent(testId, userId, 'conversion', {
      variant: variant.id,
      goal: goalId,
      value: value
    });
  }

  // Track custom event
  trackEvent(testId, userId, eventType, data = {}) {
    const event = {
      testId,
      userId,
      eventType,
      data,
      timestamp: new Date()
    };

    // Store locally
    const resultKey = `${testId}_${eventType}`;
    if (!this.results.has(resultKey)) {
      this.results.set(resultKey, []);
    }
    this.results.get(resultKey).push(event);

    // Send to backend
    this.sendEventToBackend(event);
  }

  // Get test results
  getTestResults(testId) {
    const test = this.tests.get(testId);
    if (!test) return null;

    const results = {
      testId,
      testName: test.name,
      variants: {},
      totalParticipants: 0,
      conversionRates: {},
      statisticalSignificance: {},
      winner: null
    };

    // Calculate results for each variant
    test.variants.forEach(variant => {
      const assignments = this.getEventsByVariant(testId, 'assignment', variant.id);
      const conversions = this.getEventsByVariant(testId, 'conversion', variant.id);

      results.variants[variant.id] = {
        name: variant.name,
        participants: assignments.length,
        conversions: conversions.length,
        conversionRate: assignments.length > 0 ? (conversions.length / assignments.length) * 100 : 0
      };

      results.totalParticipants += assignments.length;
    });

    // Calculate statistical significance
    results.statisticalSignificance = this.calculateStatisticalSignificance(results.variants);

    // Determine winner
    results.winner = this.determineWinner(results.variants, results.statisticalSignificance);

    return results;
  }

  // Calculate statistical significance
  calculateStatisticalSignificance(variants) {
    const variantIds = Object.keys(variants);
    if (variantIds.length < 2) return {};

    const control = variants[variantIds[0]];
    const significance = {};

    for (let i = 1; i < variantIds.length; i++) {
      const variant = variants[variantIds[i]];
      const pValue = this.calculatePValue(control, variant);
      
      significance[variantIds[i]] = {
        pValue,
        isSignificant: pValue < 0.05,
        confidenceLevel: (1 - pValue) * 100
      };
    }

    return significance;
  }

  // Simple p-value calculation (Chi-square test)
  calculatePValue(control, variant) {
    const n1 = control.participants;
    const n2 = variant.participants;
    const x1 = control.conversions;
    const x2 = variant.conversions;

    if (n1 === 0 || n2 === 0) return 1;

    const p1 = x1 / n1;
    const p2 = x2 / n2;
    const p = (x1 + x2) / (n1 + n2);

    const se = Math.sqrt(p * (1 - p) * (1/n1 + 1/n2));
    const z = Math.abs(p1 - p2) / se;

    // Simplified p-value calculation
    return Math.max(0.001, 2 * (1 - this.normalCDF(z)));
  }

  // Normal cumulative distribution function approximation
  normalCDF(x) {
    return 0.5 * (1 + this.erf(x / Math.sqrt(2)));
  }

  // Error function approximation
  erf(x) {
    const a1 =  0.254829592;
    const a2 = -0.284496736;
    const a3 =  1.421413741;
    const a4 = -1.453152027;
    const a5 =  1.061405429;
    const p  =  0.3275911;

    const sign = x < 0 ? -1 : 1;
    x = Math.abs(x);

    const t = 1.0 / (1.0 + p * x);
    const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

    return sign * y;
  }

  // Determine test winner
  determineWinner(variants, significance) {
    let bestVariant = null;
    let bestRate = 0;

    Object.entries(variants).forEach(([variantId, data]) => {
      if (data.conversionRate > bestRate) {
        const isSignificant = significance[variantId]?.isSignificant !== false;
        if (isSignificant) {
          bestVariant = variantId;
          bestRate = data.conversionRate;
        }
      }
    });

    return bestVariant;
  }

  // Utility functions
  hashUserId(userId) {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      const char = userId.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  getEventsByVariant(testId, eventType, variantId) {
    const resultKey = `${testId}_${eventType}`;
    const events = this.results.get(resultKey) || [];
    return events.filter(event => event.data.variant === variantId);
  }

  matchesTargetAudience(audience, userId) {
    // Implement audience targeting logic
    // This could check user properties, segments, etc.
    return true;
  }

  // Persistence methods
  saveUserAssignments() {
    if (typeof window !== 'undefined') {
      localStorage.setItem('ab_test_assignments', JSON.stringify(Array.from(this.userAssignments.entries())));
    }
  }

  loadUserAssignments() {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('ab_test_assignments');
      if (saved) {
        this.userAssignments = new Map(JSON.parse(saved));
      }
    }
  }

  async loadActiveTests() {
    try {
      const response = await fetch('/api/admin/ab-tests');
      const data = await response.json();
      
      if (data.success) {
        data.tests.forEach(test => {
          this.tests.set(test.id, test);
        });
      }
    } catch (error) {
      console.error('Failed to load A/B tests:', error);
    }
  }

  async saveTestToBackend(test) {
    try {
      await fetch('/api/admin/ab-tests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(test)
      });
    } catch (error) {
      console.error('Failed to save A/B test:', error);
    }
  }

  async sendEventToBackend(event) {
    try {
      await fetch('/api/admin/ab-tests/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(event)
      });
    } catch (error) {
      console.error('Failed to send A/B test event:', error);
    }
  }
}

// Create singleton instance
const abTesting = new ABTestingFramework();

// Auto-initialize
if (typeof window !== 'undefined') {
  abTesting.initialize();
}

export default abTesting;
