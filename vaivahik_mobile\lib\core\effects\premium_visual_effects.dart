import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../../app/theme.dart';

/// ✨ PREMIUM VISUAL EFFECTS SYSTEM - Stunning Visual Enhancements
/// Features: Particle effects, 3D transformations, gradient animations, shimmer effects
/// Provides world-class visual effects that enhance user engagement and app appeal

/// Particle Effect Widget
class ParticleEffect extends StatefulWidget {
  final Widget child;
  final int particleCount;
  final Color particleColor;
  final double particleSize;
  final Duration animationDuration;
  final bool isActive;

  const ParticleEffect({
    super.key,
    required this.child,
    this.particleCount = 20,
    this.particleColor = AppColors.primary,
    this.particleSize = 4.0,
    this.animationDuration = const Duration(seconds: 3),
    this.isActive = true,
  });

  @override
  State<ParticleEffect> createState() => _ParticleEffectState();
}

class _ParticleEffectState extends State<ParticleEffect>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _initializeParticles();

    if (widget.isActive) {
      _controller.repeat();
    }
  }

  void _initializeParticles() {
    _particles = List.generate(widget.particleCount, (index) {
      return Particle(
        position: Offset(
          math.Random().nextDouble(),
          math.Random().nextDouble(),
        ),
        velocity: Offset(
          (math.Random().nextDouble() - 0.5) * 2,
          (math.Random().nextDouble() - 0.5) * 2,
        ),
        size: widget.particleSize * (0.5 + math.Random().nextDouble() * 0.5),
        opacity: 0.3 + math.Random().nextDouble() * 0.7,
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        if (widget.isActive)
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return CustomPaint(
                  painter: ParticlePainter(
                    particles: _particles,
                    animationValue: _controller.value,
                    color: widget.particleColor,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }
}

class Particle {
  Offset position;
  final Offset velocity;
  final double size;
  final double opacity;

  Particle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.opacity,
  });
}

class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final double animationValue;
  final Color color;

  ParticlePainter({
    required this.particles,
    required this.animationValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (final particle in particles) {
      // Update particle position
      final newX = (particle.position.dx + particle.velocity.dx * animationValue) % 1.0;
      final newY = (particle.position.dy + particle.velocity.dy * animationValue) % 1.0;
      
      particle.position = Offset(newX, newY);

      // Draw particle
      paint.color = color.withValues(alpha: particle.opacity);
      canvas.drawCircle(
        Offset(
          particle.position.dx * size.width,
          particle.position.dy * size.height,
        ),
        particle.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Shimmer Effect Widget
class ShimmerEffect extends StatefulWidget {
  final Widget child;
  final Color baseColor;
  final Color highlightColor;
  final Duration duration;
  final bool enabled;

  const ShimmerEffect({
    super.key,
    required this.child,
    this.baseColor = const Color(0xFFE0E0E0),
    this.highlightColor = const Color(0xFFF5F5F5),
    this.duration = const Duration(milliseconds: 1500),
    this.enabled = true,
  });

  @override
  State<ShimmerEffect> createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<ShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.enabled) {
      _controller.repeat();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                widget.baseColor,
                widget.highlightColor,
                widget.baseColor,
              ],
              stops: [
                math.max(0.0, _animation.value - 0.3),
                _animation.value,
                math.min(1.0, _animation.value + 0.3),
              ],
            ).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }
}

/// 3D Card Effect Widget
class Card3DEffect extends StatefulWidget {
  final Widget child;
  final double depth;
  final bool enableTilt;
  final Duration animationDuration;

  const Card3DEffect({
    super.key,
    required this.child,
    this.depth = 0.01,
    this.enableTilt = true,
    this.animationDuration = const Duration(milliseconds: 200),
  });

  @override
  State<Card3DEffect> createState() => _Card3DEffectState();
}

class _Card3DEffectState extends State<Card3DEffect>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _rotationX;
  late Animation<double> _rotationY;



  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _rotationX = Tween<double>(begin: 0.0, end: 0.0).animate(_controller);
    _rotationY = Tween<double>(begin: 0.0, end: 0.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanUpdate: widget.enableTilt ? _onPanUpdate : null,
      onPanEnd: widget.enableTilt ? _onPanEnd : null,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, widget.depth)
              ..rotateX(_rotationX.value)
              ..rotateY(_rotationY.value),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1 + (_rotationX.value.abs() + _rotationY.value.abs()) * 0.1),
                    blurRadius: 20 + (_rotationX.value.abs() + _rotationY.value.abs()) * 10,
                    offset: Offset(
                      _rotationY.value * 10,
                      _rotationX.value * 10,
                    ),
                  ),
                ],
              ),
              child: widget.child,
            ),
          );
        },
      ),
    );
  }

  void _onPanUpdate(DragUpdateDetails details) {
    final size = context.size!;
    final center = Offset(size.width / 2, size.height / 2);
    final position = details.localPosition - center;

    final rotationY = (position.dx / size.width) * 0.3;
    final rotationX = -(position.dy / size.height) * 0.3;

    _rotationX = Tween<double>(
      begin: _rotationX.value,
      end: rotationX,
    ).animate(_controller);

    _rotationY = Tween<double>(
      begin: _rotationY.value,
      end: rotationY,
    ).animate(_controller);

    _controller.forward(from: 0);
  }

  void _onPanEnd(DragEndDetails details) {
    _rotationX = Tween<double>(
      begin: _rotationX.value,
      end: 0.0,
    ).animate(_controller);

    _rotationY = Tween<double>(
      begin: _rotationY.value,
      end: 0.0,
    ).animate(_controller);

    _controller.forward(from: 0);
  }
}

/// Gradient Animation Widget
class GradientAnimation extends StatefulWidget {
  final Widget child;
  final List<Color> colors;
  final Duration duration;
  final AlignmentGeometry begin;
  final AlignmentGeometry end;

  const GradientAnimation({
    super.key,
    required this.child,
    required this.colors,
    this.duration = const Duration(seconds: 3),
    this.begin = Alignment.topLeft,
    this.end = Alignment.bottomRight,
  });

  @override
  State<GradientAnimation> createState() => _GradientAnimationState();
}

class _GradientAnimationState extends State<GradientAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_controller);

    _controller.repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: widget.begin,
              end: widget.end,
              colors: _interpolateColors(widget.colors, _animation.value),
            ),
          ),
          child: widget.child,
        );
      },
    );
  }

  List<Color> _interpolateColors(List<Color> colors, double t) {
    if (colors.length < 2) return colors;

    final result = <Color>[];
    for (int i = 0; i < colors.length; i++) {
      final nextIndex = (i + 1) % colors.length;
      result.add(Color.lerp(colors[i], colors[nextIndex], t)!);
    }
    return result;
  }
}

/// Ripple Effect Widget
class RippleEffect extends StatefulWidget {
  final Widget child;
  final Color rippleColor;
  final Duration duration;
  final VoidCallback? onTap;

  const RippleEffect({
    super.key,
    required this.child,
    this.rippleColor = AppColors.primary,
    this.duration = const Duration(milliseconds: 600),
    this.onTap,
  });

  @override
  State<RippleEffect> createState() => _RippleEffectState();
}

class _RippleEffectState extends State<RippleEffect>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  Offset? _tapPosition;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (details) {
        setState(() {
          _tapPosition = details.localPosition;
        });
        _controller.forward(from: 0);
        widget.onTap?.call();
      },
      child: Stack(
        children: [
          widget.child,
          if (_tapPosition != null)
            Positioned.fill(
              child: AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return CustomPaint(
                    painter: RipplePainter(
                      center: _tapPosition!,
                      radius: _animation.value,
                      color: widget.rippleColor,
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}

class RipplePainter extends CustomPainter {
  final Offset center;
  final double radius;
  final Color color;

  RipplePainter({
    required this.center,
    required this.radius,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withValues(alpha: 0.3 * (1 - radius))
      ..style = PaintingStyle.fill;

    final maxRadius = math.sqrt(size.width * size.width + size.height * size.height);
    canvas.drawCircle(center, radius * maxRadius, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Floating Action Button with Premium Effects
class PremiumFloatingActionButton extends StatefulWidget {
  final VoidCallback onPressed;
  final Widget icon;
  final String? tooltip;
  final Color? backgroundColor;
  final bool enableParticles;
  final bool enableGlow;

  const PremiumFloatingActionButton({
    super.key,
    required this.onPressed,
    required this.icon,
    this.tooltip,
    this.backgroundColor,
    this.enableParticles = true,
    this.enableGlow = true,
  });

  @override
  State<PremiumFloatingActionButton> createState() => _PremiumFloatingActionButtonState();
}

class _PremiumFloatingActionButtonState extends State<PremiumFloatingActionButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.25,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.elasticOut,
    ));

    if (widget.enableGlow) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Widget fab = AnimatedBuilder(
      animation: Listenable.merge([_pulseController, _rotationController]),
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value * 2 * math.pi,
            child: Container(
              decoration: BoxDecoration(
                gradient: AppGradients.primaryGradient,
                borderRadius: BorderRadius.circular(28),
                boxShadow: widget.enableGlow ? [
                  BoxShadow(
                    color: (widget.backgroundColor ?? AppColors.primary).withValues(alpha: 0.4),
                    blurRadius: 20 * _pulseAnimation.value,
                    spreadRadius: 5 * _pulseAnimation.value,
                  ),
                ] : null,
              ),
              child: FloatingActionButton(
                onPressed: () {
                  _rotationController.forward().then((_) {
                    _rotationController.reverse();
                  });
                  widget.onPressed();
                },
                backgroundColor: Colors.transparent,
                elevation: 0,
                tooltip: widget.tooltip,
                child: widget.icon,
              ),
            ),
          ),
        );
      },
    );

    if (widget.enableParticles) {
      fab = ParticleEffect(
        particleCount: 10,
        particleColor: widget.backgroundColor ?? AppColors.primary,
        particleSize: 2.0,
        child: fab,
      );
    }

    return fab;
  }
}
