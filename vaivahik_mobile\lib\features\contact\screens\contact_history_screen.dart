import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../providers/contact_provider.dart';
import '../widgets/contact_history_card.dart';
import '../models/contact_models.dart';

class ContactHistoryScreen extends StatefulWidget {
  const ContactHistoryScreen({super.key});

  @override
  State<ContactHistoryScreen> createState() => _ContactHistoryScreenState();
}

class _ContactHistoryScreenState extends State<ContactHistoryScreen> {
  String _selectedFilter = 'ALL';
  final List<String> _filterOptions = ['ALL', 'CONTACT_REVEAL', 'CALL_ATTEMPT', 'CALL_CONNECTED'];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ContactProvider>().loadAccessHistory();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Consumer<ContactProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.accessHistory.isEmpty) {
            return const LoadingWidget(size: 50);
          }

          if (provider.error != null && provider.accessHistory.isEmpty) {
            return CustomErrorWidget(
              message: provider.error!,
              onRetry: () => provider.loadAccessHistory(),
            );
          }

          final filteredHistory = _getFilteredHistory(provider.accessHistory);

          if (filteredHistory.isEmpty) {
            return EmptyStateWidget(
              title: _selectedFilter == 'ALL' ? 'No Contact History' : 'No ${_getFilterLabel(_selectedFilter)}',
              message: _selectedFilter == 'ALL' 
                  ? 'Your contact access history will appear here'
                  : 'No ${_getFilterLabel(_selectedFilter).toLowerCase()} found',
              icon: Icons.history,
            );
          }

          return Column(
            children: [
              _buildFilterChips(),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () => provider.loadAccessHistory(),
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredHistory.length,
                    itemBuilder: (context, index) {
                      final history = filteredHistory[index];
                      return ContactHistoryCard(
                        history: history,
                        onTap: () => _showHistoryDetails(history),
                      );
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text(
        'Contact History',
        style: AppTextStyles.h2.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AppColors.primary,
      elevation: 0,
      iconTheme: const IconThemeData(color: Colors.white),
      actions: [
        IconButton(
          onPressed: () => _showFilterDialog(),
          icon: const Icon(Icons.filter_list),
          tooltip: 'Filter',
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _filterOptions.length,
        itemBuilder: (context, index) {
          final filter = _filterOptions[index];
          final isSelected = _selectedFilter == filter;
          
          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Text(_getFilterLabel(filter)),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedFilter = filter;
                });
              },
              selectedColor: AppColors.primary.withValues(alpha: 0.2),
              checkmarkColor: AppColors.primary,
              labelStyle: AppTextStyles.bodySmall.copyWith(
                color: isSelected ? AppColors.primary : Colors.grey[600],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          );
        },
      ),
    );
  }

  List<ContactAccessHistory> _getFilteredHistory(List<ContactAccessHistory> history) {
    if (_selectedFilter == 'ALL') {
      return history;
    }
    
    return history.where((item) => item.accessType == _selectedFilter).toList();
  }

  String _getFilterLabel(String filter) {
    switch (filter) {
      case 'ALL':
        return 'All';
      case 'CONTACT_REVEAL':
        return 'Contact Reveals';
      case 'CALL_ATTEMPT':
        return 'Call Attempts';
      case 'CALL_CONNECTED':
        return 'Connected Calls';
      default:
        return filter;
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter History'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: _filterOptions.map((filter) {
            return RadioListTile<String>(
              title: Text(_getFilterLabel(filter)),
              value: filter,
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = value!;
                });
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showHistoryDetails(ContactAccessHistory history) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Contact Access Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('User', history.contactOwner?.fullName ?? 'Unknown'),
            _buildDetailRow('Access Type', _getAccessTypeLabel(history.accessType ?? 'CONTACT_REVEAL')),
            _buildDetailRow('Date', _formatDateTime(history.accessedAt)),
            if (history.accessReason != null)
              _buildDetailRow('Reason', _getReasonText(history.accessReason!)),
            if (history.contactNumber != null)
              _buildDetailRow('Contact', history.contactNumber!),
            if (history.callDuration != null)
              _buildDetailRow('Call Duration', '${history.callDuration} seconds'),
            _buildDetailRow('Premium Access', history.isPremiumAccess ? 'Yes' : 'No'),
            _buildDetailRow('Platform', history.platform?.toUpperCase() ?? 'UNKNOWN'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodySmall,
            ),
          ),
        ],
      ),
    );
  }

  String _getAccessTypeLabel(String accessType) {
    switch (accessType) {
      case 'CONTACT_REVEAL':
        return 'Contact Revealed';
      case 'CALL_ATTEMPT':
        return 'Call Attempted';
      case 'CALL_CONNECTED':
        return 'Call Connected';
      default:
        return accessType.replaceAll('_', ' ');
    }
  }

  String _getReasonText(String reason) {
    switch (reason) {
      case 'PREMIUM_ONLY':
        return 'Premium Access';
      case 'MUTUAL_INTEREST':
        return 'Mutual Interest';
      case 'ACCEPTED_INTEREST':
        return 'Accepted Interest';
      default:
        return reason.replaceAll('_', ' ').toLowerCase();
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
