/**
 * Birthday Routes
 * 
 * API routes for birthday-related functionality including birthday detection,
 * AI-powered wishes generation, and birthday celebration features.
 */

const express = require('express');
const router = express.Router();
const birthdayController = require('../controllers/birthday.controller');
const { authenticateToken } = require('../middleware/auth.middleware');

// User birthday routes
router.get('/check', authenticateToken, birthdayController.checkBirthday);
router.get('/wishes', authenticateToken, birthdayController.getBirthdayWishes);
router.post('/notify', authenticateToken, birthdayController.sendBirthdayNotification);

module.exports = router;
