import 'dart:io';
import 'package:dio/dio.dart';
import '../api/api_client.dart';
import '../models/user_model.dart';

class ProfileService {
  final ApiClient _apiClient;

  ProfileService(this._apiClient);

  // Get user profile
  Future<UserModel> getUserProfile() async {
    try {
      final response = await _apiClient.get('/users/profile');

      if (response['success'] == true) {
        return UserModel.fromJson(response['user']);
      } else {
        throw Exception('Failed to fetch profile');
      }
    } catch (e) {
      throw Exception('Error fetching profile: $e');
    }
  }

  // Update basic details
  Future<Map<String, dynamic>> updateBasicDetails({
    required String fullName,
    required String gender,
    required DateTime dateOfBirth,
    String? birthTime,
    String? birthPlace,
    String? height,
    String? profileFor,
  }) async {
    try {
      final data = {
        'fullName': fullName,
        'gender': gender,
        'dateOfBirth': dateOfBirth.toIso8601String(),
        if (birthTime != null) 'birthTime': birthTime,
        if (birthPlace != null) 'birthPlace': birthPlace,
        if (height != null) 'height': height,
        if (profileFor != null) 'profileFor': profileFor,
      };

      final response = await _apiClient.put('/users/basic-details', data);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to update basic details');
      }
    } catch (e) {
      throw Exception('Error updating basic details: $e');
    }
  }

  // Update education and career
  Future<Map<String, dynamic>> updateEducationCareer({
    String? education,
    String? occupation,
    String? incomeRange,
    String? workLocation,
  }) async {
    try {
      final data = {
        if (education != null) 'education': education,
        if (occupation != null) 'occupation': occupation,
        if (incomeRange != null) 'incomeRange': incomeRange,
        if (workLocation != null) 'workLocation': workLocation,
      };

      final response = await _apiClient.put('/users/education-career', data);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to update education and career');
      }
    } catch (e) {
      throw Exception('Error updating education and career: $e');
    }
  }

  // Update location details
  Future<Map<String, dynamic>> updateLocationDetails({
    String? city,
    String? state,
    String? country,
    String? nativePlace,
    double? latitude,
    double? longitude,
  }) async {
    try {
      final data = {
        if (city != null) 'city': city,
        if (state != null) 'state': state,
        if (country != null) 'country': country,
        if (nativePlace != null) 'nativePlace': nativePlace,
        if (latitude != null) 'latitude': latitude,
        if (longitude != null) 'longitude': longitude,
      };

      final response = await _apiClient.put('/users/location-details', data);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to update location details');
      }
    } catch (e) {
      throw Exception('Error updating location details: $e');
    }
  }

  // Update family details
  Future<Map<String, dynamic>> updateFamilyDetails({
    String? fatherName,
    String? motherName,
    String? uncleName,
    int? totalSiblings,
    int? marriedSiblings,
    String? familyContact,
    String? familyType,
    String? familyValues,
  }) async {
    try {
      final data = {
        if (fatherName != null) 'fatherName': fatherName,
        if (motherName != null) 'motherName': motherName,
        if (uncleName != null) 'uncleName': uncleName,
        if (totalSiblings != null) 'totalSiblings': totalSiblings,
        if (marriedSiblings != null) 'marriedSiblings': marriedSiblings,
        if (familyContact != null) 'familyContact': familyContact,
        if (familyType != null) 'familyType': familyType,
        if (familyValues != null) 'familyValues': familyValues,
      };

      final response = await _apiClient.put('/users/family-details', data);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to update family details');
      }
    } catch (e) {
      throw Exception('Error updating family details: $e');
    }
  }

  // Update lifestyle habits
  Future<Map<String, dynamic>> updateLifestyleHabits({
    String? diet,
    String? drinking,
    String? smoking,
    List<String>? hobbies,
    List<String>? interests,
    String? exerciseHabits,
  }) async {
    try {
      final data = {
        if (diet != null) 'diet': diet,
        if (drinking != null) 'drinking': drinking,
        if (smoking != null) 'smoking': smoking,
        if (hobbies != null) 'hobbies': hobbies.join(','),
        if (interests != null) 'interests': interests.join(','),
        if (exerciseHabits != null) 'exerciseHabits': exerciseHabits,
      };

      final response = await _apiClient.put('/users/lifestyle-habits', data);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to update lifestyle habits');
      }
    } catch (e) {
      throw Exception('Error updating lifestyle habits: $e');
    }
  }

  // Update partner preferences
  Future<Map<String, dynamic>> updatePartnerPreferences({
    String? ageRange,
    String? heightRange,
    String? education,
    String? occupation,
    String? incomeRange,
    String? location,
    String? maritalStatus,
    String? diet,
    String? smoking,
    String? drinking,
  }) async {
    try {
      final data = {
        if (ageRange != null) 'ageRange': ageRange,
        if (heightRange != null) 'heightRange': heightRange,
        if (education != null) 'education': education,
        if (occupation != null) 'occupation': occupation,
        if (incomeRange != null) 'incomeRange': incomeRange,
        if (location != null) 'location': location,
        if (maritalStatus != null) 'maritalStatus': maritalStatus,
        if (diet != null) 'diet': diet,
        if (smoking != null) 'smoking': smoking,
        if (drinking != null) 'drinking': drinking,
      };

      final response = await _apiClient.put('/users/partner-preferences', data);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to update partner preferences');
      }
    } catch (e) {
      throw Exception('Error updating partner preferences: $e');
    }
  }

  // Update about me
  Future<Map<String, dynamic>> updateAboutMe({
    required String aboutMe,
  }) async {
    try {
      final data = {
        'aboutMe': aboutMe,
      };

      final response = await _apiClient.put('/users/about-me', data);

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to update about me');
      }
    } catch (e) {
      throw Exception('Error updating about me: $e');
    }
  }

  // Upload profile photos
  Future<Map<String, dynamic>> uploadProfilePhotos(List<File> photos) async {
    try {
      final formData = FormData();
      
      for (int i = 0; i < photos.length; i++) {
        formData.files.add(MapEntry(
          'profilePhotos',
          await MultipartFile.fromFile(
            photos[i].path,
            filename: 'photo_${i + 1}.jpg',
          ),
        ));
      }

      final response = await _apiClient.post('/users/photos', {'formData': formData});

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to upload photos');
      }
    } catch (e) {
      throw Exception('Error uploading photos: $e');
    }
  }

  // Get critical field status
  Future<Map<String, dynamic>> getCriticalFieldStatus() async {
    try {
      final response = await _apiClient.get('/users/critical-fields');

      if (response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to fetch critical field status');
      }
    } catch (e) {
      throw Exception('Error fetching critical field status: $e');
    }
  }
}
