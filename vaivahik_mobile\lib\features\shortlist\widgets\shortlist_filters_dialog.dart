import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../app/theme.dart';


import '../models/shortlist_model.dart';

/// 🔍 SHORTLIST FILTERS DIALOG - Advanced Filtering Interface
/// Features: Search, Demographics, Interest Status, Sort Options
class ShortlistFiltersDialog extends StatefulWidget {
  final ShortlistFilters? currentFilters;
  final Function(ShortlistFilters) onApply;
  final VoidCallback onClear;

  const ShortlistFiltersDialog({
    super.key,
    this.currentFilters,
    required this.onApply,
    required this.onClear,
  });

  @override
  State<ShortlistFiltersDialog> createState() => _ShortlistFiltersDialogState();
}

class _ShortlistFiltersDialogState extends State<ShortlistFiltersDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late TabController _tabController;
  
  // Form controllers
  final _searchController = TextEditingController();
  
  // Filter values
  bool? _interestSent;
  String? _interestStatus;
  String? _location;
  String? _education;
  String? _occupation;
  int? _minAge;
  int? _maxAge;
  double? _minHeight;
  double? _maxHeight;
  String? _religion;
  String? _caste;
  String? _maritalStatus;
  bool? _isVerified;
  bool? _isOnline;
  String? _sortBy;
  bool? _sortAscending;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _tabController = TabController(length: 3, vsync: this);
    
    // Initialize with current filters
    _initializeFilters();
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _initializeFilters() {
    final filters = widget.currentFilters;
    if (filters != null) {
      _searchController.text = filters.searchQuery ?? '';
      _interestSent = filters.interestSent;
      _interestStatus = filters.interestStatus;
      _location = filters.location;
      _education = filters.education;
      _occupation = filters.occupation;
      _minAge = filters.minAge;
      _maxAge = filters.maxAge;
      _minHeight = filters.minHeight;
      _maxHeight = filters.maxHeight;
      _religion = filters.religion;
      _caste = filters.caste;
      _maritalStatus = filters.maritalStatus;
      _isVerified = filters.isVerified;
      _isOnline = filters.isOnline;
      _sortBy = filters.sortBy;
      _sortAscending = filters.sortAscending;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 600),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            _buildTabBar(),
            Flexible(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildBasicFilters(),
                  _buildAdvancedFilters(),
                  _buildSortOptions(),
                ],
              ),
            ),
            _buildActionButtons(),
          ],
        ),
      ).animate(controller: _animationController)
       .scale(curve: Curves.elasticOut)
       .fadeIn(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.filter_list,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Filter Shortlist',
                  style: AppTextStyles.h2.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Refine your shortlisted profiles',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
            color: AppColors.textSecondary,
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        indicatorWeight: 3,
        labelStyle: AppTextStyles.bodyMedium.copyWith(
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'Basic'),
          Tab(text: 'Advanced'),
          Tab(text: 'Sort'),
        ],
      ),
    );
  }

  Widget _buildBasicFilters() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              labelText: 'Search',
              hintText: 'Search by name, location, education...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
          ),
          const SizedBox(height: 20),
          
          // Interest Status
          Text(
            'Interest Status',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip(
                'All',
                _interestSent == null,
                () => setState(() => _interestSent = null),
              ),
              _buildFilterChip(
                'Interest Sent',
                _interestSent == true,
                () => setState(() => _interestSent = true),
              ),
              _buildFilterChip(
                'No Interest',
                _interestSent == false,
                () => setState(() => _interestSent = false),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Interest Response Status
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Interest Response',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
            value: _interestStatus,
            items: const [
              DropdownMenuItem(value: null, child: Text('All')),
              DropdownMenuItem(value: 'PENDING', child: Text('Pending')),
              DropdownMenuItem(value: 'ACCEPTED', child: Text('Accepted')),
              DropdownMenuItem(value: 'DECLINED', child: Text('Declined')),
            ],
            onChanged: (value) => setState(() => _interestStatus = value),
          ),
          const SizedBox(height: 20),
          
          // Online Status
          Text(
            'Online Status',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip(
                'All',
                _isOnline == null,
                () => setState(() => _isOnline = null),
              ),
              _buildFilterChip(
                'Online',
                _isOnline == true,
                () => setState(() => _isOnline = true),
              ),
              _buildFilterChip(
                'Offline',
                _isOnline == false,
                () => setState(() => _isOnline = false),
              ),
            ],
          ),
          const SizedBox(height: 20),
          
          // Verification Status
          Text(
            'Verification',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip(
                'All',
                _isVerified == null,
                () => setState(() => _isVerified = null),
              ),
              _buildFilterChip(
                'Verified',
                _isVerified == true,
                () => setState(() => _isVerified = true),
              ),
              _buildFilterChip(
                'Not Verified',
                _isVerified == false,
                () => setState(() => _isVerified = false),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Age Range
          Text(
            'Age Range',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          RangeSlider(
            min: 18,
            max: 60,
            values: RangeValues(
              _minAge?.toDouble() ?? 18,
              _maxAge?.toDouble() ?? 60,
            ),
            divisions: 42,
            labels: RangeLabels(
              '${_minAge ?? 18}',
              '${_maxAge ?? 60}',
            ),
            onChanged: (values) {
              setState(() {
                _minAge = values.start.round();
                _maxAge = values.end.round();
              });
            },
          ),
          const SizedBox(height: 20),
          
          // Height Range
          Text(
            'Height Range',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          RangeSlider(
            min: 4.5,
            max: 6.5,
            values: RangeValues(
              _minHeight ?? 4.5,
              _maxHeight ?? 6.5,
            ),
            divisions: 20,
            labels: RangeLabels(
              '${_minHeight?.toStringAsFixed(1) ?? "4.5"}ft',
              '${_maxHeight?.toStringAsFixed(1) ?? "6.5"}ft',
            ),
            onChanged: (values) {
              setState(() {
                _minHeight = values.start;
                _maxHeight = values.end;
              });
            },
          ),
          const SizedBox(height: 20),
          
          // Location
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Location',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
            value: _location,
            items: const [
              DropdownMenuItem(value: null, child: Text('All Locations')),
              DropdownMenuItem(value: 'Mumbai', child: Text('Mumbai')),
              DropdownMenuItem(value: 'Pune', child: Text('Pune')),
              DropdownMenuItem(value: 'Nashik', child: Text('Nashik')),
              DropdownMenuItem(value: 'Nagpur', child: Text('Nagpur')),
              DropdownMenuItem(value: 'Aurangabad', child: Text('Aurangabad')),
            ],
            onChanged: (value) => setState(() => _location = value),
          ),
          const SizedBox(height: 16),
          
          // Education
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Education',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
            value: _education,
            items: const [
              DropdownMenuItem(value: null, child: Text('All Education')),
              DropdownMenuItem(value: 'Graduate', child: Text('Graduate')),
              DropdownMenuItem(value: 'Post Graduate', child: Text('Post Graduate')),
              DropdownMenuItem(value: 'Doctorate', child: Text('Doctorate')),
              DropdownMenuItem(value: 'Professional', child: Text('Professional')),
            ],
            onChanged: (value) => setState(() => _education = value),
          ),
          const SizedBox(height: 16),
          
          // Occupation
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Occupation',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
            value: _occupation,
            items: const [
              DropdownMenuItem(value: null, child: Text('All Occupations')),
              DropdownMenuItem(value: 'Software Engineer', child: Text('Software Engineer')),
              DropdownMenuItem(value: 'Doctor', child: Text('Doctor')),
              DropdownMenuItem(value: 'Teacher', child: Text('Teacher')),
              DropdownMenuItem(value: 'Business', child: Text('Business')),
              DropdownMenuItem(value: 'Government Job', child: Text('Government Job')),
            ],
            onChanged: (value) => setState(() => _occupation = value),
          ),
          const SizedBox(height: 16),
          
          // Religion & Caste
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: 'Religion',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: AppColors.surface,
                  ),
                  value: _religion,
                  items: const [
                    DropdownMenuItem(value: null, child: Text('All')),
                    DropdownMenuItem(value: 'Hindu', child: Text('Hindu')),
                    DropdownMenuItem(value: 'Muslim', child: Text('Muslim')),
                    DropdownMenuItem(value: 'Christian', child: Text('Christian')),
                    DropdownMenuItem(value: 'Sikh', child: Text('Sikh')),
                  ],
                  onChanged: (value) => setState(() => _religion = value),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: 'Caste',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: AppColors.surface,
                  ),
                  value: _caste,
                  items: const [
                    DropdownMenuItem(value: null, child: Text('All')),
                    DropdownMenuItem(value: 'Maratha', child: Text('Maratha')),
                    DropdownMenuItem(value: 'Brahmin', child: Text('Brahmin')),
                    DropdownMenuItem(value: 'Kshatriya', child: Text('Kshatriya')),
                  ],
                  onChanged: (value) => setState(() => _caste = value),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          // Marital Status
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              labelText: 'Marital Status',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: AppColors.surface,
            ),
            value: _maritalStatus,
            items: const [
              DropdownMenuItem(value: null, child: Text('All')),
              DropdownMenuItem(value: 'Never Married', child: Text('Never Married')),
              DropdownMenuItem(value: 'Divorced', child: Text('Divorced')),
              DropdownMenuItem(value: 'Widowed', child: Text('Widowed')),
            ],
            onChanged: (value) => setState(() => _maritalStatus = value),
          ),
        ],
      ),
    );
  }

  Widget _buildSortOptions() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sort By',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 12),
          ...ShortlistSortOption.values.map((option) {
            return RadioListTile<String>(
              title: Text(option.label),
              value: option.value,
              groupValue: _sortBy,
              onChanged: (value) => setState(() => _sortBy = value),
              activeColor: AppColors.primary,
            );
          }),
          const SizedBox(height: 20),
          
          Text(
            'Sort Order',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip(
                'Ascending',
                _sortAscending == true,
                () => setState(() => _sortAscending = true),
              ),
              _buildFilterChip(
                'Descending',
                _sortAscending == false,
                () => setState(() => _sortAscending = false),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onTap(),
      selectedColor: AppColors.primary.withValues(alpha: 0.2),
      checkmarkColor: AppColors.primary,
      labelStyle: TextStyle(
        color: isSelected ? AppColors.primary : AppColors.textSecondary,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                widget.onClear();
                Navigator.pop(context);
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.textSecondary,
                side: const BorderSide(color: AppColors.textSecondary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Clear All'),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: _applyFilters,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Apply Filters'),
            ),
          ),
        ],
      ),
    );
  }

  void _applyFilters() {
    final filters = ShortlistFilters(
      searchQuery: _searchController.text.trim().isEmpty ? null : _searchController.text.trim(),
      interestSent: _interestSent,
      interestStatus: _interestStatus,
      location: _location,
      education: _education,
      occupation: _occupation,
      minAge: _minAge,
      maxAge: _maxAge,
      minHeight: _minHeight,
      maxHeight: _maxHeight,
      religion: _religion,
      caste: _caste,
      maritalStatus: _maritalStatus,
      isVerified: _isVerified,
      isOnline: _isOnline,
      sortBy: _sortBy,
      sortAscending: _sortAscending,
    );
    
    widget.onApply(filters);
    Navigator.pop(context);
  }
}
