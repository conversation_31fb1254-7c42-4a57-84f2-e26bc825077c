import 'package:flutter/material.dart';
import '../../../app/theme.dart';
import '../models/shortlist_model.dart';

/// 📋 SHORTLIST CARD - Beautiful Profile Card for Shortlist
/// Features: Profile Info, Actions, Interest Status, Notes
class ShortlistCard extends StatefulWidget {
  final ShortlistItem item;
  final VoidCallback? onTap;
  final VoidCallback? onRemove;
  final VoidCallback? onEditNote;
  final VoidCallback? onSendInterest;
  final VoidCallback? onCall;
  final ShortlistCardStyle style;

  const ShortlistCard({
    super.key,
    required this.item,
    this.onTap,
    this.onRemove,
    this.onEditNote,
    this.onSendInterest,
    this.onCall,
    this.style = ShortlistCardStyle.detailed,
  });

  @override
  State<ShortlistCard> createState() => _ShortlistCardState();
}

class _ShortlistCardState extends State<ShortlistCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Card(
        elevation: 4,
        shadowColor: Colors.black.withValues(alpha: 0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  Colors.grey[50]!,
                ],
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                const SizedBox(height: 12),
                _buildProfileInfo(),
                if (widget.item.note?.isNotEmpty == true) ...[
                  const SizedBox(height: 12),
                  _buildNoteSection(),
                ],
                const SizedBox(height: 16),
                _buildInterestStatus(),
                const SizedBox(height: 16),
                _buildActionButtons(),
                if (_isExpanded) ...[
                  const SizedBox(height: 16),
                  _buildExpandedContent(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Profile Picture
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipOval(
            child: widget.item.profile.profilePicture?.isNotEmpty == true
                ? Image.network(
                    widget.item.profile.profilePicture!,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) => _buildAvatarFallback(),
                  )
                : _buildAvatarFallback(),
          ),
        ),
        const SizedBox(width: 16),
        
        // Name and Basic Info
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.item.fullName,
                      style: AppTextStyles.h4.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (widget.item.profile.isVerified == true)
                    Container(
                      margin: const EdgeInsets.only(left: 8),
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.verified,
                            size: 12,
                            color: Colors.blue[600],
                          ),
                          const SizedBox(width: 2),
                          Text(
                            'Verified',
                            style: AppTextStyles.bodySmall.copyWith(
                              color: Colors.blue[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                '${widget.item.displayAge} • ${widget.item.displayLocation}',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (widget.item.profile.compatibility != null)
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  child: Row(
                    children: [
                      Icon(
                        Icons.favorite,
                        size: 14,
                        color: Colors.pink[400],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${widget.item.profile.compatibility}% Match',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: Colors.pink[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
        
        // Online Status
        if (widget.item.profile.isOnline == true)
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
          ),
      ],
    );
  }

  Widget _buildAvatarFallback() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppColors.primary.withValues(alpha: 0.7),
            AppColors.primary,
          ],
        ),
      ),
      child: Center(
        child: Text(
          widget.item.fullName.isNotEmpty 
              ? widget.item.fullName[0].toUpperCase()
              : '?',
          style: AppTextStyles.h2.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  Widget _buildProfileInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  Icons.school,
                  'Education',
                  widget.item.displayEducation,
                ),
              ),
              Container(
                width: 1,
                height: 30,
                color: AppColors.primary.withValues(alpha: 0.2),
              ),
              Expanded(
                child: _buildInfoItem(
                  Icons.work,
                  'Occupation',
                  widget.item.displayOccupation,
                ),
              ),
            ],
          ),
          if (widget.item.profile.religion?.isNotEmpty == true ||
              widget.item.profile.motherTongue?.isNotEmpty == true) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                if (widget.item.profile.religion?.isNotEmpty == true)
                  Expanded(
                    child: _buildInfoItem(
                      Icons.temple_hindu,
                      'Religion',
                      widget.item.profile.religion!,
                    ),
                  ),
                if (widget.item.profile.religion?.isNotEmpty == true &&
                    widget.item.profile.motherTongue?.isNotEmpty == true)
                  Container(
                    width: 1,
                    height: 30,
                    color: AppColors.primary.withValues(alpha: 0.2),
                  ),
                if (widget.item.profile.motherTongue?.isNotEmpty == true)
                  Expanded(
                    child: _buildInfoItem(
                      Icons.language,
                      'Language',
                      widget.item.profile.motherTongue!,
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        children: [
          Icon(
            icon,
            size: 16,
            color: AppColors.primary,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w500,
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildNoteSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.amber.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.note,
            size: 16,
            color: Colors.amber[700],
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.item.note!,
              style: AppTextStyles.bodySmall.copyWith(
                color: Colors.amber[800],
                fontStyle: FontStyle.italic,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          IconButton(
            icon: const Icon(Icons.edit, size: 16),
            onPressed: widget.onEditNote,
            color: Colors.amber[700],
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
            padding: EdgeInsets.zero,
          ),
        ],
      ),
    );
  }

  Widget _buildInterestStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: widget.item.interestStatusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: widget.item.interestStatusColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            widget.item.interestStatusIcon,
            size: 16,
            color: widget.item.interestStatusColor,
          ),
          const SizedBox(width: 6),
          Text(
            widget.item.interestStatusText,
            style: AppTextStyles.bodySmall.copyWith(
              color: widget.item.interestStatusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        if (!widget.item.interestSent)
          Expanded(
            child: ElevatedButton.icon(
              onPressed: widget.onSendInterest,
              icon: const Icon(Icons.favorite, size: 16),
              label: const Text('Send Interest'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
        if (!widget.item.interestSent) const SizedBox(width: 8),
        
        Expanded(
          child: OutlinedButton.icon(
            onPressed: widget.onCall,
            icon: const Icon(Icons.phone, size: 16),
            label: const Text('Call'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.primary,
              side: const BorderSide(color: AppColors.primary),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(vertical: 8),
            ),
          ),
        ),
        const SizedBox(width: 8),
        
        IconButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
            if (_isExpanded) {
              _animationController.forward();
            } else {
              _animationController.reverse();
            }
          },
          icon: AnimatedRotation(
            turns: _isExpanded ? 0.5 : 0,
            duration: const Duration(milliseconds: 300),
            child: const Icon(Icons.expand_more),
          ),
          color: AppColors.textSecondary,
        ),
        
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: AppColors.textSecondary),
          onSelected: (value) {
            switch (value) {
              case 'edit_note':
                widget.onEditNote?.call();
                break;
              case 'remove':
                widget.onRemove?.call();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit_note',
              child: ListTile(
                leading: Icon(Icons.edit_note),
                title: Text('Edit Note'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Remove', style: TextStyle(color: Colors.red)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExpandedContent() {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SizeTransition(
          sizeFactor: _animationController,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.grey.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Additional Details',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 8),
                if (widget.item.profile.height != null)
                  _buildDetailRow('Height', '${widget.item.profile.height} ft'),
                if (widget.item.profile.maritalStatus?.isNotEmpty == true)
                  _buildDetailRow('Marital Status', widget.item.profile.maritalStatus!),
                if (widget.item.profile.income?.isNotEmpty == true)
                  _buildDetailRow('Income', widget.item.profile.income!),
                _buildDetailRow('Added on', _formatDate(widget.item.createdAt)),
                if (widget.item.profile.lastSeen != null)
                  _buildDetailRow('Last seen', _formatLastSeen(widget.item.profile.lastSeen!)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 5) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }
}
