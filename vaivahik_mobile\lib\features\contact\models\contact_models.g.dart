// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ContactRevealRequest _$ContactRevealRequestFromJson(
        Map<String, dynamic> json) =>
    _ContactRevealRequest(
      targetUserId: json['targetUserId'] as String,
      reason: json['reason'] as String?,
      platform: json['platform'] as String? ?? 'mobile',
    );

Map<String, dynamic> _$ContactRevealRequestToJson(
        _ContactRevealRequest instance) =>
    <String, dynamic>{
      'targetUserId': instance.targetUserId,
      'reason': instance.reason,
      'platform': instance.platform,
    };

_ContactRevealResponse _$ContactRevealResponseFromJson(
        Map<String, dynamic> json) =>
    _ContactRevealResponse(
      success: json['success'] as bool,
      contactNumber: json['contactNumber'] as String?,
      contactOwnerName: json['contactOwnerName'] as String?,
      accessReason: json['accessReason'] as String?,
      callAvailability: json['callAvailability'] as String?,
      error: json['error'] as String?,
      message: json['message'] as String?,
      upgradeRequired: json['upgradeRequired'] as bool? ?? false,
    );

Map<String, dynamic> _$ContactRevealResponseToJson(
        _ContactRevealResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'contactNumber': instance.contactNumber,
      'contactOwnerName': instance.contactOwnerName,
      'accessReason': instance.accessReason,
      'callAvailability': instance.callAvailability,
      'error': instance.error,
      'message': instance.message,
      'upgradeRequired': instance.upgradeRequired,
    };

_ContactPrivacySettings _$ContactPrivacySettingsFromJson(
        Map<String, dynamic> json) =>
    _ContactPrivacySettings(
      allowDirectCalls: json['allowDirectCalls'] as bool? ?? true,
      contactRevealPreference:
          json['contactRevealPreference'] as String? ?? 'PREMIUM_ONLY',
      requireMutualInterest: json['requireMutualInterest'] as bool? ?? true,
      callAvailability: json['callAvailability'] as String? ?? 'ANYTIME',
    );

Map<String, dynamic> _$ContactPrivacySettingsToJson(
        _ContactPrivacySettings instance) =>
    <String, dynamic>{
      'allowDirectCalls': instance.allowDirectCalls,
      'contactRevealPreference': instance.contactRevealPreference,
      'requireMutualInterest': instance.requireMutualInterest,
      'callAvailability': instance.callAvailability,
    };

_ContactRevealOption _$ContactRevealOptionFromJson(Map<String, dynamic> json) =>
    _ContactRevealOption(
      value: json['value'] as String,
      label: json['label'] as String,
      description: json['description'] as String,
      icon: json['icon'] as String,
    );

Map<String, dynamic> _$ContactRevealOptionToJson(
        _ContactRevealOption instance) =>
    <String, dynamic>{
      'value': instance.value,
      'label': instance.label,
      'description': instance.description,
      'icon': instance.icon,
    };

_ContactAccessHistory _$ContactAccessHistoryFromJson(
        Map<String, dynamic> json) =>
    _ContactAccessHistory(
      id: json['id'] as String,
      accessorId: json['accessorId'] as String,
      contactOwnerId: json['contactOwnerId'] as String,
      accessedAt: DateTime.parse(json['accessedAt'] as String),
      accessType: json['accessType'] as String?,
      contactNumber: json['contactNumber'] as String?,
      accessReason: json['accessReason'] as String?,
      isPremiumAccess: json['isPremiumAccess'] as bool? ?? false,
      featurePurchaseId: json['featurePurchaseId'] as String?,
      callDuration: (json['callDuration'] as num?)?.toInt(),
      callStatus: json['callStatus'] as String?,
      platform: json['platform'] as String?,
      accessor: json['accessor'] == null
          ? null
          : ContactAccessUser.fromJson(
              json['accessor'] as Map<String, dynamic>),
      contactOwner: json['contactOwner'] == null
          ? null
          : ContactAccessUser.fromJson(
              json['contactOwner'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ContactAccessHistoryToJson(
        _ContactAccessHistory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'accessorId': instance.accessorId,
      'contactOwnerId': instance.contactOwnerId,
      'accessedAt': instance.accessedAt.toIso8601String(),
      'accessType': instance.accessType,
      'contactNumber': instance.contactNumber,
      'accessReason': instance.accessReason,
      'isPremiumAccess': instance.isPremiumAccess,
      'featurePurchaseId': instance.featurePurchaseId,
      'callDuration': instance.callDuration,
      'callStatus': instance.callStatus,
      'platform': instance.platform,
      'accessor': instance.accessor,
      'contactOwner': instance.contactOwner,
    };

_ContactAccessUser _$ContactAccessUserFromJson(Map<String, dynamic> json) =>
    _ContactAccessUser(
      id: json['id'] as String,
      fullName: json['fullName'] as String?,
      profilePhoto: json['profilePhoto'] as String?,
      age: (json['age'] as num?)?.toInt(),
      location: json['location'] as String?,
    );

Map<String, dynamic> _$ContactAccessUserToJson(_ContactAccessUser instance) =>
    <String, dynamic>{
      'id': instance.id,
      'fullName': instance.fullName,
      'profilePhoto': instance.profilePhoto,
      'age': instance.age,
      'location': instance.location,
    };

_ContactRevealStats _$ContactRevealStatsFromJson(Map<String, dynamic> json) =>
    _ContactRevealStats(
      totalRevealed: (json['totalRevealed'] as num?)?.toInt() ?? 0,
      totalRequested: (json['totalRequested'] as num?)?.toInt() ?? 0,
      premiumReveals: (json['premiumReveals'] as num?)?.toInt() ?? 0,
      mutualInterestReveals:
          (json['mutualInterestReveals'] as num?)?.toInt() ?? 0,
      acceptedInterestReveals:
          (json['acceptedInterestReveals'] as num?)?.toInt() ?? 0,
      callsInitiated: (json['callsInitiated'] as num?)?.toInt() ?? 0,
      callsConnected: (json['callsConnected'] as num?)?.toInt() ?? 0,
      platformStats: (json['platformStats'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      reasonStats: (json['reasonStats'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
    );

Map<String, dynamic> _$ContactRevealStatsToJson(_ContactRevealStats instance) =>
    <String, dynamic>{
      'totalRevealed': instance.totalRevealed,
      'totalRequested': instance.totalRequested,
      'premiumReveals': instance.premiumReveals,
      'mutualInterestReveals': instance.mutualInterestReveals,
      'acceptedInterestReveals': instance.acceptedInterestReveals,
      'callsInitiated': instance.callsInitiated,
      'callsConnected': instance.callsConnected,
      'platformStats': instance.platformStats,
      'reasonStats': instance.reasonStats,
    };
