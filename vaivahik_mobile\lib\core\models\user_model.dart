class UserModel {
  final String id;
  final String phone;
  final String? email;
  final String? firstName;
  final String? lastName;
  final String? profileFor;
  final String? gender;
  final DateTime? dateOfBirth;
  final String? birthTime;
  final String? birthPlace;
  final String? maritalStatus;
  final String? religion;
  final String? caste;
  final String? subCaste;
  final String? motherTongue;
  final String? education;
  final String? occupation;
  final String? income;
  final String? height;
  final String? weight;
  final String? complexion;
  final String? bloodGroup;
  final String? familyType;
  final String? familyValues;
  final String? aboutMe;
  final List<String>? photos;
  final String? profilePhoto;
  final bool isVerified;
  final bool isPremium;
  final bool isActive;
  final int profileCompletionPercentage;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UserModel({
    required this.id,
    required this.phone,
    this.email,
    this.firstName,
    this.lastName,
    this.profileFor,
    this.gender,
    this.dateOfBirth,
    this.birthTime,
    this.birthPlace,
    this.maritalStatus,
    this.religion,
    this.caste,
    this.subCaste,
    this.motherTongue,
    this.education,
    this.occupation,
    this.income,
    this.height,
    this.weight,
    this.complexion,
    this.bloodGroup,
    this.familyType,
    this.familyValues,
    this.aboutMe,
    this.photos,
    this.profilePhoto,
    required this.isVerified,
    required this.isPremium,
    required this.isActive,
    required this.profileCompletionPercentage,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      profileFor: json['profileFor'],
      gender: json['gender'],
      dateOfBirth: json['dateOfBirth'] != null ? DateTime.parse(json['dateOfBirth']) : null,
      birthTime: json['birthTime'],
      birthPlace: json['birthPlace'],
      maritalStatus: json['maritalStatus'],
      religion: json['religion'],
      caste: json['caste'],
      subCaste: json['subCaste'],
      motherTongue: json['motherTongue'],
      education: json['education'],
      occupation: json['occupation'],
      income: json['income'],
      height: json['height'],
      weight: json['weight'],
      complexion: json['complexion'],
      bloodGroup: json['bloodGroup'],
      familyType: json['familyType'],
      familyValues: json['familyValues'],
      aboutMe: json['aboutMe'],
      photos: json['photos'] != null ? List<String>.from(json['photos']) : null,
      profilePhoto: json['profilePhoto'],
      isVerified: json['isVerified'] ?? false,
      isPremium: json['isPremium'] ?? false,
      isActive: json['isActive'] ?? true,
      profileCompletionPercentage: json['profileCompletionPercentage']?.toInt() ?? 0,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : DateTime.now(),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'phone': phone,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'profileFor': profileFor,
      'gender': gender,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'birthTime': birthTime,
      'birthPlace': birthPlace,
      'maritalStatus': maritalStatus,
      'religion': religion,
      'caste': caste,
      'subCaste': subCaste,
      'motherTongue': motherTongue,
      'education': education,
      'occupation': occupation,
      'income': income,
      'height': height,
      'weight': weight,
      'complexion': complexion,
      'bloodGroup': bloodGroup,
      'familyType': familyType,
      'familyValues': familyValues,
      'aboutMe': aboutMe,
      'photos': photos,
      'profilePhoto': profilePhoto,
      'isVerified': isVerified,
      'isPremium': isPremium,
      'isActive': isActive,
      'profileCompletionPercentage': profileCompletionPercentage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  UserModel copyWith({
    String? id,
    String? phone,
    String? email,
    String? firstName,
    String? lastName,
    String? profileFor,
    String? gender,
    DateTime? dateOfBirth,
    String? birthTime,
    String? birthPlace,
    String? maritalStatus,
    String? religion,
    String? caste,
    String? subCaste,
    String? motherTongue,
    String? education,
    String? occupation,
    String? income,
    String? height,
    String? weight,
    String? complexion,
    String? bloodGroup,
    String? familyType,
    String? familyValues,
    String? aboutMe,
    List<String>? photos,
    String? profilePhoto,
    bool? isVerified,
    bool? isPremium,
    bool? isActive,
    int? profileCompletionPercentage,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserModel(
      id: id ?? this.id,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      profileFor: profileFor ?? this.profileFor,
      gender: gender ?? this.gender,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      birthTime: birthTime ?? this.birthTime,
      birthPlace: birthPlace ?? this.birthPlace,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      religion: religion ?? this.religion,
      caste: caste ?? this.caste,
      subCaste: subCaste ?? this.subCaste,
      motherTongue: motherTongue ?? this.motherTongue,
      education: education ?? this.education,
      occupation: occupation ?? this.occupation,
      income: income ?? this.income,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      complexion: complexion ?? this.complexion,
      bloodGroup: bloodGroup ?? this.bloodGroup,
      familyType: familyType ?? this.familyType,
      familyValues: familyValues ?? this.familyValues,
      aboutMe: aboutMe ?? this.aboutMe,
      photos: photos ?? this.photos,
      profilePhoto: profilePhoto ?? this.profilePhoto,
      isVerified: isVerified ?? this.isVerified,
      isPremium: isPremium ?? this.isPremium,
      isActive: isActive ?? this.isActive,
      profileCompletionPercentage: profileCompletionPercentage ?? this.profileCompletionPercentage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else {
      return 'User';
    }
  }

  String get displayName {
    if (firstName != null) {
      return firstName!;
    } else {
      return phone;
    }
  }

  int get age {
    if (dateOfBirth != null) {
      final now = DateTime.now();
      final age = now.year - dateOfBirth!.year;
      if (now.month < dateOfBirth!.month || 
          (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
        return age - 1;
      }
      return age;
    }
    return 0;
  }

  bool get isProfileComplete {
    return profileCompletionPercentage >= 80;
  }
}

class AuthResult {
  final bool success;
  final String? message;
  final String? token;
  final UserModel? user;

  const AuthResult({
    required this.success,
    this.message,
    this.token,
    this.user,
  });

  factory AuthResult.fromJson(Map<String, dynamic> json) {
    return AuthResult(
      success: json['success'] ?? false,
      message: json['message'],
      token: json['token'],
      user: json['user'] != null ? UserModel.fromJson(json['user']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'token': token,
      'user': user?.toJson(),
    };
  }
}
