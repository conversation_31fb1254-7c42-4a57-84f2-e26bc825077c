class AppNotification {
  final String id;
  final String userId;
  final String title;
  final String body;
  final NotificationType type;
  final NotificationPriority priority;
  final Map<String, dynamic>? data;
  final String? imageUrl;
  final String? actionUrl;
  final bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;
  final DateTime? scheduledAt;
  final String? groupKey;
  final String? tag;

  const AppNotification({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    required this.priority,
    this.data,
    this.imageUrl,
    this.actionUrl,
    required this.isRead,
    required this.createdAt,
    this.readAt,
    this.scheduledAt,
    this.groupKey,
    this.tag,
  });

  factory AppNotification.fromJson(Map<String, dynamic> json) {
    return AppNotification(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      body: json['body']?.toString() ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => NotificationType.general,
      ),
      priority: NotificationPriority.values.firstWhere(
        (e) => e.name == json['priority']?.toString(),
        orElse: () => NotificationPriority.normal,
      ),
      data: json['data'] as Map<String, dynamic>?,
      imageUrl: json['imageUrl']?.toString(),
      actionUrl: json['actionUrl']?.toString(),
      isRead: json['isRead'] ?? false,
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      readAt: json['readAt'] != null 
          ? DateTime.tryParse(json['readAt'].toString())
          : null,
      scheduledAt: json['scheduledAt'] != null 
          ? DateTime.tryParse(json['scheduledAt'].toString())
          : null,
      groupKey: json['groupKey']?.toString(),
      tag: json['tag']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'title': title,
      'body': body,
      'type': type.name,
      'priority': priority.name,
      'data': data,
      'imageUrl': imageUrl,
      'actionUrl': actionUrl,
      'isRead': isRead,
      'createdAt': createdAt.toIso8601String(),
      'readAt': readAt?.toIso8601String(),
      'scheduledAt': scheduledAt?.toIso8601String(),
      'groupKey': groupKey,
      'tag': tag,
    };
  }

  AppNotification copyWith({
    String? id,
    String? userId,
    String? title,
    String? body,
    NotificationType? type,
    NotificationPriority? priority,
    Map<String, dynamic>? data,
    String? imageUrl,
    String? actionUrl,
    bool? isRead,
    DateTime? createdAt,
    DateTime? readAt,
    DateTime? scheduledAt,
    String? groupKey,
    String? tag,
  }) {
    return AppNotification(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      body: body ?? this.body,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      data: data ?? this.data,
      imageUrl: imageUrl ?? this.imageUrl,
      actionUrl: actionUrl ?? this.actionUrl,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      readAt: readAt ?? this.readAt,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      groupKey: groupKey ?? this.groupKey,
      tag: tag ?? this.tag,
    );
  }
}

enum NotificationType {
  general,
  match,
  interest,
  message,
  call,
  premium,
  profile,
  verification,
  system,
  promotion,
  reminder,
  kundali
}

enum NotificationPriority {
  low,
  normal,
  high,
  urgent
}

class NotificationSettings {
  final bool pushNotificationsEnabled;
  final bool emailNotificationsEnabled;
  final bool smsNotificationsEnabled;
  final Map<NotificationType, bool> typeSettings;
  final NotificationSound sound;
  final bool vibrationEnabled;
  final bool ledEnabled;
  final String? quietHoursStart;
  final String? quietHoursEnd;
  final bool quietHoursEnabled;
  final bool showOnLockScreen;
  final bool showPreview;
  final NotificationGrouping grouping;

  const NotificationSettings({
    required this.pushNotificationsEnabled,
    required this.emailNotificationsEnabled,
    required this.smsNotificationsEnabled,
    required this.typeSettings,
    required this.sound,
    required this.vibrationEnabled,
    required this.ledEnabled,
    this.quietHoursStart,
    this.quietHoursEnd,
    required this.quietHoursEnabled,
    required this.showOnLockScreen,
    required this.showPreview,
    required this.grouping,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    final typeSettingsMap = <NotificationType, bool>{};
    if (json['typeSettings'] != null) {
      (json['typeSettings'] as Map<String, dynamic>).forEach((key, value) {
        final type = NotificationType.values.firstWhere(
          (e) => e.name == key,
          orElse: () => NotificationType.general,
        );
        typeSettingsMap[type] = value ?? true;
      });
    }

    return NotificationSettings(
      pushNotificationsEnabled: json['pushNotificationsEnabled'] ?? true,
      emailNotificationsEnabled: json['emailNotificationsEnabled'] ?? true,
      smsNotificationsEnabled: json['smsNotificationsEnabled'] ?? false,
      typeSettings: typeSettingsMap,
      sound: NotificationSound.values.firstWhere(
        (e) => e.name == json['sound']?.toString(),
        orElse: () => NotificationSound.defaultSound,
      ),
      vibrationEnabled: json['vibrationEnabled'] ?? true,
      ledEnabled: json['ledEnabled'] ?? true,
      quietHoursStart: json['quietHoursStart']?.toString(),
      quietHoursEnd: json['quietHoursEnd']?.toString(),
      quietHoursEnabled: json['quietHoursEnabled'] ?? false,
      showOnLockScreen: json['showOnLockScreen'] ?? true,
      showPreview: json['showPreview'] ?? true,
      grouping: NotificationGrouping.values.firstWhere(
        (e) => e.name == json['grouping']?.toString(),
        orElse: () => NotificationGrouping.byType,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    final typeSettingsJson = <String, bool>{};
    typeSettings.forEach((key, value) {
      typeSettingsJson[key.name] = value;
    });

    return {
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'emailNotificationsEnabled': emailNotificationsEnabled,
      'smsNotificationsEnabled': smsNotificationsEnabled,
      'typeSettings': typeSettingsJson,
      'sound': sound.name,
      'vibrationEnabled': vibrationEnabled,
      'ledEnabled': ledEnabled,
      'quietHoursStart': quietHoursStart,
      'quietHoursEnd': quietHoursEnd,
      'quietHoursEnabled': quietHoursEnabled,
      'showOnLockScreen': showOnLockScreen,
      'showPreview': showPreview,
      'grouping': grouping.name,
    };
  }
}

enum NotificationSound {
  defaultSound,
  silent,
  chime,
  bell,
  notification,
  custom
}

enum NotificationGrouping {
  none,
  byType,
  byUser,
  smart
}

class FCMToken {
  final String token;
  final String deviceId;
  final String platform;
  final DateTime createdAt;
  final DateTime lastUsed;
  final bool isActive;

  const FCMToken({
    required this.token,
    required this.deviceId,
    required this.platform,
    required this.createdAt,
    required this.lastUsed,
    required this.isActive,
  });

  factory FCMToken.fromJson(Map<String, dynamic> json) {
    return FCMToken(
      token: json['token']?.toString() ?? '',
      deviceId: json['deviceId']?.toString() ?? '',
      platform: json['platform']?.toString() ?? '',
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      lastUsed: DateTime.tryParse(json['lastUsed']?.toString() ?? '') ?? DateTime.now(),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'token': token,
      'deviceId': deviceId,
      'platform': platform,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'isActive': isActive,
    };
  }
}

class NotificationTemplate {
  final String id;
  final String name;
  final NotificationType type;
  final String titleTemplate;
  final String bodyTemplate;
  final Map<String, dynamic>? defaultData;
  final bool isActive;
  final String? imageUrl;
  final List<NotificationAction>? actions;

  const NotificationTemplate({
    required this.id,
    required this.name,
    required this.type,
    required this.titleTemplate,
    required this.bodyTemplate,
    this.defaultData,
    required this.isActive,
    this.imageUrl,
    this.actions,
  });

  factory NotificationTemplate.fromJson(Map<String, dynamic> json) {
    List<NotificationAction>? actions;
    if (json['actions'] != null) {
      actions = (json['actions'] as List)
          .map((action) => NotificationAction.fromJson(action))
          .toList();
    }

    return NotificationTemplate(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      type: NotificationType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => NotificationType.general,
      ),
      titleTemplate: json['titleTemplate']?.toString() ?? '',
      bodyTemplate: json['bodyTemplate']?.toString() ?? '',
      defaultData: json['defaultData'] as Map<String, dynamic>?,
      isActive: json['isActive'] ?? true,
      imageUrl: json['imageUrl']?.toString(),
      actions: actions,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'titleTemplate': titleTemplate,
      'bodyTemplate': bodyTemplate,
      'defaultData': defaultData,
      'isActive': isActive,
      'imageUrl': imageUrl,
      'actions': actions?.map((action) => action.toJson()).toList(),
    };
  }
}

class NotificationAction {
  final String id;
  final String title;
  final String? icon;
  final String? actionUrl;
  final Map<String, dynamic>? data;

  const NotificationAction({
    required this.id,
    required this.title,
    this.icon,
    this.actionUrl,
    this.data,
  });

  factory NotificationAction.fromJson(Map<String, dynamic> json) {
    return NotificationAction(
      id: json['id']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      icon: json['icon']?.toString(),
      actionUrl: json['actionUrl']?.toString(),
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'icon': icon,
      'actionUrl': actionUrl,
      'data': data,
    };
  }
}

class NotificationStatistics {
  final int totalSent;
  final int totalDelivered;
  final int totalOpened;
  final int totalClicked;
  final double deliveryRate;
  final double openRate;
  final double clickRate;
  final Map<NotificationType, int> sentByType;
  final Map<String, int> sentByDay;
  final Map<String, int> sentByHour;

  const NotificationStatistics({
    required this.totalSent,
    required this.totalDelivered,
    required this.totalOpened,
    required this.totalClicked,
    required this.deliveryRate,
    required this.openRate,
    required this.clickRate,
    required this.sentByType,
    required this.sentByDay,
    required this.sentByHour,
  });

  factory NotificationStatistics.fromJson(Map<String, dynamic> json) {
    final sentByTypeMap = <NotificationType, int>{};
    if (json['sentByType'] != null) {
      (json['sentByType'] as Map<String, dynamic>).forEach((key, value) {
        final type = NotificationType.values.firstWhere(
          (e) => e.name == key,
          orElse: () => NotificationType.general,
        );
        sentByTypeMap[type] = value?.toInt() ?? 0;
      });
    }

    return NotificationStatistics(
      totalSent: json['totalSent']?.toInt() ?? 0,
      totalDelivered: json['totalDelivered']?.toInt() ?? 0,
      totalOpened: json['totalOpened']?.toInt() ?? 0,
      totalClicked: json['totalClicked']?.toInt() ?? 0,
      deliveryRate: json['deliveryRate']?.toDouble() ?? 0.0,
      openRate: json['openRate']?.toDouble() ?? 0.0,
      clickRate: json['clickRate']?.toDouble() ?? 0.0,
      sentByType: sentByTypeMap,
      sentByDay: Map<String, int>.from(json['sentByDay'] ?? {}),
      sentByHour: Map<String, int>.from(json['sentByHour'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    final sentByTypeJson = <String, int>{};
    sentByType.forEach((key, value) {
      sentByTypeJson[key.name] = value;
    });

    return {
      'totalSent': totalSent,
      'totalDelivered': totalDelivered,
      'totalOpened': totalOpened,
      'totalClicked': totalClicked,
      'deliveryRate': deliveryRate,
      'openRate': openRate,
      'clickRate': clickRate,
      'sentByType': sentByTypeJson,
      'sentByDay': sentByDay,
      'sentByHour': sentByHour,
    };
  }
}
