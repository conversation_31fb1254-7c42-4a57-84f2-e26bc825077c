import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../design/glassmorphism_system.dart';
import '../gestures/gesture_navigation.dart';
import '../effects/premium_visual_effects.dart' as effects;
import '../../app/theme.dart';

/// 🚀 INNOVATION FEATURES SYSTEM - Competitive Edge Technologies
/// Features: AI-powered interactions, smart animations, adaptive UI, predictive gestures
/// Integrates all advanced systems to create a revolutionary matrimony app experience

/// Innovation Features Provider
final innovationFeaturesProvider = StateNotifierProvider<InnovationFeaturesNotifier, InnovationFeaturesState>((ref) {
  return InnovationFeaturesNotifier();
});

/// Innovation Features State
class InnovationFeaturesState {
  final bool smartAnimationsEnabled;
  final bool adaptiveUIEnabled;
  final bool predictiveGesturesEnabled;
  final bool immersiveEffectsEnabled;
  final bool personalizedThemingEnabled;
  final double userEngagementScore;
  final Map<String, dynamic> userPreferences;

  const InnovationFeaturesState({
    this.smartAnimationsEnabled = true,
    this.adaptiveUIEnabled = true,
    this.predictiveGesturesEnabled = true,
    this.immersiveEffectsEnabled = true,
    this.personalizedThemingEnabled = true,
    this.userEngagementScore = 0.0,
    this.userPreferences = const {},
  });

  InnovationFeaturesState copyWith({
    bool? smartAnimationsEnabled,
    bool? adaptiveUIEnabled,
    bool? predictiveGesturesEnabled,
    bool? immersiveEffectsEnabled,
    bool? personalizedThemingEnabled,
    double? userEngagementScore,
    Map<String, dynamic>? userPreferences,
  }) {
    return InnovationFeaturesState(
      smartAnimationsEnabled: smartAnimationsEnabled ?? this.smartAnimationsEnabled,
      adaptiveUIEnabled: adaptiveUIEnabled ?? this.adaptiveUIEnabled,
      predictiveGesturesEnabled: predictiveGesturesEnabled ?? this.predictiveGesturesEnabled,
      immersiveEffectsEnabled: immersiveEffectsEnabled ?? this.immersiveEffectsEnabled,
      personalizedThemingEnabled: personalizedThemingEnabled ?? this.personalizedThemingEnabled,
      userEngagementScore: userEngagementScore ?? this.userEngagementScore,
      userPreferences: userPreferences ?? this.userPreferences,
    );
  }
}

/// Innovation Features Notifier
class InnovationFeaturesNotifier extends StateNotifier<InnovationFeaturesState> {
  InnovationFeaturesNotifier() : super(const InnovationFeaturesState()) {
    _initializeFeatures();
  }

  void _initializeFeatures() {
    // Initialize AI-powered features based on user behavior
    _analyzeUserBehavior();
    _setupAdaptiveUI();
    _enablePredictiveGestures();
  }

  void _analyzeUserBehavior() {
    // Simulate AI analysis of user behavior
    // In real implementation, this would analyze user interaction patterns
    state = state.copyWith(userEngagementScore: 0.75);
  }

  void _setupAdaptiveUI() {
    // Configure adaptive UI based on user preferences and device capabilities
    final preferences = {
      'preferredAnimationSpeed': 'normal',
      'visualEffectsIntensity': 'high',
      'gesturesSensitivity': 'medium',
    };
    state = state.copyWith(userPreferences: preferences);
  }

  void _enablePredictiveGestures() {
    // Enable predictive gesture recognition
    state = state.copyWith(predictiveGesturesEnabled: true);
  }

  void updateFeature(String feature, bool enabled) {
    switch (feature) {
      case 'smartAnimations':
        state = state.copyWith(smartAnimationsEnabled: enabled);
        break;
      case 'adaptiveUI':
        state = state.copyWith(adaptiveUIEnabled: enabled);
        break;
      case 'predictiveGestures':
        state = state.copyWith(predictiveGesturesEnabled: enabled);
        break;
      case 'immersiveEffects':
        state = state.copyWith(immersiveEffectsEnabled: enabled);
        break;
      case 'personalizedTheming':
        state = state.copyWith(personalizedThemingEnabled: enabled);
        break;
    }
  }
}

/// Smart Profile Card with All Innovation Features
class SmartProfileCard extends ConsumerStatefulWidget {
  final Map<String, dynamic> profileData;
  final VoidCallback? onTap;
  final VoidCallback? onLike;
  final VoidCallback? onPass;

  const SmartProfileCard({
    super.key,
    required this.profileData,
    this.onTap,
    this.onLike,
    this.onPass,
  });

  @override
  ConsumerState<SmartProfileCard> createState() => _SmartProfileCardState();
}

class _SmartProfileCardState extends ConsumerState<SmartProfileCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _likeController;
  bool _isHovered = false;
  bool _isLiked = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _likeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _likeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final innovationState = ref.watch(innovationFeaturesProvider);

    return SwipeNavigationWrapper(
      onSwipeLeft: widget.onPass,
      onSwipeRight: widget.onLike,
      enableHapticFeedback: innovationState.predictiveGesturesEnabled,
      child: effects.Card3DEffect(
        enableTilt: innovationState.immersiveEffectsEnabled,
        child: GlassmorphismCard(
          onTap: widget.onTap,
          child: effects.ParticleEffect(
            isActive: innovationState.immersiveEffectsEnabled && _isHovered,
            particleCount: 15,
            particleColor: AppColors.primary.withValues(alpha: 0.3),
            child: Container(
              height: 400,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildProfileHeader(),
                  const SizedBox(height: 16),
                  _buildProfileImage(),
                  const SizedBox(height: 16),
                  _buildProfileInfo(),
                  const Spacer(),
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.2);
  }

  Widget _buildProfileHeader() {
    return Row(
      children: [
        effects.ShimmerEffect(
          enabled: false,
          child: Container(
            width: 12,
            height: 12,
            decoration: const BoxDecoration(
              color: Colors.green,
              shape: BoxShape.circle,
            ),
          ),
        ),
        const SizedBox(width: 8),
        const Text(
          'Online',
          style: TextStyle(
            color: Colors.green,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const Spacer(),
        GestureDetector(
          onTap: () {
            setState(() {
              _isLiked = !_isLiked;
            });
            if (_isLiked) {
              _likeController.forward();
            } else {
              _likeController.reverse();
            }
          },
          child: AnimatedBuilder(
            animation: _likeController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + _likeController.value * 0.3,
                child: Icon(
                  _isLiked ? Icons.favorite : Icons.favorite_border,
                  color: _isLiked ? Colors.red : Colors.grey,
                  size: 24,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfileImage() {
    return Center(
      child: GestureDetector(
        onTapDown: (_) {
          setState(() => _isHovered = true);
          _hoverController.forward();
        },
        onTapUp: (_) {
          setState(() => _isHovered = false);
          _hoverController.reverse();
        },
        onTapCancel: () {
          setState(() => _isHovered = false);
          _hoverController.reverse();
        },
        child: AnimatedBuilder(
          animation: _hoverController,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 + _hoverController.value * 0.05,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(60),
                  gradient: AppGradients.primaryGradient,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withValues(alpha: 0.3 + _hoverController.value * 0.2),
                      blurRadius: 20 + _hoverController.value * 10,
                      spreadRadius: 5 + _hoverController.value * 5,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(60),
                  child: Image.network(
                    widget.profileData['imageUrl'] ?? 'https://via.placeholder.com/120',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(
                        Icons.person,
                        size: 60,
                        color: Colors.white,
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.profileData['name'] ?? 'Unknown',
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ).animate().fadeIn(delay: 200.ms).slideX(begin: -0.2),
        const SizedBox(height: 4),
        Text(
          '${widget.profileData['age'] ?? 'N/A'} years • ${widget.profileData['location'] ?? 'Unknown'}',
          style: const TextStyle(
            fontSize: 16,
            color: AppColors.textSecondary,
          ),
        ).animate().fadeIn(delay: 400.ms).slideX(begin: -0.2),
        const SizedBox(height: 8),
        Text(
          widget.profileData['profession'] ?? 'Professional',
          style: const TextStyle(
            fontSize: 14,
            color: AppColors.textSecondary,
          ),
        ).animate().fadeIn(delay: 600.ms).slideX(begin: -0.2),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: effects.RippleEffect(
            rippleColor: Colors.red,
            onTap: widget.onPass,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.close, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Pass',
                    style: TextStyle(
                      color: Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: effects.RippleEffect(
            rippleColor: AppColors.primary,
            onTap: widget.onLike,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                gradient: AppGradients.primaryGradient,
                borderRadius: BorderRadius.circular(25),
                boxShadow: AppShadows.buttonShadow,
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.favorite, color: Colors.white, size: 20),
                  SizedBox(width: 8),
                  Text(
                    'Like',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    ).animate().fadeIn(delay: 800.ms).slideY(begin: 0.3);
  }
}

/// Innovation Dashboard Widget
class InnovationDashboard extends ConsumerWidget {
  const InnovationDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final innovationState = ref.watch(innovationFeaturesProvider);

    return GlassmorphismCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Innovation Features',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildFeatureToggle(
            'Smart Animations',
            'AI-powered motion design',
            innovationState.smartAnimationsEnabled,
            (value) => ref.read(innovationFeaturesProvider.notifier).updateFeature('smartAnimations', value),
          ),
          _buildFeatureToggle(
            'Adaptive UI',
            'Interface adapts to your usage',
            innovationState.adaptiveUIEnabled,
            (value) => ref.read(innovationFeaturesProvider.notifier).updateFeature('adaptiveUI', value),
          ),
          _buildFeatureToggle(
            'Predictive Gestures',
            'Anticipates your actions',
            innovationState.predictiveGesturesEnabled,
            (value) => ref.read(innovationFeaturesProvider.notifier).updateFeature('predictiveGestures', value),
          ),
          _buildFeatureToggle(
            'Immersive Effects',
            'Premium visual enhancements',
            innovationState.immersiveEffectsEnabled,
            (value) => ref.read(innovationFeaturesProvider.notifier).updateFeature('immersiveEffects', value),
          ),
          _buildFeatureToggle(
            'Personalized Theming',
            'Themes that match your mood',
            innovationState.personalizedThemingEnabled,
            (value) => ref.read(innovationFeaturesProvider.notifier).updateFeature('personalizedTheming', value),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureToggle(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }
}

/// Enhanced App Experience Widget
class EnhancedAppExperience extends ConsumerWidget {
  final Widget child;

  const EnhancedAppExperience({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final innovationState = ref.watch(innovationFeaturesProvider);

    Widget enhancedChild = child;

    // Apply smart animations
    if (innovationState.smartAnimationsEnabled) {
      enhancedChild = enhancedChild.animate().fadeIn(duration: 300.ms);
    }

    // Apply immersive effects
    if (innovationState.immersiveEffectsEnabled) {
      enhancedChild = effects.ParticleEffect(
        isActive: false, // Activate based on user interaction
        child: enhancedChild,
      );
    }

    // Apply predictive gestures
    if (innovationState.predictiveGesturesEnabled) {
      enhancedChild = SwipeNavigationWrapper(
        child: enhancedChild,
      );
    }

    return enhancedChild;
  }
}
