import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../app/theme.dart';


/// 📝 ADD NOTE DIALOG - Beautiful Note Management Interface
/// Features: Add/Edit Notes, Character Counter, Quick Templates
class AddNoteDialog extends StatefulWidget {
  final String? initialNote;
  final Function(String?) onSave;
  final String title;
  final String hint;

  const AddNoteDialog({
    super.key,
    this.initialNote,
    required this.onSave,
    this.title = 'Add Note',
    this.hint = 'Add a personal note about this profile...',
  });

  @override
  State<AddNoteDialog> createState() => _AddNoteDialogState();
}

class _AddNoteDialogState extends State<AddNoteDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late TextEditingController _noteController;
  final FocusNode _focusNode = FocusNode();
  
  static const int maxLength = 500;
  static const List<String> quickTemplates = [
    'Interested in this profile',
    'Good match for family values',
    'Similar educational background',
    'Compatible lifestyle',
    'Impressive career profile',
    'Good communication skills',
    'Family-oriented person',
    'Shared interests and hobbies',
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _noteController = TextEditingController(text: widget.initialNote ?? '');
    
    // Auto-focus the text field
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _noteController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: const EdgeInsets.all(16),
      child: Container(
        constraints: const BoxConstraints(maxHeight: 500),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildNoteField(),
                    const SizedBox(height: 16),
                    _buildQuickTemplates(),
                  ],
                ),
              ),
            ),
            _buildActionButtons(),
          ],
        ),
      ).animate(controller: _animationController)
       .scale(curve: Curves.elasticOut)
       .fadeIn(),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.edit_note,
              color: AppColors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.title,
                  style: AppTextStyles.h2.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Add personal notes for better organization',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.close),
            color: AppColors.textSecondary,
          ),
        ],
      ),
    ).animate(controller: _animationController)
     .slideY(begin: -1, curve: Curves.elasticOut)
     .fadeIn();
  }

  Widget _buildNoteField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Note',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.primary.withValues(alpha: 0.3),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withValues(alpha: 0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _noteController,
            focusNode: _focusNode,
            maxLines: 6,
            maxLength: maxLength,
            decoration: InputDecoration(
              hintText: widget.hint,
              hintStyle: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary.withValues(alpha: 0.7),
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[50],
              contentPadding: const EdgeInsets.all(16),
              counterStyle: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textPrimary,
            ),
            onChanged: (value) => setState(() {}),
          ),
        ),
      ],
    ).animate(controller: _animationController)
     .slideX(begin: -1, delay: 200.ms, curve: Curves.elasticOut)
     .fadeIn(delay: 200.ms);
  }

  Widget _buildQuickTemplates() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.lightbulb_outline,
              size: 16,
              color: Colors.amber[600],
            ),
            const SizedBox(width: 6),
            Text(
              'Quick Templates',
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: quickTemplates.map((template) {
            return InkWell(
              onTap: () => _addTemplate(template),
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.add,
                      size: 14,
                      color: AppColors.primary,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      template,
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.blue.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 16,
                color: Colors.blue[600],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Tap on templates to add them to your note. You can edit and customize as needed.',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: Colors.blue[700],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    ).animate(controller: _animationController)
     .slideX(begin: 1, delay: 400.ms, curve: Curves.elasticOut)
     .fadeIn(delay: 400.ms);
  }

  Widget _buildActionButtons() {
    final hasChanges = _noteController.text.trim() != (widget.initialNote ?? '');
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.textSecondary,
                side: const BorderSide(color: AppColors.textSecondary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('Cancel'),
            ),
          ),
          const SizedBox(width: 12),
          if (widget.initialNote?.isNotEmpty == true) ...[
            OutlinedButton(
              onPressed: () {
                widget.onSave(null);
                Navigator.pop(context);
              },
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              ),
              child: const Text('Remove'),
            ),
            const SizedBox(width: 12),
          ],
          Expanded(
            flex: 2,
            child: ElevatedButton(
              onPressed: hasChanges ? _saveNote : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: hasChanges ? AppColors.primary : Colors.grey,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.save, size: 16),
                  const SizedBox(width: 6),
                  Text(widget.initialNote?.isNotEmpty == true ? 'Update' : 'Save'),
                ],
              ),
            ),
          ),
        ],
      ),
    ).animate(controller: _animationController)
     .slideY(begin: 1, delay: 600.ms, curve: Curves.elasticOut)
     .fadeIn(delay: 600.ms);
  }

  void _addTemplate(String template) {
    final currentText = _noteController.text;
    final newText = currentText.isEmpty 
        ? template 
        : '$currentText\n• $template';
    
    if (newText.length <= maxLength) {
      _noteController.text = newText;
      _noteController.selection = TextSelection.fromPosition(
        TextPosition(offset: newText.length),
      );
      setState(() {});
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Note cannot exceed 500 characters'),
          backgroundColor: Colors.orange,
        ),
      );
    }
  }

  void _saveNote() {
    final note = _noteController.text.trim();
    widget.onSave(note.isEmpty ? null : note);
    Navigator.pop(context);
  }
}

/// 📝 QUICK NOTE DIALOG - Simplified version for quick notes
class QuickNoteDialog extends StatelessWidget {
  final String? initialNote;
  final Function(String?) onSave;

  const QuickNoteDialog({
    super.key,
    this.initialNote,
    required this.onSave,
  });

  @override
  Widget build(BuildContext context) {
    final controller = TextEditingController(text: initialNote ?? '');
    
    return AlertDialog(
      title: const Text('Quick Note'),
      content: TextField(
        controller: controller,
        maxLines: 3,
        maxLength: 200,
        decoration: const InputDecoration(
          hintText: 'Add a quick note...',
          border: OutlineInputBorder(),
        ),
        autofocus: true,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        if (initialNote?.isNotEmpty == true)
          TextButton(
            onPressed: () {
              onSave(null);
              Navigator.pop(context);
            },
            child: const Text('Remove', style: TextStyle(color: Colors.red)),
          ),
        ElevatedButton(
          onPressed: () {
            final note = controller.text.trim();
            onSave(note.isEmpty ? null : note);
            Navigator.pop(context);
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
}
