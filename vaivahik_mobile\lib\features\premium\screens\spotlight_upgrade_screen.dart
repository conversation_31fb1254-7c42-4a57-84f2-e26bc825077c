import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';


import '../../../core/services/spotlight_service.dart';
import '../../../core/models/spotlight_models.dart';
import '../../../core/providers/auth_provider.dart';

/// ⭐ SPOTLIGHT UPGRADE SYSTEM - Premium Features
/// Features: Profile Boosting, Featured Listings, Priority Matching, Premium Badges

class SpotlightUpgradeScreen extends ConsumerStatefulWidget {
  const SpotlightUpgradeScreen({super.key});

  @override
  ConsumerState<SpotlightUpgradeScreen> createState() => _SpotlightUpgradeScreenState();
}

class _SpotlightUpgradeScreenState extends ConsumerState<SpotlightUpgradeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _shimmerController;

  final SpotlightService _spotlightService = SpotlightService();

  int _selectedPlanIndex = 1; // Default to most popular plan
  bool _isLoading = false;

  final List<SpotlightPlan> _spotlightPlans = [
    const SpotlightPlan(
      id: 'basic_spotlight',
      name: 'Basic Spotlight',
      duration: '7 Days',
      originalPrice: 299,
      discountedPrice: 199,
      color: Colors.blue,
      gradient: LinearGradient(colors: [Colors.blue, Colors.blueAccent]),
      features: [
        'Profile visibility boost',
        '2x more profile views',
        'Priority in search results',
        'Basic analytics',
      ],
      badge: null,
      isPopular: false,
    ),
    const SpotlightPlan(
      id: 'premium_spotlight',
      name: 'Premium Spotlight',
      duration: '15 Days',
      originalPrice: 599,
      discountedPrice: 399,
      color: Colors.purple,
      gradient: LinearGradient(colors: [Colors.purple, Colors.purpleAccent]),
      features: [
        'Everything in Basic',
        '5x more profile views',
        'Featured in top matches',
        'Premium badge display',
        'Advanced analytics',
        'Priority customer support',
      ],
      badge: 'MOST POPULAR',
      isPopular: true,
    ),
    const SpotlightPlan(
      id: 'super_spotlight',
      name: 'Super Spotlight',
      duration: '30 Days',
      originalPrice: 999,
      discountedPrice: 699,
      color: Colors.amber,
      gradient: LinearGradient(colors: [Colors.amber, Colors.orange]),
      features: [
        'Everything in Premium',
        '10x more profile views',
        'Top featured placement',
        'Gold premium badge',
        'Detailed match analytics',
        'Personal relationship advisor',
        'Unlimited rewinds',
      ],
      badge: 'BEST VALUE',
      isPopular: false,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _animationController.forward();
    _shimmerController.repeat();
    _loadSpotlightData();
  }

  Future<void> _loadSpotlightData() async {
    try {
      await _spotlightService.getSpotlightFeatures();
      await _spotlightService.getCurrentSpotlight();

      if (mounted) {
        setState(() {
          // Features loaded successfully
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading spotlight data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black,
              Colors.purple.withValues(alpha: 0.2),
              Colors.black,
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(),
              Expanded(
                child: _buildPlansList(),
              ),
              _buildBottomActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'LIMITED TIME OFFER',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  Colors.yellow.withValues(alpha: 0.3),
                  Colors.transparent,
                ],
              ),
              borderRadius: BorderRadius.circular(100),
            ),
            child: Icon(
              Icons.star,
              size: 60,
              color: Colors.yellow[400],
            ),
          ).animate(controller: _shimmerController)
           .shimmer(duration: 2000.ms)
           .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.2, 1.2)),
          const SizedBox(height: 16),
          const Text(
            'Get Spotlight',
            style: TextStyle(
              color: Colors.white,
              fontSize: 32,
              fontWeight: FontWeight.bold,
            ),
          ).animate()
           .fadeIn(delay: 200.ms)
           .slideY(begin: 0.3),
          const SizedBox(height: 8),
          const Text(
            'Boost your profile visibility and get\n10x more matches instantly!',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ).animate()
           .fadeIn(delay: 400.ms)
           .slideY(begin: 0.3),
        ],
      ),
    );
  }

  Widget _buildPlansList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _spotlightPlans.length,
      itemBuilder: (context, index) {
        final plan = _spotlightPlans[index];
        final isSelected = _selectedPlanIndex == index;
        
        return _buildPlanCard(plan, isSelected, index);
      },
    );
  }

  Widget _buildPlanCard(SpotlightPlan plan, bool isSelected, int index) {
    return GestureDetector(
      onTap: () => setState(() => _selectedPlanIndex = index),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          gradient: isSelected ? plan.gradient : null,
          color: isSelected ? null : Colors.grey[900],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? Colors.white.withValues(alpha: 0.3) : Colors.grey[800]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: plan.color.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ] : null,
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.star,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              plan.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              plan.duration,
                              style: const TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          if (plan.originalPrice > plan.discountedPrice)
                            Text(
                              '₹${plan.originalPrice}',
                              style: const TextStyle(
                                color: Colors.white54,
                                fontSize: 14,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          Text(
                            '₹${plan.discountedPrice}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  ...plan.features.map((feature) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.check_circle,
                          color: Colors.white,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            feature,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
            
            // Popular/Best Value Badge
            if (plan.badge != null)
              Positioned(
                top: -1,
                right: 20,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: plan.isPopular ? Colors.red : Colors.green,
                    borderRadius: const BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Text(
                    plan.badge!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            
            // Selection indicator
            if (isSelected)
              Positioned(
                top: 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.check,
                    color: plan.color,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    ).animate()
     .slideX(begin: 1, delay: (index * 100).ms)
     .fadeIn(delay: (index * 100).ms);
  }

  Widget _buildBottomActions() {
    final selectedPlan = _spotlightPlans[_selectedPlanIndex];
    final savings = selectedPlan.originalPrice - selectedPlan.discountedPrice;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (savings > 0)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.savings,
                    color: Colors.green[400],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'You save ₹$savings with this plan!',
                    style: TextStyle(
                      color: Colors.green[400],
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isLoading ? null : () => _purchaseSpotlight(selectedPlan),
              style: ElevatedButton.styleFrom(
                backgroundColor: selectedPlan.color,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      'Get ${selectedPlan.name} - ₹${selectedPlan.discountedPrice}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.security,
                color: Colors.grey[400],
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                'Secure payment • Cancel anytime',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 1, delay: 800.ms)
     .fadeIn(delay: 800.ms);
  }

  Future<void> _purchaseSpotlight(SpotlightPlan plan) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authState = ref.read(authProvider);
      final user = authState.user;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Convert SpotlightPlan to SpotlightFeature for service
      final feature = SpotlightFeature(
        id: plan.id,
        name: plan.name,
        description: 'Spotlight feature - ${plan.duration}',
        price: plan.originalPrice.toDouble(),
        discountedPrice: plan.discountedPrice.toDouble(),
        durationHours: _getDurationHours(plan.duration),
        defaultCount: 1,
        isActive: true,
        benefits: plan.features,
        icon: 'TrendingUp',
        popularity: 85,
        successRate: 90,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _spotlightService.purchaseSpotlight(
        feature: feature,
        userDetails: {
          'name': user.fullName,
          'email': user.email,
          'phone': user.phone,
        },
        onSuccess: (spotlight) {
          if (mounted) {
            setState(() {
              // Spotlight activated successfully
            });
            _showSuccessDialog(plan);
          }
        },
        onError: (error) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Payment failed: $error'),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  int _getDurationHours(String duration) {
    if (duration.contains('7 Days')) return 168;
    if (duration.contains('15 Days')) return 360;
    if (duration.contains('30 Days')) return 720;
    return 168; // Default to 7 days
  }

  void _showSuccessDialog(SpotlightPlan plan) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: plan.gradient,
                  borderRadius: BorderRadius.circular(50),
                ),
                child: const Icon(
                  Icons.star,
                  color: Colors.white,
                  size: 40,
                ),
              ).animate()
               .scale(curve: Curves.elasticOut)
               .shimmer(delay: 500.ms),
              const SizedBox(height: 20),
              const Text(
                'Spotlight Activated!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Your ${plan.name} is now active.\nGet ready for more matches!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // Close dialog
                    Navigator.of(context).pop(); // Close spotlight screen
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: plan.color,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Continue',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SpotlightPlan {
  final String id;
  final String name;
  final String duration;
  final int originalPrice;
  final int discountedPrice;
  final Color color;
  final Gradient gradient;
  final List<String> features;
  final String? badge;
  final bool isPopular;

  const SpotlightPlan({
    required this.id,
    required this.name,
    required this.duration,
    required this.originalPrice,
    required this.discountedPrice,
    required this.color,
    required this.gradient,
    required this.features,
    this.badge,
    required this.isPopular,
  });
}
