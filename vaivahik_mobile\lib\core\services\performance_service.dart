import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../api/api_client.dart';

/// Performance monitoring service for tracking app performance metrics
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  factory PerformanceService() => _instance;
  PerformanceService._internal();

  final ApiClient _apiClient = ApiClient();
  Timer? _metricsTimer;
  final Map<String, List<double>> _metrics = {};
  final Map<String, int> _counters = {};
  final List<Map<String, dynamic>> _events = [];
  
  // Configuration
  static const int _maxEvents = 100;
  static const int _metricsInterval = 60; // seconds
  static const double _slowThreshold = 1000.0; // milliseconds
  
  bool _isInitialized = false;
  bool _isEnabled = true;

  /// Initialize performance monitoring
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      _isEnabled = prefs.getBool('performance_monitoring_enabled') ?? true;
      
      if (_isEnabled) {
        _startMetricsCollection();
        _initializeSystemMetrics();
        debugPrint('PerformanceService: Initialized successfully');
      }
      
      _isInitialized = true;
    } catch (e) {
      debugPrint('PerformanceService: Failed to initialize - $e');
    }
  }

  /// Start periodic metrics collection
  void _startMetricsCollection() {
    _metricsTimer?.cancel();
    _metricsTimer = Timer.periodic(
      const Duration(seconds: _metricsInterval),
      (_) => _collectAndSendMetrics(),
    );
  }

  /// Initialize system-level metrics monitoring
  void _initializeSystemMetrics() {
    // Monitor memory usage
    Timer.periodic(const Duration(seconds: 30), (_) {
      _recordMemoryUsage();
    });
  }

  /// Record API request performance
  void recordApiRequest({
    required String endpoint,
    required String method,
    required int responseTime,
    required int statusCode,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isEnabled) return;

    try {
      final key = '$method $endpoint';
      _metrics[key] ??= [];
      _metrics[key]!.add(responseTime.toDouble());
      
      // Keep only recent metrics
      if (_metrics[key]!.length > 50) {
        _metrics[key]!.removeAt(0);
      }
      
      // Count requests
      _counters['${key}_count'] = (_counters['${key}_count'] ?? 0) + 1;
      
      // Track errors
      if (statusCode >= 400) {
        _counters['${key}_errors'] = (_counters['${key}_errors'] ?? 0) + 1;
      }
      
      // Record slow requests
      if (responseTime > _slowThreshold) {
        _recordEvent('slow_api_request', {
          'endpoint': endpoint,
          'method': method,
          'response_time': responseTime,
          'status_code': statusCode,
          ...?additionalData,
        });
      }
    } catch (e) {
      debugPrint('PerformanceService: Error recording API request - $e');
    }
  }

  /// Record screen navigation performance
  void recordScreenNavigation({
    required String fromScreen,
    required String toScreen,
    required int navigationTime,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isEnabled) return;

    try {
      final key = 'navigation_${fromScreen}_to_$toScreen';
      _metrics[key] ??= [];
      _metrics[key]!.add(navigationTime.toDouble());
      
      if (_metrics[key]!.length > 20) {
        _metrics[key]!.removeAt(0);
      }
      
      _counters['navigation_count'] = (_counters['navigation_count'] ?? 0) + 1;
      
      if (navigationTime > 2000) { // 2 seconds threshold
        _recordEvent('slow_navigation', {
          'from_screen': fromScreen,
          'to_screen': toScreen,
          'navigation_time': navigationTime,
          ...?additionalData,
        });
      }
    } catch (e) {
      debugPrint('PerformanceService: Error recording navigation - $e');
    }
  }

  /// Record widget build performance
  void recordWidgetBuild({
    required String widgetName,
    required int buildTime,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isEnabled) return;

    try {
      final key = 'widget_build_$widgetName';
      _metrics[key] ??= [];
      _metrics[key]!.add(buildTime.toDouble());
      
      if (_metrics[key]!.length > 30) {
        _metrics[key]!.removeAt(0);
      }
      
      if (buildTime > 100) { // 100ms threshold
        _recordEvent('slow_widget_build', {
          'widget_name': widgetName,
          'build_time': buildTime,
          ...?additionalData,
        });
      }
    } catch (e) {
      debugPrint('PerformanceService: Error recording widget build - $e');
    }
  }

  /// Record custom performance metric
  void recordCustomMetric({
    required String name,
    required double value,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isEnabled) return;

    try {
      _metrics[name] ??= [];
      _metrics[name]!.add(value);
      
      if (_metrics[name]!.length > 50) {
        _metrics[name]!.removeAt(0);
      }
      
      _recordEvent('custom_metric', {
        'name': name,
        'value': value,
        ...?additionalData,
      });
    } catch (e) {
      debugPrint('PerformanceService: Error recording custom metric - $e');
    }
  }

  /// Record memory usage
  void _recordMemoryUsage() {
    try {
      // This is a simplified approach - in production you might use
      // platform-specific methods to get accurate memory usage
      const key = 'memory_usage';
      _metrics[key] ??= [];
      
      // Placeholder for memory usage - would need platform-specific implementation
      final memoryUsage = _getApproximateMemoryUsage();
      _metrics[key]!.add(memoryUsage);
      
      if (_metrics[key]!.length > 20) {
        _metrics[key]!.removeAt(0);
      }
    } catch (e) {
      debugPrint('PerformanceService: Error recording memory usage - $e');
    }
  }

  /// Get approximate memory usage (simplified)
  double _getApproximateMemoryUsage() {
    // This is a placeholder - in a real implementation you would use
    // platform channels to get actual memory usage from native code
    return 0.0;
  }

  /// Record performance event
  void _recordEvent(String type, Map<String, dynamic> data) {
    try {
      _events.add({
        'type': type,
        'timestamp': DateTime.now().toIso8601String(),
        'data': data,
      });
      
      // Keep only recent events
      if (_events.length > _maxEvents) {
        _events.removeAt(0);
      }
    } catch (e) {
      debugPrint('PerformanceService: Error recording event - $e');
    }
  }

  /// Collect and send metrics to backend
  Future<void> _collectAndSendMetrics() async {
    if (!_isEnabled || _metrics.isEmpty) return;

    try {
      final metricsData = _generateMetricsReport();
      
      // Send to backend
      await _apiClient.post('/analytics/performance', metricsData);
      
      debugPrint('PerformanceService: Metrics sent successfully');
    } catch (e) {
      debugPrint('PerformanceService: Failed to send metrics - $e');
    }
  }

  /// Generate comprehensive metrics report
  Map<String, dynamic> _generateMetricsReport() {
    final report = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'device_info': _getDeviceInfo(),
      'metrics': <String, dynamic>{},
      'counters': Map<String, dynamic>.from(_counters),
      'events': List<Map<String, dynamic>>.from(_events),
    };

    // Process metrics to get statistics
    _metrics.forEach((key, values) {
      if (values.isNotEmpty) {
        values.sort();
        report['metrics'][key] = {
          'count': values.length,
          'min': values.first,
          'max': values.last,
          'avg': values.reduce((a, b) => a + b) / values.length,
          'p50': values[values.length ~/ 2],
          'p95': values[(values.length * 0.95).floor()],
        };
      }
    });

    return report;
  }

  /// Get device information
  Map<String, dynamic> _getDeviceInfo() {
    return {
      'platform': Platform.operatingSystem,
      'version': Platform.operatingSystemVersion,
      'is_debug': kDebugMode,
    };
  }

  /// Get current performance summary
  Map<String, dynamic> getPerformanceSummary() {
    return _generateMetricsReport();
  }

  /// Enable/disable performance monitoring
  Future<void> setEnabled(bool enabled) async {
    _isEnabled = enabled;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('performance_monitoring_enabled', enabled);
    
    if (enabled && _isInitialized) {
      _startMetricsCollection();
    } else {
      _metricsTimer?.cancel();
    }
  }

  /// Clear all collected metrics
  void clearMetrics() {
    _metrics.clear();
    _counters.clear();
    _events.clear();
  }

  /// Dispose resources
  void dispose() {
    _metricsTimer?.cancel();
    clearMetrics();
  }
}

/// Performance monitoring mixin for widgets
mixin PerformanceMonitoringMixin {
  final PerformanceService _performanceService = PerformanceService();
  
  /// Measure widget build time
  T measureBuild<T>(String widgetName, T Function() buildFunction) {
    final stopwatch = Stopwatch()..start();
    final result = buildFunction();
    stopwatch.stop();
    
    _performanceService.recordWidgetBuild(
      widgetName: widgetName,
      buildTime: stopwatch.elapsedMilliseconds,
    );
    
    return result;
  }
  
  /// Measure async operation
  Future<T> measureAsync<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      
      _performanceService.recordCustomMetric(
        name: operationName,
        value: stopwatch.elapsedMilliseconds.toDouble(),
      );
      
      return result;
    } catch (e) {
      stopwatch.stop();
      
      _performanceService.recordCustomMetric(
        name: '${operationName}_error',
        value: stopwatch.elapsedMilliseconds.toDouble(),
        additionalData: {'error': e.toString()},
      );
      
      rethrow;
    }
  }
}
