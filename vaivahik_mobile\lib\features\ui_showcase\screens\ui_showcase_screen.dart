import 'package:flutter/material.dart';
import '../../../app/theme.dart';
import '../../../core/animations/app_animations.dart';
import '../../../core/responsive/responsive_helper.dart';
import '../../../core/widgets/enhanced_ui_components.dart';

/// UI Showcase Screen - Demonstrates the enhanced design system
/// Shows all the new components and animations in action
class UIShowcaseScreen extends StatefulWidget {
  const UIShowcaseScreen({super.key});

  @override
  State<UIShowcaseScreen> createState() => _UIShowcaseScreenState();
}

class _UIShowcaseScreenState extends State<UIShowcaseScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final TextEditingController _textController = TextEditingController();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppAnimations.slow,
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('UI Showcase'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: ResponsiveContainer(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              AppAnimations.cardEntranceAnimation(
                animation: _animationController,
                delay: 0,
                child: _buildHeaderSection(),
              ),
              
              SizedBox(height: ResponsiveSpacing.lg(context)),
              
              // Cards Section
              AppAnimations.cardEntranceAnimation(
                animation: _animationController,
                delay: 1,
                child: _buildCardsSection(),
              ),
              
              SizedBox(height: ResponsiveSpacing.lg(context)),
              
              // Buttons Section
              AppAnimations.cardEntranceAnimation(
                animation: _animationController,
                delay: 2,
                child: _buildButtonsSection(),
              ),
              
              SizedBox(height: ResponsiveSpacing.lg(context)),
              
              // Input Fields Section
              AppAnimations.cardEntranceAnimation(
                animation: _animationController,
                delay: 3,
                child: _buildInputFieldsSection(),
              ),
              
              SizedBox(height: ResponsiveSpacing.lg(context)),
              
              // Typography Section
              AppAnimations.cardEntranceAnimation(
                animation: _animationController,
                delay: 4,
                child: _buildTypographySection(),
              ),
              
              SizedBox(height: ResponsiveSpacing.xl(context)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      width: double.infinity,
      padding: ResponsiveHelper.responsivePadding(context),
      decoration: BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: BorderRadius.circular(20),
        boxShadow: AppShadows.elevatedShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Enhanced Design System',
            style: AppTextStyles.displayMedium.copyWith(color: Colors.white),
          ),
          SizedBox(height: ResponsiveSpacing.sm(context)),
          Text(
            'World-class UI components matching our website design',
            style: AppTextStyles.bodyLarge.copyWith(
              color: Colors.white.withValues(alpha: 0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Enhanced Cards', style: AppTextStyles.h2),
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        // Regular Card
        EnhancedCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Regular Card', style: AppTextStyles.h4),
              SizedBox(height: ResponsiveSpacing.sm(context)),
              const Text(
                'This is a regular card with subtle shadows and modern design.',
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
        
        // Premium Card
        EnhancedCard(
          isPremium: true,
          isElevated: true,
          onTap: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Premium card tapped!')),
            );
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Text('Premium Card', style: AppTextStyles.h4),
                  Spacer(),
                  Icon(Icons.star, color: AppColors.accent, size: 20),
                ],
              ),
              SizedBox(height: ResponsiveSpacing.sm(context)),
              const Text(
                'This is a premium card with gold gradient and enhanced shadows.',
                style: AppTextStyles.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButtonsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Enhanced Buttons', style: AppTextStyles.h2),
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        // Primary Button
        EnhancedButton(
          text: 'Primary Button',
          type: ButtonType.primary,
          size: ButtonSize.large,
          isFullWidth: true,
          onPressed: () {
            setState(() => _isLoading = !_isLoading);
            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) setState(() => _isLoading = false);
            });
          },
          isLoading: _isLoading,
        ),
        
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        // Button Row
        Row(
          children: [
            Expanded(
              child: EnhancedButton(
                text: 'Secondary',
                type: ButtonType.secondary,
                size: ButtonSize.medium,
                onPressed: () {},
              ),
            ),
            SizedBox(width: ResponsiveSpacing.sm(context)),
            Expanded(
              child: EnhancedButton(
                text: 'Accent',
                type: ButtonType.accent,
                size: ButtonSize.medium,
                icon: const Icon(Icons.star, size: 16),
                onPressed: () {},
              ),
            ),
          ],
        ),
        
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        // Small Buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            EnhancedButton(
              text: 'Small',
              type: ButtonType.primary,
              size: ButtonSize.small,
              onPressed: () {},
            ),
            EnhancedButton(
              text: 'Outline',
              type: ButtonType.outline,
              size: ButtonSize.small,
              onPressed: () {},
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInputFieldsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Enhanced Input Fields', style: AppTextStyles.h2),
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        EnhancedTextField(
          label: 'Full Name',
          hint: 'Enter your full name',
          controller: _textController,
          isRequired: true,
          prefixIcon: const Icon(Icons.person),
          validator: (value) {
            if (value?.isEmpty ?? true) {
              return 'Name is required';
            }
            return null;
          },
        ),
        
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        const EnhancedTextField(
          label: 'Password',
          hint: 'Enter your password',
          isPassword: true,
          isRequired: true,
          prefixIcon: Icon(Icons.lock),
        ),
        
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        const EnhancedTextField(
          label: 'Email',
          hint: 'Enter your email address',
          keyboardType: TextInputType.emailAddress,
          prefixIcon: Icon(Icons.email),
        ),
      ],
    );
  }

  Widget _buildTypographySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Typography System', style: AppTextStyles.h2),
        SizedBox(height: ResponsiveSpacing.md(context)),
        
        EnhancedCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Display Large', style: AppTextStyles.displayLarge),
              SizedBox(height: ResponsiveSpacing.sm(context)),
              const Text('Headline 1', style: AppTextStyles.h1),
              const Text('Headline 2', style: AppTextStyles.h2),
              const Text('Headline 3', style: AppTextStyles.h3),
              const Text('Headline 4', style: AppTextStyles.h4),
              SizedBox(height: ResponsiveSpacing.sm(context)),
              const Text('Body Large - This is the main body text style used throughout the app.',
                   style: AppTextStyles.bodyLarge),
              const Text('Body Medium - This is used for secondary content and descriptions.',
                   style: AppTextStyles.bodyMedium),
              const Text('Body Small - This is used for captions and helper text.',
                   style: AppTextStyles.bodySmall),
              SizedBox(height: ResponsiveSpacing.sm(context)),
              const Text('Premium Text', style: AppTextStyles.premium),
              const Text('Error Text', style: AppTextStyles.error),
              const Text('Caption Text', style: AppTextStyles.caption),
            ],
          ),
        ),
      ],
    );
  }
}
