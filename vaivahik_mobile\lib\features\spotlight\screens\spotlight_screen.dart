import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../app/theme.dart';
import '../../../core/widgets/enhanced_ui_components.dart';
import '../../../core/models/spotlight_models.dart';
import '../../../core/providers/spotlight_provider.dart';
import '../../../core/widgets/feature_access_widget.dart';
import '../../../core/services/feature_access_service.dart';

/// 🌟 COMPREHENSIVE SPOTLIGHT SYSTEM - World-Class Profile Boosting
/// Features: Profile Boost, Super Spotlight, Express Interest, Verification
/// Reuses existing website logic and API for complete feature parity

class SpotlightScreen extends ConsumerStatefulWidget {
  const SpotlightScreen({super.key});

  @override
  ConsumerState<SpotlightScreen> createState() => _SpotlightScreenState();
}

class _SpotlightScreenState extends ConsumerState<SpotlightScreen> 
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final spotlightFeaturesAsync = ref.watch(spotlightFeaturesProvider);
    final activeSpotlightsAsync = ref.watch(activeSpotlightsProvider);
    final spotlightState = ref.watch(spotlightProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildSpotlightTabs(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildFeaturesTab(spotlightFeaturesAsync, activeSpotlightsAsync),
                _buildActiveSpotlightsTab(activeSpotlightsAsync),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(spotlightState),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Spotlight Features',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: () => _showSpotlightInfo(),
          icon: const Icon(Icons.info_outline),
          tooltip: 'Spotlight Info',
        ),
        IconButton(
          onPressed: () => _showSpotlightHistory(),
          icon: const Icon(Icons.history),
          tooltip: 'Spotlight History',
        ),
      ],
    );
  }

  Widget _buildSpotlightTabs() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: AppGradients.primaryGradient,
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
        tabs: const [
          Tab(
            icon: Icon(Icons.flash_on, size: 20),
            text: 'Features',
          ),
          Tab(
            icon: Icon(Icons.trending_up, size: 20),
            text: 'Active',
          ),
          Tab(
            icon: Icon(Icons.analytics, size: 20),
            text: 'Analytics',
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2);
  }

  Widget _buildFeaturesTab(
    AsyncValue<List<SpotlightFeature>> featuresAsync,
    AsyncValue<List<UserSpotlight>> activeSpotlightsAsync,
  ) {
    return featuresAsync.when(
      data: (features) => _buildFeaturesContent(features, activeSpotlightsAsync),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildFeaturesContent(
    List<SpotlightFeature> features,
    AsyncValue<List<UserSpotlight>> activeSpotlightsAsync,
  ) {
    final activeSpotlights = activeSpotlightsAsync.value ?? [];
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCurrentSpotlightStatus(activeSpotlights),
          const SizedBox(height: 24),
          _buildFeaturesHeader(),
          const SizedBox(height: 16),
          ...features.asMap().entries.map((entry) {
            final index = entry.key;
            final feature = entry.value;
            final isActive = activeSpotlights.any((s) => 
                s.spotlightId == feature.id && s.isActive && !s.isExpired);
            
            return _buildFeatureCard(feature, isActive, index)
                .animate(delay: (index * 100).ms)
                .fadeIn(duration: 600.ms)
                .slideX(begin: 0.2);
          }),
          const SizedBox(height: 100), // Space for FAB
        ],
      ),
    );
  }

  Widget _buildCurrentSpotlightStatus(List<UserSpotlight> activeSpotlights) {
    final currentSpotlight = activeSpotlights.firstWhere(
      (s) => s.isActive && !s.isExpired,
      orElse: () => throw StateError('No active spotlight'),
    );

    if (activeSpotlights.isEmpty) {
      return _buildNoActiveSpotlightCard();
    }

    return _buildActiveSpotlightCard(currentSpotlight);
  }

  Widget _buildNoActiveSpotlightCard() {
    return EnhancedCard(
      child: Column(
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: AppGradients.primaryGradient,
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.flash_off,
              size: 40,
              color: Colors.white,
            ),
          ).animate().scale(duration: 800.ms),
          const SizedBox(height: 16),
          const Text(
            'No Active Spotlight',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Boost your profile visibility and get more matches with our spotlight features.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          EnhancedButton(
            text: 'Explore Features',
            onPressed: () => _tabController.animateTo(0),
            type: ButtonType.primary,
            icon: const Icon(Icons.explore, size: 16),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2);
  }

  Widget _buildActiveSpotlightCard(UserSpotlight spotlight) {
    return EnhancedCard(
      isPremium: true,
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: AppGradients.primaryGradient,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Icon(
                  Icons.flash_on,
                  size: 30,
                  color: Colors.white,
                ),
              ).animate().scale(duration: 800.ms),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'Spotlight Active',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            'LIVE',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      spotlight.spotlight?.name ?? 'Premium Spotlight',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSpotlightProgress(spotlight),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildSpotlightStat(
                  'Time Left',
                  spotlight.timeRemainingText,
                  Icons.timer,
                ),
              ),
              Expanded(
                child: _buildSpotlightStat(
                  'Progress',
                  '${(spotlight.progressPercentage * 100).toInt()}%',
                  Icons.trending_up,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2);
  }

  Widget _buildSpotlightProgress(UserSpotlight spotlight) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              spotlight.timeRemainingText,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: spotlight.progressPercentage,
          backgroundColor: Colors.grey[200],
          valueColor: const AlwaysStoppedAnimation<Color>(AppColors.primary),
          minHeight: 6,
        ),
      ],
    );
  }

  Widget _buildSpotlightStat(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.primary.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.primary, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesHeader() {
    return Row(
      children: [
        const Icon(Icons.flash_on, color: AppColors.primary),
        const SizedBox(width: 8),
        const Text(
          'Available Features',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        TextButton.icon(
          onPressed: () => _showSpotlightComparison(),
          icon: const Icon(Icons.compare_arrows, size: 16),
          label: const Text('Compare'),
        ),
      ],
    );
  }

  Widget _buildFeatureCard(SpotlightFeature feature, bool isActive, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        isPremium: feature.price > 299,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: _getFeatureGradient(index),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Center(
                    child: Text(
                      _getFeatureIcon(feature.icon),
                      style: const TextStyle(fontSize: 24),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            feature.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (isActive) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.green,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: const Text(
                                'ACTIVE',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        feature.description,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildFeatureStats(feature),
            const SizedBox(height: 16),
            _buildFeatureBenefits(feature),
            const SizedBox(height: 16),
            _buildFeaturePricing(feature, isActive),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureStats(SpotlightFeature feature) {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            'Duration',
            feature.duration,
            Icons.timer,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            'Popularity',
            '${feature.popularity}%',
            Icons.trending_up,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            'Success Rate',
            '${feature.successRate}%',
            Icons.check_circle,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.primary, size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureBenefits(SpotlightFeature feature) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Benefits:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...feature.benefits.map((benefit) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  benefit,
                  style: const TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildFeaturePricing(SpotlightFeature feature, bool isActive) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (feature.hasDiscount) ...[
                Text(
                  '₹${feature.price.toInt()}',
                  style: const TextStyle(
                    fontSize: 14,
                    decoration: TextDecoration.lineThrough,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 2),
              ],
              Row(
                children: [
                  Text(
                    '₹${feature.finalPrice.toInt()}',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  if (feature.hasDiscount) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '${feature.discountPercent}% OFF',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
        const SizedBox(width: 16),
        FeatureAccessWidget(
          feature: FeatureType.profileBoost,
          child: EnhancedButton(
            text: isActive ? 'Active' : 'Purchase',
            onPressed: isActive ? null : () => _purchaseSpotlight(feature),
            type: isActive ? ButtonType.secondary : ButtonType.primary,
            icon: Icon(
              isActive ? Icons.check : Icons.flash_on,
              size: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActiveSpotlightsTab(AsyncValue<List<UserSpotlight>> activeSpotlightsAsync) {
    return activeSpotlightsAsync.when(
      data: (spotlights) => _buildActiveSpotlightsContent(spotlights),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildActiveSpotlightsContent(List<UserSpotlight> spotlights) {
    if (spotlights.isEmpty) {
      return _buildEmptyActiveSpotlights();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Your Active Spotlights',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...spotlights.asMap().entries.map((entry) {
            final index = entry.key;
            final spotlight = entry.value;
            return _buildActiveSpotlightItem(spotlight, index)
                .animate(delay: (index * 100).ms)
                .fadeIn(duration: 600.ms)
                .slideX(begin: 0.2);
          }),
        ],
      ),
    );
  }

  Widget _buildEmptyActiveSpotlights() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(50),
            ),
            child: Icon(
              Icons.flash_off,
              size: 50,
              color: Colors.grey[400],
            ),
          ).animate().scale(duration: 800.ms),
          const SizedBox(height: 24),
          const Text(
            'No Active Spotlights',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Purchase a spotlight feature to boost your profile visibility.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          EnhancedButton(
            text: 'Browse Features',
            onPressed: () => _tabController.animateTo(0),
            type: ButtonType.primary,
            icon: const Icon(Icons.flash_on, size: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildActiveSpotlightItem(UserSpotlight spotlight, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: _getFeatureGradient(index),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.flash_on,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        spotlight.spotlight?.name ?? 'Spotlight Feature',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Expires: ${_formatDateTime(spotlight.endTime)}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: spotlight.isExpired ? Colors.red : Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    spotlight.isExpired ? 'EXPIRED' : 'ACTIVE',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildSpotlightProgress(spotlight),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildSpotlightStat(
                    'Time Left',
                    spotlight.timeRemainingText,
                    Icons.timer,
                  ),
                ),
                Expanded(
                  child: _buildSpotlightStat(
                    'Paid',
                    '₹${spotlight.pricePaid.toInt()}',
                    Icons.payment,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.analytics,
            size: 64,
            color: AppColors.primary,
          ),
          SizedBox(height: 16),
          Text(
            'Analytics Coming Soon',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Track your spotlight performance and engagement metrics.',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading Spotlight Features...',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Features',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(spotlightFeaturesProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(SpotlightState spotlightState) {
    if (spotlightState.isPurchasing) {
      return const FloatingActionButton(
        onPressed: null,
        backgroundColor: Colors.grey,
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 2,
        ),
      );
    }

    return FloatingActionButton.extended(
      onPressed: () => _showQuickActions(),
      backgroundColor: AppColors.primary,
      icon: const Icon(Icons.more_horiz),
      label: const Text('Quick Actions'),
    );
  }

  // Utility methods
  Gradient _getFeatureGradient(int index) {
    final gradients = [
      AppGradients.primaryGradient,
      const LinearGradient(
        colors: [Colors.orange, Colors.deepOrange],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      const LinearGradient(
        colors: [Colors.purple, Colors.deepPurple],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      const LinearGradient(
        colors: [Colors.green, Colors.teal],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    ];
    return gradients[index % gradients.length];
  }

  String _getFeatureIcon(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'trendingup':
        return '📈';
      case 'flashon':
        return '⚡';
      case 'favorite':
        return '❤️';
      case 'verified':
        return '✅';
      case 'star':
        return '⭐';
      default:
        return '🔥';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Action methods
  void _purchaseSpotlight(SpotlightFeature feature) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20),
              child: Text(
                'Purchase ${feature.name}',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPurchaseFeatureDetails(feature),
                    const SizedBox(height: 20),
                    _buildPurchaseActions(feature),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPurchaseFeatureDetails(SpotlightFeature feature) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          feature.description,
          style: const TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 16),
        const Text(
          'Benefits:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...feature.benefits.map((benefit) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.green, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(benefit, style: const TextStyle(fontSize: 14)),
              ),
            ],
          ),
        )),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.primary.withAlpha(26),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              const Icon(Icons.info, color: AppColors.primary),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Pricing Details',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                    Text(
                      'Duration: ${feature.duration}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      'Price: ₹${feature.finalPrice.toInt()}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPurchaseActions(SpotlightFeature feature) {
    return Column(
      children: [
        EnhancedButton(
          text: 'Purchase for ₹${feature.finalPrice.toInt()}',
          onPressed: () => _confirmPurchase(feature),
          type: ButtonType.primary,
          isFullWidth: true,
          icon: const Icon(Icons.payment, size: 16),
        ),
        const SizedBox(height: 12),
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
      ],
    );
  }

  void _confirmPurchase(SpotlightFeature feature) {
    Navigator.pop(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Purchase'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Feature: ${feature.name}'),
            Text('Duration: ${feature.duration}'),
            Text('Price: ₹${feature.finalPrice.toInt()}'),
            const SizedBox(height: 16),
            const Text(
              'This will activate the spotlight feature immediately. Continue?',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processPurchase(feature);
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _processPurchase(SpotlightFeature feature) {
    final spotlightNotifier = ref.read(spotlightProvider.notifier);

    spotlightNotifier.purchaseSpotlight(
      feature: feature,
      userDetails: {
        'name': 'User Name', // Get from user profile
        'email': '<EMAIL>', // Get from user profile
        'phone': '**********', // Get from user profile
      },
      onSuccess: (spotlight) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${feature.name} activated successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        ref.invalidate(activeSpotlightsProvider);
      },
      onError: (error) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchase failed: $error'),
            backgroundColor: Colors.red,
          ),
        );
      },
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'Quick Actions',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.history, color: AppColors.primary),
              title: const Text('Spotlight History'),
              onTap: () {
                Navigator.pop(context);
                _showSpotlightHistory();
              },
            ),
            ListTile(
              leading: const Icon(Icons.compare_arrows, color: AppColors.primary),
              title: const Text('Compare Features'),
              onTap: () {
                Navigator.pop(context);
                _showSpotlightComparison();
              },
            ),
            ListTile(
              leading: const Icon(Icons.info, color: AppColors.primary),
              title: const Text('How Spotlight Works'),
              onTap: () {
                Navigator.pop(context);
                _showSpotlightInfo();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showSpotlightHistory() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening spotlight history...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showSpotlightComparison() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening feature comparison...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showSpotlightInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('How Spotlight Works'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Spotlight features help boost your profile visibility and increase your chances of finding the perfect match.',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(height: 16),
              Text(
                'Available Features:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Profile Boost - Increase visibility by 300%'),
              Text('• Super Spotlight - Premium visibility for 7 days'),
              Text('• Express Interest - Unlimited interests for 30 days'),
              Text('• Profile Verification - Get verified badge'),
              SizedBox(height: 16),
              Text(
                'Benefits:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Higher profile views'),
              Text('• Better match recommendations'),
              Text('• Increased response rates'),
              Text('• Priority in search results'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}
