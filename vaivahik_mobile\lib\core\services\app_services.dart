import 'package:flutter/foundation.dart';
import 'performance_service.dart';
import 'security_service.dart';
import 'testing_service.dart';

/// Central service manager for all app services
class AppServices {
  static final AppServices _instance = AppServices._internal();
  factory AppServices() => _instance;
  AppServices._internal();

  // Service instances
  late final PerformanceService _performanceService;
  late final SecurityService _securityService;
  late final TestingService _testingService;

  bool _isInitialized = false;

  /// Initialize all app services
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      debugPrint('AppServices: Initializing all services...');

      // Initialize service instances
      _performanceService = PerformanceService();
      _securityService = SecurityService();
      _testingService = TestingService();

      // Initialize services in order
      await _performanceService.initialize();
      await _securityService.initialize();
      await _testingService.initialize();

      _isInitialized = true;
      debugPrint('AppServices: All services initialized successfully');
    } catch (e) {
      debugPrint('AppServices: Failed to initialize services - $e');
      rethrow;
    }
  }

  /// Get performance service instance
  PerformanceService get performance {
    if (!_isInitialized) {
      throw StateError('AppServices not initialized. Call initialize() first.');
    }
    return _performanceService;
  }

  /// Get security service instance
  SecurityService get security {
    if (!_isInitialized) {
      throw StateError('AppServices not initialized. Call initialize() first.');
    }
    return _securityService;
  }

  /// Get testing service instance
  TestingService get testing {
    if (!_isInitialized) {
      throw StateError('AppServices not initialized. Call initialize() first.');
    }
    return _testingService;
  }

  /// Get comprehensive app health status
  Map<String, dynamic> getAppHealthStatus() {
    if (!_isInitialized) {
      return {'status': 'not_initialized'};
    }

    return {
      'status': 'healthy',
      'timestamp': DateTime.now().toIso8601String(),
      'services': {
        'performance': _performanceService.getPerformanceSummary(),
        'security': _securityService.getSecuritySummary(),
        'testing': _testingService.getTestingSummary(),
      },
    };
  }

  /// Dispose all services
  void dispose() {
    if (_isInitialized) {
      _performanceService.dispose();
      _securityService.dispose();
      _testingService.dispose();
      _isInitialized = false;
      debugPrint('AppServices: All services disposed');
    }
  }
}

/// Extension for easy access to app services
extension AppServicesExtension on AppServices {
  /// Record API request performance across all relevant services
  void recordApiRequest({
    required String endpoint,
    required String method,
    required int responseTime,
    required int statusCode,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isInitialized) return;

    // Record in performance service
    performance.recordApiRequest(
      endpoint: endpoint,
      method: method,
      responseTime: responseTime,
      statusCode: statusCode,
      additionalData: additionalData,
    );

    // Record authentication attempts in security service if auth-related
    if (endpoint.contains('auth') || endpoint.contains('login')) {
      security.recordAuthAttempt(
        success: statusCode < 400,
        method: 'api_$method',
        additionalData: {
          'endpoint': endpoint,
          'response_time': responseTime,
          ...?additionalData,
        },
      );
    }
  }

  /// Record user feedback across relevant services
  void recordUserFeedback({
    required String type,
    required String feedback,
    int? rating,
    Map<String, dynamic>? additionalData,
  }) {
    if (!_isInitialized) return;

    testing.recordUserFeedback(
      type: type,
      feedback: feedback,
      rating: rating,
      additionalData: additionalData,
    );
  }
}
