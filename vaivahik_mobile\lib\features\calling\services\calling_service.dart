import 'dart:async';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/api/api_client.dart';
import '../models/call_models.dart';

class CallingService {
  final ApiClient _apiClient;
  StreamController<CallSession>? _incomingCallController;
  StreamController<CallSession>? _callStatusController;

  CallingService(this._apiClient);

  // Stream for incoming calls
  Stream<CallSession> get incomingCallStream {
    _incomingCallController ??= StreamController<CallSession>.broadcast();
    return _incomingCallController!.stream;
  }

  // Stream for call status updates
  Stream<CallSession> get callStatusStream {
    _callStatusController ??= StreamController<CallSession>.broadcast();
    return _callStatusController!.stream;
  }

  // Initiate a voice call
  Future<CallSession> initiateVoiceCall(String receiverId) async {
    try {
      final response = await _apiClient.post('/calls/initiate', {
        'receiverId': receiverId,
        'type': 'voice',
      });

      if (response['success'] == true) {
        final callSession = CallSession.fromJson(response['call'] ?? response['data']);
        
        // For native phone dialer approach
        await _makeNativeCall(callSession);
        
        return callSession;
      } else {
        throw Exception(response['message'] ?? 'Failed to initiate voice call');
      }
    } catch (e) {
      throw Exception('Error initiating voice call: $e');
    }
  }

  // Initiate a video call
  Future<CallSession> initiateVideoCall(String receiverId) async {
    try {
      final response = await _apiClient.post('/calls/initiate', {
        'receiverId': receiverId,
        'type': 'video',
      });

      if (response['success'] == true) {
        final callSession = CallSession.fromJson(response['call'] ?? response['data']);
        
        // For video calls, we would typically use WebRTC or similar
        // But as per user preference, we'll use native approach where possible
        await _makeNativeVideoCall(callSession);
        
        return callSession;
      } else {
        throw Exception(response['message'] ?? 'Failed to initiate video call');
      }
    } catch (e) {
      throw Exception('Error initiating video call: $e');
    }
  }

  // Accept an incoming call
  Future<bool> acceptCall(String callId) async {
    try {
      final response = await _apiClient.post('/calls/$callId/accept', {});

      if (response['success'] == true) {
        final callSession = CallSession.fromJson(response['call'] ?? response['data']);
        _callStatusController?.add(callSession);
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to accept call');
      }
    } catch (e) {
      throw Exception('Error accepting call: $e');
    }
  }

  // Decline an incoming call
  Future<bool> declineCall(String callId, {String? reason}) async {
    try {
      final response = await _apiClient.post('/calls/$callId/decline', {
        if (reason != null) 'reason': reason,
      });

      if (response['success'] == true) {
        final callSession = CallSession.fromJson(response['call'] ?? response['data']);
        _callStatusController?.add(callSession);
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to decline call');
      }
    } catch (e) {
      throw Exception('Error declining call: $e');
    }
  }

  // End an active call
  Future<bool> endCall(String callId) async {
    try {
      final response = await _apiClient.post('/calls/$callId/end', {});

      if (response['success'] == true) {
        final callSession = CallSession.fromJson(response['call'] ?? response['data']);
        _callStatusController?.add(callSession);
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to end call');
      }
    } catch (e) {
      throw Exception('Error ending call: $e');
    }
  }

  // Get call history
  Future<List<CallHistory>> getCallHistory({
    int page = 1,
    int limit = 20,
    CallType? type,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (type != null) 'type': type.name,
      };

      final response = await _apiClient.get('/calls/history', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> historyData = response['history'] ?? response['data'] ?? [];
        return historyData.map((call) => CallHistory.fromJson(call)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch call history');
      }
    } catch (e) {
      throw Exception('Error fetching call history: $e');
    }
  }

  // Get active call session
  Future<CallSession?> getActiveCall() async {
    try {
      final response = await _apiClient.get('/calls/active');

      if (response['success'] == true && response['call'] != null) {
        return CallSession.fromJson(response['call']);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Update call quality feedback
  Future<bool> updateCallQuality(String callId, CallQuality quality, {String? feedback}) async {
    try {
      final response = await _apiClient.post('/calls/$callId/quality', {
        'quality': quality.name,
        if (feedback != null) 'feedback': feedback,
      });

      return response['success'] == true;
    } catch (e) {
      return false;
    }
  }

  // Get call settings
  Future<CallSettings> getCallSettings() async {
    try {
      final response = await _apiClient.get('/calls/settings');

      if (response['success'] == true) {
        return CallSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch call settings');
      }
    } catch (e) {
      throw Exception('Error fetching call settings: $e');
    }
  }

  // Update call settings
  Future<CallSettings> updateCallSettings(CallSettings settings) async {
    try {
      final response = await _apiClient.put('/calls/settings', settings.toJson());

      if (response['success'] == true) {
        return CallSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update call settings');
      }
    } catch (e) {
      throw Exception('Error updating call settings: $e');
    }
  }

  // Get call statistics
  Future<CallStatistics> getCallStatistics() async {
    try {
      final response = await _apiClient.get('/calls/statistics');

      if (response['success'] == true) {
        return CallStatistics.fromJson(response['statistics'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch call statistics');
      }
    } catch (e) {
      throw Exception('Error fetching call statistics: $e');
    }
  }

  // Block calls from a user
  Future<bool> blockCalls(String userId) async {
    try {
      final response = await _apiClient.post('/calls/block', {
        'userId': userId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error blocking calls: $e');
    }
  }

  // Unblock calls from a user
  Future<bool> unblockCalls(String userId) async {
    try {
      final response = await _apiClient.post('/calls/unblock', {
        'userId': userId,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error unblocking calls: $e');
    }
  }

  // Report a call
  Future<bool> reportCall(String callId, String reason, {String? details}) async {
    try {
      final response = await _apiClient.post('/calls/$callId/report', {
        'reason': reason,
        if (details != null) 'details': details,
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error reporting call: $e');
    }
  }

  // Private method to make native phone call
  Future<void> _makeNativeCall(CallSession callSession) async {
    try {
      // Get the phone number from the backend or use a placeholder
      final phoneNumber = await _getPhoneNumber(callSession.receiverId);
      
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
        if (await canLaunchUrl(phoneUri)) {
          await launchUrl(phoneUri);
        } else {
          throw Exception('Cannot launch phone dialer');
        }
      } else {
        throw Exception('Phone number not available');
      }
    } catch (e) {
      throw Exception('Error making native call: $e');
    }
  }

  // Private method to make native video call (if supported)
  Future<void> _makeNativeVideoCall(CallSession callSession) async {
    try {
      // For video calls, we might use platform-specific implementations
      // or fallback to in-app video calling
      
      // Try to get phone number for FaceTime (iOS) or similar
      final phoneNumber = await _getPhoneNumber(callSession.receiverId);
      
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        // Try FaceTime on iOS
        final Uri facetimeUri = Uri(scheme: 'facetime', path: phoneNumber);
        if (await canLaunchUrl(facetimeUri)) {
          await launchUrl(facetimeUri);
          return;
        }
        
        // Fallback to regular phone call
        final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
        if (await canLaunchUrl(phoneUri)) {
          await launchUrl(phoneUri);
          return;
        }
      }
      
      throw Exception('Video calling not supported on this device');
    } catch (e) {
      throw Exception('Error making native video call: $e');
    }
  }

  // Private method to get phone number for a user
  Future<String?> _getPhoneNumber(String userId) async {
    try {
      final response = await _apiClient.get('/users/$userId/phone');
      
      if (response['success'] == true) {
        return response['phone']?.toString();
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Handle incoming call notification
  void handleIncomingCall(Map<String, dynamic> callData) {
    try {
      final callSession = CallSession.fromJson(callData);
      _incomingCallController?.add(callSession);
    } catch (e) {
      // Handle error
    }
  }

  // Handle call status update
  void handleCallStatusUpdate(Map<String, dynamic> callData) {
    try {
      final callSession = CallSession.fromJson(callData);
      _callStatusController?.add(callSession);
    } catch (e) {
      // Handle error
    }
  }

  // Dispose resources
  void dispose() {
    _incomingCallController?.close();
    _callStatusController?.close();
  }
}
