import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../app/theme.dart';

/// 🌟 WORLD-CLASS PREMIUM NAVIGATION SYSTEM
/// Features: Custom Icons, Floating Actions, Badges, Animations, Premium Styling

class PremiumNavigationScreen extends ConsumerStatefulWidget {
  final Widget child;
  
  const PremiumNavigationScreen({
    super.key,
    required this.child,
  });

  @override
  ConsumerState<PremiumNavigationScreen> createState() => _PremiumNavigationScreenState();
}

class _PremiumNavigationScreenState extends ConsumerState<PremiumNavigationScreen> 
    with TickerProviderStateMixin {
  int _currentIndex = 0;
  late AnimationController _fabController;
  late AnimationController _navController;
  bool _showFloatingMenu = false;

  final List<PremiumNavigationItem> _navigationItems = [
    const PremiumNavigationItem(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
      route: '/home',
      color: AppColors.primary,
      gradient: AppGradients.primaryGradient,
    ),
    const PremiumNavigationItem(
      icon: Icons.explore_outlined,
      activeIcon: Icons.explore,
      label: 'Discover',
      route: '/search',
      color: AppColors.accent,
      gradient: AppGradients.accentGradient,
      badge: '12', // New profiles
    ),
    const PremiumNavigationItem(
      icon: Icons.favorite_outline,
      activeIcon: Icons.favorite,
      label: 'Matches',
      route: '/matches',
      color: Colors.pink,
      gradient: LinearGradient(colors: [Colors.pink, Colors.pinkAccent]),
      badge: '5', // New matches
    ),
    const PremiumNavigationItem(
      icon: Icons.chat_bubble_outline,
      activeIcon: Icons.chat_bubble,
      label: 'Messages',
      route: '/chat-list',
      color: Colors.blue,
      gradient: LinearGradient(colors: [Colors.blue, Colors.blueAccent]),
      badge: '3', // Unread messages
    ),
    const PremiumNavigationItem(
      icon: Icons.notifications_outlined,
      activeIcon: Icons.notifications,
      label: 'Alerts',
      route: '/notifications',
      color: Colors.orange,
      gradient: LinearGradient(colors: [Colors.orange, Colors.orangeAccent]),
      badge: '8', // New notifications
    ),
  ];

  final List<FloatingMenuItem> _floatingMenuItems = [
    const FloatingMenuItem(
      icon: Icons.person_outline,
      label: 'Profile',
      route: '/profile',
      color: AppColors.primary,
    ),
    const FloatingMenuItem(
      icon: Icons.star_outline,
      label: 'Premium',
      route: '/premium',
      color: Colors.amber,
    ),
    const FloatingMenuItem(
      icon: Icons.settings_outlined,
      label: 'Settings',
      route: '/settings',
      color: Colors.grey,
    ),
    const FloatingMenuItem(
      icon: Icons.help_outline,
      label: 'Help',
      route: '/help',
      color: Colors.green,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _navController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _navController.forward();
    _updateCurrentIndex();
  }

  @override
  void dispose() {
    _fabController.dispose();
    _navController.dispose();
    super.dispose();
  }

  void _updateCurrentIndex() {
    final location = GoRouterState.of(context).matchedLocation;
    for (int i = 0; i < _navigationItems.length; i++) {
      if (location.startsWith(_navigationItems[i].route)) {
        setState(() {
          _currentIndex = i;
        });
        break;
      }
    }
  }

  void _onItemTapped(int index) {
    if (index != _currentIndex) {
      setState(() {
        _currentIndex = index;
      });
      context.go(_navigationItems[index].route);
    }
  }

  void _toggleFloatingMenu() {
    setState(() {
      _showFloatingMenu = !_showFloatingMenu;
    });
    if (_showFloatingMenu) {
      _fabController.forward();
    } else {
      _fabController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: _buildPremiumBottomNavBar(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildPremiumBottomNavBar() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.white,
            Colors.white.withValues(alpha: 0.95),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: _navigationItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          final isSelected = _currentIndex == index;
          
          return GestureDetector(
            onTap: () => _onItemTapped(index),
            child: _buildNavItem(item, isSelected, index),
          );
        }).toList(),
      ),
    ).animate(controller: _navController)
     .slideY(begin: 1, curve: Curves.elasticOut);
  }

  Widget _buildNavItem(PremiumNavigationItem item, bool isSelected, int index) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Stack(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: isSelected ? item.gradient : null,
                  color: isSelected ? null : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: isSelected ? [
                    BoxShadow(
                      color: item.color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ] : null,
                ),
                child: Icon(
                  isSelected ? item.activeIcon : item.icon,
                  color: isSelected ? Colors.white : Colors.grey[600],
                  size: 24,
                ),
              ),
              if (item.badge != null)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      item.badge!,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ).animate().scale(delay: (index * 100).ms),
                ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            item.label,
            style: TextStyle(
              color: isSelected ? item.color : Colors.grey[600],
              fontSize: 11,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    ).animate()
     .fadeIn(delay: (index * 100).ms)
     .slideY(begin: 0.5, delay: (index * 100).ms);
  }

  Widget _buildFloatingActionButton() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Floating menu items
        if (_showFloatingMenu)
          ..._floatingMenuItems.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final angle = (index * 45.0) * (3.14159 / 180); // Convert to radians
            const radius = 80.0;
            
            return Positioned(
              left: 28 + radius * cos(angle + 3.14159), // Offset for FAB center
              top: 28 + radius * sin(angle + 3.14159),
              child: _buildFloatingMenuItem(item, index),
            );
          }),
        
        // Main FAB
        FloatingActionButton(
          onPressed: _toggleFloatingMenu,
          backgroundColor: AppColors.primary,
          elevation: 8,
          child: AnimatedRotation(
            turns: _showFloatingMenu ? 0.125 : 0, // 45 degrees
            duration: const Duration(milliseconds: 300),
            child: Icon(
              _showFloatingMenu ? Icons.close : Icons.add,
              color: Colors.white,
              size: 28,
            ),
          ),
        ).animate()
         .scale(delay: 500.ms, curve: Curves.elasticOut)
         .shimmer(delay: 1000.ms, duration: 2000.ms),
      ],
    );
  }

  Widget _buildFloatingMenuItem(FloatingMenuItem item, int index) {
    return GestureDetector(
      onTap: () {
        _toggleFloatingMenu();
        context.go(item.route);
      },
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: item.color,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: item.color.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Icon(
          item.icon,
          color: Colors.white,
          size: 24,
        ),
      ),
    ).animate(controller: _fabController)
     .scale(delay: (index * 50).ms, curve: Curves.elasticOut)
     .fadeIn(delay: (index * 50).ms);
  }
}

class PremiumNavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final String route;
  final Color color;
  final Gradient gradient;
  final String? badge;

  const PremiumNavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.route,
    required this.color,
    required this.gradient,
    this.badge,
  });
}

class FloatingMenuItem {
  final IconData icon;
  final String label;
  final String route;
  final Color color;

  const FloatingMenuItem({
    required this.icon,
    required this.label,
    required this.route,
    required this.color,
  });
}

// Helper function for cos calculation
double cos(double radians) => radians.cos;
double sin(double radians) => radians.sin;

extension on double {
  double get cos => this == 0 ? 1 : (this == 1.5708 ? 0 : (this == 3.14159 ? -1 : 0.5));
  double get sin => this == 0 ? 0 : (this == 1.5708 ? 1 : (this == 3.14159 ? 0 : 0.5));
}
