import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 🎨 DYNAMIC THEMING SYSTEM - Personalized & Seasonal Themes
/// Features: Personalized themes, seasonal variations, mood-based colors, time-based themes
/// Reuses existing website theming logic with enhanced mobile-specific adaptations

/// Theme Provider for Dynamic Theming
final dynamicThemeProvider = StateNotifierProvider<DynamicThemeNotifier, DynamicThemeState>((ref) {
  return DynamicThemeNotifier();
});

/// Dynamic Theme State
class DynamicThemeState {
  final ThemeType currentTheme;
  final SeasonalTheme seasonalTheme;
  final MoodTheme moodTheme;
  final TimeBasedTheme timeBasedTheme;
  final bool isPersonalized;
  final Map<String, dynamic> personalizedSettings;

  const DynamicThemeState({
    this.currentTheme = ThemeType.matrimony,
    this.seasonalTheme = SeasonalTheme.spring,
    this.moodTheme = MoodTheme.romantic,
    this.timeBasedTheme = TimeBasedTheme.day,
    this.isPersonalized = false,
    this.personalizedSettings = const {},
  });

  DynamicThemeState copyWith({
    ThemeType? currentTheme,
    SeasonalTheme? seasonalTheme,
    MoodTheme? moodTheme,
    TimeBasedTheme? timeBasedTheme,
    bool? isPersonalized,
    Map<String, dynamic>? personalizedSettings,
  }) {
    return DynamicThemeState(
      currentTheme: currentTheme ?? this.currentTheme,
      seasonalTheme: seasonalTheme ?? this.seasonalTheme,
      moodTheme: moodTheme ?? this.moodTheme,
      timeBasedTheme: timeBasedTheme ?? this.timeBasedTheme,
      isPersonalized: isPersonalized ?? this.isPersonalized,
      personalizedSettings: personalizedSettings ?? this.personalizedSettings,
    );
  }
}

/// Dynamic Theme Notifier
class DynamicThemeNotifier extends StateNotifier<DynamicThemeState> {
  DynamicThemeNotifier() : super(const DynamicThemeState()) {
    _loadThemeSettings();
    _updateTimeBasedTheme();
    _updateSeasonalTheme();
  }

  Future<void> _loadThemeSettings() async {
    final prefs = await SharedPreferences.getInstance();
    final themeIndex = prefs.getInt('theme_type') ?? 0;
    final seasonalIndex = prefs.getInt('seasonal_theme') ?? 0;
    final moodIndex = prefs.getInt('mood_theme') ?? 0;
    final isPersonalized = prefs.getBool('is_personalized') ?? false;

    state = state.copyWith(
      currentTheme: ThemeType.values[themeIndex],
      seasonalTheme: SeasonalTheme.values[seasonalIndex],
      moodTheme: MoodTheme.values[moodIndex],
      isPersonalized: isPersonalized,
    );
  }

  Future<void> updateTheme(ThemeType theme) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('theme_type', theme.index);
    state = state.copyWith(currentTheme: theme);
  }

  Future<void> updateSeasonalTheme(SeasonalTheme theme) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('seasonal_theme', theme.index);
    state = state.copyWith(seasonalTheme: theme);
  }

  Future<void> updateMoodTheme(MoodTheme theme) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('mood_theme', theme.index);
    state = state.copyWith(moodTheme: theme);
  }

  void _updateTimeBasedTheme() {
    final hour = DateTime.now().hour;
    TimeBasedTheme timeTheme;

    if (hour >= 6 && hour < 12) {
      timeTheme = TimeBasedTheme.morning;
    } else if (hour >= 12 && hour < 17) {
      timeTheme = TimeBasedTheme.day;
    } else if (hour >= 17 && hour < 20) {
      timeTheme = TimeBasedTheme.evening;
    } else {
      timeTheme = TimeBasedTheme.night;
    }

    state = state.copyWith(timeBasedTheme: timeTheme);
  }

  void _updateSeasonalTheme() {
    final month = DateTime.now().month;
    SeasonalTheme seasonalTheme;

    if (month >= 3 && month <= 5) {
      seasonalTheme = SeasonalTheme.spring;
    } else if (month >= 6 && month <= 8) {
      seasonalTheme = SeasonalTheme.summer;
    } else if (month >= 9 && month <= 11) {
      seasonalTheme = SeasonalTheme.autumn;
    } else {
      seasonalTheme = SeasonalTheme.winter;
    }

    state = state.copyWith(seasonalTheme: seasonalTheme);
  }

  ThemeData getThemeData() {
    return DynamicThemeBuilder.buildTheme(state);
  }
}

/// Theme Types
enum ThemeType {
  matrimony,
  elegant,
  modern,
  traditional,
  premium,
  minimal,
}

enum SeasonalTheme {
  spring,
  summer,
  autumn,
  winter,
}

enum MoodTheme {
  romantic,
  energetic,
  calm,
  sophisticated,
  playful,
}

enum TimeBasedTheme {
  morning,
  day,
  evening,
  night,
}

/// Dynamic Theme Builder
class DynamicThemeBuilder {
  static ThemeData buildTheme(DynamicThemeState themeState) {
    final colorScheme = _getColorScheme(themeState);
    final gradients = _getGradients(themeState);

    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      fontFamily: 'Inter',
      appBarTheme: AppBarTheme(
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: colorScheme.onSurface,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
      cardTheme: CardThemeData(
        surfaceTintColor: colorScheme.surface,
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
      ),
      extensions: [
        DynamicThemeExtension(
          gradients: gradients,
          seasonalColors: _getSeasonalColors(themeState.seasonalTheme),
          moodColors: _getMoodColors(themeState.moodTheme),
          timeColors: _getTimeColors(themeState.timeBasedTheme),
        ),
      ],
    );
  }

  static ColorScheme _getColorScheme(DynamicThemeState themeState) {
    switch (themeState.currentTheme) {
      case ThemeType.matrimony:
        return _getMatrimonyColorScheme(themeState);
      case ThemeType.elegant:
        return _getElegantColorScheme(themeState);
      case ThemeType.modern:
        return _getModernColorScheme(themeState);
      case ThemeType.traditional:
        return _getTraditionalColorScheme(themeState);
      case ThemeType.premium:
        return _getPremiumColorScheme(themeState);
      case ThemeType.minimal:
        return _getMinimalColorScheme(themeState);
    }
  }

  static ColorScheme _getMatrimonyColorScheme(DynamicThemeState themeState) {
    final baseColors = _applyTimeAndSeasonalModifications(
      primary: const Color(0xFFE91E63),
      secondary: const Color(0xFF9C27B0),
      themeState: themeState,
    );

    return ColorScheme.light(
      primary: baseColors['primary']!,
      secondary: baseColors['secondary']!,
      surface: Colors.white,
      error: const Color(0xFFE53E3E),
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: const Color(0xFF1A1A1A),
      onError: Colors.white,
    );
  }

  static ColorScheme _getElegantColorScheme(DynamicThemeState themeState) {
    final baseColors = _applyTimeAndSeasonalModifications(
      primary: const Color(0xFF2D3748),
      secondary: const Color(0xFF4A5568),
      themeState: themeState,
    );

    return ColorScheme.light(
      primary: baseColors['primary']!,
      secondary: baseColors['secondary']!,
      surface: Colors.white,
      error: const Color(0xFFE53E3E),
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: const Color(0xFF1A202C),
      onError: Colors.white,
    );
  }

  static ColorScheme _getModernColorScheme(DynamicThemeState themeState) {
    final baseColors = _applyTimeAndSeasonalModifications(
      primary: const Color(0xFF3182CE),
      secondary: const Color(0xFF38B2AC),
      themeState: themeState,
    );

    return ColorScheme.light(
      primary: baseColors['primary']!,
      secondary: baseColors['secondary']!,
      surface: Colors.white,
      error: const Color(0xFFE53E3E),
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: const Color(0xFF2D3748),
      onError: Colors.white,
    );
  }

  static ColorScheme _getTraditionalColorScheme(DynamicThemeState themeState) {
    final baseColors = _applyTimeAndSeasonalModifications(
      primary: const Color(0xFFD69E2E),
      secondary: const Color(0xFFDD6B20),
      themeState: themeState,
    );

    return ColorScheme.light(
      primary: baseColors['primary']!,
      secondary: baseColors['secondary']!,
      surface: Colors.white,
      error: const Color(0xFFE53E3E),
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: const Color(0xFF744210),
      onError: Colors.white,
    );
  }

  static ColorScheme _getPremiumColorScheme(DynamicThemeState themeState) {
    final baseColors = _applyTimeAndSeasonalModifications(
      primary: const Color(0xFF805AD5),
      secondary: const Color(0xFFD53F8C),
      themeState: themeState,
    );

    return ColorScheme.light(
      primary: baseColors['primary']!,
      secondary: baseColors['secondary']!,
      surface: Colors.white,
      error: const Color(0xFFE53E3E),
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: const Color(0xFF553C9A),
      onError: Colors.white,
    );
  }

  static ColorScheme _getMinimalColorScheme(DynamicThemeState themeState) {
    final baseColors = _applyTimeAndSeasonalModifications(
      primary: const Color(0xFF718096),
      secondary: const Color(0xFFA0AEC0),
      themeState: themeState,
    );

    return ColorScheme.light(
      primary: baseColors['primary']!,
      secondary: baseColors['secondary']!,
      surface: Colors.white,
      error: const Color(0xFFE53E3E),
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onSurface: const Color(0xFF2D3748),
      onError: Colors.white,
    );
  }

  static Map<String, Color> _applyTimeAndSeasonalModifications(
    {required Color primary, required Color secondary, required DynamicThemeState themeState}
  ) {
    // Apply seasonal modifications
    final seasonalModifier = _getSeasonalModifier(themeState.seasonalTheme);
    final timeModifier = _getTimeModifier(themeState.timeBasedTheme);
    final moodModifier = _getMoodModifier(themeState.moodTheme);

    return {
      'primary': _modifyColor(primary, seasonalModifier + timeModifier + moodModifier),
      'secondary': _modifyColor(secondary, seasonalModifier + timeModifier + moodModifier),
    };
  }

  static double _getSeasonalModifier(SeasonalTheme season) {
    switch (season) {
      case SeasonalTheme.spring:
        return 0.1; // Brighter
      case SeasonalTheme.summer:
        return 0.15; // More vibrant
      case SeasonalTheme.autumn:
        return -0.1; // Warmer, darker
      case SeasonalTheme.winter:
        return -0.2; // Cooler, darker
    }
  }

  static double _getTimeModifier(TimeBasedTheme time) {
    switch (time) {
      case TimeBasedTheme.morning:
        return 0.1; // Brighter
      case TimeBasedTheme.day:
        return 0.0; // Normal
      case TimeBasedTheme.evening:
        return -0.05; // Slightly warmer
      case TimeBasedTheme.night:
        return -0.15; // Darker
    }
  }

  static double _getMoodModifier(MoodTheme mood) {
    switch (mood) {
      case MoodTheme.romantic:
        return 0.05; // Slightly warmer
      case MoodTheme.energetic:
        return 0.2; // Much brighter
      case MoodTheme.calm:
        return -0.1; // Cooler
      case MoodTheme.sophisticated:
        return -0.05; // Slightly darker
      case MoodTheme.playful:
        return 0.15; // Brighter and more vibrant
    }
  }

  static Color _modifyColor(Color color, double modifier) {
    final hsl = HSLColor.fromColor(color);
    final newLightness = (hsl.lightness + modifier).clamp(0.0, 1.0);
    return hsl.withLightness(newLightness).toColor();
  }

  static Map<String, Gradient> _getGradients(DynamicThemeState themeState) {
    // Implementation for gradients based on theme state
    return {
      'primary': LinearGradient(
        colors: [
          _getColorScheme(themeState).primary,
          _getColorScheme(themeState).secondary,
        ],
      ),
      'background': LinearGradient(
        colors: [
          _getColorScheme(themeState).surface,
          _getColorScheme(themeState).surface,
        ],
      ),
    };
  }

  static Map<String, Color> _getSeasonalColors(SeasonalTheme season) {
    switch (season) {
      case SeasonalTheme.spring:
        return {
          'accent': const Color(0xFF68D391),
          'highlight': const Color(0xFFF6E05E),
        };
      case SeasonalTheme.summer:
        return {
          'accent': const Color(0xFF4FD1C7),
          'highlight': const Color(0xFFFC8181),
        };
      case SeasonalTheme.autumn:
        return {
          'accent': const Color(0xFFED8936),
          'highlight': const Color(0xFFD69E2E),
        };
      case SeasonalTheme.winter:
        return {
          'accent': const Color(0xFF63B3ED),
          'highlight': const Color(0xFF9F7AEA),
        };
    }
  }

  static Map<String, Color> _getMoodColors(MoodTheme mood) {
    switch (mood) {
      case MoodTheme.romantic:
        return {
          'accent': const Color(0xFFF687B3),
          'highlight': const Color(0xFFED64A6),
        };
      case MoodTheme.energetic:
        return {
          'accent': const Color(0xFFFC8181),
          'highlight': const Color(0xFFF6E05E),
        };
      case MoodTheme.calm:
        return {
          'accent': const Color(0xFF81E6D9),
          'highlight': const Color(0xFF90CDF4),
        };
      case MoodTheme.sophisticated:
        return {
          'accent': const Color(0xFFA0AEC0),
          'highlight': const Color(0xFF718096),
        };
      case MoodTheme.playful:
        return {
          'accent': const Color(0xFF9F7AEA),
          'highlight': const Color(0xFF68D391),
        };
    }
  }

  static Map<String, Color> _getTimeColors(TimeBasedTheme time) {
    switch (time) {
      case TimeBasedTheme.morning:
        return {
          'accent': const Color(0xFFF6E05E),
          'highlight': const Color(0xFFFC8181),
        };
      case TimeBasedTheme.day:
        return {
          'accent': const Color(0xFF63B3ED),
          'highlight': const Color(0xFF68D391),
        };
      case TimeBasedTheme.evening:
        return {
          'accent': const Color(0xFFED8936),
          'highlight': const Color(0xFFD69E2E),
        };
      case TimeBasedTheme.night:
        return {
          'accent': const Color(0xFF9F7AEA),
          'highlight': const Color(0xFF805AD5),
        };
    }
  }
}

/// Theme Extension for Dynamic Colors
class DynamicThemeExtension extends ThemeExtension<DynamicThemeExtension> {
  final Map<String, Gradient> gradients;
  final Map<String, Color> seasonalColors;
  final Map<String, Color> moodColors;
  final Map<String, Color> timeColors;

  const DynamicThemeExtension({
    required this.gradients,
    required this.seasonalColors,
    required this.moodColors,
    required this.timeColors,
  });

  @override
  DynamicThemeExtension copyWith({
    Map<String, Gradient>? gradients,
    Map<String, Color>? seasonalColors,
    Map<String, Color>? moodColors,
    Map<String, Color>? timeColors,
  }) {
    return DynamicThemeExtension(
      gradients: gradients ?? this.gradients,
      seasonalColors: seasonalColors ?? this.seasonalColors,
      moodColors: moodColors ?? this.moodColors,
      timeColors: timeColors ?? this.timeColors,
    );
  }

  @override
  DynamicThemeExtension lerp(ThemeExtension<DynamicThemeExtension>? other, double t) {
    if (other is! DynamicThemeExtension) {
      return this;
    }
    return DynamicThemeExtension(
      gradients: gradients,
      seasonalColors: seasonalColors,
      moodColors: moodColors,
      timeColors: timeColors,
    );
  }
}
