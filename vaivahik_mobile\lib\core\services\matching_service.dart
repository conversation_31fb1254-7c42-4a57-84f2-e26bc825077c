import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/api_client.dart';
import '../../features/profile/models/profile_model.dart';

class MatchFilters {
  final int? minAge;
  final int? maxAge;
  final String? minHeight;
  final String? maxHeight;
  final List<String>? religions;
  final List<String>? castes;
  final List<String>? educations;
  final List<String>? occupations;
  final String? maritalStatus;
  final String? location;
  final int? maxDistance;
  final bool? hasPhoto;
  final bool? isVerified;
  final bool? isPremium;

  const MatchFilters({
    this.minAge,
    this.maxAge,
    this.minHeight,
    this.maxHeight,
    this.religions,
    this.castes,
    this.educations,
    this.occupations,
    this.maritalStatus,
    this.location,
    this.maxDistance,
    this.hasPhoto,
    this.isVerified,
    this.isPremium,
  });

  Map<String, dynamic> toJson() {
    return {
      'minAge': minAge,
      'maxAge': maxAge,
      'minHeight': minHeight,
      'maxHeight': maxHeight,
      'religions': religions,
      'castes': castes,
      'educations': educations,
      'occupations': occupations,
      'maritalStatus': maritalStatus,
      'location': location,
      'maxDistance': maxDistance,
      'hasPhoto': hasPhoto,
      'isVerified': isVerified,
      'isPremium': isPremium,
    };
  }

  MatchFilters copyWith({
    int? minAge,
    int? maxAge,
    String? minHeight,
    String? maxHeight,
    List<String>? religions,
    List<String>? castes,
    List<String>? educations,
    List<String>? occupations,
    String? maritalStatus,
    String? location,
    int? maxDistance,
    bool? hasPhoto,
    bool? isVerified,
    bool? isPremium,
  }) {
    return MatchFilters(
      minAge: minAge ?? this.minAge,
      maxAge: maxAge ?? this.maxAge,
      minHeight: minHeight ?? this.minHeight,
      maxHeight: maxHeight ?? this.maxHeight,
      religions: religions ?? this.religions,
      castes: castes ?? this.castes,
      educations: educations ?? this.educations,
      occupations: occupations ?? this.occupations,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      location: location ?? this.location,
      maxDistance: maxDistance ?? this.maxDistance,
      hasPhoto: hasPhoto ?? this.hasPhoto,
      isVerified: isVerified ?? this.isVerified,
      isPremium: isPremium ?? this.isPremium,
    );
  }
}

class MatchResult {
  final ProfileModel profile;
  final double compatibilityScore;
  final Map<String, dynamic> matchReasons;
  final bool isLiked;
  final bool isShortlisted;
  final bool hasInterest;
  final String? matchCategory;
  final bool isOnline;
  final DateTime? lastSeen;
  final bool usingML;
  final Map<String, dynamic>? aiExplanation;

  const MatchResult({
    required this.profile,
    required this.compatibilityScore,
    required this.matchReasons,
    this.isLiked = false,
    this.isShortlisted = false,
    this.hasInterest = false,
    this.matchCategory,
    this.isOnline = false,
    this.lastSeen,
    this.usingML = false,
    this.aiExplanation,
  });

  factory MatchResult.fromJson(Map<String, dynamic> json) {
    return MatchResult(
      profile: ProfileModel.fromJson(json['profile'] ?? json),
      compatibilityScore: (json['compatibilityScore'] ?? json['score'] ?? 0.0).toDouble(),
      matchReasons: json['matchReasons'] ?? json['reasons'] ?? {},
      isLiked: json['isLiked'] ?? false,
      isShortlisted: json['isShortlisted'] ?? false,
      hasInterest: json['hasInterest'] ?? false,
      matchCategory: json['matchCategory'] ?? json['category'],
      isOnline: json['isOnline'] ?? false,
      lastSeen: json['lastSeen'] != null ? DateTime.parse(json['lastSeen']) : null,
      usingML: json['usingML'] ?? false,
      aiExplanation: json['aiExplanation'] ?? json['explanation'],
    );
  }
}

class CategorizedMatches {
  final List<MatchResult> aiMatches;
  final List<MatchResult> verifiedMatches;
  final List<MatchResult> premiumMatches;
  final List<MatchResult> recentMatches;
  final List<MatchResult> nearbyMatches;
  final List<MatchResult> compatibleMatches;
  final bool usingML;

  const CategorizedMatches({
    required this.aiMatches,
    required this.verifiedMatches,
    required this.premiumMatches,
    required this.recentMatches,
    required this.nearbyMatches,
    required this.compatibleMatches,
    this.usingML = false,
  });

  factory CategorizedMatches.fromJson(Map<String, dynamic> json) {
    return CategorizedMatches(
      aiMatches: (json['aiMatches'] as List? ?? [])
          .map((match) => MatchResult.fromJson(match))
          .toList(),
      verifiedMatches: (json['verifiedMatches'] as List? ?? [])
          .map((match) => MatchResult.fromJson(match))
          .toList(),
      premiumMatches: (json['premiumMatches'] as List? ?? [])
          .map((match) => MatchResult.fromJson(match))
          .toList(),
      recentMatches: (json['recentMatches'] as List? ?? [])
          .map((match) => MatchResult.fromJson(match))
          .toList(),
      nearbyMatches: (json['nearbyMatches'] as List? ?? [])
          .map((match) => MatchResult.fromJson(match))
          .toList(),
      compatibleMatches: (json['compatibleMatches'] as List? ?? [])
          .map((match) => MatchResult.fromJson(match))
          .toList(),
      usingML: json['usingML'] ?? false,
    );
  }
}

class MatchingService {
  final ApiClient _apiClient = ApiClient();

  // AI-based matches using website's production matching service
  Future<List<MatchResult>> getAIMatches({
    int limit = 10,
    int offset = 0,
    int? minScore,
    bool includeExplanation = false,
  }) async {
    try {
      final queryParams = {
        'limit': limit.toString(),
        'offset': offset.toString(),
        if (minScore != null) 'minScore': minScore.toString(),
        'includeExplanation': includeExplanation.toString(),
      };

      final response = await _apiClient.get('/v2/matches', queryParams: queryParams);

      if (response['success'] == true) {
        final matchesData = response['matches'] as List;
        return matchesData.map((match) => MatchResult.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch AI matches');
      }
    } catch (e) {
      throw Exception('Error fetching AI matches: $e');
    }
  }

  // Get categorized matches (AI, Verified, Premium, etc.) using website logic
  Future<CategorizedMatches> getCategorizedMatches({
    int limit = 12,
    int offset = 0,
  }) async {
    try {
      final queryParams = {
        'limit': limit.toString(),
        'offset': offset.toString(),
      };

      final response = await _apiClient.get('/matches/categorized', queryParams: queryParams);

      if (response['success'] == true) {
        return CategorizedMatches.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch categorized matches');
      }
    } catch (e) {
      throw Exception('Error fetching categorized matches: $e');
    }
  }

  // Get compatibility score with another user
  Future<Map<String, dynamic>> getCompatibilityScore(String userId) async {
    try {
      final response = await _apiClient.get('/matching/compatibility/$userId');

      if (response['success'] == true) {
        return response['compatibility'];
      } else {
        throw Exception(response['message'] ?? 'Failed to get compatibility score');
      }
    } catch (e) {
      throw Exception('Error getting compatibility score: $e');
    }
  }

  Future<List<MatchResult>> getMatches({
    int page = 1,
    int limit = 20,
    MatchFilters? filters,
  }) async {
    try {
      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
        if (filters != null) ...filters.toJson().map((k, v) => MapEntry(k, v?.toString())),
      };

      final response = await _apiClient.get('/matches', queryParams: queryParams);

      if (response['success'] == true) {
        final matchesData = response['data']['matches'] as List;
        return matchesData.map((match) => MatchResult.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch matches');
      }
    } catch (e) {
      throw Exception('Error fetching matches: $e');
    }
  }

  Future<List<MatchResult>> getRecommendedMatches({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get('/matches/recommended', queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      });
      
      if (response['success'] == true) {
        final matchesData = response['data']['matches'] as List;
        return matchesData.map((match) => MatchResult.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch recommended matches');
      }
    } catch (e) {
      throw Exception('Error fetching recommended matches: $e');
    }
  }

  // Enhanced profile actions with tracking
  Future<bool> likeProfile(String profileId, {String? reason}) async {
    try {
      final response = await _apiClient.post('/matches/like', {
        'profileId': profileId,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error liking profile: $e');
    }
  }

  Future<bool> dislikeProfile(String profileId, {String? reason}) async {
    try {
      final response = await _apiClient.post('/matches/dislike', {
        'profileId': profileId,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error disliking profile: $e');
    }
  }

  Future<bool> superLikeProfile(String profileId, {String? message}) async {
    try {
      final response = await _apiClient.post('/matches/super-like', {
        'profileId': profileId,
        'message': message,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error super liking profile: $e');
    }
  }

  Future<bool> passProfile(String profileId) async {
    try {
      final response = await _apiClient.post('/matches/pass', {
        'profileId': profileId,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error passing profile: $e');
    }
  }

  Future<bool> shortlistProfile(String profileId, {bool remove = false}) async {
    try {
      final response = await _apiClient.post('/matches/shortlist', {
        'profileId': profileId,
        'action': remove ? 'remove' : 'add',
        'timestamp': DateTime.now().toIso8601String(),
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error shortlisting profile: $e');
    }
  }

  Future<bool> blockProfile(String profileId, {String? reason}) async {
    try {
      final response = await _apiClient.post('/matches/block', {
        'profileId': profileId,
        'reason': reason,
        'timestamp': DateTime.now().toIso8601String(),
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error blocking profile: $e');
    }
  }

  Future<bool> sendInterest(String profileId, {String? message}) async {
    try {
      final response = await _apiClient.post('/matches/interest', {
        'profileId': profileId,
        'message': message,
      });
      
      return response['success'] == true;
    } catch (e) {
      throw Exception('Error sending interest: $e');
    }
  }

  Future<List<MatchResult>> getMutualMatches({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get('/matches/mutual', queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      });
      
      if (response['success'] == true) {
        final matchesData = response['data']['matches'] as List;
        return matchesData.map((match) => MatchResult.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch mutual matches');
      }
    } catch (e) {
      throw Exception('Error fetching mutual matches: $e');
    }
  }

  Future<List<MatchResult>> getShortlistedProfiles({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get('/matches/shortlisted', queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      });
      
      if (response['success'] == true) {
        final matchesData = response['data']['matches'] as List;
        return matchesData.map((match) => MatchResult.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch shortlisted profiles');
      }
    } catch (e) {
      throw Exception('Error fetching shortlisted profiles: $e');
    }
  }

  Future<List<MatchResult>> getRecentlyViewedProfiles({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get('/matches/recent', queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      });
      
      if (response['success'] == true) {
        final matchesData = response['data']['matches'] as List;
        return matchesData.map((match) => MatchResult.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch recently viewed profiles');
      }
    } catch (e) {
      throw Exception('Error fetching recently viewed profiles: $e');
    }
  }
}

// Riverpod providers
final matchingServiceProvider = Provider<MatchingService>((ref) {
  return MatchingService();
});

final matchFiltersProvider = StateProvider<MatchFilters>((ref) {
  return const MatchFilters();
});

final matchesProvider = FutureProvider.family<List<MatchResult>, Map<String, dynamic>>((ref, params) async {
  final matchingService = ref.read(matchingServiceProvider);
  final filters = params['filters'] as MatchFilters?;
  final page = params['page'] as int? ?? 1;
  final limit = params['limit'] as int? ?? 20;
  
  return matchingService.getMatches(
    page: page,
    limit: limit,
    filters: filters,
  );
});

final recommendedMatchesProvider = FutureProvider<List<MatchResult>>((ref) async {
  final matchingService = ref.read(matchingServiceProvider);
  return matchingService.getRecommendedMatches();
});

final mutualMatchesProvider = FutureProvider<List<MatchResult>>((ref) async {
  final matchingService = ref.read(matchingServiceProvider);
  return matchingService.getMutualMatches();
});

// AI-based matching providers
final aiMatchesProvider = FutureProvider.family<List<MatchResult>, Map<String, dynamic>>((ref, params) async {
  final matchingService = ref.read(matchingServiceProvider);
  final limit = params['limit'] as int? ?? 10;
  final offset = params['offset'] as int? ?? 0;
  final minScore = params['minScore'] as int?;
  final includeExplanation = params['includeExplanation'] as bool? ?? false;

  return matchingService.getAIMatches(
    limit: limit,
    offset: offset,
    minScore: minScore,
    includeExplanation: includeExplanation,
  );
});

final categorizedMatchesProvider = FutureProvider.family<CategorizedMatches, Map<String, dynamic>>((ref, params) async {
  final matchingService = ref.read(matchingServiceProvider);
  final limit = params['limit'] as int? ?? 12;
  final offset = params['offset'] as int? ?? 0;

  return matchingService.getCategorizedMatches(
    limit: limit,
    offset: offset,
  );
});

final compatibilityScoreProvider = FutureProvider.family<Map<String, dynamic>, String>((ref, userId) async {
  final matchingService = ref.read(matchingServiceProvider);
  return matchingService.getCompatibilityScore(userId);
});
