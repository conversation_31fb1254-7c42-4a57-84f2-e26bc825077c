import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/api/api_client.dart';
import '../../../core/api/api_endpoints.dart';

import '../models/interest_model.dart';

/// 💝 INTERESTS SERVICE - Complete Interest Management System
/// Features: Send/Receive Interests, Dashboard Analytics, Real-time Updates

// API Client provider
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

class InterestsService {
  final ApiClient _apiClient;

  InterestsService(this._apiClient);

  /// Send interest to a user
  Future<InterestModel> sendInterest({
    required String targetUserId,
    String? message,
  }) async {
    try {
      final request = SendInterestRequest(
        targetUserId: targetUserId,
        message: message ?? 'I found your profile interesting and would like to connect.',
      );

      final response = await _apiClient.post(
        ApiEndpoints.sendInterest,
        request.toJson(),
      );

      if (response['success'] == true) {
        return InterestModel.fromJson(response['data']);
      } else {
        throw ApiException(response['message'] ?? 'Failed to send interest');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to send interest: $e');
    }
  }

  /// Respond to an interest (accept/decline)
  Future<InterestModel> respondToInterest({
    required String interestId,
    required String response,
    String? message,
  }) async {
    try {
      final request = RespondInterestRequest(
        interestId: interestId,
        response: response,
        message: message,
      );

      final responseData = await _apiClient.post(
        ApiEndpoints.respondInterest,
        request.toJson(),
      );

      if (responseData['success'] == true) {
        return InterestModel.fromJson(responseData['data']);
      } else {
        throw ApiException(responseData['message'] ?? 'Failed to respond to interest');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to respond to interest: $e');
    }
  }

  /// Get all interests (sent and received)
  Future<InterestListResponse> getInterests({
    int page = 1,
    int limit = 20,
    String? type, // 'sent' or 'received'
    String? status, // 'pending', 'accepted', 'declined'
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      if (type != null) queryParams['type'] = type;
      if (status != null) queryParams['status'] = status;

      final response = await _apiClient.get(
        ApiEndpoints.interests,
        queryParams: queryParams,
      );

      if (response['success'] == true) {
        return InterestListResponse.fromJson(response);
      } else {
        throw ApiException(response['message'] ?? 'Failed to fetch interests');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to fetch interests: $e');
    }
  }

  /// Get interests dashboard data
  Future<InterestDashboardResponse> getDashboardData() async {
    try {
      final response = await _apiClient.get('/interests/dashboard');

      if (response['success'] == true) {
        return InterestDashboardResponse.fromJson(response);
      } else {
        throw ApiException(response['message'] ?? 'Failed to fetch dashboard data');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to fetch dashboard data: $e');
    }
  }

  /// Get received interests
  Future<List<InterestModel>> getReceivedInterests({
    int page = 1,
    int limit = 20,
    String? status,
  }) async {
    try {
      final response = await getInterests(
        page: page,
        limit: limit,
        type: 'received',
        status: status,
      );
      return response.data.received;
    } catch (e) {
      throw ApiException('Failed to fetch received interests: $e');
    }
  }

  /// Get sent interests
  Future<List<InterestModel>> getSentInterests({
    int page = 1,
    int limit = 20,
    String? status,
  }) async {
    try {
      final response = await getInterests(
        page: page,
        limit: limit,
        type: 'sent',
        status: status,
      );
      return response.data.sent;
    } catch (e) {
      throw ApiException('Failed to fetch sent interests: $e');
    }
  }

  /// Get pending interests count
  Future<Map<String, int>> getPendingCounts() async {
    try {
      final response = await _apiClient.get('/interests/pending-counts');

      if (response['success'] == true) {
        return {
          'received': response['data']['received'] ?? 0,
          'sent': response['data']['sent'] ?? 0,
        };
      } else {
        throw ApiException(response['message'] ?? 'Failed to fetch pending counts');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to fetch pending counts: $e');
    }
  }

  /// Delete/withdraw an interest
  Future<bool> deleteInterest(String interestId) async {
    try {
      final response = await _apiClient.delete('/interests/$interestId');

      if (response['success'] == true) {
        return true;
      } else {
        throw ApiException(response['message'] ?? 'Failed to delete interest');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to delete interest: $e');
    }
  }

  /// Mark interest as read
  Future<bool> markAsRead(String interestId) async {
    try {
      final response = await _apiClient.patch(
        '/interests/$interestId/read',
        {'isRead': true},
      );

      if (response['success'] == true) {
        return true;
      } else {
        throw ApiException(response['message'] ?? 'Failed to mark as read');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to mark as read: $e');
    }
  }

  /// Get interest statistics
  Future<ActivityStatsModel> getInterestStats() async {
    try {
      final response = await _apiClient.get('/interests/stats');

      if (response['success'] == true) {
        return ActivityStatsModel.fromJson(response['data']);
      } else {
        throw ApiException(response['message'] ?? 'Failed to fetch stats');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to fetch stats: $e');
    }
  }

  /// Check if interest already sent to user
  Future<bool> hasInterestSent(String targetUserId) async {
    try {
      final response = await _apiClient.get(
        '/interests/check/$targetUserId',
      );

      if (response['success'] == true) {
        return response['data']['hasSent'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      return false; // Assume no interest sent if error
    }
  }

  /// Get mutual interests (both users sent interest to each other)
  Future<List<InterestModel>> getMutualInterests() async {
    try {
      final response = await _apiClient.get('/interests/mutual');

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((json) => InterestModel.fromJson(json)).toList();
      } else {
        throw ApiException(response['message'] ?? 'Failed to fetch mutual interests');
      }
    } on DioException catch (e) {
      throw ApiException(_handleDioError(e));
    } catch (e) {
      throw ApiException('Failed to fetch mutual interests: $e');
    }
  }

  /// Handle Dio errors
  String _handleDioError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
        return 'Connection timeout. Please check your internet connection.';
      case DioExceptionType.sendTimeout:
        return 'Request timeout. Please try again.';
      case DioExceptionType.receiveTimeout:
        return 'Server response timeout. Please try again.';
      case DioExceptionType.badResponse:
        final statusCode = e.response?.statusCode;
        final message = e.response?.data?['message'];
        if (statusCode == 400) {
          return message ?? 'Invalid request. Please check your input.';
        } else if (statusCode == 401) {
          return 'Authentication failed. Please login again.';
        } else if (statusCode == 403) {
          return 'Access denied. You don\'t have permission for this action.';
        } else if (statusCode == 404) {
          return 'Resource not found.';
        } else if (statusCode == 429) {
          return 'Too many requests. Please wait before trying again.';
        } else if (statusCode != null && statusCode >= 500) {
          return 'Server error. Please try again later.';
        }
        return message ?? 'Request failed. Please try again.';
      case DioExceptionType.cancel:
        return 'Request was cancelled.';
      case DioExceptionType.unknown:
        return 'Network error. Please check your internet connection.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }
}

// Provider for InterestsService
final interestsServiceProvider = Provider<InterestsService>((ref) {
  final apiClient = ref.watch(apiClientProvider);
  return InterestsService(apiClient);
});
