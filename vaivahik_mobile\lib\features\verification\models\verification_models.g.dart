// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'verification_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_VerificationDocument _$VerificationDocumentFromJson(
        Map<String, dynamic> json) =>
    _VerificationDocument(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
      url: json['url'] as String,
      filename: json['filename'] as String,
      filesize: (json['filesize'] as num).toInt(),
      mimeType: json['mimeType'] as String,
      status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
      adminNotes: json['adminNotes'] as String?,
      uploadedAt: DateTime.parse(json['uploadedAt'] as String),
      reviewedAt: json['reviewedAt'] == null
          ? null
          : DateTime.parse(json['reviewedAt'] as String),
      reviewedBy: json['reviewedBy'] as String?,
    );

Map<String, dynamic> _$VerificationDocumentToJson(
        _VerificationDocument instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': _$DocumentTypeEnumMap[instance.type]!,
      'url': instance.url,
      'filename': instance.filename,
      'filesize': instance.filesize,
      'mimeType': instance.mimeType,
      'status': _$DocumentStatusEnumMap[instance.status]!,
      'adminNotes': instance.adminNotes,
      'uploadedAt': instance.uploadedAt.toIso8601String(),
      'reviewedAt': instance.reviewedAt?.toIso8601String(),
      'reviewedBy': instance.reviewedBy,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.aadharCard: 'AADHAR_CARD',
  DocumentType.panCard: 'PAN_CARD',
  DocumentType.voterId: 'VOTER_ID',
  DocumentType.passport: 'PASSPORT',
  DocumentType.drivingLicense: 'DRIVING_LICENSE',
  DocumentType.other: 'OTHER',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.pendingReview: 'PENDING_REVIEW',
  DocumentStatus.approved: 'APPROVED',
  DocumentStatus.rejected: 'REJECTED',
};

_DocumentUploadRequest _$DocumentUploadRequestFromJson(
        Map<String, dynamic> json) =>
    _DocumentUploadRequest(
      documentType: $enumDecode(_$DocumentTypeEnumMap, json['documentType']),
      filePath: json['filePath'] as String,
      filename: json['filename'] as String,
    );

Map<String, dynamic> _$DocumentUploadRequestToJson(
        _DocumentUploadRequest instance) =>
    <String, dynamic>{
      'documentType': _$DocumentTypeEnumMap[instance.documentType]!,
      'filePath': instance.filePath,
      'filename': instance.filename,
    };

_DocumentUploadResponse _$DocumentUploadResponseFromJson(
        Map<String, dynamic> json) =>
    _DocumentUploadResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      document: json['document'] == null
          ? null
          : VerificationDocument.fromJson(
              json['document'] as Map<String, dynamic>),
      error: json['error'] as String?,
    );

Map<String, dynamic> _$DocumentUploadResponseToJson(
        _DocumentUploadResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'document': instance.document,
      'error': instance.error,
    };

_VerificationStatus _$VerificationStatusFromJson(Map<String, dynamic> json) =>
    _VerificationStatus(
      isVerified: json['isVerified'] as bool,
      profileStatus: json['profileStatus'] as String,
      documents: (json['documents'] as List<dynamic>)
          .map((e) => VerificationDocument.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalDocuments: (json['totalDocuments'] as num).toInt(),
      approvedDocuments: (json['approvedDocuments'] as num).toInt(),
      pendingDocuments: (json['pendingDocuments'] as num).toInt(),
      rejectedDocuments: (json['rejectedDocuments'] as num).toInt(),
      lastUploadedAt: json['lastUploadedAt'] == null
          ? null
          : DateTime.parse(json['lastUploadedAt'] as String),
      lastReviewedAt: json['lastReviewedAt'] == null
          ? null
          : DateTime.parse(json['lastReviewedAt'] as String),
    );

Map<String, dynamic> _$VerificationStatusToJson(_VerificationStatus instance) =>
    <String, dynamic>{
      'isVerified': instance.isVerified,
      'profileStatus': instance.profileStatus,
      'documents': instance.documents,
      'totalDocuments': instance.totalDocuments,
      'approvedDocuments': instance.approvedDocuments,
      'pendingDocuments': instance.pendingDocuments,
      'rejectedDocuments': instance.rejectedDocuments,
      'lastUploadedAt': instance.lastUploadedAt?.toIso8601String(),
      'lastReviewedAt': instance.lastReviewedAt?.toIso8601String(),
    };

_DocumentDeleteRequest _$DocumentDeleteRequestFromJson(
        Map<String, dynamic> json) =>
    _DocumentDeleteRequest(
      documentId: json['documentId'] as String,
    );

Map<String, dynamic> _$DocumentDeleteRequestToJson(
        _DocumentDeleteRequest instance) =>
    <String, dynamic>{
      'documentId': instance.documentId,
    };

_DocumentDeleteResponse _$DocumentDeleteResponseFromJson(
        Map<String, dynamic> json) =>
    _DocumentDeleteResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      error: json['error'] as String?,
    );

Map<String, dynamic> _$DocumentDeleteResponseToJson(
        _DocumentDeleteResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'error': instance.error,
    };

_VerificationStats _$VerificationStatsFromJson(Map<String, dynamic> json) =>
    _VerificationStats(
      totalUploaded: (json['totalUploaded'] as num).toInt(),
      approved: (json['approved'] as num).toInt(),
      pending: (json['pending'] as num).toInt(),
      rejected: (json['rejected'] as num).toInt(),
      completionPercentage: (json['completionPercentage'] as num).toDouble(),
      missingDocuments: (json['missingDocuments'] as List<dynamic>)
          .map((e) => $enumDecode(_$DocumentTypeEnumMap, e))
          .toList(),
      requiredDocuments: (json['requiredDocuments'] as List<dynamic>)
          .map((e) => $enumDecode(_$DocumentTypeEnumMap, e))
          .toList(),
    );

Map<String, dynamic> _$VerificationStatsToJson(_VerificationStats instance) =>
    <String, dynamic>{
      'totalUploaded': instance.totalUploaded,
      'approved': instance.approved,
      'pending': instance.pending,
      'rejected': instance.rejected,
      'completionPercentage': instance.completionPercentage,
      'missingDocuments': instance.missingDocuments
          .map((e) => _$DocumentTypeEnumMap[e]!)
          .toList(),
      'requiredDocuments': instance.requiredDocuments
          .map((e) => _$DocumentTypeEnumMap[e]!)
          .toList(),
    };

_FileUploadProgress _$FileUploadProgressFromJson(Map<String, dynamic> json) =>
    _FileUploadProgress(
      filename: json['filename'] as String,
      progress: (json['progress'] as num).toDouble(),
      isCompleted: json['isCompleted'] as bool,
      hasError: json['hasError'] as bool,
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$FileUploadProgressToJson(_FileUploadProgress instance) =>
    <String, dynamic>{
      'filename': instance.filename,
      'progress': instance.progress,
      'isCompleted': instance.isCompleted,
      'hasError': instance.hasError,
      'errorMessage': instance.errorMessage,
    };
