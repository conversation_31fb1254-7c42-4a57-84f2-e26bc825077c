import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../api/api_client.dart';

/// 🔧 API SERVICE - Token Management and Authentication
/// Provides centralized token management for API requests
/// Integrates with existing authentication system

class ApiService {
  static const _storage = FlutterSecureStorage();
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';

  /// Get current authentication token
  static Future<String?> getToken() async {
    try {
      return await _storage.read(key: _tokenKey);
    } catch (e) {
      print('Error getting token: $e');
      return null;
    }
  }

  /// Get refresh token
  static Future<String?> getRefreshToken() async {
    try {
      return await _storage.read(key: _refreshTokenKey);
    } catch (e) {
      print('Error getting refresh token: $e');
      return null;
    }
  }

  /// Store authentication token
  static Future<void> setToken(String token) async {
    try {
      await _storage.write(key: _tokenKey, value: token);
    } catch (e) {
      print('Error storing token: $e');
    }
  }

  /// Store refresh token
  static Future<void> setRefreshToken(String refreshToken) async {
    try {
      await _storage.write(key: _refreshTokenKey, value: refreshToken);
    } catch (e) {
      print('Error storing refresh token: $e');
    }
  }

  /// Clear all tokens
  static Future<void> clearTokens() async {
    try {
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _refreshTokenKey);
    } catch (e) {
      print('Error clearing tokens: $e');
    }
  }

  /// Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  /// Get API client instance with authentication
  static ApiClient getAuthenticatedClient() {
    return ApiClient();
  }

  /// Refresh authentication token
  static Future<bool> refreshAuthToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) return false;

      final apiClient = ApiClient();
      final response = await apiClient.post('/auth/refresh', {
        'refreshToken': refreshToken,
      });

      if (response['success'] == true) {
        final newToken = response['data']['token'];
        final newRefreshToken = response['data']['refreshToken'];
        
        await setToken(newToken);
        if (newRefreshToken != null) {
          await setRefreshToken(newRefreshToken);
        }
        
        return true;
      }
      
      return false;
    } catch (e) {
      print('Error refreshing token: $e');
      return false;
    }
  }

  /// Logout and clear all authentication data
  static Future<void> logout() async {
    try {
      final apiClient = ApiClient();
      await apiClient.post('/auth/logout', {});
    } catch (e) {
      print('Error during logout: $e');
    } finally {
      await clearTokens();
    }
  }
}
