import { useState, useEffect } from 'react';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import abTesting from '@/utils/abTesting';

export default function ABTestingPage() {
  const [tests, setTests] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTest, setSelectedTest] = useState(null);
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(true);

  // Form state for creating new test
  const [newTest, setNewTest] = useState({
    name: '',
    description: '',
    variants: [
      { id: 'control', name: 'Control', weight: 50 },
      { id: 'variant_a', name: 'Variant A', weight: 50 }
    ],
    trafficAllocation: 100,
    conversionGoals: ['subscription', 'profile_complete'],
    targetAudience: 'all'
  });

  useEffect(() => {
    loadTests();
  }, []);

  const loadTests = async () => {
    setLoading(true);
    try {
      // Load tests from A/B testing framework
      const activeTests = Array.from(abTesting.tests.values());
      setTests(activeTests);

      // Load results for each test
      const results = {};
      for (const test of activeTests) {
        results[test.id] = abTesting.getTestResults(test.id);
      }
      setTestResults(results);
    } catch (error) {
      console.error('Failed to load A/B tests:', error);
    } finally {
      setLoading(false);
    }
  };

  const createTest = async () => {
    try {
      const test = abTesting.createTest({
        ...newTest,
        createdBy: 'admin' // In real app, get from auth context
      });

      setTests([...tests, test]);
      setShowCreateModal(false);
      setNewTest({
        name: '',
        description: '',
        variants: [
          { id: 'control', name: 'Control', weight: 50 },
          { id: 'variant_a', name: 'Variant A', weight: 50 }
        ],
        trafficAllocation: 100,
        conversionGoals: ['subscription', 'profile_complete'],
        targetAudience: 'all'
      });
    } catch (error) {
      console.error('Failed to create A/B test:', error);
    }
  };

  const addVariant = () => {
    const newVariant = {
      id: `variant_${String.fromCharCode(65 + newTest.variants.length - 1)}`,
      name: `Variant ${String.fromCharCode(65 + newTest.variants.length - 1)}`,
      weight: Math.floor(100 / (newTest.variants.length + 1))
    };

    // Redistribute weights
    const totalVariants = newTest.variants.length + 1;
    const equalWeight = Math.floor(100 / totalVariants);
    
    setNewTest({
      ...newTest,
      variants: [
        ...newTest.variants.map(v => ({ ...v, weight: equalWeight })),
        newVariant
      ]
    });
  };

  const removeVariant = (index) => {
    if (newTest.variants.length <= 2) return; // Keep at least 2 variants

    const updatedVariants = newTest.variants.filter((_, i) => i !== index);
    const equalWeight = Math.floor(100 / updatedVariants.length);
    
    setNewTest({
      ...newTest,
      variants: updatedVariants.map(v => ({ ...v, weight: equalWeight }))
    });
  };

  const updateVariantWeight = (index, weight) => {
    const updatedVariants = [...newTest.variants];
    updatedVariants[index].weight = parseInt(weight);
    
    setNewTest({
      ...newTest,
      variants: updatedVariants
    });
  };

  return (
    <EnhancedAdminLayout title="A/B Testing">
      <div className="ab-testing-page">
        <div className="page-header">
          <h1>A/B Testing Dashboard</h1>
          <button 
            className="create-test-btn"
            onClick={() => setShowCreateModal(true)}
          >
            Create New Test
          </button>
        </div>

        {loading ? (
          <div className="loading">Loading A/B tests...</div>
        ) : (
          <div className="tests-grid">
            {tests.map(test => {
              const results = testResults[test.id];
              return (
                <div key={test.id} className="test-card">
                  <div className="test-header">
                    <h3>{test.name}</h3>
                    <span className={`status ${test.status}`}>{test.status}</span>
                  </div>
                  
                  <p className="test-description">{test.description}</p>
                  
                  <div className="test-stats">
                    <div className="stat">
                      <span className="label">Participants:</span>
                      <span className="value">{results?.totalParticipants || 0}</span>
                    </div>
                    <div className="stat">
                      <span className="label">Traffic:</span>
                      <span className="value">{test.trafficAllocation}%</span>
                    </div>
                  </div>

                  {results && (
                    <div className="variants-results">
                      {Object.entries(results.variants).map(([variantId, data]) => (
                        <div key={variantId} className="variant-result">
                          <div className="variant-name">{data.name}</div>
                          <div className="conversion-rate">
                            {data.conversionRate.toFixed(2)}%
                          </div>
                          {results.winner === variantId && (
                            <span className="winner-badge">Winner</span>
                          )}
                        </div>
                      ))}
                    </div>
                  )}

                  <div className="test-actions">
                    <button onClick={() => setSelectedTest(test)}>
                      View Details
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Create Test Modal */}
        {showCreateModal && (
          <div className="modal-overlay">
            <div className="modal">
              <div className="modal-header">
                <h2>Create New A/B Test</h2>
                <button 
                  className="close-btn"
                  onClick={() => setShowCreateModal(false)}
                >
                  ×
                </button>
              </div>

              <div className="modal-content">
                <div className="form-group">
                  <label>Test Name</label>
                  <input
                    type="text"
                    value={newTest.name}
                    onChange={(e) => setNewTest({...newTest, name: e.target.value})}
                    placeholder="e.g., Homepage Hero Button Color"
                  />
                </div>

                <div className="form-group">
                  <label>Description</label>
                  <textarea
                    value={newTest.description}
                    onChange={(e) => setNewTest({...newTest, description: e.target.value})}
                    placeholder="Describe what you're testing..."
                  />
                </div>

                <div className="form-group">
                  <label>Variants</label>
                  {newTest.variants.map((variant, index) => (
                    <div key={index} className="variant-input">
                      <input
                        type="text"
                        value={variant.name}
                        onChange={(e) => {
                          const updatedVariants = [...newTest.variants];
                          updatedVariants[index].name = e.target.value;
                          setNewTest({...newTest, variants: updatedVariants});
                        }}
                        placeholder="Variant name"
                      />
                      <input
                        type="number"
                        value={variant.weight}
                        onChange={(e) => updateVariantWeight(index, e.target.value)}
                        min="0"
                        max="100"
                        placeholder="Weight %"
                      />
                      {newTest.variants.length > 2 && (
                        <button 
                          type="button"
                          onClick={() => removeVariant(index)}
                          className="remove-variant"
                        >
                          Remove
                        </button>
                      )}
                    </div>
                  ))}
                  <button type="button" onClick={addVariant} className="add-variant">
                    Add Variant
                  </button>
                </div>

                <div className="form-group">
                  <label>Traffic Allocation (%)</label>
                  <input
                    type="number"
                    value={newTest.trafficAllocation}
                    onChange={(e) => setNewTest({...newTest, trafficAllocation: parseInt(e.target.value)})}
                    min="1"
                    max="100"
                  />
                </div>

                <div className="form-group">
                  <label>Target Audience</label>
                  <select
                    value={newTest.targetAudience}
                    onChange={(e) => setNewTest({...newTest, targetAudience: e.target.value})}
                  >
                    <option value="all">All Users</option>
                    <option value="new_users">New Users</option>
                    <option value="returning_users">Returning Users</option>
                    <option value="premium_users">Premium Users</option>
                  </select>
                </div>
              </div>

              <div className="modal-actions">
                <button 
                  onClick={() => setShowCreateModal(false)}
                  className="cancel-btn"
                >
                  Cancel
                </button>
                <button 
                  onClick={createTest}
                  className="create-btn"
                  disabled={!newTest.name || !newTest.description}
                >
                  Create Test
                </button>
              </div>
            </div>
          </div>
        )}

        <style jsx>{`
          .ab-testing-page {
            padding: 20px;
          }

          .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
          }

          .create-test-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
          }

          .tests-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
          }

          .test-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
          }

          .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
          }

          .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
          }

          .status.active {
            background: #4caf50;
            color: white;
          }

          .test-stats {
            display: flex;
            gap: 20px;
            margin: 15px 0;
          }

          .stat {
            display: flex;
            flex-direction: column;
            gap: 4px;
          }

          .variants-results {
            margin: 15px 0;
          }

          .variant-result {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          }

          .winner-badge {
            background: #ff6b6b;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 10px;
          }

          .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
          }

          .modal {
            background: white;
            border-radius: 16px;
            width: 90%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
          }

          .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid #eee;
          }

          .modal-content {
            padding: 20px;
          }

          .form-group {
            margin-bottom: 20px;
          }

          .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
          }

          .form-group input,
          .form-group textarea,
          .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 8px;
          }

          .variant-input {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            align-items: center;
          }

          .variant-input input {
            flex: 1;
          }

          .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            padding: 20px;
            border-top: 1px solid #eee;
          }

          .create-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
          }

          .create-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
          }
        `}</style>
      </div>
    </EnhancedAdminLayout>
  );
}
