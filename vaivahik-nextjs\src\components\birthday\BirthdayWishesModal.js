import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogActions,
  Box,
  Typography,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Grid,
  IconButton,
  Fade,
  Zoom,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Close as CloseIcon,
  Cake as CakeIcon,
  Star as StarIcon,
  LocalOffer as OfferIcon,
  Share as ShareIcon,
  Favorite as FavoriteIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import confetti from 'canvas-confetti';

/**
 * Birthday Wishes Modal Component
 * 
 * Displays AI-generated birthday wishes with animations, special offers,
 * and celebration effects when user opens the app on their birthday.
 */
const BirthdayWishesModal = ({ open, onClose, birthdayData }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [showConfetti, setShowConfetti] = useState(false);

  useEffect(() => {
    if (open && birthdayData) {
      // Trigger confetti animation when modal opens
      setShow<PERSON>onfetti(true);
      triggerConfetti();
    }
  }, [open, birthdayData]);

  const triggerConfetti = () => {
    // Multiple confetti bursts
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

    function randomInRange(min, max) {
      return Math.random() * (max - min) + min;
    }

    const interval = setInterval(function() {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);
      
      // Left side
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
      }));
      
      // Right side
      confetti(Object.assign({}, defaults, {
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
      }));
    }, 250);
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: 'Birthday Celebration!',
        text: `Celebrating my birthday with Vaivahik! ${birthdayData?.birthdayWishes?.message}`,
        url: window.location.origin
      });
    }
  };

  if (!birthdayData?.birthdayWishes) return null;

  const wishes = birthdayData.birthdayWishes;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : 3,
          background: `linear-gradient(135deg, ${wishes.color || '#FF6B6B'} 0%, ${theme.palette.primary.main} 100%)`,
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }
      }}
      TransitionComponent={Zoom}
      TransitionProps={{ timeout: 500 }}
    >
      {/* Background decorative elements */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.4"%3E%3Ccircle cx="30" cy="30" r="4"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          zIndex: 0
        }}
      />

      {/* Close button */}
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          color: 'white',
          backgroundColor: 'rgba(255,255,255,0.2)',
          zIndex: 2,
          '&:hover': {
            backgroundColor: 'rgba(255,255,255,0.3)'
          }
        }}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent sx={{ p: 4, position: 'relative', zIndex: 1 }}>
        <AnimatePresence>
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Main birthday message */}
            <Box textAlign="center" mb={4}>
              <motion.div
                animate={{ 
                  rotate: [0, -10, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{ 
                  duration: 2,
                  repeat: Infinity,
                  repeatDelay: 3
                }}
              >
                <Typography
                  variant="h2"
                  sx={{ 
                    fontSize: { xs: '3rem', md: '4rem' },
                    mb: 2,
                    textShadow: '2px 2px 4px rgba(0,0,0,0.3)'
                  }}
                >
                  {wishes.emoji || '🎉'}
                </Typography>
              </motion.div>
              
              <Typography
                variant="h4"
                sx={{ 
                  fontWeight: 'bold',
                  mb: 2,
                  fontSize: { xs: '1.5rem', md: '2rem' },
                  textShadow: '1px 1px 2px rgba(0,0,0,0.3)'
                }}
              >
                {wishes.title}
              </Typography>
              
              <Typography
                variant="h6"
                sx={{ 
                  opacity: 0.9,
                  lineHeight: 1.6,
                  maxWidth: '600px',
                  mx: 'auto'
                }}
              >
                {wishes.message}
              </Typography>

              {wishes.age && (
                <Chip
                  icon={<CakeIcon />}
                  label={`${wishes.age} Years Young!`}
                  sx={{
                    mt: 2,
                    backgroundColor: 'rgba(255,255,255,0.2)',
                    color: 'white',
                    fontWeight: 'bold',
                    fontSize: '1rem',
                    height: 40
                  }}
                />
              )}
            </Box>

            {/* Zodiac message */}
            {wishes.zodiacMessage && (
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                <Card sx={{ mb: 3, backgroundColor: 'rgba(255,255,255,0.15)', backdropFilter: 'blur(10px)' }}>
                  <CardContent>
                    <Box display="flex" alignItems="center" mb={1}>
                      <StarIcon sx={{ mr: 1, color: '#FFD700' }} />
                      <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold' }}>
                        Your Birthday Stars Say:
                      </Typography>
                    </Box>
                    <Typography variant="body1" sx={{ color: 'white', opacity: 0.9 }}>
                      {wishes.zodiacMessage}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Special offers */}
            {wishes.specialOffers && wishes.specialOffers.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              >
                <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', color: 'white' }}>
                  <OfferIcon sx={{ mr: 1 }} />
                  Birthday Special Offers
                </Typography>
                <Grid container spacing={2}>
                  {wishes.specialOffers.map((offer, index) => (
                    <Grid item xs={12} md={6} key={index}>
                      <Card sx={{ 
                        backgroundColor: 'rgba(255,255,255,0.2)', 
                        backdropFilter: 'blur(10px)',
                        border: '1px solid rgba(255,255,255,0.3)'
                      }}>
                        <CardContent>
                          <Typography variant="h6" sx={{ color: 'white', fontWeight: 'bold', mb: 1 }}>
                            {offer.title}
                          </Typography>
                          <Typography variant="body2" sx={{ color: 'white', opacity: 0.9, mb: 2 }}>
                            {offer.description}
                          </Typography>
                          <Chip
                            label={`Code: ${offer.code}`}
                            sx={{
                              backgroundColor: '#FFD700',
                              color: '#000',
                              fontWeight: 'bold'
                            }}
                          />
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </motion.div>
            )}

            {/* Action buttons */}
            <Box display="flex" justifyContent="center" gap={2} mt={4}>
              <Button
                variant="contained"
                size="large"
                onClick={handleShare}
                startIcon={<ShareIcon />}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.2)',
                  backdropFilter: 'blur(10px)',
                  color: 'white',
                  border: '1px solid rgba(255,255,255,0.3)',
                  '&:hover': {
                    backgroundColor: 'rgba(255,255,255,0.3)'
                  }
                }}
              >
                Share Joy
              </Button>
              
              <Button
                variant="contained"
                size="large"
                onClick={onClose}
                startIcon={<FavoriteIcon />}
                sx={{
                  backgroundColor: 'rgba(255,255,255,0.9)',
                  color: wishes.color || theme.palette.primary.main,
                  fontWeight: 'bold',
                  '&:hover': {
                    backgroundColor: 'white'
                  }
                }}
              >
                Thank You!
              </Button>
            </Box>
          </motion.div>
        </AnimatePresence>
      </DialogContent>
    </Dialog>
  );
};

export default BirthdayWishesModal;
