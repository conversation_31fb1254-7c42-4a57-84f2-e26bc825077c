import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/api_client.dart';

class KundaliData {
  final String userId;
  final DateTime birthDate;
  final String birthTime;
  final String birthPlace;
  final double latitude;
  final double longitude;
  final String timezone;
  final Map<String, dynamic> planetaryPositions;
  final Map<String, dynamic> houses;
  final Map<String, dynamic> nakshatras;
  final Map<String, dynamic> yogas;
  final bool isPremium;

  const KundaliData({
    required this.userId,
    required this.birthDate,
    required this.birthTime,
    required this.birthPlace,
    required this.latitude,
    required this.longitude,
    required this.timezone,
    required this.planetaryPositions,
    required this.houses,
    required this.nakshatras,
    required this.yogas,
    this.isPremium = false,
  });

  factory KundaliData.fromJson(Map<String, dynamic> json) {
    return KundaliData(
      userId: json['userId'] ?? '',
      birthDate: DateTime.parse(json['birthDate']),
      birthTime: json['birthTime'] ?? '',
      birthPlace: json['birthPlace'] ?? '',
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      timezone: json['timezone'] ?? '',
      planetaryPositions: json['planetaryPositions'] ?? {},
      houses: json['houses'] ?? {},
      nakshatras: json['nakshatras'] ?? {},
      yogas: json['yogas'] ?? {},
      isPremium: json['isPremium'] ?? false,
    );
  }
}

class KundaliMatchResult {
  final double totalScore;
  final Map<String, dynamic> gunaScores;
  final List<String> compatibleAspects;
  final List<String> incompatibleAspects;
  final String overallCompatibility;
  final Map<String, dynamic> detailedAnalysis;
  final bool isPremiumAnalysis;
  final String recommendation;

  const KundaliMatchResult({
    required this.totalScore,
    required this.gunaScores,
    required this.compatibleAspects,
    required this.incompatibleAspects,
    required this.overallCompatibility,
    required this.detailedAnalysis,
    this.isPremiumAnalysis = false,
    required this.recommendation,
  });

  factory KundaliMatchResult.fromJson(Map<String, dynamic> json) {
    return KundaliMatchResult(
      totalScore: (json['totalScore'] ?? 0.0).toDouble(),
      gunaScores: json['gunaScores'] ?? {},
      compatibleAspects: List<String>.from(json['compatibleAspects'] ?? []),
      incompatibleAspects: List<String>.from(json['incompatibleAspects'] ?? []),
      overallCompatibility: json['overallCompatibility'] ?? '',
      detailedAnalysis: json['detailedAnalysis'] ?? {},
      isPremiumAnalysis: json['isPremiumAnalysis'] ?? false,
      recommendation: json['recommendation'] ?? '',
    );
  }
}

class PlanetaryPosition {
  final String planet;
  final double longitude;
  final String sign;
  final String nakshatra;
  final int house;
  final bool isRetrograde;
  final String significance;

  const PlanetaryPosition({
    required this.planet,
    required this.longitude,
    required this.sign,
    required this.nakshatra,
    required this.house,
    this.isRetrograde = false,
    required this.significance,
  });

  factory PlanetaryPosition.fromJson(Map<String, dynamic> json) {
    return PlanetaryPosition(
      planet: json['planet'] ?? '',
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      sign: json['sign'] ?? '',
      nakshatra: json['nakshatra'] ?? '',
      house: json['house'] ?? 1,
      isRetrograde: json['isRetrograde'] ?? false,
      significance: json['significance'] ?? '',
    );
  }
}

class KundaliService {
  final ApiClient _apiClient = ApiClient();

  // Generate Kundali using website's comprehensive kundali service
  Future<KundaliData> generateKundali({
    required DateTime birthDate,
    required String birthTime,
    required String birthPlace,
    required double latitude,
    required double longitude,
    String? timezone,
    bool isPremium = false,
  }) async {
    try {
      final response = await _apiClient.post('/kundali/generate', {
        'birthDate': birthDate.toIso8601String(),
        'birthTime': birthTime,
        'birthPlace': birthPlace,
        'latitude': latitude,
        'longitude': longitude,
        'timezone': timezone ?? 'Asia/Kolkata',
        'isPremium': isPremium,
        'includeYogas': isPremium,
        'includeTransits': isPremium,
        'includePredictions': isPremium,
      });

      if (response['success'] == true) {
        return KundaliData.fromJson(response['kundali']);
      } else {
        throw Exception(response['message'] ?? 'Failed to generate kundali');
      }
    } catch (e) {
      throw Exception('Error generating kundali: $e');
    }
  }

  // Match two kundalis using Ashtakoot Guna matching
  Future<KundaliMatchResult> matchKundalis({
    required String userId1,
    required String userId2,
    bool isPremium = false,
  }) async {
    try {
      final response = await _apiClient.post('/kundali/match', {
        'userId1': userId1,
        'userId2': userId2,
        'isPremium': isPremium,
        'includeDetailedAnalysis': isPremium,
        'includeRemedies': isPremium,
        'includePredictions': isPremium,
      });

      if (response['success'] == true) {
        return KundaliMatchResult.fromJson(response['matchResult']);
      } else {
        throw Exception(response['message'] ?? 'Failed to match kundalis');
      }
    } catch (e) {
      throw Exception('Error matching kundalis: $e');
    }
  }

  // Get planetary positions for a specific date/time
  Future<List<PlanetaryPosition>> getPlanetaryPositions({
    required DateTime dateTime,
    required double latitude,
    required double longitude,
    String? timezone,
  }) async {
    try {
      final response = await _apiClient.get('/kundali/planetary-positions', queryParams: {
        'dateTime': dateTime.toIso8601String(),
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'timezone': timezone ?? 'Asia/Kolkata',
      });

      if (response['success'] == true) {
        final positions = response['positions'] as List;
        return positions.map((pos) => PlanetaryPosition.fromJson(pos)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to get planetary positions');
      }
    } catch (e) {
      throw Exception('Error getting planetary positions: $e');
    }
  }

  // Get compatibility analysis between two users
  Future<Map<String, dynamic>> getCompatibilityAnalysis({
    required String userId1,
    required String userId2,
    bool includeRemedies = false,
  }) async {
    try {
      final response = await _apiClient.get('/kundali/compatibility/$userId1/$userId2', queryParams: {
        'includeRemedies': includeRemedies.toString(),
      });

      if (response['success'] == true) {
        return response['analysis'];
      } else {
        throw Exception(response['message'] ?? 'Failed to get compatibility analysis');
      }
    } catch (e) {
      throw Exception('Error getting compatibility analysis: $e');
    }
  }

  // Get Manglik status and analysis
  Future<Map<String, dynamic>> getManglikAnalysis(String userId) async {
    try {
      final response = await _apiClient.get('/kundali/manglik/$userId');

      if (response['success'] == true) {
        return response['analysis'];
      } else {
        throw Exception(response['message'] ?? 'Failed to get Manglik analysis');
      }
    } catch (e) {
      throw Exception('Error getting Manglik analysis: $e');
    }
  }

  // Get daily horoscope
  Future<Map<String, dynamic>> getDailyHoroscope({
    required String userId,
    DateTime? date,
  }) async {
    try {
      final response = await _apiClient.get('/kundali/horoscope/daily/$userId', queryParams: {
        'date': (date ?? DateTime.now()).toIso8601String().split('T')[0],
      });

      if (response['success'] == true) {
        return response['horoscope'];
      } else {
        throw Exception(response['message'] ?? 'Failed to get daily horoscope');
      }
    } catch (e) {
      throw Exception('Error getting daily horoscope: $e');
    }
  }

  // Get saved kundali for a user
  Future<KundaliData?> getUserKundali(String userId) async {
    try {
      final response = await _apiClient.get('/kundali/user/$userId');

      if (response['success'] == true && response['kundali'] != null) {
        return KundaliData.fromJson(response['kundali']);
      } else {
        return null;
      }
    } catch (e) {
      throw Exception('Error getting user kundali: $e');
    }
  }

  // Save kundali for a user
  Future<bool> saveUserKundali({
    required String userId,
    required KundaliData kundaliData,
  }) async {
    try {
      final response = await _apiClient.post('/kundali/save', {
        'userId': userId,
        'kundaliData': {
          'birthDate': kundaliData.birthDate.toIso8601String(),
          'birthTime': kundaliData.birthTime,
          'birthPlace': kundaliData.birthPlace,
          'latitude': kundaliData.latitude,
          'longitude': kundaliData.longitude,
          'timezone': kundaliData.timezone,
        },
      });

      return response['success'] == true;
    } catch (e) {
      throw Exception('Error saving kundali: $e');
    }
  }
}

// Riverpod providers
final kundaliServiceProvider = Provider<KundaliService>((ref) {
  return KundaliService();
});

final userKundaliProvider = FutureProvider.family<KundaliData?, String>((ref, userId) async {
  final kundaliService = ref.read(kundaliServiceProvider);
  return kundaliService.getUserKundali(userId);
});

final kundaliMatchProvider = FutureProvider.family<KundaliMatchResult, Map<String, dynamic>>((ref, params) async {
  final kundaliService = ref.read(kundaliServiceProvider);
  final userId1 = params['userId1'] as String;
  final userId2 = params['userId2'] as String;
  final isPremium = params['isPremium'] as bool? ?? false;
  
  return kundaliService.matchKundalis(
    userId1: userId1,
    userId2: userId2,
    isPremium: isPremium,
  );
});

final planetaryPositionsProvider = FutureProvider.family<List<PlanetaryPosition>, Map<String, dynamic>>((ref, params) async {
  final kundaliService = ref.read(kundaliServiceProvider);
  final dateTime = params['dateTime'] as DateTime;
  final latitude = params['latitude'] as double;
  final longitude = params['longitude'] as double;
  final timezone = params['timezone'] as String?;
  
  return kundaliService.getPlanetaryPositions(
    dateTime: dateTime,
    latitude: latitude,
    longitude: longitude,
    timezone: timezone,
  );
});

final dailyHoroscopeProvider = FutureProvider.family<Map<String, dynamic>, Map<String, dynamic>>((ref, params) async {
  final kundaliService = ref.read(kundaliServiceProvider);
  final userId = params['userId'] as String;
  final date = params['date'] as DateTime?;
  
  return kundaliService.getDailyHoroscope(
    userId: userId,
    date: date,
  );
});
