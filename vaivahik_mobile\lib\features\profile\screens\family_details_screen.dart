import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/widgets/premium_widgets.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../app/theme.dart';

class FamilyDetailsScreen extends StatefulWidget {
  const FamilyDetailsScreen({super.key});

  @override
  State<FamilyDetailsScreen> createState() => _FamilyDetailsScreenState();
}

class _FamilyDetailsScreenState extends State<FamilyDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fatherNameController = TextEditingController();
  final _motherNameController = TextEditingController();
  final _uncleNameController = TextEditingController();
  final _familyContactController = TextEditingController();
  
  String? selectedFamilyType;
  String? selectedFamilyValues;
  int? totalSiblings;
  int? marriedSiblings;
  
  bool isLoading = false;

  final List<String> familyTypeOptions = [
    'Nuclear Family',
    'Joint Family',
    'Extended Family',
  ];
  
  final List<String> familyValuesOptions = [
    'Traditional',
    'Moderate',
    'Liberal',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Family Details',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              GlassmorphicCard(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.family_restroom_outlined,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Family Information',
                              style: TextStyle(
                                color: AppTheme.textColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Tell us about your family',
                              style: TextStyle(
                                color: AppTheme.textColor.withValues(alpha: 0.7),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
              
              const SizedBox(height: 24),
              
              // Family Members Section
              _buildFormSection(
                title: 'Family Members',
                icon: Icons.people,
                children: [
                  CustomTextField(
                    controller: _fatherNameController,
                    label: 'Father\'s Name',
                    hint: 'Enter father\'s name',
                    prefixIcon: const Icon(Icons.person),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter father\'s name';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  CustomTextField(
                    controller: _motherNameController,
                    label: 'Mother\'s Name',
                    hint: 'Enter mother\'s name',
                    prefixIcon: const Icon(Icons.person),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter mother\'s name';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  CustomTextField(
                    controller: _uncleNameController,
                    label: 'Uncle\'s Name (Optional)',
                    hint: 'Enter uncle\'s name',
                    prefixIcon: const Icon(Icons.person),
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Family Background Section
              _buildFormSection(
                title: 'Family Background',
                icon: Icons.home,
                children: [
                  _buildDropdownField(
                    label: 'Family Type',
                    hint: 'Select family type',
                    value: selectedFamilyType,
                    items: familyTypeOptions,
                    onChanged: (value) => setState(() => selectedFamilyType = value),
                    prefixIcon: Icons.home,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildDropdownField(
                    label: 'Family Values',
                    hint: 'Select family values',
                    value: selectedFamilyValues,
                    items: familyValuesOptions,
                    onChanged: (value) => setState(() => selectedFamilyValues = value),
                    prefixIcon: Icons.favorite,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  CustomTextField(
                    controller: _familyContactController,
                    label: 'Family Contact',
                    hint: 'Enter family contact number',
                    prefixIcon: const Icon(Icons.phone),
                    keyboardType: TextInputType.phone,
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Save Button
              PremiumGradientButton(
                text: isLoading ? 'Saving...' : 'Save & Continue',
                onPressed: isLoading ? null : _saveFamilyDetails,
                width: double.infinity,
              ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return GlassmorphicCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData prefixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.cardColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            hint: Text(
              hint,
              style: TextStyle(
                color: AppTheme.textColor.withValues(alpha: 0.5),
              ),
            ),
            decoration: InputDecoration(
              prefixIcon: Icon(prefixIcon, color: AppTheme.primaryColor),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            dropdownColor: AppTheme.cardColor,
            style: TextStyle(color: AppTheme.textColor),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Future<void> _saveFamilyDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      // Implement API call to save family details
      await _saveFamilyDetailsToAPI();
      await Future.delayed(const Duration(seconds: 2)); // Mock API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Family details saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _saveFamilyDetailsToAPI() async {
    // Simulate API call to save family details information
    print('Saving family details information to API...');
    print('Father Name: ${_fatherNameController.text}');
    print('Mother Name: ${_motherNameController.text}');
    print('Uncle Name: ${_uncleNameController.text}');
    print('Family Contact: ${_familyContactController.text}');
    print('Family Type: $selectedFamilyType');
    print('Family Values: $selectedFamilyValues');
    print('Total Siblings: $totalSiblings');
    print('Married Siblings: $marriedSiblings');
    // In a real implementation, this would make HTTP request to backend
    await Future.delayed(const Duration(milliseconds: 500));
    print('Family details information saved successfully');
  }

  @override
  void dispose() {
    _fatherNameController.dispose();
    _motherNameController.dispose();
    _uncleNameController.dispose();
    _familyContactController.dispose();
    super.dispose();
  }
}
