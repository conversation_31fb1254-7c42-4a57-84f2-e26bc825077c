import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../app/theme.dart';
import '../../../core/api/api_client.dart';


/// 💝 INTEREST & ACTIVITY DASHBOARD - Comprehensive Analytics
/// Features: Interest Management, Activity Timeline, Match Analytics, Interactive Charts

class ActivityDashboardScreen extends ConsumerStatefulWidget {
  const ActivityDashboardScreen({super.key});

  @override
  ConsumerState<ActivityDashboardScreen> createState() => _ActivityDashboardScreenState();
}

class _ActivityDashboardScreenState extends ConsumerState<ActivityDashboardScreen> 
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late TabController _tabController;
  
  bool _isLoading = true;
  ActivityStats _stats = ActivityStats.empty();
  List<InterestActivity> _recentActivities = [];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _tabController = TabController(length: 3, vsync: this);
    
    _animationController.forward();
    _loadDashboardData();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDashboardData() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/interests/dashboard');
      
      if (response['success'] == true) {
        final data = response['data'];
        setState(() {
          _stats = ActivityStats.fromJson(data['stats']);
          _recentActivities = (data['recent_activities'] as List)
              .map((activity) => InterestActivity.fromJson(activity))
              .toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Activity Dashboard'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadDashboardData,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isLoading ? _buildLoadingState() : _buildDashboard(),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading your activity...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboard() {
    return Column(
      children: [
        _buildStatsOverview(),
        _buildTabBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildInterestsTab(),
              _buildAnalyticsTab(),
              _buildActivityTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatsOverview() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.favorite,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Your Activity Summary',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Track your matrimony journey',
                      style: TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Interests Sent',
                  _stats.interestsSent.toString(),
                  Icons.send,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Interests Received',
                  _stats.interestsReceived.toString(),
                  Icons.inbox,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'Profile Views',
                  _stats.profileViews.toString(),
                  Icons.visibility,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  'Matches',
                  _stats.totalMatches.toString(),
                  Icons.favorite,
                  Colors.pink,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate(controller: _animationController)
     .slideY(begin: -0.5, curve: Curves.elasticOut)
     .fadeIn();
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            textAlign: TextAlign.center,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        tabs: const [
          Tab(
            icon: Icon(Icons.favorite),
            text: 'Interests',
          ),
          Tab(
            icon: Icon(Icons.analytics),
            text: 'Analytics',
          ),
          Tab(
            icon: Icon(Icons.timeline),
            text: 'Activity',
          ),
        ],
      ),
    ).animate()
     .slideX(begin: 1, delay: 200.ms)
     .fadeIn(delay: 200.ms);
  }

  Widget _buildInterestsTab() {
    return DefaultTabController(
      length: 2,
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const TabBar(
              labelColor: Colors.black,
              unselectedLabelColor: Colors.grey,
              tabs: [
                Tab(text: 'Sent'),
                Tab(text: 'Received'),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              children: [
                _buildInterestsList(InterestType.sent),
                _buildInterestsList(InterestType.received),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInterestsList(InterestType type) {
    final interests = _recentActivities
        .where((activity) => activity.type == type)
        .toList();

    if (interests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type == InterestType.sent ? Icons.send : Icons.inbox,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              type == InterestType.sent 
                  ? 'No interests sent yet'
                  : 'No interests received yet',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              type == InterestType.sent
                  ? 'Start exploring profiles and send interests'
                  : 'Your profile will receive interests from others',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: interests.length,
      itemBuilder: (context, index) {
        final interest = interests[index];
        return _buildInterestCard(interest, index);
      },
    );
  }

  Widget _buildInterestCard(InterestActivity interest, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          radius: 25,
          backgroundColor: interest.profilePhoto != null 
              ? Colors.transparent 
              : Colors.grey[300],
          backgroundImage: interest.profilePhoto != null 
              ? NetworkImage(interest.profilePhoto!) 
              : null,
          child: interest.profilePhoto == null 
              ? Icon(Icons.person, color: Colors.grey[600])
              : null,
        ),
        title: Text(
          interest.profileName,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${interest.age} years • ${interest.location}',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              _formatDateTime(interest.timestamp),
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: _buildInterestStatus(interest.status),
        onTap: () => _viewProfile(interest.profileId),
      ),
    ).animate()
     .slideX(begin: 1, delay: (index * 100).ms)
     .fadeIn(delay: (index * 100).ms);
  }

  Widget _buildInterestStatus(InterestStatus status) {
    Color color;
    IconData icon;
    String text;

    switch (status) {
      case InterestStatus.pending:
        color = Colors.orange;
        icon = Icons.schedule;
        text = 'Pending';
        break;
      case InterestStatus.accepted:
        color = Colors.green;
        icon = Icons.check_circle;
        text = 'Accepted';
        break;
      case InterestStatus.declined:
        color = Colors.red;
        icon = Icons.cancel;
        text = 'Declined';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildProfileViewsChart(),
          const SizedBox(height: 20),
          _buildInterestTrendsChart(),
          const SizedBox(height: 20),
          _buildMatchSuccessRate(),
        ],
      ),
    );
  }

  Widget _buildProfileViewsChart() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Profile Views (Last 7 Days)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: const FlGridData(show: false),
                titlesData: const FlTitlesData(show: false),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: _stats.weeklyViews.asMap().entries.map((entry) {
                      return FlSpot(entry.key.toDouble(), entry.value.toDouble());
                    }).toList(),
                    isCurved: true,
                    color: AppColors.primary,
                    barWidth: 3,
                    dotData: const FlDotData(show: true),
                    belowBarData: BarAreaData(
                      show: true,
                      color: AppColors.primary.withValues(alpha: 0.1),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 0.5, delay: 400.ms)
     .fadeIn(delay: 400.ms);
  }

  Widget _buildInterestTrendsChart() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Interest Trends',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: 20,
                barTouchData: BarTouchData(enabled: false),
                titlesData: const FlTitlesData(show: false),
                borderData: FlBorderData(show: false),
                barGroups: [
                  BarChartGroupData(
                    x: 0,
                    barRods: [
                      BarChartRodData(
                        toY: _stats.interestsSent.toDouble(),
                        color: Colors.blue,
                        width: 20,
                      ),
                    ],
                  ),
                  BarChartGroupData(
                    x: 1,
                    barRods: [
                      BarChartRodData(
                        toY: _stats.interestsReceived.toDouble(),
                        color: Colors.green,
                        width: 20,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildLegendItem('Sent', Colors.blue),
              _buildLegendItem('Received', Colors.green),
            ],
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 0.5, delay: 600.ms)
     .fadeIn(delay: 600.ms);
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildMatchSuccessRate() {
    final successRate = _stats.interestsSent > 0 
        ? (_stats.acceptedInterests / _stats.interestsSent * 100).round()
        : 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        children: [
          const Text(
            'Match Success Rate',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          SizedBox(
            height: 150,
            child: PieChart(
              PieChartData(
                sectionsSpace: 2,
                centerSpaceRadius: 40,
                sections: [
                  PieChartSectionData(
                    color: Colors.green,
                    value: successRate.toDouble(),
                    title: '$successRate%',
                    radius: 50,
                    titleStyle: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  PieChartSectionData(
                    color: Colors.grey[300]!,
                    value: (100 - successRate).toDouble(),
                    title: '',
                    radius: 50,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Success Rate',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 0.5, delay: 800.ms)
     .fadeIn(delay: 800.ms);
  }

  Widget _buildActivityTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _recentActivities.length,
      itemBuilder: (context, index) {
        final activity = _recentActivities[index];
        return _buildActivityItem(activity, index);
      },
    );
  }

  Widget _buildActivityItem(InterestActivity activity, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getActivityColor(activity.type).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getActivityIcon(activity.type),
              color: _getActivityColor(activity.type),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getActivityDescription(activity),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _formatDateTime(activity.timestamp),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    ).animate()
     .slideX(begin: 1, delay: (index * 50).ms)
     .fadeIn(delay: (index * 50).ms);
  }

  Color _getActivityColor(InterestType type) {
    switch (type) {
      case InterestType.sent:
        return Colors.blue;
      case InterestType.received:
        return Colors.green;
    }
  }

  IconData _getActivityIcon(InterestType type) {
    switch (type) {
      case InterestType.sent:
        return Icons.send;
      case InterestType.received:
        return Icons.inbox;
    }
  }

  String _getActivityDescription(InterestActivity activity) {
    switch (activity.type) {
      case InterestType.sent:
        return 'You sent interest to ${activity.profileName}';
      case InterestType.received:
        return '${activity.profileName} sent you interest';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  void _viewProfile(String profileId) {
    // Navigate to profile view
  }
}

class ActivityStats {
  final int interestsSent;
  final int interestsReceived;
  final int profileViews;
  final int totalMatches;
  final int acceptedInterests;
  final List<int> weeklyViews;

  const ActivityStats({
    required this.interestsSent,
    required this.interestsReceived,
    required this.profileViews,
    required this.totalMatches,
    required this.acceptedInterests,
    required this.weeklyViews,
  });

  factory ActivityStats.empty() {
    return const ActivityStats(
      interestsSent: 0,
      interestsReceived: 0,
      profileViews: 0,
      totalMatches: 0,
      acceptedInterests: 0,
      weeklyViews: [0, 0, 0, 0, 0, 0, 0],
    );
  }

  factory ActivityStats.fromJson(Map<String, dynamic> json) {
    return ActivityStats(
      interestsSent: json['interests_sent'] ?? 0,
      interestsReceived: json['interests_received'] ?? 0,
      profileViews: json['profile_views'] ?? 0,
      totalMatches: json['total_matches'] ?? 0,
      acceptedInterests: json['accepted_interests'] ?? 0,
      weeklyViews: List<int>.from(json['weekly_views'] ?? [0, 0, 0, 0, 0, 0, 0]),
    );
  }
}

class InterestActivity {
  final String id;
  final String profileId;
  final String profileName;
  final String? profilePhoto;
  final int age;
  final String location;
  final InterestType type;
  final InterestStatus status;
  final DateTime timestamp;

  const InterestActivity({
    required this.id,
    required this.profileId,
    required this.profileName,
    this.profilePhoto,
    required this.age,
    required this.location,
    required this.type,
    required this.status,
    required this.timestamp,
  });

  factory InterestActivity.fromJson(Map<String, dynamic> json) {
    return InterestActivity(
      id: json['id'],
      profileId: json['profile_id'],
      profileName: json['profile_name'],
      profilePhoto: json['profile_photo'],
      age: json['age'],
      location: json['location'],
      type: InterestType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => InterestType.sent,
      ),
      status: InterestStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => InterestStatus.pending,
      ),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

enum InterestType { sent, received }
enum InterestStatus { pending, accepted, declined }
