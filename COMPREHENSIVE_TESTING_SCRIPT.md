# 🧪 **COMPREHENSIVE TESTING SCRIPT**

## 🎯 **HIGH PRIORITY TESTING**

### **🎁 1. REFERRAL CODE GENERATION TEST**

#### **Backend API Test:**
```bash
# Test referral code generation
curl -X GET "http://localhost:8000/api/user/referral-code" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"

# Expected Response:
{
  "success": true,
  "referralCode": "PRIYA2024AB",
  "referralLink": "https://vaivahik.com/register?ref=PRIYA2024AB",
  "stats": {
    "totalReferrals": 0,
    "successfulReferrals": 0,
    "totalRewards": 0
  }
}
```

#### **Frontend Test Steps:**
```javascript
// Website Test
1. Login to user dashboard
2. Navigate to "Refer & Earn" section
3. Verify referral code is displayed
4. Test copy functionality
5. Verify sharing options work
6. Check referral history

// Mobile App Test
1. Open mobile app
2. Navigate to Referral screen
3. Verify code generation
4. Test native sharing
5. Check stats display
6. Verify history tracking
```

### **📱 2. MOBILE APP SHARING TEST**

#### **Test Scenarios:**
```dart
// Test sharing functionality
void testMobileSharing() {
  // 1. WhatsApp sharing
  shareToWhatsApp("Join Vaivahik with my code: PRIYA2024AB");
  
  // 2. SMS sharing
  shareViaSMS("Use my referral code PRIYA2024AB to join Vaivahik");
  
  // 3. Email sharing
  shareViaEmail("Invitation to join Vaivahik matrimony");
  
  // 4. Social media sharing
  shareToSocialMedia("Find your perfect match on Vaivahik!");
  
  // 5. Copy to clipboard
  copyToClipboard("PRIYA2024AB");
}
```

### **💰 3. WALLET CREDIT APPLICATION TEST**

#### **Payment Integration Test:**
```javascript
// Test wallet credit during payment
const testWalletPayment = async () => {
  // Setup: User has ₹150 wallet credits
  const userWallet = 150;
  const planPrice = 999;
  
  // Test payment flow
  const paymentData = {
    planId: "premium_monthly",
    originalAmount: planPrice,
    walletCredits: userWallet,
    finalAmount: planPrice - userWallet, // ₹849
    paymentMethod: "razorpay"
  };
  
  // API call
  const response = await fetch('/api/payments/process', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(paymentData)
  });
  
  // Verify wallet deduction
  console.log('Payment processed:', response);
};
```

### **🛡️ 4. ADMIN PANEL REFERRAL CONTROLS TEST**

#### **Admin Dashboard Test:**
```javascript
// Test admin referral program management
const testAdminControls = async () => {
  // 1. Create referral program
  const programData = {
    name: "Diwali Special Referral",
    description: "Special rewards for Diwali season",
    referrerRewardType: "cash",
    referrerRewardAmount: 100,
    refereeRewardType: "cash", 
    refereeRewardAmount: 50,
    startDate: "2024-10-01",
    endDate: "2024-11-15",
    maxReferralsPerUser: 10
  };
  
  const createResponse = await adminPost('/admin/referral-programs', programData);
  
  // 2. Test program monitoring
  const statsResponse = await adminGet('/admin/referral-programs/stats');
  
  // 3. Test manual credit adjustment
  const adjustmentData = {
    userId: "user123",
    amount: 25,
    reason: "Bonus reward for top referrer"
  };
  
  const adjustResponse = await adminPost('/admin/wallet/adjust', adjustmentData);
};
```

### **📊 5. CROSS-PLATFORM CONSISTENCY TEST**

#### **API Consistency Check:**
```bash
# Test same endpoints work for both platforms
ENDPOINTS=(
  "/api/user/referral-code"
  "/api/user/refer"
  "/api/user/redeem-referral"
  "/api/payments/plans"
  "/api/user/profile"
)

for endpoint in "${ENDPOINTS[@]}"; do
  echo "Testing: $endpoint"
  curl -X GET "http://localhost:8000/api$endpoint" \
    -H "Authorization: Bearer $TOKEN" \
    -H "Content-Type: application/json"
done
```

---

## 🛡️ **ADMIN PANEL TESTING**

### **📊 1. CREATE TEST REFERRAL PROGRAM**

#### **Test Data:**
```json
{
  "name": "Test Referral Program",
  "description": "Testing referral system functionality",
  "status": "active",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2024-12-31T23:59:59Z",
  "referrerRewardType": "cash",
  "referrerRewardAmount": 100,
  "refereeRewardType": "cash",
  "refereeRewardAmount": 50,
  "maxReferralsPerUser": 5,
  "conversionRequirement": "profile_complete",
  "termsAndConditions": "Standard terms apply"
}
```

#### **Admin Panel Steps:**
```
1. Login to admin panel
2. Navigate to Referral Programs
3. Click "Create New Program"
4. Fill in test data above
5. Save and verify creation
6. Check program appears in list
7. Test edit functionality
8. Test status toggle (active/inactive)
```

### **💰 2. SET REWARD AMOUNTS TEST**

#### **Reward Configuration:**
```javascript
const rewardTests = [
  {
    scenario: "High Value Rewards",
    referrerReward: { type: "cash", amount: 200 },
    refereeReward: { type: "cash", amount: 100 }
  },
  {
    scenario: "Subscription Rewards", 
    referrerReward: { type: "subscription_days", amount: 30 },
    refereeReward: { type: "subscription_days", amount: 15 }
  },
  {
    scenario: "Mixed Rewards",
    referrerReward: { type: "cash", amount: 150 },
    refereeReward: { type: "premium_features", amount: 1 }
  }
];
```

### **📈 3. MONITOR REFERRAL DASHBOARD**

#### **Metrics to Verify:**
```
✅ Total Referrals Generated
✅ Successful Conversions
✅ Pending Referrals
✅ Total Rewards Distributed
✅ Top Referrers List
✅ Conversion Rate Analytics
✅ Revenue Impact Analysis
✅ Fraud Detection Alerts
```

### **🔧 4. MANUAL CREDIT ADJUSTMENTS**

#### **Test Scenarios:**
```javascript
const creditAdjustments = [
  {
    type: "bonus_reward",
    userId: "user123",
    amount: 50,
    reason: "Exceptional referrer bonus"
  },
  {
    type: "correction",
    userId: "user456", 
    amount: -25,
    reason: "Duplicate reward correction"
  },
  {
    type: "compensation",
    userId: "user789",
    amount: 100,
    reason: "Service issue compensation"
  }
];
```

### **🛡️ 5. FRAUD DETECTION TEST**

#### **Fraud Scenarios to Test:**
```
🚨 Multiple accounts from same IP
🚨 Rapid referral generation
🚨 Self-referral attempts
🚨 Fake email/phone patterns
🚨 Suspicious user behavior
🚨 Bulk account creation
```

---

## 📱 **MOBILE DELETE PROFILE TEST**

### **🔧 Test Implementation:**

#### **Test Steps:**
```dart
void testDeleteProfile() async {
  // 1. Navigate to Settings
  await tester.tap(find.byIcon(Icons.settings));
  await tester.pumpAndSettle();
  
  // 2. Find Delete Account option
  await tester.scrollUntilVisible(
    find.text('Delete Account'),
    find.byType(SingleChildScrollView),
  );
  
  // 3. Tap Delete Account
  await tester.tap(find.text('Delete Account'));
  await tester.pumpAndSettle();
  
  // 4. Verify warning dialog
  expect(find.text('Delete Account'), findsOneWidget);
  expect(find.text('This action cannot be undone'), findsOneWidget);
  
  // 5. Test confirmation flow
  await tester.tap(find.text('Continue'));
  await tester.pumpAndSettle();
  
  // 6. Test final confirmation
  await tester.enterText(find.byType(TextField), 'DELETE');
  await tester.tap(find.text('Delete Account'));
  
  // 7. Verify API call and navigation
  expect(find.text('Deleting your account...'), findsOneWidget);
}
```

---

## 🎯 **EXPECTED RESULTS**

### **✅ SUCCESS CRITERIA:**

```
🎁 REFERRAL SYSTEM:
├── ✅ Codes generate successfully
├── ✅ Sharing works on all platforms
├── ✅ Rewards process correctly
├── ✅ Admin controls function properly
└── ✅ Cross-platform consistency maintained

💰 WALLET SYSTEM:
├── ✅ Credits apply during payment
├── ✅ Deductions calculate correctly
├── ✅ Transaction history accurate
└── ✅ Admin adjustments work

📱 MOBILE DELETE:
├── ✅ Delete option appears in settings
├── ✅ Warning dialogs display properly
├── ✅ Confirmation process works
├── ✅ API call succeeds
└── ✅ User session cleared

🛡️ ADMIN PANEL:
├── ✅ Real data displays (no mock fallback)
├── ✅ Recent activity shows actual data
├── ✅ Newly joined members list accurate
├── ✅ All controls functional
└── ✅ Premium design applied
```

**RUN THIS COMPREHENSIVE TEST SUITE TO VERIFY ALL FUNCTIONALITY!** 🚀
