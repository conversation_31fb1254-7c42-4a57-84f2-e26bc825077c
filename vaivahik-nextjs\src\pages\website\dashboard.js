/**
 * Website Dashboard - Backend Integrated with Comprehensive Features
 * Leverages your existing backend infrastructure and admin components
 */

import { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import {
  Box,
  Grid,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Button,
  Avatar,
  Chip,
  IconButton,
  Badge,
  Drawer,
  AppBar,
  Toolbar,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  useTheme,
  useMediaQuery,
  Container,
  CircularProgress,
  Tooltip
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Dashboard as DashboardIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  Message as MessageIcon,
  Favorite as FavoriteIcon,
  Star as StarIcon,
  WorkspacePremium as PremiumIcon,
  Menu as MenuIcon,
  Phone as PhoneIcon,
  Chat as ChatIcon,
  Psychology as KundliIcon,
  Logout as LogoutIcon,
  Home as HomeIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Description as BiodataIcon,
  FlashOn as SpotlightIcon,
  Notifications as NotificationsIcon,
  Settings as SettingsIcon,
  FiberManualRecord as OnlineIcon,
  Verified as VerifiedIcon,
  LocationOn as LocationIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import ChatSupportBot from '@/components/support/ChatSupportBot';
import birthdayService from '@/services/birthdayService';
import BirthdayWishesModal from '@/components/birthday/BirthdayWishesModal';
import EnhancedMatchDashboard from '@/components/enhanced/EnhancedMatchDashboard';
import ProfileCompletionDashboard from '@/website/components/profile/ProfileCompletionDashboard';
import PhotoUploadReminder from '@/components/profile/PhotoUploadReminder';

// Import API services to connect to your backend
import { API_BASE_URL } from '@/config/apiConfig';

// Import existing dashboard components from admin
import dynamic from 'next/dynamic';

// Simplified component loading with better error handling
const LoadingComponent = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
    <Typography>Loading...</Typography>
  </Box>
);

const ErrorComponent = ({ error }) => (
  <Box sx={{ p: 4, textAlign: 'center' }}>
    <Typography variant="h6" color="error" gutterBottom>
      Component Loading Error
    </Typography>
    <Typography variant="body2" color="text.secondary">
      This feature is temporarily unavailable. Please try again later.
    </Typography>
  </Box>
);

// Dynamically import components with better error handling
const VerificationQueueWidget = dynamic(() => import('@/components/dashboard/VerificationQueueWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const ChatModuleWidget = dynamic(() => import('@/components/dashboard/ChatModuleWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const BiodataTemplatesWidget = dynamic(() => import('@/components/dashboard/BiodataTemplatesWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const SpotlightFeaturesWidget = dynamic(() => import('@/components/dashboard/SpotlightFeaturesWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const ProfileCompletionWidget = dynamic(() => import('@/components/dashboard/ProfileCompletionWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const AIMatchingWidget = dynamic(() => import('@/components/dashboard/AIMatchingWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const AdvancedSearchWidget = dynamic(() => import('@/components/dashboard/AdvancedSearchWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const UserAnalyticsWidget = dynamic(() => import('@/components/dashboard/UserAnalyticsWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const PremiumFeaturesWidget = dynamic(() => import('@/components/dashboard/PremiumFeaturesWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

const ReferEarnWidget = dynamic(() => import('@/components/dashboard/ReferEarnWidget').catch(() => ({ default: ErrorComponent })), {
  loading: LoadingComponent,
  ssr: false
});

// Styled Components matching landing page design
const DashboardContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  background: 'linear-gradient(135deg, #F8F9FF 0%, #FFFFFF 50%, #F0F4FF 100%)',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 25% 25%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.08) 0%, transparent 50%)
    `,
    zIndex: 0
  }
}));

const Sidebar = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: 'linear-gradient(180deg, #6366F1 0%, #8B5CF6 50%, #A855F7 100%)',
    color: 'white',
    border: 'none',
    boxShadow: '4px 0 20px rgba(0,0,0,0.1)',
    zIndex: theme.zIndex.drawer + 1
  }
}));

const TopBar = styled(AppBar)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.98)',
  backdropFilter: 'blur(24px)',
  color: '#1F2937',
  boxShadow: '0 4px 32px rgba(99, 102, 241, 0.08)',
  borderBottom: '1px solid rgba(99, 102, 241, 0.1)',
  zIndex: theme.zIndex.drawer + 2,
  '& .MuiToolbar-root': {
    minHeight: '72px',
    padding: '0 24px',
  }
}));

const MainContent = styled(Box)(({ theme }) => ({
  flexGrow: 1,
  marginLeft: 0,
  padding: '32px',
  minHeight: '100vh',
  position: 'relative',
  zIndex: 1,
  transition: theme.transitions.create(['margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  [theme.breakpoints.up('md')]: {
    marginLeft: 280,
  },
  [theme.breakpoints.down('sm')]: {
    padding: '16px',
  },
}));

const ProfileCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.98)',
  backdropFilter: 'blur(20px)',
  borderRadius: 24,
  border: '1px solid rgba(139, 92, 246, 0.1)',
  boxShadow: '0 8px 32px rgba(139, 92, 246, 0.08)',
  transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  overflow: 'hidden',
  position: 'relative',
  '&:hover': {
    transform: 'translateY(-12px) scale(1.02)',
    boxShadow: '0 24px 64px rgba(139, 92, 246, 0.2)',
    border: '1px solid rgba(139, 92, 246, 0.2)',
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '3px',
    background: 'linear-gradient(90deg, #8B5CF6, #A855F7, #C084FC)',
    opacity: 0,
    transition: 'opacity 0.3s ease',
  },
  '&:hover::before': {
    opacity: 1,
  }
}));

const StatsCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.98)',
  backdropFilter: 'blur(20px)',
  borderRadius: 24,
  border: '1px solid rgba(99, 102, 241, 0.1)',
  boxShadow: '0 8px 32px rgba(99, 102, 241, 0.08)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  textAlign: 'center',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-8px)',
    boxShadow: '0 20px 48px rgba(99, 102, 241, 0.15)',
    border: '1px solid rgba(99, 102, 241, 0.2)',
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #6366F1, #8B5CF6, #A855F7)',
    zIndex: 1
  }
}));

const ActionButton = styled(Button)(({ theme, variant }) => ({
  borderRadius: 25,
  padding: '8px 20px',
  fontWeight: 600,
  textTransform: 'none',
  boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s ease',
  ...(variant === 'call' && {
    background: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #45a049, #7cb342)',
      transform: 'translateY(-2px)',
      boxShadow: '0 6px 20px rgba(76, 175, 80, 0.4)'
    }
  }),
  ...(variant === 'chat' && {
    background: 'linear-gradient(135deg, #4A00E0, #8E2DE2)',
    color: 'white',
    '&:hover': {
      background: 'linear-gradient(135deg, #3A00B0, #7E1DD2)',
      transform: 'translateY(-2px)',
      boxShadow: '0 6px 20px rgba(74, 0, 224, 0.4)'
    }
  }),
  ...(variant === 'kundli' && {
    background: 'linear-gradient(135deg, #FFD700, #FFF2BF)',
    color: '#333',
    '&:hover': {
      background: 'linear-gradient(135deg, #F0C700, #FFE8A0)',
      transform: 'translateY(-2px)',
      boxShadow: '0 6px 20px rgba(255, 215, 0, 0.4)'
    }
  })
}));

export default function UserDashboard() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // State management
  const [activeTab, setActiveTab] = useState(0);
  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
  const [loading, setLoading] = useState(true);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [profileModalOpen, setProfileModalOpen] = useState(false);
  const [stats, setStats] = useState({
    profileViews: 127,
    interests: 23,
    messages: 8,
    matches: 45
  });
  const [aiMatches, setAiMatches] = useState([]);
  const [notifications, setNotifications] = useState(3);
  const [backendStatus, setBackendStatus] = useState({
    api: false,
    ml: false,
    chat: false,
    notifications: false
  });

  // Birthday wishes state
  const [birthdayData, setBirthdayData] = useState(null);
  const [showBirthdayModal, setShowBirthdayModal] = useState(false);

  // Icon gradient function for premium design
  const getIconGradient = (value) => {
    const gradients = {
      0: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Dashboard
      1: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', // AI Matches
      2: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', // Search
      3: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)', // Profile
      4: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', // Messages
      5: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', // Analytics
      6: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)', // Verification
      7: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)', // Biodata
      8: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', // Spotlight
      9: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)', // Premium
    };
    return gradients[value] || 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
  };

  // Navigation items
  const navigationItems = [
    { label: 'Dashboard', icon: DashboardIcon, value: 0 },
    { label: 'AI Matches', icon: StarIcon, value: 1, premium: true },
    { label: 'Advanced Search', icon: SearchIcon, value: 2, premium: true },
    { label: 'My Profile', icon: PersonIcon, value: 3 },
    { label: 'Messages', icon: MessageIcon, value: 4, premium: true },
    { label: 'Analytics', icon: TrendingUpIcon, value: 5 },
    { label: 'Verification', icon: SecurityIcon, value: 6 },
    { label: 'Biodata Templates', icon: BiodataIcon, value: 7 },
    { label: 'Spotlight', icon: SpotlightIcon, value: 8, premium: true },
    { label: 'Premium Plans', icon: PremiumIcon, value: 9 },
  ];

  // Mock AI-powered matches data with comprehensive details
  const mockAiMatches = [
    {
      id: 1,
      name: 'Priya Sharma',
      age: 26,
      height: '5\'4"',
      location: 'Mumbai, Maharashtra',
      education: 'MBA Finance',
      occupation: 'Financial Analyst',
      income: '₹12-15 LPA',
      caste: 'Maratha',
      subcaste: 'Kunbi',
      religion: 'Hindu',
      motherTongue: 'Marathi',
      photo: '/api/placeholder/300/400',
      photos: ['/api/placeholder/300/400', '/api/placeholder/300/400', '/api/placeholder/300/400'],
      compatibility: 94,
      isOnline: true,
      lastSeen: 'Online now',
      verified: true,
      premium: false,
      interests: ['Reading', 'Traveling', 'Cooking', 'Classical Music'],
      about: 'Looking for a life partner who shares similar values and dreams. I believe in balancing traditional values with modern thinking.',
      aiInsight: 'Perfect match based on career ambitions and family values',
      familyDetails: {
        fatherOccupation: 'Business',
        motherOccupation: 'Homemaker',
        siblings: '1 Sister (Married)'
      },
      horoscope: {
        rashi: 'Kanya',
        nakshatra: 'Hasta',
        manglik: 'No'
      },
      lifestyle: {
        diet: 'Vegetarian',
        smoking: 'No',
        drinking: 'No'
      }
    },
    {
      id: 2,
      name: 'Anita Patil',
      age: 24,
      height: '5\'2"',
      location: 'Pune, Maharashtra',
      education: 'B.Tech Computer Science',
      occupation: 'Software Engineer',
      income: '₹8-12 LPA',
      caste: 'Maratha',
      subcaste: 'Patil',
      religion: 'Hindu',
      motherTongue: 'Marathi',
      photo: '/api/placeholder/300/400',
      photos: ['/api/placeholder/300/400', '/api/placeholder/300/400', '/api/placeholder/300/400'],
      compatibility: 89,
      isOnline: false,
      lastSeen: '2 hours ago',
      verified: true,
      premium: true,
      interests: ['Technology', 'Music', 'Fitness', 'Photography'],
      about: 'Passionate about technology and looking for someone who understands my ambitions. Love exploring new places and trying different cuisines.',
      aiInsight: 'Excellent compatibility in professional goals and lifestyle',
      familyDetails: {
        fatherOccupation: 'Engineer',
        motherOccupation: 'Teacher',
        siblings: '1 Brother (Unmarried)'
      },
      horoscope: {
        rashi: 'Mithun',
        nakshatra: 'Punarvasu',
        manglik: 'Yes'
      },
      lifestyle: {
        diet: 'Vegetarian',
        smoking: 'No',
        drinking: 'Occasionally'
      }
    },
    {
      id: 3,
      name: 'Kavya Desai',
      age: 27,
      height: '5\'5"',
      location: 'Nashik, Maharashtra',
      education: 'M.Sc Biotechnology',
      occupation: 'Research Scientist',
      income: '₹6-10 LPA',
      caste: 'Maratha',
      subcaste: 'Desai',
      religion: 'Hindu',
      motherTongue: 'Marathi',
      photo: '/api/placeholder/300/400',
      photos: ['/api/placeholder/300/400', '/api/placeholder/300/400', '/api/placeholder/300/400'],
      compatibility: 87,
      isOnline: true,
      lastSeen: 'Online now',
      verified: false,
      premium: false,
      interests: ['Science', 'Nature', 'Photography', 'Yoga'],
      about: 'Love exploring nature and conducting research. Seeking a understanding partner who appreciates science and nature.',
      aiInsight: 'Strong match in intellectual interests and life philosophy',
      familyDetails: {
        fatherOccupation: 'Doctor',
        motherOccupation: 'Nurse',
        siblings: 'No Siblings'
      },
      horoscope: {
        rashi: 'Vrishchik',
        nakshatra: 'Anuradha',
        manglik: 'No'
      },
      lifestyle: {
        diet: 'Vegetarian',
        smoking: 'No',
        drinking: 'No'
      }
    },
    {
      id: 4,
      name: 'Sneha Jadhav',
      age: 25,
      height: '5\'3"',
      location: 'Kolhapur, Maharashtra',
      education: 'B.Com',
      occupation: 'Accountant',
      income: '₹4-6 LPA',
      caste: 'Maratha',
      subcaste: 'Jadhav',
      religion: 'Hindu',
      motherTongue: 'Marathi',
      photo: '/api/placeholder/300/400',
      photos: ['/api/placeholder/300/400', '/api/placeholder/300/400', '/api/placeholder/300/400'],
      compatibility: 92,
      isOnline: false,
      lastSeen: '30 minutes ago',
      verified: true,
      premium: true,
      interests: ['Dancing', 'Cooking', 'Family Time', 'Gardening'],
      about: 'Family-oriented person who believes in traditional values with modern thinking. Love cooking and spending time with family.',
      aiInsight: 'Excellent match for family values and traditional approach',
      familyDetails: {
        fatherOccupation: 'Farmer',
        motherOccupation: 'Homemaker',
        siblings: '2 Brothers (Both Married)'
      },
      horoscope: {
        rashi: 'Kark',
        nakshatra: 'Pushya',
        manglik: 'No'
      },
      lifestyle: {
        diet: 'Vegetarian',
        smoking: 'No',
        drinking: 'No'
      }
    }
  ];

  // Check backend service status
  const checkBackendStatus = async () => {
    const statusChecks = {
      api: `${API_BASE_URL.replace('/api', '')}/health`,
      ml: `${API_BASE_URL}/matches/health`,
      chat: `${API_BASE_URL}/users/conversations`,
      notifications: `${API_BASE_URL}/notifications`
    };

    const newStatus = { api: false, ml: false, chat: false, notifications: false };

    for (const [service, url] of Object.entries(statusChecks)) {
      try {
        const response = await fetch(url, {
          method: 'GET',
          headers: service !== 'api' ? {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          } : { 'Content-Type': 'application/json' }
        });
        newStatus[service] = response.ok;
      } catch (error) {
        console.log(`${service} service unavailable:`, error.message);
        newStatus[service] = false;
      }
    }

    setBackendStatus(newStatus);
  };

  useEffect(() => {
    if (user?.id) {
      fetchDashboardData();
      checkBackendStatus();

      // Set up periodic refresh for real-time updates
      const refreshInterval = setInterval(() => {
        if (!loading) {
          fetchDashboardData();
          checkBackendStatus();
        }
      }, 30000); // Refresh every 30 seconds

      return () => clearInterval(refreshInterval);
    }
  }, [user?.id]);

  // Refresh data when user comes back to the tab
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && !loading) {
        fetchDashboardData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [loading]);

  // Birthday check function
  const checkBirthday = async () => {
    try {
      const response = await birthdayService.checkBirthday();
      if (response.success && response.data.isBirthday) {
        setBirthdayData(response.data);

        // Only show modal if not shown today
        if (birthdayService.shouldShowBirthdayWishes()) {
          setShowBirthdayModal(true);
          birthdayService.markBirthdayWishesShown();
        }
      }
    } catch (error) {
      console.error('Error checking birthday:', error);
    }
  };

  // Check birthday on component mount
  useEffect(() => {
    if (user?.id) {
      checkBirthday();
    }
  }, [user?.id]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      if (!user?.id) {
        console.log('No user ID available, using mock data');
        setAiMatches(mockAiMatches);
        return;
      }

      // Fetch real data from your backend APIs
      const [dashboardStats, aiMatches, userProfile] = await Promise.allSettled([
        // Get dashboard statistics from your backend
        fetch(`${API_BASE_URL}/user/dashboard/${user.id}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        }).then(res => res.ok ? res.json() : null),

        // Get AI matches from your ML service
        fetch(`${API_BASE_URL}/matches`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        }).then(res => res.ok ? res.json() : null),

        // Get user profile data
        fetch(`${API_BASE_URL}/users/profile`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
            'Content-Type': 'application/json'
          }
        }).then(res => res.ok ? res.json() : null)
      ]);

      // Update stats if available
      if (dashboardStats.status === 'fulfilled' && dashboardStats.value) {
        const statsData = dashboardStats.value;
        setStats(prev => ({
          ...prev,
          profileViews: statsData.profileViews || prev.profileViews,
          interests: statsData.interests || prev.interests,
          messages: statsData.messages || prev.messages,
          matches: statsData.matches || prev.matches
        }));
      }

      // Update AI matches if available
      if (aiMatches.status === 'fulfilled' && aiMatches.value?.matches) {
        setAiMatches(aiMatches.value.matches);
      } else {
        // Fallback to mock data if API fails
        console.log('Using mock AI matches data');
        setAiMatches(mockAiMatches);
      }

      console.log('Dashboard data fetched successfully');

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load some dashboard data, showing available information');
      // Fallback to mock data
      setAiMatches(mockAiMatches);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  const handleSidebarToggle = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleLogout = async () => {
    try {
      await logout();
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Failed to logout');
    }
  };

  const handleProfileAction = async (action, profileId, profile = null) => {
    try {
      switch (action) {
        case 'view':
          setSelectedProfile(profile);
          setProfileModalOpen(true);
          break;
        case 'call':
          // Use your backend calling API
          const callResponse = await fetch(`${API_BASE_URL}/contact/initiate-call`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ targetUserId: profileId })
          });

          if (callResponse.ok) {
            toast.success(`Call initiated to ${profile?.name || profileId}`);
          } else {
            toast.error('Failed to initiate call. Please try again.');
          }
          break;
        case 'chat':
          // Use your backend chat API to start conversation
          const chatResponse = await fetch(`${API_BASE_URL}/users/conversations`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ participantId: profileId })
          });

          if (chatResponse.ok) {
            const conversation = await chatResponse.json();
            router.push(`/chat/${conversation.id}`);
          } else {
            toast.error('Failed to start chat. Please try again.');
          }
          break;
        case 'kundli':
          router.push(`/kundli/${profileId}`);
          break;
        case 'interest':
          // Use your backend interest API
          const interestResponse = await fetch(`${API_BASE_URL}/interests`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('accessToken')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ targetUserId: profileId })
          });

          if (interestResponse.ok) {
            toast.success(`Interest sent to ${profile?.name || profileId}`);
            // Update local state to reflect the interest
            setStats(prev => ({ ...prev, interests: prev.interests + 1 }));
          } else {
            toast.error('Failed to send interest. Please try again.');
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error(`Error handling ${action} action:`, error);
      toast.error(`Failed to ${action}. Please try again.`);
    }
  };

  const renderDashboardOverview = () => (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      {/* Welcome Section */}
      <Box sx={{
        mb: 6,
        textAlign: 'center',
        background: 'linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1))',
        borderRadius: '32px',
        padding: '48px 32px',
        backdropFilter: 'blur(20px)',
        border: '1px solid rgba(255, 255, 255, 0.1)'
      }}>
        <Typography variant="h2" fontWeight="800" sx={{
          background: 'linear-gradient(135deg, #FFFFFF, #F8FAFC)',
          backgroundClip: 'text',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          mb: 3,
          fontSize: { xs: '2rem', md: '3rem' }
        }}>
          Welcome back, {user?.name || 'Beautiful Soul'}! ✨
        </Typography>
        <Typography variant="h5" sx={{
          color: 'rgba(255,255,255,0.9)',
          mb: 4,
          fontWeight: 500,
          fontSize: { xs: '1.1rem', md: '1.5rem' }
        }}>
          Your perfect match is waiting to meet you. Let's make magic happen! 💕
        </Typography>
      </Box>

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{
                width: 72,
                height: 72,
                borderRadius: '20px',
                background: 'linear-gradient(135deg, #6366F1, #8B5CF6)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px',
                boxShadow: '0 8px 24px rgba(99, 102, 241, 0.3)'
              }}>
                <TrendingUpIcon sx={{ fontSize: 36, color: 'white' }} />
              </Box>
              <Typography variant="h3" fontWeight="800" sx={{
                background: 'linear-gradient(135deg, #6366F1, #8B5CF6)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}>
                {stats.profileViews}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                Profile Views
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>

        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{
                width: 72,
                height: 72,
                borderRadius: '20px',
                background: 'linear-gradient(135deg, #EC4899, #F472B6)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px',
                boxShadow: '0 8px 24px rgba(236, 72, 153, 0.3)'
              }}>
                <FavoriteIcon sx={{ fontSize: 36, color: 'white' }} />
              </Box>
              <Typography variant="h3" fontWeight="800" sx={{
                background: 'linear-gradient(135deg, #EC4899, #F472B6)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}>
                {stats.interests}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                Interests
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>

        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{
                width: 72,
                height: 72,
                borderRadius: '20px',
                background: 'linear-gradient(135deg, #10B981, #34D399)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px',
                boxShadow: '0 8px 24px rgba(16, 185, 129, 0.3)'
              }}>
                <MessageIcon sx={{ fontSize: 36, color: 'white' }} />
              </Box>
              <Typography variant="h3" fontWeight="800" sx={{
                background: 'linear-gradient(135deg, #10B981, #34D399)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}>
                {stats.messages}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                Messages
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>

        <Grid item xs={6} md={3}>
          <StatsCard>
            <CardContent sx={{ p: 3 }}>
              <Box sx={{
                width: 72,
                height: 72,
                borderRadius: '20px',
                background: 'linear-gradient(135deg, #F59E0B, #FBBF24)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 20px',
                boxShadow: '0 8px 24px rgba(245, 158, 11, 0.3)'
              }}>
                <StarIcon sx={{ fontSize: 36, color: 'white' }} />
              </Box>
              <Typography variant="h3" fontWeight="800" sx={{
                background: 'linear-gradient(135deg, #F59E0B, #FBBF24)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}>
                {stats.matches}
              </Typography>
              <Typography variant="body2" color="text.secondary" fontWeight="500">
                AI Matches
              </Typography>
            </CardContent>
          </StatsCard>
        </Grid>
      </Grid>

      {/* AI-Powered Matches Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="700" sx={{ color: 'white', mb: 3 }}>
          🤖 AI-Powered Perfect Matches
        </Typography>
        <Grid container spacing={3}>
          {aiMatches.map((match) => (
            <Grid item xs={12} sm={6} md={4} key={match.id}>
              <ProfileCard onClick={() => handleProfileAction('view', match.id, match)}>
                <CardMedia
                  component="img"
                  height="300"
                  image={match.photo}
                  alt={match.name}
                  sx={{ objectFit: 'cover', cursor: 'pointer' }}
                />
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="600" sx={{ flexGrow: 1 }}>
                      {match.name}, {match.age}
                    </Typography>
                    {match.isOnline && (
                      <Tooltip title="Online now">
                        <OnlineIcon sx={{ color: '#4CAF50', fontSize: 12, mr: 1 }} />
                      </Tooltip>
                    )}
                    {match.verified && (
                      <Tooltip title="Verified Profile">
                        <VerifiedIcon sx={{ color: '#4CAF50', fontSize: 20 }} />
                      </Tooltip>
                    )}
                    {match.premium && (
                      <Tooltip title="Premium Member">
                        <PremiumIcon sx={{ color: '#FFD700', fontSize: 20, ml: 0.5 }} />
                      </Tooltip>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <LocationIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.location}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <WorkIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.occupation}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <EducationIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.education}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <HeightIcon sx={{ fontSize: 16, color: '#666', mr: 1 }} />
                    <Typography variant="body2" color="text.secondary">
                      {match.height} • {match.income}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                      {match.caste} • {match.motherTongue}
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.85rem' }}>
                      Last seen: {match.lastSeen}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                    <Chip
                      label={`${match.compatibility}% Match`}
                      color="primary"
                      size="small"
                      sx={{
                        background: 'linear-gradient(135deg, #6366F1, #8B5CF6)',
                        color: 'white',
                        fontWeight: 600
                      }}
                    />
                    <Chip
                      label={match.lifestyle.diet}
                      size="small"
                      variant="outlined"
                      sx={{ fontSize: '0.75rem' }}
                    />
                    <Chip
                      label={match.horoscope.manglik === 'No' ? 'Non-Manglik' : 'Manglik'}
                      size="small"
                      variant="outlined"
                      color={match.horoscope.manglik === 'No' ? 'success' : 'warning'}
                      sx={{ fontSize: '0.75rem' }}
                    />
                  </Box>

                  <Typography variant="body2" color="primary" sx={{
                    fontStyle: 'italic',
                    mb: 2,
                    background: 'linear-gradient(135deg, #4A00E0, #8E2DE2)',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontWeight: 600
                  }}>
                    🤖 AI Insight: {match.aiInsight}
                  </Typography>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {match.about}
                  </Typography>
                </CardContent>

                <CardActions sx={{ p: 3, pt: 0, gap: 1, flexWrap: 'wrap' }}>
                  <ActionButton
                    variant="call"
                    size="small"
                    startIcon={<PhoneIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProfileAction('call', match.id, match);
                    }}
                  >
                    Call
                  </ActionButton>
                  <ActionButton
                    variant="chat"
                    size="small"
                    startIcon={<ChatIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProfileAction('chat', match.id, match);
                    }}
                  >
                    Chat
                  </ActionButton>
                  <ActionButton
                    variant="kundli"
                    size="small"
                    startIcon={<KundliIcon />}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleProfileAction('kundli', match.id, match);
                    }}
                  >
                    Kundli
                  </ActionButton>
                  <Tooltip title="Send Interest">
                    <IconButton
                      color="primary"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleProfileAction('interest', match.id, match);
                      }}
                      sx={{
                        background: 'linear-gradient(135deg, #6366F1, #8B5CF6)',
                        color: 'white',
                        '&:hover': {
                          background: 'linear-gradient(135deg, #4F46E5, #6366F1)',
                        }
                      }}
                    >
                      <FavoriteIcon />
                    </IconButton>
                  </Tooltip>
                </CardActions>
              </ProfileCard>
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Refer & Earn Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="700" sx={{ color: 'white', mb: 3 }}>
          💰 Refer & Earn
        </Typography>
        <ReferEarnWidget
          userId={user?.id}
          currentUser={user}
        />
      </Box>

      {/* Backend Status & Features Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="700" sx={{ color: 'white', mb: 3 }}>
          🚀 Backend-Powered Features
        </Typography>

        {/* Backend Status Indicators */}
        <Box sx={{ mb: 3, p: 3, backgroundColor: 'rgba(255,255,255,0.1)', borderRadius: 2 }}>
          <Typography variant="h6" fontWeight="600" sx={{ color: 'white', mb: 2 }}>
            🔗 Live Backend Services
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: backendStatus.api ? '#10B981' : '#EF4444',
                  mr: 1
                }} />
                <Typography variant="body2" sx={{ color: 'white' }}>
                  API Server
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: backendStatus.ml ? '#10B981' : '#EF4444',
                  mr: 1
                }} />
                <Typography variant="body2" sx={{ color: 'white' }}>
                  ML Service
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: backendStatus.chat ? '#10B981' : '#EF4444',
                  mr: 1
                }} />
                <Typography variant="body2" sx={{ color: 'white' }}>
                  Chat System
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} md={3}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box sx={{
                  width: 12,
                  height: 12,
                  borderRadius: '50%',
                  backgroundColor: backendStatus.notifications ? '#10B981' : '#EF4444',
                  mr: 1
                }} />
                <Typography variant="body2" sx={{ color: 'white' }}>
                  Notifications
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <StatsCard onClick={() => setActiveTab(1)} sx={{ cursor: 'pointer', height: '100%' }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <StarIcon sx={{ fontSize: 32, color: '#FF9800', mr: 2 }} />
                  <Typography variant="h6" fontWeight="600">
                    ML-Powered Matching
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Advanced 2-tower PyTorch model with phased implementation
                </Typography>
                <Chip
                  label={backendStatus.ml ? "Live Backend API" : "Mock Data"}
                  size="small"
                  color={backendStatus.ml ? "success" : "warning"}
                />
              </CardContent>
            </StatsCard>
          </Grid>

          <Grid item xs={12} md={6}>
            <StatsCard onClick={() => setActiveTab(4)} sx={{ cursor: 'pointer', height: '100%' }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <MessageIcon sx={{ fontSize: 32, color: '#4CAF50', mr: 2 }} />
                  <Typography variant="h6" fontWeight="600">
                    Real-time Chat System
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  WebSocket-powered messaging with moderation & premium features
                </Typography>
                <Chip
                  label={backendStatus.chat ? "Socket.IO + Redis" : "Demo Mode"}
                  size="small"
                  color={backendStatus.chat ? "success" : "warning"}
                />
              </CardContent>
            </StatsCard>
          </Grid>
        </Grid>
      </Box>

      {/* Quick Actions Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight="700" sx={{ color: 'white', mb: 3 }}>
          ⚡ Quick Actions
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(3)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <PersonIcon sx={{ fontSize: 48, color: '#2196F3', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Complete Profile
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Add more details to get better matches
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(6)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <SecurityIcon sx={{ fontSize: 48, color: '#4CAF50', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Get Verified
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Increase trust with profile verification
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(7)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <BiodataIcon sx={{ fontSize: 48, color: '#FF9800', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Create Biodata
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Generate beautiful biodata templates
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatsCard onClick={() => setActiveTab(9)} sx={{ cursor: 'pointer' }}>
              <CardContent sx={{ p: 3, textAlign: 'center' }}>
                <PremiumIcon sx={{ fontSize: 48, color: '#E91E63', mb: 2 }} />
                <Typography variant="h6" fontWeight="600" gutterBottom>
                  Go Premium
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Unlock advanced features & priority
                </Typography>
              </CardContent>
            </StatsCard>
          </Grid>
        </Grid>
      </Box>
    </Container>
  );

  const renderTabContent = () => {
    const containerStyle = { py: 4 };

    try {
      switch (activeTab) {
        case 0:
          return renderDashboardOverview();
        case 1:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <EnhancedMatchDashboard />
            </Container>
          );
        case 2:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <AdvancedSearchWidget
                isPremium={user?.isPremium || false}
                onPremiumFeatureClick={() => setActiveTab(9)}
              />
            </Container>
          );
        case 3:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              {/* Photo Upload Reminder - Critical for user engagement */}
              <PhotoUploadReminder
                photoCount={user?.photos?.length || 0}
                variant="banner"
                showStats={true}
              />

              <ProfileCompletionDashboard
                userData={user}
                onUpdateSection={(section) => {
                  console.log('Updating section:', section);
                  // Handle section updates
                }}
              />
            </Container>
          );
        case 4:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <ChatModuleWidget
                userId={user?.id}
                isPremium={user?.isPremium || false}
                onPremiumFeatureClick={() => setActiveTab(9)}
              />
            </Container>
          );
        case 5:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <UserAnalyticsWidget
                userId={user?.id}
                currentUser={user}
              />
            </Container>
          );
        case 6:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <VerificationQueueWidget
                userId={user?.id}
                isVerified={user?.isVerified || false}
              />
            </Container>
          );
        case 7:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <BiodataTemplatesWidget
                userId={user?.id}
                userGender={user?.gender}
              />
            </Container>
          );
        case 8:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <SpotlightFeaturesWidget
                userId={user?.id}
                currentSpotlight={null}
              />
            </Container>
          );
        case 9:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <PremiumFeaturesWidget
                userId={user?.id}
                currentPlan={user?.isPremium ? 'premium' : null}
                onUpgrade={(plan) => {
                  console.log('Upgraded to:', plan);
                  toast.success(`Upgraded to ${plan} plan!`);
                }}
              />
            </Container>
          );
        default:
          return (
            <Container maxWidth="xl" sx={containerStyle}>
              <Typography variant="h4" sx={{ color: 'white', textAlign: 'center', py: 8 }}>
                {navigationItems.find(item => item.value === activeTab)?.label} - Coming Soon
              </Typography>
            </Container>
          );
      }
    } catch (error) {
      console.error('Error rendering tab content:', error);
      return (
        <Container maxWidth="xl" sx={containerStyle}>
          <Typography variant="h4" sx={{ color: 'white', textAlign: 'center', py: 8 }}>
            Loading {navigationItems.find(item => item.value === activeTab)?.label}...
          </Typography>
        </Container>
      );
    }
  };

  return (
    <>
      <Head>
        <title>Dashboard - Find Your Perfect Match | Vaivahik</title>
        <meta name="description" content="Your personalized matrimony dashboard with AI-powered matches and comprehensive features" />
      </Head>

      <DashboardContainer>
        {/* Top Navigation Bar */}
        <TopBar position="fixed">
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              onClick={handleSidebarToggle}
              edge="start"
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuIcon />
            </IconButton>
            
            <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1, fontWeight: 700 }}>
              Vaivahik - AI Matrimony Platform
            </Typography>

            {/* Backend Status Indicators - Clickable Navigation */}
            <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
              <Tooltip title={`API Server: ${backendStatus.api ? 'Connected' : 'Disconnected'} - Click to view API status`}>
                <IconButton
                  size="small"
                  onClick={() => setActiveTab(5)}
                  sx={{ p: 0.5, mr: 0.5 }}
                >
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: backendStatus.api ? '#10B981' : '#EF4444',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.2)',
                      boxShadow: '0 0 8px rgba(16, 185, 129, 0.6)'
                    }
                  }} />
                </IconButton>
              </Tooltip>
              <Tooltip title={`ML Service: ${backendStatus.ml ? 'Connected' : 'Disconnected'} - Click to view AI matches`}>
                <IconButton
                  size="small"
                  onClick={() => setActiveTab(1)}
                  sx={{ p: 0.5, mr: 0.5 }}
                >
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: backendStatus.ml ? '#10B981' : '#EF4444',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.2)',
                      boxShadow: '0 0 8px rgba(16, 185, 129, 0.6)'
                    }
                  }} />
                </IconButton>
              </Tooltip>
              <Tooltip title={`Chat Service: ${backendStatus.chat ? 'Connected' : 'Disconnected'} - Click to view messages`}>
                <IconButton
                  size="small"
                  onClick={() => setActiveTab(4)}
                  sx={{ p: 0.5, mr: 0.5 }}
                >
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    backgroundColor: backendStatus.chat ? '#10B981' : '#EF4444',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.2)',
                      boxShadow: '0 0 8px rgba(16, 185, 129, 0.6)'
                    }
                  }} />
                </IconButton>
              </Tooltip>
            </Box>

            <IconButton color="inherit" sx={{ mr: 1 }}>
              <Badge badgeContent={notifications} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>

            <IconButton color="inherit" onClick={handleLogout}>
              <LogoutIcon />
            </IconButton>
          </Toolbar>
        </TopBar>

        {/* Sidebar Navigation */}
        <Sidebar
          variant={isMobile ? "temporary" : "persistent"}
          open={sidebarOpen}
          onClose={handleSidebarToggle}
          ModalProps={{ keepMounted: true }}
        >
          <Toolbar />
          <Box sx={{ p: 3, textAlign: 'center', borderBottom: '1px solid rgba(255,255,255,0.1)' }}>
            <Avatar
              src={user?.profilePhoto || '/api/placeholder/80/80'}
              sx={{ width: 80, height: 80, margin: '0 auto 16px', border: '3px solid #4CAF50' }}
            />
            <Typography variant="h6" fontWeight="600" gutterBottom>
              {user?.name || 'Beautiful Soul'}
            </Typography>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              {user?.location || 'Mumbai, India'}
            </Typography>
            <Chip
              label={user?.isPremium ? 'Premium Member' : 'Free Member'}
              size="small"
              sx={{
                mt: 1,
                background: user?.isPremium ? '#FFD700' : '#666',
                color: user?.isPremium ? '#000' : '#fff'
              }}
            />
          </Box>

          <List sx={{ px: 2, py: 2 }}>
            {navigationItems.map((item) => (
              <ListItem key={item.value} disablePadding>
                <ListItemButton
                  onClick={() => handleTabChange(item.value)}
                  sx={{
                    borderRadius: 4,
                    mb: 2,
                    mx: 1,
                    p: 2,
                    background: activeTab === item.value
                      ? 'linear-gradient(135deg, #ff6b6b 0%, #feca57 100%)'
                      : 'rgba(255,255,255,0.05)',
                    backdropFilter: 'blur(20px)',
                    border: activeTab === item.value
                      ? '1px solid rgba(255,255,255,0.3)'
                      : '1px solid rgba(255,255,255,0.1)',
                    boxShadow: activeTab === item.value
                      ? '0 12px 35px rgba(255, 107, 107, 0.4)'
                      : '0 4px 15px rgba(0,0,0,0.1)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    position: 'relative',
                    overflow: 'hidden',
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: '-100%',
                      width: '100%',
                      height: '100%',
                      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
                      transition: 'left 0.6s ease',
                    },
                    '&:hover': {
                      background: activeTab === item.value
                        ? 'linear-gradient(135deg, #ff6b6b 0%, #feca57 100%)'
                        : 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      transform: 'translateX(8px) scale(1.02)',
                      boxShadow: activeTab === item.value
                        ? '0 15px 45px rgba(255, 107, 107, 0.5)'
                        : '0 8px 25px rgba(102, 126, 234, 0.3)',
                      '&::before': {
                        left: '100%',
                      }
                    }
                  }}
                >
                <ListItemIcon sx={{
                  minWidth: 48,
                  mr: 2,
                  transition: 'all 0.4s ease',
                  position: 'relative',
                  zIndex: 1,
                }}>
                  <Box sx={{
                    width: 40,
                    height: 40,
                    borderRadius: 2.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: activeTab === item.value
                      ? 'rgba(255,255,255,0.3)'
                      : getIconGradient(item.value),
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    boxShadow: activeTab === item.value
                      ? '0 6px 20px rgba(0,0,0,0.3)'
                      : '0 4px 15px rgba(0,0,0,0.2)',
                    transition: 'all 0.4s ease',
                    transform: activeTab === item.value ? 'scale(1.1)' : 'scale(1)',
                    '&:hover': {
                      transform: 'scale(1.1) rotate(5deg)',
                      boxShadow: '0 8px 25px rgba(0,0,0,0.3)',
                    }
                  }}>
                    <item.icon sx={{
                      color: '#FFFFFF',
                      fontSize: 20,
                      filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3))'
                    }} />
                  </Box>
                </ListItemIcon>
                <ListItemText
                  primary={item.label}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: activeTab === item.value ? 700 : 600,
                      fontSize: '14px',
                      letterSpacing: '0.5px',
                      color: '#FFFFFF',
                      textShadow: activeTab === item.value
                        ? '0 2px 4px rgba(0,0,0,0.3)'
                        : '0 1px 2px rgba(0,0,0,0.2)',
                      transition: 'all 0.4s ease',
                      position: 'relative',
                      zIndex: 1,
                    }
                  }}
                />
                {item.premium && (
                  <Chip
                    label="Premium"
                    size="small"
                    sx={{
                      background: 'linear-gradient(135deg, #FFD700 0%, #FFA500 100%)',
                      color: '#000',
                      fontSize: '0.65rem',
                      fontWeight: 700,
                      height: 22,
                      borderRadius: 3,
                      boxShadow: '0 4px 15px rgba(255, 215, 0, 0.4)',
                      border: '1px solid rgba(255, 255, 255, 0.3)',
                      backdropFilter: 'blur(10px)',
                      transition: 'all 0.3s ease',
                      position: 'relative',
                      zIndex: 1,
                      '&:hover': {
                        transform: 'scale(1.05)',
                        boxShadow: '0 6px 20px rgba(255, 215, 0, 0.5)',
                      }
                    }}
                  />
                )}
                </ListItemButton>
              </ListItem>
            ))}
          </List>

          <Box sx={{ mt: 'auto', p: 2 }}>
            <Button
              fullWidth
              variant="contained"
              startIcon={<HomeIcon />}
              onClick={() => router.push('/')}
              sx={{
                background: 'linear-gradient(135deg, #4A00E0, #8E2DE2)',
                borderRadius: 2,
                py: 1.5
              }}
            >
              Back to Home
            </Button>
          </Box>
        </Sidebar>

        {/* Main Content Area */}
        <MainContent>
          <Toolbar />
          <Box sx={{ position: 'relative', zIndex: 1 }}>
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                <Typography variant="h6" sx={{ color: 'white' }}>Loading your perfect matches...</Typography>
              </Box>
            ) : (
              renderTabContent()
            )}
          </Box>
        </MainContent>

        {/* Detailed Profile Modal */}
        {selectedProfile && (
          <Dialog
            open={profileModalOpen}
            onClose={() => setProfileModalOpen(false)}
            maxWidth="md"
            fullWidth
            sx={{
              '& .MuiDialog-paper': {
                borderRadius: 3,
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(20px)'
              }
            }}
          >
            <Box sx={{ p: 4 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h5" fontWeight="700">
                  {selectedProfile.name}'s Profile
                </Typography>
                <IconButton onClick={() => setProfileModalOpen(false)}>
                  <CloseIcon />
                </IconButton>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Avatar
                      src={selectedProfile.photo}
                      sx={{ width: 200, height: 200, margin: '0 auto 16px', border: '4px solid #FF69B4' }}
                    />
                    <Typography variant="h6" fontWeight="600" gutterBottom>
                      {selectedProfile.name}, {selectedProfile.age}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {selectedProfile.location}
                    </Typography>
                    <Chip
                      label={`${selectedProfile.compatibility}% Match`}
                      color="primary"
                      sx={{
                        background: 'linear-gradient(135deg, #6366F1, #8B5CF6)',
                        color: 'white',
                        fontWeight: 600,
                        mb: 2
                      }}
                    />
                  </Box>
                </Grid>

                <Grid item xs={12} md={8}>
                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    Basic Information
                  </Typography>
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Height</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.height}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Income</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.income}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Education</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.education}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Occupation</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.occupation}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Caste</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.caste}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Mother Tongue</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.motherTongue}</Typography>
                    </Grid>
                  </Grid>

                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    About
                  </Typography>
                  <Typography variant="body1" sx={{ mb: 3 }}>
                    {selectedProfile.about}
                  </Typography>

                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    Family Details
                  </Typography>
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Father's Occupation</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.familyDetails.fatherOccupation}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Mother's Occupation</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.familyDetails.motherOccupation}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="body2" color="text.secondary">Siblings</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.familyDetails.siblings}</Typography>
                    </Grid>
                  </Grid>

                  <Typography variant="h6" fontWeight="600" gutterBottom>
                    Horoscope Details
                  </Typography>
                  <Grid container spacing={2} sx={{ mb: 3 }}>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Rashi</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.horoscope.rashi}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Nakshatra</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.horoscope.nakshatra}</Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2" color="text.secondary">Manglik</Typography>
                      <Typography variant="body1" fontWeight="500">{selectedProfile.horoscope.manglik}</Typography>
                    </Grid>
                  </Grid>

                  <Box sx={{ display: 'flex', gap: 2, mt: 4 }}>
                    <ActionButton
                      variant="call"
                      startIcon={<PhoneIcon />}
                      onClick={() => {
                        handleProfileAction('call', selectedProfile.id, selectedProfile);
                        setProfileModalOpen(false);
                      }}
                    >
                      Call Now
                    </ActionButton>
                    <ActionButton
                      variant="chat"
                      startIcon={<ChatIcon />}
                      onClick={() => {
                        handleProfileAction('chat', selectedProfile.id, selectedProfile);
                        setProfileModalOpen(false);
                      }}
                    >
                      Start Chat
                    </ActionButton>
                    <ActionButton
                      variant="kundli"
                      startIcon={<KundliIcon />}
                      onClick={() => {
                        handleProfileAction('kundli', selectedProfile.id, selectedProfile);
                        setProfileModalOpen(false);
                      }}
                    >
                      View Kundli
                    </ActionButton>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Dialog>
        )}

        {/* Chat Support Bot */}
        <ChatSupportBot />

        {/* Birthday Wishes Modal */}
        <BirthdayWishesModal
          open={showBirthdayModal}
          onClose={() => setShowBirthdayModal(false)}
          birthdayData={birthdayData}
        />
      </DashboardContainer>
    </>
  );
}
