import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../features/auth/providers/auth_provider.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/auth/screens/register_screen.dart';
import '../../features/auth/screens/forgot_password_screen.dart';
import '../../features/auth/screens/otp_verification_screen.dart';
import '../../features/home/<USER>/home_screen.dart';
import '../../features/profile/screens/profile_screen.dart';
import '../../features/profile/screens/edit_profile_screen.dart';
import '../../features/matching/screens/matches_screen.dart';
import '../../features/matching/screens/match_detail_screen.dart';
import '../../features/chat/screens/chat_list_screen.dart';
import '../../features/chat/screens/chat_screen.dart';
import '../../features/calling/screens/call_screen.dart';
import '../../features/calling/screens/call_history_screen.dart';
import '../../features/kundali/screens/kundali_screen.dart';
import '../../features/kundali/screens/kundali_matching_screen.dart';
import '../../features/premium/screens/premium_plans_screen.dart';
import '../../features/premium/screens/payment_screen.dart';
import '../../features/notifications/screens/notifications_screen.dart';
import '../../features/privacy/screens/privacy_settings_screen.dart';
import '../../features/search/screens/search_screen.dart';
import '../../features/biodata/screens/biodata_screen.dart';
import '../../features/analytics/screens/analytics_screen.dart';
import '../../features/verification/screens/document_verification_screen.dart';
import '../screens/splash_screen.dart';
import '../screens/onboarding_screen.dart';
import '../screens/main_navigation_screen.dart';
import '../screens/settings_screen.dart';

final appRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);
  
  return GoRouter(
    initialLocation: '/splash',
    redirect: (context, state) {
      final isLoggedIn = authState.isAuthenticated;
      final isLoggingIn = state.matchedLocation == '/login' || 
                         state.matchedLocation == '/register' ||
                         state.matchedLocation == '/forgot-password' ||
                         state.matchedLocation.startsWith('/otp-verification');
      
      // If not logged in and not on auth screens, redirect to login
      if (!isLoggedIn && !isLoggingIn && 
          state.matchedLocation != '/splash' && 
          state.matchedLocation != '/onboarding') {
        return '/login';
      }
      
      // If logged in and on auth screens, redirect to home
      if (isLoggedIn && isLoggingIn) {
        return '/home';
      }
      
      return null;
    },
    routes: [
      // Splash and Onboarding
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/onboarding',
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      
      // Authentication Routes
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/register',
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      GoRoute(
        path: '/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordScreen(),
      ),
      GoRoute(
        path: '/otp-verification/:type',
        name: 'otp-verification',
        builder: (context, state) {
          final phone = state.uri.queryParameters['phone'] ?? '';
          return OtpVerificationScreen(
            phoneNumber: phone,
          );
        },
      ),
      
      // Main App Routes (Protected)
      ShellRoute(
        builder: (context, state, child) => MainNavigationScreen(child: child),
        routes: [
          GoRoute(
            path: '/home',
            name: 'home',
            builder: (context, state) => const HomeScreen(),
          ),
          GoRoute(
            path: '/matches',
            name: 'matches',
            builder: (context, state) => const MatchesScreen(),
            routes: [
              GoRoute(
                path: 'detail/:userId',
                name: 'match-detail',
                builder: (context, state) {
                  final userId = state.pathParameters['userId']!;
                  return MatchDetailScreen(matchId: userId);
                },
              ),
            ],
          ),
          GoRoute(
            path: '/chat',
            name: 'chat-list',
            builder: (context, state) => const ChatListScreen(),
            routes: [
              GoRoute(
                path: ':roomId',
                name: 'chat',
                builder: (context, state) {
                  final roomId = state.pathParameters['roomId']!;
                  final userName = state.uri.queryParameters['userName'];
                  final receiverId = state.uri.queryParameters['receiverId'];
                  return ChatScreen(
                    chatId: roomId,
                    userName: userName ?? 'User',
                    receiverId: receiverId ?? '',
                  );
                },
              ),
            ],
          ),
          GoRoute(
            path: '/profile',
            name: 'profile',
            builder: (context, state) => const ProfileScreen(),
            routes: [
              GoRoute(
                path: 'edit',
                name: 'edit-profile',
                builder: (context, state) => const EditProfileScreen(),
              ),
            ],
          ),
        ],
      ),
      
      // Other Routes
      GoRoute(
        path: '/search',
        name: 'search',
        builder: (context, state) => const SearchScreen(),
      ),
      GoRoute(
        path: '/call/:callId',
        name: 'call',
        builder: (context, state) {
          final callId = state.pathParameters['callId']!;
          final isVideoCall = state.uri.queryParameters['video'] == 'true';
          final callerName = state.uri.queryParameters['callerName'] ?? 'Unknown';
          return CallScreen(
            callId: callId,
            userName: callerName,
            isVideoCall: isVideoCall,
          );
        },
      ),
      GoRoute(
        path: '/call-history',
        name: 'call-history',
        builder: (context, state) => const CallHistoryScreen(),
      ),
      GoRoute(
        path: '/kundali',
        name: 'kundali',
        builder: (context, state) => const KundaliScreen(),
        routes: [
          GoRoute(
            path: 'matching/:userId',
            name: 'kundali-matching',
            builder: (context, state) {
              final userId = state.pathParameters['userId']!;
              return KundaliMatchingScreen(userId1: userId, userId2: 'current_user');
            },
          ),
        ],
      ),
      GoRoute(
        path: '/premium',
        name: 'premium',
        builder: (context, state) => const PremiumPlansScreen(),
        routes: [
          GoRoute(
            path: 'payment/:planId',
            name: 'payment',
            builder: (context, state) {
              final planId = state.pathParameters['planId']!;
              final amount = state.uri.queryParameters['amount'] ?? '0';
              return PaymentScreen(planId: planId, amount: amount);
            },
          ),
        ],
      ),
      GoRoute(
        path: '/notifications',
        name: 'notifications',
        builder: (context, state) => const NotificationsScreen(),
      ),
      GoRoute(
        path: '/privacy',
        name: 'privacy',
        builder: (context, state) => const PrivacySettingsScreen(),
      ),
      GoRoute(
        path: '/biodata',
        name: 'biodata',
        builder: (context, state) => const BiodataScreen(),
      ),
      GoRoute(
        path: '/analytics',
        name: 'analytics',
        builder: (context, state) => const AnalyticsScreen(),
      ),
      GoRoute(
        path: '/verification',
        name: 'verification',
        builder: (context, state) => const DocumentVerificationScreen(),
      ),
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page Not Found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    ),
  );
});

// Route Names for easy access
class AppRoutes {
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String otpVerification = '/otp-verification';
  static const String home = '/home';
  static const String matches = '/matches';
  static const String matchDetail = '/matches/detail';
  static const String chatList = '/chat';
  static const String chat = '/chat';
  static const String profile = '/profile';
  static const String editProfile = '/profile/edit';
  static const String search = '/search';
  static const String call = '/call';
  static const String callHistory = '/call-history';
  static const String kundali = '/kundali';
  static const String kundaliMatching = '/kundali/matching';
  static const String premium = '/premium';
  static const String payment = '/premium/payment';
  static const String notifications = '/notifications';
  static const String privacy = '/privacy';
  static const String biodata = '/biodata';
  static const String analytics = '/analytics';
  static const String verification = '/verification';
  static const String settings = '/settings';
}

// Navigation Extensions
extension GoRouterExtension on GoRouter {
  void pushAndClearStack(String location) {
    while (canPop()) {
      pop();
    }
    pushReplacement(location);
  }
}

extension BuildContextExtension on BuildContext {
  void showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : null,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  void showLoadingDialog() {
    showDialog(
      context: this,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
  
  void hideLoadingDialog() {
    if (Navigator.canPop(this)) {
      Navigator.pop(this);
    }
  }
}
