import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/widgets/feature_access_widget.dart';
import '../../../core/services/feature_access_service.dart';

class SearchScreen extends ConsumerWidget {
  const SearchScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const TextField(
          decoration: InputDecoration(
            hintText: 'Search profiles...',
            border: InputBorder.none,
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          // Advanced Search Button with Feature Access
          FeatureAccessWidget(
            feature: FeatureType.advancedSearch,
            child: IconButton(
              icon: const Icon(Icons.tune),
              onPressed: () {
                // Navigate to advanced search
                Navigator.pushNamed(context, '/advanced-search');
              },
              tooltip: 'Advanced Search',
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Basic Search Section
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Basic filters available to all users
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'Age Range',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '18-25', child: Text('18-25 years')),
                          DropdownMenuItem(value: '26-30', child: Text('26-30 years')),
                          DropdownMenuItem(value: '31-35', child: Text('31-35 years')),
                          DropdownMenuItem(value: '36-40', child: Text('36-40 years')),
                        ],
                        onChanged: (value) {},
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: 'City',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'mumbai', child: Text('Mumbai')),
                          DropdownMenuItem(value: 'pune', child: Text('Pune')),
                          DropdownMenuItem(value: 'nashik', child: Text('Nashik')),
                          DropdownMenuItem(value: 'nagpur', child: Text('Nagpur')),
                        ],
                        onChanged: (value) {},
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Premium Search Filters
                FeatureAccessWidget(
                  feature: FeatureType.advancedSearch,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Income Range',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: '0-5', child: Text('0-5 Lakhs')),
                                DropdownMenuItem(value: '5-10', child: Text('5-10 Lakhs')),
                                DropdownMenuItem(value: '10-20', child: Text('10-20 Lakhs')),
                                DropdownMenuItem(value: '20+', child: Text('20+ Lakhs')),
                              ],
                              onChanged: (value) {},
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Education',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: 'graduate', child: Text('Graduate')),
                                DropdownMenuItem(value: 'postgraduate', child: Text('Post Graduate')),
                                DropdownMenuItem(value: 'professional', child: Text('Professional')),
                              ],
                              onChanged: (value) {},
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Occupation',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: 'engineer', child: Text('Engineer')),
                                DropdownMenuItem(value: 'doctor', child: Text('Doctor')),
                                DropdownMenuItem(value: 'business', child: Text('Business')),
                                DropdownMenuItem(value: 'teacher', child: Text('Teacher')),
                              ],
                              onChanged: (value) {},
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: DropdownButtonFormField<String>(
                              decoration: const InputDecoration(
                                labelText: 'Marital Status',
                                border: OutlineInputBorder(),
                              ),
                              items: const [
                                DropdownMenuItem(value: 'never_married', child: Text('Never Married')),
                                DropdownMenuItem(value: 'divorced', child: Text('Divorced')),
                                DropdownMenuItem(value: 'widowed', child: Text('Widowed')),
                              ],
                              onChanged: (value) {},
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    // Perform search
                  },
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child: const Text('Search Profiles'),
                ),
              ],
            ),
          ),

          // Search Results Section
          const Expanded(
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.search, size: 80, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Search Results',
                    style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text('Search results will appear here...'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
