import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/api/api_client.dart';
import '../models/content_model.dart';

/// 📄 Content Management Providers
/// Using existing website backend APIs

// API Client provider
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

// Content Provider
final contentProvider = StateNotifierProvider.family<ContentNotifier, AsyncValue<ContentModel>, String>(
  (ref, contentKey) => ContentNotifier(ref.read(apiClientProvider), contentKey),
);

class ContentNotifier extends StateNotifier<AsyncValue<ContentModel>> {
  final ApiClient _apiClient;
  final String _contentKey;

  ContentNotifier(this._apiClient, this._contentKey) : super(const AsyncValue.loading()) {
    loadContent();
  }

  Future<void> loadContent() async {
    try {
      state = const AsyncValue.loading();
      
      final response = await _apiClient.get('/content/$_contentKey');
      
      if (response['success'] == true && response['page'] != null) {
        final content = ContentModel.fromJson(response['page']);
        state = AsyncValue.data(content);
      } else {
        throw Exception(response['message'] ?? 'Failed to load content');
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  Future<void> refresh() async {
    await loadContent();
  }
}

// Social Media Links Provider
final socialMediaLinksProvider = StateNotifierProvider<SocialMediaLinksNotifier, AsyncValue<List<SocialMediaLink>>>(
  (ref) => SocialMediaLinksNotifier(ref.read(apiClientProvider)),
);

class SocialMediaLinksNotifier extends StateNotifier<AsyncValue<List<SocialMediaLink>>> {
  final ApiClient _apiClient;

  SocialMediaLinksNotifier(this._apiClient) : super(const AsyncValue.loading()) {
    loadSocialMediaLinks();
  }

  Future<void> loadSocialMediaLinks() async {
    try {
      state = const AsyncValue.loading();
      
      final response = await _apiClient.get('/social-media-links');
      
      if (response['success'] == true && response['links'] != null) {
        final links = (response['links'] as List)
            .map((json) => SocialMediaLink.fromJson(json))
            .toList();
        state = AsyncValue.data(links);
      } else {
        throw Exception(response['message'] ?? 'Failed to load social media links');
      }
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

// FAQ Provider
final faqProvider = StateNotifierProvider<FAQNotifier, AsyncValue<List<FAQItem>>>(
  (ref) => FAQNotifier(ref.read(apiClientProvider)),
);

class FAQNotifier extends StateNotifier<AsyncValue<List<FAQItem>>> {
  final ApiClient _apiClient;

  FAQNotifier(this._apiClient) : super(const AsyncValue.loading()) {
    loadFAQ();
  }

  Future<void> loadFAQ() async {
    try {
      state = const AsyncValue.loading();
      
      // Load FAQ from content management system
      final response = await _apiClient.get('/content/faq');
      
      if (response['success'] == true) {
        // Parse FAQ content or use fallback
        final faqItems = _parseFAQContent(response['page']?['content'] ?? '');
        state = AsyncValue.data(faqItems);
      } else {
        // Use fallback FAQ
        state = AsyncValue.data(_getFallbackFAQ());
      }
    } catch (e, _) {
      // Use fallback FAQ on error
      state = AsyncValue.data(_getFallbackFAQ());
    }
  }

  List<FAQItem> _parseFAQContent(String content) {
    // Simple parsing - in production, this would be more sophisticated
    return _getFallbackFAQ();
  }

  List<FAQItem> _getFallbackFAQ() {
    return [
      FAQItem(
        question: 'How do I create a profile?',
        answer: 'To create a profile, download the app, enter your phone number, verify with OTP, and complete the registration form with your basic details.',
        category: 'registration',
      ),
      FAQItem(
        question: 'What documents are required for verification?',
        answer: 'You need a government-issued ID proof (Aadhaar, PAN, Passport) and optionally education/income certificates for enhanced verification.',
        category: 'verification',
      ),
      FAQItem(
        question: 'How does the matching algorithm work?',
        answer: 'Our AI-powered matching considers your preferences, compatibility factors, location, education, and lifestyle to suggest the most suitable profiles.',
        category: 'matching',
      ),
      FAQItem(
        question: 'What are the premium features?',
        answer: 'Premium features include unlimited messaging, advanced search filters, profile boost, contact reveal, and priority customer support.',
        category: 'premium',
      ),
      FAQItem(
        question: 'How much does premium cost?',
        answer: 'Premium plans start from ₹999/month. We offer various plans including 3-month, 6-month, and annual subscriptions with discounts.',
        category: 'premium',
      ),
      FAQItem(
        question: 'Can I cancel my subscription?',
        answer: 'Yes, you can cancel your subscription anytime from the app settings. Refunds are available within 7 days if no premium features were used.',
        category: 'premium',
      ),
      FAQItem(
        question: 'How is my privacy protected?',
        answer: 'We have strict privacy controls. You can control who sees your photos, contact details, and profile information. Your data is encrypted and secure.',
        category: 'privacy',
      ),
      FAQItem(
        question: 'What is Kundli matching?',
        answer: 'Kundli matching is a traditional astrological compatibility analysis based on birth details. Our system provides detailed compatibility reports.',
        category: 'kundli',
      ),
    ];
  }
}

// Chatbot Provider
final chatbotProvider = StateNotifierProvider<ChatbotNotifier, List<ChatMessage>>(
  (ref) => ChatbotNotifier(ref.read(apiClientProvider)),
);

class ChatMessage {
  final String message;
  final bool isUser;
  final DateTime timestamp;
  final List<String> suggestions;

  ChatMessage({
    required this.message,
    required this.isUser,
    required this.timestamp,
    this.suggestions = const [],
  });
}

class ChatbotNotifier extends StateNotifier<List<ChatMessage>> {
  final ApiClient _apiClient;

  ChatbotNotifier(this._apiClient) : super([
    ChatMessage(
      message: 'Hello! I\'m here to help you with any questions about Vaivahik. How can I assist you today?',
      isUser: false,
      timestamp: DateTime.now(),
      suggestions: [
        'How to create profile?',
        'Premium features',
        'Matching process',
        'Contact support',
      ],
    ),
  ]);

  Future<void> sendMessage(String message) async {
    // Add user message
    state = [
      ...state,
      ChatMessage(
        message: message,
        isUser: true,
        timestamp: DateTime.now(),
      ),
    ];

    try {
      // Get bot response (using simple logic for now)
      final response = await _getBotResponse(message);
      
      // Add bot response
      state = [
        ...state,
        ChatMessage(
          message: response.message,
          isUser: false,
          timestamp: DateTime.now(),
          suggestions: response.suggestions,
        ),
      ];
    } catch (e) {
      // Add error response
      state = [
        ...state,
        ChatMessage(
          message: 'I apologize, but I\'m having trouble responding right now. Please contact our support team for immediate assistance.',
          isUser: false,
          timestamp: DateTime.now(),
          suggestions: ['Contact Support'],
        ),
      ];
    }
  }

  Future<ChatbotResponse> _getBotResponse(String message) async {
    // In a real implementation, this would use _apiClient to call chatbot API
    // For now, we'll use local logic but keep _apiClient for future use
    print('ChatBot using API Client: ${_apiClient.runtimeType}');

    final lowerMessage = message.toLowerCase();
    
    if (lowerMessage.contains('profile') || lowerMessage.contains('register')) {
      return ChatbotResponse(
        message: 'To create your profile:\n1. Enter your phone number\n2. Verify with OTP\n3. Complete basic details\n4. Add photos\n5. Complete remaining profile sections\n\nWould you like help with any specific step?',
        suggestions: ['Photo upload', 'Profile completion', 'Verification'],
      );
    } else if (lowerMessage.contains('premium') || lowerMessage.contains('subscription')) {
      return ChatbotResponse(
        message: 'Our Premium features include:\n• Unlimited messaging\n• Advanced search filters\n• Profile boost\n• Contact reveal\n• Priority support\n\nPlans start from ₹999/month. Would you like to know more about any specific feature?',
        suggestions: ['View plans', 'Contact reveal', 'Profile boost'],
      );
    } else if (lowerMessage.contains('match') || lowerMessage.contains('search')) {
      return ChatbotResponse(
        message: 'Our AI matching considers:\n• Your preferences\n• Compatibility factors\n• Location proximity\n• Education & career\n• Lifestyle choices\n\nYou can also use advanced search filters to find specific profiles.',
        suggestions: ['Search filters', 'Compatibility', 'Location matching'],
      );
    } else if (lowerMessage.contains('contact') || lowerMessage.contains('support')) {
      return ChatbotResponse(
        message: 'You can reach our support team:\n📧 Email: <EMAIL>\n📞 Phone: +91-XXXXXXXXXX\n💬 In-app chat (Premium users)\n\nWe typically respond within 24 hours.',
        suggestions: ['Email support', 'Premium support'],
        type: 'contact',
      );
    } else {
      return ChatbotResponse(
        message: 'I can help you with:\n• Profile creation and completion\n• Premium features and plans\n• Matching and search process\n• Technical support\n\nWhat would you like to know more about?',
        suggestions: ['Profile help', 'Premium features', 'Matching process', 'Technical support'],
      );
    }
  }

  void clearChat() {
    state = [
      ChatMessage(
        message: 'Hello! I\'m here to help you with any questions about Vaivahik. How can I assist you today?',
        isUser: false,
        timestamp: DateTime.now(),
        suggestions: [
          'How to create profile?',
          'Premium features',
          'Matching process',
          'Contact support',
        ],
      ),
    ];
  }
}
