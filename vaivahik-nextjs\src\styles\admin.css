/* Admin Panel Styles */

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

:root {
  --primary: #5e35b1; /* Deep Purple */
  --primary-light: #7e57c2;
  --primary-dark: #4527a0;
  --secondary: #ff5722; /* Deep Orange */
  --text-light: #f5f5f5;
  --text-dark: #333;
  --bg-light: #f9f9f9;
  --bg-dark: #2d3748; /* Dark Gray for potential dark mode */
  --success: #4caf50; /* Green */
  --warning: #ff9800; /* Amber */
  --danger: #f44336; /* Red */
  --info: #2196f3; /* Blue */
  --border: #e0e0e0;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

body {
  background-color: var(--bg-light);
  color: var(--text-dark);
  line-height: 1.6; /* Improved readability */
}

/* Layout */
.admin-container {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  width: 260px;
  background: linear-gradient(to bottom, var(--primary), var(--primary-dark));
  color: var(--text-light);
  padding: 20px 0;
  position: fixed; /* Fixed sidebar */
  height: 100vh;
  overflow-y: auto; /* Scroll if content exceeds height */
  transition: all 0.3s ease;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 70px;
}

.sidebar.collapsed .sidebar-logo span,
.sidebar.collapsed .user-info,
.sidebar.collapsed .nav-text,
.sidebar.collapsed .nav-category {
  display: none;
}

.sidebar.collapsed .sidebar-header {
  justify-content: center; /* Center logo when collapsed */
  padding-bottom: 18px; /* Adjust padding */
}

.sidebar.collapsed .sidebar-logo div {
  font-size: 1.5rem; /* Adjust icon size */
}

.sidebar.collapsed .sidebar-toggle {
  position: absolute;
  top: 23px;
  right: 20px; /* Keep toggle visible */
}

.sidebar.collapsed .sidebar-user {
  padding: 15px 0; /* Adjust padding */
  justify-content: center;
}

.sidebar.collapsed .nav-item {
  justify-content: center; /* Center icons */
  padding: 15px 20px; /* Adjust padding */
}

.sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  text-decoration: none; /* Remove underline if it's a link */
}
.sidebar-logo div { /* Style the 'V' */
  background-color: var(--secondary);
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: bold;
}

.sidebar-logo span {
  color: white; /* Keep admin text white */
}

.sidebar-toggle {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.5rem; /* Make toggle icon larger */
  padding: 5px; /* Add padding for easier clicking */
  line-height: 1; /* Prevent extra spacing */
}

.sidebar-user {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: white; /* Ensure text is visible */
  overflow: hidden;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
}

.user-role {
  font-size: 0.8rem;
  opacity: 0.8;
}

.sidebar-nav {
  padding: 20px 0;
}

.nav-category {
  padding: 10px 20px;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 10px;
}
.nav-category:first-child {
  margin-top: 0; /* Remove margin for the first category */
}

.nav-item {
  padding: 14px 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 16px;
  margin: 8px 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  width: calc(100% - 24px);
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s ease;
}

.nav-item:hover {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateX(8px) scale(1.02);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.nav-item:hover::before {
  left: 100%;
}

.nav-item.active {
  background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
  color: white;
  box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
  transform: translateX(8px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.3);
}

.nav-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem; /* Slightly larger icons */
}

/* Main Content */
.main-content {
  flex: 1;
  margin-left: 260px; /* Match sidebar width */
  padding: 30px; /* More padding */
  transition: all 0.3s ease;
  position: relative;
  z-index: 0; /* Lower than sidebar */
  width: calc(100% - 260px); /* Ensure it doesn't extend under the sidebar */
  box-sizing: border-box;
  min-height: 100vh;
  display: block !important; /* Ensure content is displayed */
}

.main-content.collapsed {
  margin-left: 70px; /* Match collapsed sidebar width */
  width: calc(100% - 70px); /* Adjust width for collapsed sidebar */
}

/* Top Bar */
.topbar {
  background-color: white;
  padding: 15px 30px; /* More padding */
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: var(--shadow);
  border-radius: 8px;
  margin-bottom: 30px; /* More spacing */
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.9rem;
  color: #666;
}

.breadcrumb a {
  color: var(--primary);
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.topbar-actions {
  display: flex;
  align-items: center;
  gap: 20px;
}

.dark-mode-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  position: relative;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dark-mode-toggle .light-icon,
.dark-mode-toggle .dark-icon {
  position: absolute;
  transition: all 0.3s ease;
}

.dark-mode-toggle .dark-icon {
  opacity: 0;
  transform: scale(0);
}

body.dark-mode .dark-mode-toggle .light-icon {
  opacity: 0;
  transform: scale(0);
}

body.dark-mode .dark-mode-toggle .dark-icon {
  opacity: 1;
  transform: scale(1);
}

.notifications {
  position: relative;
}

.notification-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  position: relative;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--danger);
  color: white;
  font-size: 0.7rem;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Content Header */
.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--text-dark);
}

.header-actions {
  display: flex;
  gap: 15px;
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: 12px;
  box-shadow: var(--shadow);
  margin-bottom: 30px;
  border: 1px solid var(--border);
}

.card-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--border);
}

.card-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.card-subtitle {
  font-size: 0.9rem;
  color: #666;
}

.card-body {
  padding: 25px;
}

/* Button Styles */
.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #333;
  border-color: #ddd;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
  border-color: #ccc;
}

.btn-danger {
  background-color: var(--danger);
  color: white;
  border-color: var(--danger);
}

.btn-danger:hover {
  background-color: #d32f2f;
  border-color: #d32f2f;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.btn-outline-primary {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  background-color: rgba(94, 53, 177, 0.1);
}

.btn-outline-danger {
  background-color: transparent;
  color: var(--danger);
  border-color: var(--danger);
}

.btn-outline-danger:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  align-items: center;
  justify-content: center;
  overflow-y: auto;
  padding: 30px;
}

.modal.show {
  display: flex !important;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.confirmation-modal-content {
  max-width: 450px;
}

.modal-header {
  padding: 20px 25px;
  border-bottom: 1px solid var(--border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  background-color: white;
  z-index: 1;
  border-radius: 12px 12px 0 0;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-dark);
}

.close-modal {
  background: none;
  border: none;
  font-size: 1.5rem;
  line-height: 1;
  cursor: pointer;
  color: #666;
  transition: color 0.3s ease;
}

.close-modal:hover {
  color: var(--danger);
}

.modal-body {
  padding: 25px;
}

.modal-footer {
  padding: 20px 25px;
  border-top: 1px solid var(--border);
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  position: sticky;
  bottom: 0;
  background-color: white;
  z-index: 1;
  border-radius: 0 0 12px 12px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #444;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  font-size: 0.95rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: var(--primary);
  outline: none;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-hint {
  font-size: 0.8rem;
  color: #666;
  margin-top: 5px;
}

.form-check {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 10px;
}

.form-check input[type="checkbox"],
.form-check input[type="radio"] {
  width: 16px;
  height: 16px;
}

.form-check label {
  margin-bottom: 0;
  font-weight: normal;
}

/* Table Styles */
.table-container {
  overflow-x: auto;
  margin-bottom: 30px;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.data-table th {
  font-weight: 600;
  color: #444;
  background-color: #f9f9f9;
}

.data-table tr:hover {
  background-color: #f5f5f5;
}

.data-table .actions {
  display: flex;
  gap: 10px;
}

/* Notification Toast */
.notification-toast {
  position: fixed;
  bottom: 30px;
  right: 30px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  padding: 15px 20px;
  z-index: 2000;
  transform: translateY(100px);
  opacity: 0;
  transition: all 0.3s ease;
  border-left: 4px solid var(--primary);
}

.notification-toast.show {
  transform: translateY(0);
  opacity: 1;
}

.notification-toast.notification-success {
  border-left-color: var(--success);
}

.notification-toast.notification-error {
  border-left-color: var(--danger);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Loading Spinner */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark Mode Styles */
body.dark-mode {
  background-color: var(--bg-dark);
  color: var(--text-light);
}

body.dark-mode .main-content {
  background-color: var(--bg-dark);
}

body.dark-mode .topbar,
body.dark-mode .card,
body.dark-mode .modal-content {
  background-color: #3a4556;
  color: var(--text-light);
}

body.dark-mode .card-header,
body.dark-mode .modal-header,
body.dark-mode .modal-footer {
  border-color: #4a5568;
}

body.dark-mode .form-group label,
body.dark-mode .card-header h3,
body.dark-mode .page-title {
  color: var(--text-light);
}

body.dark-mode .form-group input,
body.dark-mode .form-group select,
body.dark-mode .form-group textarea {
  background-color: #2d3748;
  border-color: #4a5568;
  color: var(--text-light);
}

body.dark-mode .btn-secondary {
  background-color: #4a5568;
  color: var(--text-light);
  border-color: #4a5568;
}

body.dark-mode .data-table th {
  background-color: #2d3748;
  color: var(--text-light);
}

body.dark-mode .data-table td {
  border-color: #4a5568;
}

body.dark-mode .data-table tr:hover {
  background-color: #3a4556;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }

  .sidebar .sidebar-logo span,
  .sidebar .user-info,
  .sidebar .nav-text,
  .sidebar .nav-category {
    display: none;
  }

  .sidebar .sidebar-header {
    justify-content: center;
    padding-bottom: 18px;
  }

  .sidebar .sidebar-logo div {
    font-size: 1.5rem;
  }

  .sidebar .sidebar-toggle {
    position: absolute;
    top: 23px;
    right: 20px;
  }

  .sidebar .sidebar-user {
    padding: 15px 0;
    justify-content: center;
  }

  .sidebar .nav-item {
    justify-content: center;
    padding: 15px 20px;
  }

  .main-content {
    margin-left: 70px;
    width: calc(100% - 70px);
    padding: 20px;
  }

  .topbar {
    padding: 15px;
  }

  .card-header, .card-body {
    padding: 15px;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .header-actions {
    width: 100%;
  }
}

/* User Cell and ID Styles */
.user-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-id-cell {
  font-family: monospace;
  color: #666;
  font-size: 0.9em;
}

.user-id {
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.profile-id {
  font-family: monospace;
  color: #666;
  font-size: 0.9em;
  margin-bottom: 5px;
}
