// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'kundali_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$KundaliData {
  String get id;
  String get userId;
  String get name;
  DateTime get birthDate;
  String get birthTime;
  String get birthPlace;
  double get latitude;
  double get longitude;
  String get timezone;
  Map<String, dynamic> get planetaryPositions;
  Map<String, dynamic> get houses;
  Map<String, dynamic> get aspects;
  List<String> get doshas;
  String? get mangalDosha;
  String? get kalSarpaDosha;
  String? get pitruDosha;
  DateTime get createdAt;
  DateTime get updatedAt; // Additional premium features
  Map<String, dynamic>? get yogas;
  Map<String, dynamic>? get transits;
  List<String>? get predictions;
  String? get nakshatra;
  String? get rashi;
  double? get moonLongitude;
  String? get ascendant;
  bool? get isPremium;

  /// Create a copy of KundaliData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $KundaliDataCopyWith<KundaliData> get copyWith =>
      _$KundaliDataCopyWithImpl<KundaliData>(this as KundaliData, _$identity);

  /// Serializes this KundaliData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is KundaliData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.birthDate, birthDate) ||
                other.birthDate == birthDate) &&
            (identical(other.birthTime, birthTime) ||
                other.birthTime == birthTime) &&
            (identical(other.birthPlace, birthPlace) ||
                other.birthPlace == birthPlace) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.timezone, timezone) ||
                other.timezone == timezone) &&
            const DeepCollectionEquality()
                .equals(other.planetaryPositions, planetaryPositions) &&
            const DeepCollectionEquality().equals(other.houses, houses) &&
            const DeepCollectionEquality().equals(other.aspects, aspects) &&
            const DeepCollectionEquality().equals(other.doshas, doshas) &&
            (identical(other.mangalDosha, mangalDosha) ||
                other.mangalDosha == mangalDosha) &&
            (identical(other.kalSarpaDosha, kalSarpaDosha) ||
                other.kalSarpaDosha == kalSarpaDosha) &&
            (identical(other.pitruDosha, pitruDosha) ||
                other.pitruDosha == pitruDosha) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other.yogas, yogas) &&
            const DeepCollectionEquality().equals(other.transits, transits) &&
            const DeepCollectionEquality()
                .equals(other.predictions, predictions) &&
            (identical(other.nakshatra, nakshatra) ||
                other.nakshatra == nakshatra) &&
            (identical(other.rashi, rashi) || other.rashi == rashi) &&
            (identical(other.moonLongitude, moonLongitude) ||
                other.moonLongitude == moonLongitude) &&
            (identical(other.ascendant, ascendant) ||
                other.ascendant == ascendant) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        name,
        birthDate,
        birthTime,
        birthPlace,
        latitude,
        longitude,
        timezone,
        const DeepCollectionEquality().hash(planetaryPositions),
        const DeepCollectionEquality().hash(houses),
        const DeepCollectionEquality().hash(aspects),
        const DeepCollectionEquality().hash(doshas),
        mangalDosha,
        kalSarpaDosha,
        pitruDosha,
        createdAt,
        updatedAt,
        const DeepCollectionEquality().hash(yogas),
        const DeepCollectionEquality().hash(transits),
        const DeepCollectionEquality().hash(predictions),
        nakshatra,
        rashi,
        moonLongitude,
        ascendant,
        isPremium
      ]);

  @override
  String toString() {
    return 'KundaliData(id: $id, userId: $userId, name: $name, birthDate: $birthDate, birthTime: $birthTime, birthPlace: $birthPlace, latitude: $latitude, longitude: $longitude, timezone: $timezone, planetaryPositions: $planetaryPositions, houses: $houses, aspects: $aspects, doshas: $doshas, mangalDosha: $mangalDosha, kalSarpaDosha: $kalSarpaDosha, pitruDosha: $pitruDosha, createdAt: $createdAt, updatedAt: $updatedAt, yogas: $yogas, transits: $transits, predictions: $predictions, nakshatra: $nakshatra, rashi: $rashi, moonLongitude: $moonLongitude, ascendant: $ascendant, isPremium: $isPremium)';
  }
}

/// @nodoc
abstract mixin class $KundaliDataCopyWith<$Res> {
  factory $KundaliDataCopyWith(
          KundaliData value, $Res Function(KundaliData) _then) =
      _$KundaliDataCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String name,
      DateTime birthDate,
      String birthTime,
      String birthPlace,
      double latitude,
      double longitude,
      String timezone,
      Map<String, dynamic> planetaryPositions,
      Map<String, dynamic> houses,
      Map<String, dynamic> aspects,
      List<String> doshas,
      String? mangalDosha,
      String? kalSarpaDosha,
      String? pitruDosha,
      DateTime createdAt,
      DateTime updatedAt,
      Map<String, dynamic>? yogas,
      Map<String, dynamic>? transits,
      List<String>? predictions,
      String? nakshatra,
      String? rashi,
      double? moonLongitude,
      String? ascendant,
      bool? isPremium});
}

/// @nodoc
class _$KundaliDataCopyWithImpl<$Res> implements $KundaliDataCopyWith<$Res> {
  _$KundaliDataCopyWithImpl(this._self, this._then);

  final KundaliData _self;
  final $Res Function(KundaliData) _then;

  /// Create a copy of KundaliData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? birthDate = null,
    Object? birthTime = null,
    Object? birthPlace = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? timezone = null,
    Object? planetaryPositions = null,
    Object? houses = null,
    Object? aspects = null,
    Object? doshas = null,
    Object? mangalDosha = freezed,
    Object? kalSarpaDosha = freezed,
    Object? pitruDosha = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? yogas = freezed,
    Object? transits = freezed,
    Object? predictions = freezed,
    Object? nakshatra = freezed,
    Object? rashi = freezed,
    Object? moonLongitude = freezed,
    Object? ascendant = freezed,
    Object? isPremium = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      birthDate: null == birthDate
          ? _self.birthDate
          : birthDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      birthTime: null == birthTime
          ? _self.birthTime
          : birthTime // ignore: cast_nullable_to_non_nullable
              as String,
      birthPlace: null == birthPlace
          ? _self.birthPlace
          : birthPlace // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      timezone: null == timezone
          ? _self.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as String,
      planetaryPositions: null == planetaryPositions
          ? _self.planetaryPositions
          : planetaryPositions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      houses: null == houses
          ? _self.houses
          : houses // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      aspects: null == aspects
          ? _self.aspects
          : aspects // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      doshas: null == doshas
          ? _self.doshas
          : doshas // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mangalDosha: freezed == mangalDosha
          ? _self.mangalDosha
          : mangalDosha // ignore: cast_nullable_to_non_nullable
              as String?,
      kalSarpaDosha: freezed == kalSarpaDosha
          ? _self.kalSarpaDosha
          : kalSarpaDosha // ignore: cast_nullable_to_non_nullable
              as String?,
      pitruDosha: freezed == pitruDosha
          ? _self.pitruDosha
          : pitruDosha // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      yogas: freezed == yogas
          ? _self.yogas
          : yogas // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      transits: freezed == transits
          ? _self.transits
          : transits // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      predictions: freezed == predictions
          ? _self.predictions
          : predictions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      nakshatra: freezed == nakshatra
          ? _self.nakshatra
          : nakshatra // ignore: cast_nullable_to_non_nullable
              as String?,
      rashi: freezed == rashi
          ? _self.rashi
          : rashi // ignore: cast_nullable_to_non_nullable
              as String?,
      moonLongitude: freezed == moonLongitude
          ? _self.moonLongitude
          : moonLongitude // ignore: cast_nullable_to_non_nullable
              as double?,
      ascendant: freezed == ascendant
          ? _self.ascendant
          : ascendant // ignore: cast_nullable_to_non_nullable
              as String?,
      isPremium: freezed == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// Adds pattern-matching-related methods to [KundaliData].
extension KundaliDataPatterns on KundaliData {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_KundaliData value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KundaliData() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_KundaliData value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliData():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_KundaliData value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliData() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String name,
            DateTime birthDate,
            String birthTime,
            String birthPlace,
            double latitude,
            double longitude,
            String timezone,
            Map<String, dynamic> planetaryPositions,
            Map<String, dynamic> houses,
            Map<String, dynamic> aspects,
            List<String> doshas,
            String? mangalDosha,
            String? kalSarpaDosha,
            String? pitruDosha,
            DateTime createdAt,
            DateTime updatedAt,
            Map<String, dynamic>? yogas,
            Map<String, dynamic>? transits,
            List<String>? predictions,
            String? nakshatra,
            String? rashi,
            double? moonLongitude,
            String? ascendant,
            bool? isPremium)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KundaliData() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.name,
            _that.birthDate,
            _that.birthTime,
            _that.birthPlace,
            _that.latitude,
            _that.longitude,
            _that.timezone,
            _that.planetaryPositions,
            _that.houses,
            _that.aspects,
            _that.doshas,
            _that.mangalDosha,
            _that.kalSarpaDosha,
            _that.pitruDosha,
            _that.createdAt,
            _that.updatedAt,
            _that.yogas,
            _that.transits,
            _that.predictions,
            _that.nakshatra,
            _that.rashi,
            _that.moonLongitude,
            _that.ascendant,
            _that.isPremium);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String name,
            DateTime birthDate,
            String birthTime,
            String birthPlace,
            double latitude,
            double longitude,
            String timezone,
            Map<String, dynamic> planetaryPositions,
            Map<String, dynamic> houses,
            Map<String, dynamic> aspects,
            List<String> doshas,
            String? mangalDosha,
            String? kalSarpaDosha,
            String? pitruDosha,
            DateTime createdAt,
            DateTime updatedAt,
            Map<String, dynamic>? yogas,
            Map<String, dynamic>? transits,
            List<String>? predictions,
            String? nakshatra,
            String? rashi,
            double? moonLongitude,
            String? ascendant,
            bool? isPremium)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliData():
        return $default(
            _that.id,
            _that.userId,
            _that.name,
            _that.birthDate,
            _that.birthTime,
            _that.birthPlace,
            _that.latitude,
            _that.longitude,
            _that.timezone,
            _that.planetaryPositions,
            _that.houses,
            _that.aspects,
            _that.doshas,
            _that.mangalDosha,
            _that.kalSarpaDosha,
            _that.pitruDosha,
            _that.createdAt,
            _that.updatedAt,
            _that.yogas,
            _that.transits,
            _that.predictions,
            _that.nakshatra,
            _that.rashi,
            _that.moonLongitude,
            _that.ascendant,
            _that.isPremium);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String userId,
            String name,
            DateTime birthDate,
            String birthTime,
            String birthPlace,
            double latitude,
            double longitude,
            String timezone,
            Map<String, dynamic> planetaryPositions,
            Map<String, dynamic> houses,
            Map<String, dynamic> aspects,
            List<String> doshas,
            String? mangalDosha,
            String? kalSarpaDosha,
            String? pitruDosha,
            DateTime createdAt,
            DateTime updatedAt,
            Map<String, dynamic>? yogas,
            Map<String, dynamic>? transits,
            List<String>? predictions,
            String? nakshatra,
            String? rashi,
            double? moonLongitude,
            String? ascendant,
            bool? isPremium)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliData() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.name,
            _that.birthDate,
            _that.birthTime,
            _that.birthPlace,
            _that.latitude,
            _that.longitude,
            _that.timezone,
            _that.planetaryPositions,
            _that.houses,
            _that.aspects,
            _that.doshas,
            _that.mangalDosha,
            _that.kalSarpaDosha,
            _that.pitruDosha,
            _that.createdAt,
            _that.updatedAt,
            _that.yogas,
            _that.transits,
            _that.predictions,
            _that.nakshatra,
            _that.rashi,
            _that.moonLongitude,
            _that.ascendant,
            _that.isPremium);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _KundaliData implements KundaliData {
  const _KundaliData(
      {required this.id,
      required this.userId,
      required this.name,
      required this.birthDate,
      required this.birthTime,
      required this.birthPlace,
      required this.latitude,
      required this.longitude,
      required this.timezone,
      required final Map<String, dynamic> planetaryPositions,
      required final Map<String, dynamic> houses,
      required final Map<String, dynamic> aspects,
      required final List<String> doshas,
      this.mangalDosha,
      this.kalSarpaDosha,
      this.pitruDosha,
      required this.createdAt,
      required this.updatedAt,
      final Map<String, dynamic>? yogas,
      final Map<String, dynamic>? transits,
      final List<String>? predictions,
      this.nakshatra,
      this.rashi,
      this.moonLongitude,
      this.ascendant,
      this.isPremium})
      : _planetaryPositions = planetaryPositions,
        _houses = houses,
        _aspects = aspects,
        _doshas = doshas,
        _yogas = yogas,
        _transits = transits,
        _predictions = predictions;
  factory _KundaliData.fromJson(Map<String, dynamic> json) =>
      _$KundaliDataFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String name;
  @override
  final DateTime birthDate;
  @override
  final String birthTime;
  @override
  final String birthPlace;
  @override
  final double latitude;
  @override
  final double longitude;
  @override
  final String timezone;
  final Map<String, dynamic> _planetaryPositions;
  @override
  Map<String, dynamic> get planetaryPositions {
    if (_planetaryPositions is EqualUnmodifiableMapView)
      return _planetaryPositions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_planetaryPositions);
  }

  final Map<String, dynamic> _houses;
  @override
  Map<String, dynamic> get houses {
    if (_houses is EqualUnmodifiableMapView) return _houses;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_houses);
  }

  final Map<String, dynamic> _aspects;
  @override
  Map<String, dynamic> get aspects {
    if (_aspects is EqualUnmodifiableMapView) return _aspects;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_aspects);
  }

  final List<String> _doshas;
  @override
  List<String> get doshas {
    if (_doshas is EqualUnmodifiableListView) return _doshas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_doshas);
  }

  @override
  final String? mangalDosha;
  @override
  final String? kalSarpaDosha;
  @override
  final String? pitruDosha;
  @override
  final DateTime createdAt;
  @override
  final DateTime updatedAt;
// Additional premium features
  final Map<String, dynamic>? _yogas;
// Additional premium features
  @override
  Map<String, dynamic>? get yogas {
    final value = _yogas;
    if (value == null) return null;
    if (_yogas is EqualUnmodifiableMapView) return _yogas;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final Map<String, dynamic>? _transits;
  @override
  Map<String, dynamic>? get transits {
    final value = _transits;
    if (value == null) return null;
    if (_transits is EqualUnmodifiableMapView) return _transits;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  final List<String>? _predictions;
  @override
  List<String>? get predictions {
    final value = _predictions;
    if (value == null) return null;
    if (_predictions is EqualUnmodifiableListView) return _predictions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? nakshatra;
  @override
  final String? rashi;
  @override
  final double? moonLongitude;
  @override
  final String? ascendant;
  @override
  final bool? isPremium;

  /// Create a copy of KundaliData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$KundaliDataCopyWith<_KundaliData> get copyWith =>
      __$KundaliDataCopyWithImpl<_KundaliData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$KundaliDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _KundaliData &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.birthDate, birthDate) ||
                other.birthDate == birthDate) &&
            (identical(other.birthTime, birthTime) ||
                other.birthTime == birthTime) &&
            (identical(other.birthPlace, birthPlace) ||
                other.birthPlace == birthPlace) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.timezone, timezone) ||
                other.timezone == timezone) &&
            const DeepCollectionEquality()
                .equals(other._planetaryPositions, _planetaryPositions) &&
            const DeepCollectionEquality().equals(other._houses, _houses) &&
            const DeepCollectionEquality().equals(other._aspects, _aspects) &&
            const DeepCollectionEquality().equals(other._doshas, _doshas) &&
            (identical(other.mangalDosha, mangalDosha) ||
                other.mangalDosha == mangalDosha) &&
            (identical(other.kalSarpaDosha, kalSarpaDosha) ||
                other.kalSarpaDosha == kalSarpaDosha) &&
            (identical(other.pitruDosha, pitruDosha) ||
                other.pitruDosha == pitruDosha) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(other._yogas, _yogas) &&
            const DeepCollectionEquality().equals(other._transits, _transits) &&
            const DeepCollectionEquality()
                .equals(other._predictions, _predictions) &&
            (identical(other.nakshatra, nakshatra) ||
                other.nakshatra == nakshatra) &&
            (identical(other.rashi, rashi) || other.rashi == rashi) &&
            (identical(other.moonLongitude, moonLongitude) ||
                other.moonLongitude == moonLongitude) &&
            (identical(other.ascendant, ascendant) ||
                other.ascendant == ascendant) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        userId,
        name,
        birthDate,
        birthTime,
        birthPlace,
        latitude,
        longitude,
        timezone,
        const DeepCollectionEquality().hash(_planetaryPositions),
        const DeepCollectionEquality().hash(_houses),
        const DeepCollectionEquality().hash(_aspects),
        const DeepCollectionEquality().hash(_doshas),
        mangalDosha,
        kalSarpaDosha,
        pitruDosha,
        createdAt,
        updatedAt,
        const DeepCollectionEquality().hash(_yogas),
        const DeepCollectionEquality().hash(_transits),
        const DeepCollectionEquality().hash(_predictions),
        nakshatra,
        rashi,
        moonLongitude,
        ascendant,
        isPremium
      ]);

  @override
  String toString() {
    return 'KundaliData(id: $id, userId: $userId, name: $name, birthDate: $birthDate, birthTime: $birthTime, birthPlace: $birthPlace, latitude: $latitude, longitude: $longitude, timezone: $timezone, planetaryPositions: $planetaryPositions, houses: $houses, aspects: $aspects, doshas: $doshas, mangalDosha: $mangalDosha, kalSarpaDosha: $kalSarpaDosha, pitruDosha: $pitruDosha, createdAt: $createdAt, updatedAt: $updatedAt, yogas: $yogas, transits: $transits, predictions: $predictions, nakshatra: $nakshatra, rashi: $rashi, moonLongitude: $moonLongitude, ascendant: $ascendant, isPremium: $isPremium)';
  }
}

/// @nodoc
abstract mixin class _$KundaliDataCopyWith<$Res>
    implements $KundaliDataCopyWith<$Res> {
  factory _$KundaliDataCopyWith(
          _KundaliData value, $Res Function(_KundaliData) _then) =
      __$KundaliDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String name,
      DateTime birthDate,
      String birthTime,
      String birthPlace,
      double latitude,
      double longitude,
      String timezone,
      Map<String, dynamic> planetaryPositions,
      Map<String, dynamic> houses,
      Map<String, dynamic> aspects,
      List<String> doshas,
      String? mangalDosha,
      String? kalSarpaDosha,
      String? pitruDosha,
      DateTime createdAt,
      DateTime updatedAt,
      Map<String, dynamic>? yogas,
      Map<String, dynamic>? transits,
      List<String>? predictions,
      String? nakshatra,
      String? rashi,
      double? moonLongitude,
      String? ascendant,
      bool? isPremium});
}

/// @nodoc
class __$KundaliDataCopyWithImpl<$Res> implements _$KundaliDataCopyWith<$Res> {
  __$KundaliDataCopyWithImpl(this._self, this._then);

  final _KundaliData _self;
  final $Res Function(_KundaliData) _then;

  /// Create a copy of KundaliData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? name = null,
    Object? birthDate = null,
    Object? birthTime = null,
    Object? birthPlace = null,
    Object? latitude = null,
    Object? longitude = null,
    Object? timezone = null,
    Object? planetaryPositions = null,
    Object? houses = null,
    Object? aspects = null,
    Object? doshas = null,
    Object? mangalDosha = freezed,
    Object? kalSarpaDosha = freezed,
    Object? pitruDosha = freezed,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? yogas = freezed,
    Object? transits = freezed,
    Object? predictions = freezed,
    Object? nakshatra = freezed,
    Object? rashi = freezed,
    Object? moonLongitude = freezed,
    Object? ascendant = freezed,
    Object? isPremium = freezed,
  }) {
    return _then(_KundaliData(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      name: null == name
          ? _self.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      birthDate: null == birthDate
          ? _self.birthDate
          : birthDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      birthTime: null == birthTime
          ? _self.birthTime
          : birthTime // ignore: cast_nullable_to_non_nullable
              as String,
      birthPlace: null == birthPlace
          ? _self.birthPlace
          : birthPlace // ignore: cast_nullable_to_non_nullable
              as String,
      latitude: null == latitude
          ? _self.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as double,
      longitude: null == longitude
          ? _self.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as double,
      timezone: null == timezone
          ? _self.timezone
          : timezone // ignore: cast_nullable_to_non_nullable
              as String,
      planetaryPositions: null == planetaryPositions
          ? _self._planetaryPositions
          : planetaryPositions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      houses: null == houses
          ? _self._houses
          : houses // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      aspects: null == aspects
          ? _self._aspects
          : aspects // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      doshas: null == doshas
          ? _self._doshas
          : doshas // ignore: cast_nullable_to_non_nullable
              as List<String>,
      mangalDosha: freezed == mangalDosha
          ? _self.mangalDosha
          : mangalDosha // ignore: cast_nullable_to_non_nullable
              as String?,
      kalSarpaDosha: freezed == kalSarpaDosha
          ? _self.kalSarpaDosha
          : kalSarpaDosha // ignore: cast_nullable_to_non_nullable
              as String?,
      pitruDosha: freezed == pitruDosha
          ? _self.pitruDosha
          : pitruDosha // ignore: cast_nullable_to_non_nullable
              as String?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      yogas: freezed == yogas
          ? _self._yogas
          : yogas // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      transits: freezed == transits
          ? _self._transits
          : transits // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      predictions: freezed == predictions
          ? _self._predictions
          : predictions // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      nakshatra: freezed == nakshatra
          ? _self.nakshatra
          : nakshatra // ignore: cast_nullable_to_non_nullable
              as String?,
      rashi: freezed == rashi
          ? _self.rashi
          : rashi // ignore: cast_nullable_to_non_nullable
              as String?,
      moonLongitude: freezed == moonLongitude
          ? _self.moonLongitude
          : moonLongitude // ignore: cast_nullable_to_non_nullable
              as double?,
      ascendant: freezed == ascendant
          ? _self.ascendant
          : ascendant // ignore: cast_nullable_to_non_nullable
              as String?,
      isPremium: freezed == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
mixin _$KundaliMatching {
  String get id;
  String get user1Id;
  String get user2Id;
  KundaliData get user1Kundali;
  KundaliData get user2Kundali;
  int get totalScore;
  int get maxScore;
  double get compatibilityPercentage;
  Map<String, GunaScore> get gunaScores;
  List<String> get strengths;
  List<String> get challenges;
  List<String> get recommendations;
  MatchingStatus get status;
  bool get isPremiumFeature;
  DateTime get createdAt;
  String? get astrologerNotes;

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $KundaliMatchingCopyWith<KundaliMatching> get copyWith =>
      _$KundaliMatchingCopyWithImpl<KundaliMatching>(
          this as KundaliMatching, _$identity);

  /// Serializes this KundaliMatching to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is KundaliMatching &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user1Id, user1Id) || other.user1Id == user1Id) &&
            (identical(other.user2Id, user2Id) || other.user2Id == user2Id) &&
            (identical(other.user1Kundali, user1Kundali) ||
                other.user1Kundali == user1Kundali) &&
            (identical(other.user2Kundali, user2Kundali) ||
                other.user2Kundali == user2Kundali) &&
            (identical(other.totalScore, totalScore) ||
                other.totalScore == totalScore) &&
            (identical(other.maxScore, maxScore) ||
                other.maxScore == maxScore) &&
            (identical(
                    other.compatibilityPercentage, compatibilityPercentage) ||
                other.compatibilityPercentage == compatibilityPercentage) &&
            const DeepCollectionEquality()
                .equals(other.gunaScores, gunaScores) &&
            const DeepCollectionEquality().equals(other.strengths, strengths) &&
            const DeepCollectionEquality()
                .equals(other.challenges, challenges) &&
            const DeepCollectionEquality()
                .equals(other.recommendations, recommendations) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isPremiumFeature, isPremiumFeature) ||
                other.isPremiumFeature == isPremiumFeature) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.astrologerNotes, astrologerNotes) ||
                other.astrologerNotes == astrologerNotes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      user1Id,
      user2Id,
      user1Kundali,
      user2Kundali,
      totalScore,
      maxScore,
      compatibilityPercentage,
      const DeepCollectionEquality().hash(gunaScores),
      const DeepCollectionEquality().hash(strengths),
      const DeepCollectionEquality().hash(challenges),
      const DeepCollectionEquality().hash(recommendations),
      status,
      isPremiumFeature,
      createdAt,
      astrologerNotes);

  @override
  String toString() {
    return 'KundaliMatching(id: $id, user1Id: $user1Id, user2Id: $user2Id, user1Kundali: $user1Kundali, user2Kundali: $user2Kundali, totalScore: $totalScore, maxScore: $maxScore, compatibilityPercentage: $compatibilityPercentage, gunaScores: $gunaScores, strengths: $strengths, challenges: $challenges, recommendations: $recommendations, status: $status, isPremiumFeature: $isPremiumFeature, createdAt: $createdAt, astrologerNotes: $astrologerNotes)';
  }
}

/// @nodoc
abstract mixin class $KundaliMatchingCopyWith<$Res> {
  factory $KundaliMatchingCopyWith(
          KundaliMatching value, $Res Function(KundaliMatching) _then) =
      _$KundaliMatchingCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String user1Id,
      String user2Id,
      KundaliData user1Kundali,
      KundaliData user2Kundali,
      int totalScore,
      int maxScore,
      double compatibilityPercentage,
      Map<String, GunaScore> gunaScores,
      List<String> strengths,
      List<String> challenges,
      List<String> recommendations,
      MatchingStatus status,
      bool isPremiumFeature,
      DateTime createdAt,
      String? astrologerNotes});

  $KundaliDataCopyWith<$Res> get user1Kundali;
  $KundaliDataCopyWith<$Res> get user2Kundali;
}

/// @nodoc
class _$KundaliMatchingCopyWithImpl<$Res>
    implements $KundaliMatchingCopyWith<$Res> {
  _$KundaliMatchingCopyWithImpl(this._self, this._then);

  final KundaliMatching _self;
  final $Res Function(KundaliMatching) _then;

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? user1Id = null,
    Object? user2Id = null,
    Object? user1Kundali = null,
    Object? user2Kundali = null,
    Object? totalScore = null,
    Object? maxScore = null,
    Object? compatibilityPercentage = null,
    Object? gunaScores = null,
    Object? strengths = null,
    Object? challenges = null,
    Object? recommendations = null,
    Object? status = null,
    Object? isPremiumFeature = null,
    Object? createdAt = null,
    Object? astrologerNotes = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user1Id: null == user1Id
          ? _self.user1Id
          : user1Id // ignore: cast_nullable_to_non_nullable
              as String,
      user2Id: null == user2Id
          ? _self.user2Id
          : user2Id // ignore: cast_nullable_to_non_nullable
              as String,
      user1Kundali: null == user1Kundali
          ? _self.user1Kundali
          : user1Kundali // ignore: cast_nullable_to_non_nullable
              as KundaliData,
      user2Kundali: null == user2Kundali
          ? _self.user2Kundali
          : user2Kundali // ignore: cast_nullable_to_non_nullable
              as KundaliData,
      totalScore: null == totalScore
          ? _self.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int,
      maxScore: null == maxScore
          ? _self.maxScore
          : maxScore // ignore: cast_nullable_to_non_nullable
              as int,
      compatibilityPercentage: null == compatibilityPercentage
          ? _self.compatibilityPercentage
          : compatibilityPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      gunaScores: null == gunaScores
          ? _self.gunaScores
          : gunaScores // ignore: cast_nullable_to_non_nullable
              as Map<String, GunaScore>,
      strengths: null == strengths
          ? _self.strengths
          : strengths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      challenges: null == challenges
          ? _self.challenges
          : challenges // ignore: cast_nullable_to_non_nullable
              as List<String>,
      recommendations: null == recommendations
          ? _self.recommendations
          : recommendations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MatchingStatus,
      isPremiumFeature: null == isPremiumFeature
          ? _self.isPremiumFeature
          : isPremiumFeature // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      astrologerNotes: freezed == astrologerNotes
          ? _self.astrologerNotes
          : astrologerNotes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $KundaliDataCopyWith<$Res> get user1Kundali {
    return $KundaliDataCopyWith<$Res>(_self.user1Kundali, (value) {
      return _then(_self.copyWith(user1Kundali: value));
    });
  }

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $KundaliDataCopyWith<$Res> get user2Kundali {
    return $KundaliDataCopyWith<$Res>(_self.user2Kundali, (value) {
      return _then(_self.copyWith(user2Kundali: value));
    });
  }
}

/// Adds pattern-matching-related methods to [KundaliMatching].
extension KundaliMatchingPatterns on KundaliMatching {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_KundaliMatching value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KundaliMatching() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_KundaliMatching value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliMatching():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_KundaliMatching value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliMatching() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String user1Id,
            String user2Id,
            KundaliData user1Kundali,
            KundaliData user2Kundali,
            int totalScore,
            int maxScore,
            double compatibilityPercentage,
            Map<String, GunaScore> gunaScores,
            List<String> strengths,
            List<String> challenges,
            List<String> recommendations,
            MatchingStatus status,
            bool isPremiumFeature,
            DateTime createdAt,
            String? astrologerNotes)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KundaliMatching() when $default != null:
        return $default(
            _that.id,
            _that.user1Id,
            _that.user2Id,
            _that.user1Kundali,
            _that.user2Kundali,
            _that.totalScore,
            _that.maxScore,
            _that.compatibilityPercentage,
            _that.gunaScores,
            _that.strengths,
            _that.challenges,
            _that.recommendations,
            _that.status,
            _that.isPremiumFeature,
            _that.createdAt,
            _that.astrologerNotes);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String user1Id,
            String user2Id,
            KundaliData user1Kundali,
            KundaliData user2Kundali,
            int totalScore,
            int maxScore,
            double compatibilityPercentage,
            Map<String, GunaScore> gunaScores,
            List<String> strengths,
            List<String> challenges,
            List<String> recommendations,
            MatchingStatus status,
            bool isPremiumFeature,
            DateTime createdAt,
            String? astrologerNotes)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliMatching():
        return $default(
            _that.id,
            _that.user1Id,
            _that.user2Id,
            _that.user1Kundali,
            _that.user2Kundali,
            _that.totalScore,
            _that.maxScore,
            _that.compatibilityPercentage,
            _that.gunaScores,
            _that.strengths,
            _that.challenges,
            _that.recommendations,
            _that.status,
            _that.isPremiumFeature,
            _that.createdAt,
            _that.astrologerNotes);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String user1Id,
            String user2Id,
            KundaliData user1Kundali,
            KundaliData user2Kundali,
            int totalScore,
            int maxScore,
            double compatibilityPercentage,
            Map<String, GunaScore> gunaScores,
            List<String> strengths,
            List<String> challenges,
            List<String> recommendations,
            MatchingStatus status,
            bool isPremiumFeature,
            DateTime createdAt,
            String? astrologerNotes)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliMatching() when $default != null:
        return $default(
            _that.id,
            _that.user1Id,
            _that.user2Id,
            _that.user1Kundali,
            _that.user2Kundali,
            _that.totalScore,
            _that.maxScore,
            _that.compatibilityPercentage,
            _that.gunaScores,
            _that.strengths,
            _that.challenges,
            _that.recommendations,
            _that.status,
            _that.isPremiumFeature,
            _that.createdAt,
            _that.astrologerNotes);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _KundaliMatching implements KundaliMatching {
  const _KundaliMatching(
      {required this.id,
      required this.user1Id,
      required this.user2Id,
      required this.user1Kundali,
      required this.user2Kundali,
      required this.totalScore,
      required this.maxScore,
      required this.compatibilityPercentage,
      required final Map<String, GunaScore> gunaScores,
      required final List<String> strengths,
      required final List<String> challenges,
      required final List<String> recommendations,
      required this.status,
      required this.isPremiumFeature,
      required this.createdAt,
      this.astrologerNotes})
      : _gunaScores = gunaScores,
        _strengths = strengths,
        _challenges = challenges,
        _recommendations = recommendations;
  factory _KundaliMatching.fromJson(Map<String, dynamic> json) =>
      _$KundaliMatchingFromJson(json);

  @override
  final String id;
  @override
  final String user1Id;
  @override
  final String user2Id;
  @override
  final KundaliData user1Kundali;
  @override
  final KundaliData user2Kundali;
  @override
  final int totalScore;
  @override
  final int maxScore;
  @override
  final double compatibilityPercentage;
  final Map<String, GunaScore> _gunaScores;
  @override
  Map<String, GunaScore> get gunaScores {
    if (_gunaScores is EqualUnmodifiableMapView) return _gunaScores;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_gunaScores);
  }

  final List<String> _strengths;
  @override
  List<String> get strengths {
    if (_strengths is EqualUnmodifiableListView) return _strengths;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_strengths);
  }

  final List<String> _challenges;
  @override
  List<String> get challenges {
    if (_challenges is EqualUnmodifiableListView) return _challenges;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_challenges);
  }

  final List<String> _recommendations;
  @override
  List<String> get recommendations {
    if (_recommendations is EqualUnmodifiableListView) return _recommendations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recommendations);
  }

  @override
  final MatchingStatus status;
  @override
  final bool isPremiumFeature;
  @override
  final DateTime createdAt;
  @override
  final String? astrologerNotes;

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$KundaliMatchingCopyWith<_KundaliMatching> get copyWith =>
      __$KundaliMatchingCopyWithImpl<_KundaliMatching>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$KundaliMatchingToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _KundaliMatching &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.user1Id, user1Id) || other.user1Id == user1Id) &&
            (identical(other.user2Id, user2Id) || other.user2Id == user2Id) &&
            (identical(other.user1Kundali, user1Kundali) ||
                other.user1Kundali == user1Kundali) &&
            (identical(other.user2Kundali, user2Kundali) ||
                other.user2Kundali == user2Kundali) &&
            (identical(other.totalScore, totalScore) ||
                other.totalScore == totalScore) &&
            (identical(other.maxScore, maxScore) ||
                other.maxScore == maxScore) &&
            (identical(
                    other.compatibilityPercentage, compatibilityPercentage) ||
                other.compatibilityPercentage == compatibilityPercentage) &&
            const DeepCollectionEquality()
                .equals(other._gunaScores, _gunaScores) &&
            const DeepCollectionEquality()
                .equals(other._strengths, _strengths) &&
            const DeepCollectionEquality()
                .equals(other._challenges, _challenges) &&
            const DeepCollectionEquality()
                .equals(other._recommendations, _recommendations) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.isPremiumFeature, isPremiumFeature) ||
                other.isPremiumFeature == isPremiumFeature) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.astrologerNotes, astrologerNotes) ||
                other.astrologerNotes == astrologerNotes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      user1Id,
      user2Id,
      user1Kundali,
      user2Kundali,
      totalScore,
      maxScore,
      compatibilityPercentage,
      const DeepCollectionEquality().hash(_gunaScores),
      const DeepCollectionEquality().hash(_strengths),
      const DeepCollectionEquality().hash(_challenges),
      const DeepCollectionEquality().hash(_recommendations),
      status,
      isPremiumFeature,
      createdAt,
      astrologerNotes);

  @override
  String toString() {
    return 'KundaliMatching(id: $id, user1Id: $user1Id, user2Id: $user2Id, user1Kundali: $user1Kundali, user2Kundali: $user2Kundali, totalScore: $totalScore, maxScore: $maxScore, compatibilityPercentage: $compatibilityPercentage, gunaScores: $gunaScores, strengths: $strengths, challenges: $challenges, recommendations: $recommendations, status: $status, isPremiumFeature: $isPremiumFeature, createdAt: $createdAt, astrologerNotes: $astrologerNotes)';
  }
}

/// @nodoc
abstract mixin class _$KundaliMatchingCopyWith<$Res>
    implements $KundaliMatchingCopyWith<$Res> {
  factory _$KundaliMatchingCopyWith(
          _KundaliMatching value, $Res Function(_KundaliMatching) _then) =
      __$KundaliMatchingCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String user1Id,
      String user2Id,
      KundaliData user1Kundali,
      KundaliData user2Kundali,
      int totalScore,
      int maxScore,
      double compatibilityPercentage,
      Map<String, GunaScore> gunaScores,
      List<String> strengths,
      List<String> challenges,
      List<String> recommendations,
      MatchingStatus status,
      bool isPremiumFeature,
      DateTime createdAt,
      String? astrologerNotes});

  @override
  $KundaliDataCopyWith<$Res> get user1Kundali;
  @override
  $KundaliDataCopyWith<$Res> get user2Kundali;
}

/// @nodoc
class __$KundaliMatchingCopyWithImpl<$Res>
    implements _$KundaliMatchingCopyWith<$Res> {
  __$KundaliMatchingCopyWithImpl(this._self, this._then);

  final _KundaliMatching _self;
  final $Res Function(_KundaliMatching) _then;

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? user1Id = null,
    Object? user2Id = null,
    Object? user1Kundali = null,
    Object? user2Kundali = null,
    Object? totalScore = null,
    Object? maxScore = null,
    Object? compatibilityPercentage = null,
    Object? gunaScores = null,
    Object? strengths = null,
    Object? challenges = null,
    Object? recommendations = null,
    Object? status = null,
    Object? isPremiumFeature = null,
    Object? createdAt = null,
    Object? astrologerNotes = freezed,
  }) {
    return _then(_KundaliMatching(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      user1Id: null == user1Id
          ? _self.user1Id
          : user1Id // ignore: cast_nullable_to_non_nullable
              as String,
      user2Id: null == user2Id
          ? _self.user2Id
          : user2Id // ignore: cast_nullable_to_non_nullable
              as String,
      user1Kundali: null == user1Kundali
          ? _self.user1Kundali
          : user1Kundali // ignore: cast_nullable_to_non_nullable
              as KundaliData,
      user2Kundali: null == user2Kundali
          ? _self.user2Kundali
          : user2Kundali // ignore: cast_nullable_to_non_nullable
              as KundaliData,
      totalScore: null == totalScore
          ? _self.totalScore
          : totalScore // ignore: cast_nullable_to_non_nullable
              as int,
      maxScore: null == maxScore
          ? _self.maxScore
          : maxScore // ignore: cast_nullable_to_non_nullable
              as int,
      compatibilityPercentage: null == compatibilityPercentage
          ? _self.compatibilityPercentage
          : compatibilityPercentage // ignore: cast_nullable_to_non_nullable
              as double,
      gunaScores: null == gunaScores
          ? _self._gunaScores
          : gunaScores // ignore: cast_nullable_to_non_nullable
              as Map<String, GunaScore>,
      strengths: null == strengths
          ? _self._strengths
          : strengths // ignore: cast_nullable_to_non_nullable
              as List<String>,
      challenges: null == challenges
          ? _self._challenges
          : challenges // ignore: cast_nullable_to_non_nullable
              as List<String>,
      recommendations: null == recommendations
          ? _self._recommendations
          : recommendations // ignore: cast_nullable_to_non_nullable
              as List<String>,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as MatchingStatus,
      isPremiumFeature: null == isPremiumFeature
          ? _self.isPremiumFeature
          : isPremiumFeature // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      astrologerNotes: freezed == astrologerNotes
          ? _self.astrologerNotes
          : astrologerNotes // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $KundaliDataCopyWith<$Res> get user1Kundali {
    return $KundaliDataCopyWith<$Res>(_self.user1Kundali, (value) {
      return _then(_self.copyWith(user1Kundali: value));
    });
  }

  /// Create a copy of KundaliMatching
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $KundaliDataCopyWith<$Res> get user2Kundali {
    return $KundaliDataCopyWith<$Res>(_self.user2Kundali, (value) {
      return _then(_self.copyWith(user2Kundali: value));
    });
  }
}

/// @nodoc
mixin _$GunaScore {
  String get gunaName;
  int get obtainedScore;
  int get maxScore;
  String get description;
  CompatibilityLevel get level;
  double get percentage;

  /// Create a copy of GunaScore
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $GunaScoreCopyWith<GunaScore> get copyWith =>
      _$GunaScoreCopyWithImpl<GunaScore>(this as GunaScore, _$identity);

  /// Serializes this GunaScore to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is GunaScore &&
            (identical(other.gunaName, gunaName) ||
                other.gunaName == gunaName) &&
            (identical(other.obtainedScore, obtainedScore) ||
                other.obtainedScore == obtainedScore) &&
            (identical(other.maxScore, maxScore) ||
                other.maxScore == maxScore) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.percentage, percentage) ||
                other.percentage == percentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gunaName, obtainedScore,
      maxScore, description, level, percentage);

  @override
  String toString() {
    return 'GunaScore(gunaName: $gunaName, obtainedScore: $obtainedScore, maxScore: $maxScore, description: $description, level: $level, percentage: $percentage)';
  }
}

/// @nodoc
abstract mixin class $GunaScoreCopyWith<$Res> {
  factory $GunaScoreCopyWith(GunaScore value, $Res Function(GunaScore) _then) =
      _$GunaScoreCopyWithImpl;
  @useResult
  $Res call(
      {String gunaName,
      int obtainedScore,
      int maxScore,
      String description,
      CompatibilityLevel level,
      double percentage});
}

/// @nodoc
class _$GunaScoreCopyWithImpl<$Res> implements $GunaScoreCopyWith<$Res> {
  _$GunaScoreCopyWithImpl(this._self, this._then);

  final GunaScore _self;
  final $Res Function(GunaScore) _then;

  /// Create a copy of GunaScore
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gunaName = null,
    Object? obtainedScore = null,
    Object? maxScore = null,
    Object? description = null,
    Object? level = null,
    Object? percentage = null,
  }) {
    return _then(_self.copyWith(
      gunaName: null == gunaName
          ? _self.gunaName
          : gunaName // ignore: cast_nullable_to_non_nullable
              as String,
      obtainedScore: null == obtainedScore
          ? _self.obtainedScore
          : obtainedScore // ignore: cast_nullable_to_non_nullable
              as int,
      maxScore: null == maxScore
          ? _self.maxScore
          : maxScore // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as CompatibilityLevel,
      percentage: null == percentage
          ? _self.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// Adds pattern-matching-related methods to [GunaScore].
extension GunaScorePatterns on GunaScore {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_GunaScore value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GunaScore() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_GunaScore value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GunaScore():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_GunaScore value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GunaScore() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String gunaName, int obtainedScore, int maxScore,
            String description, CompatibilityLevel level, double percentage)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _GunaScore() when $default != null:
        return $default(_that.gunaName, _that.obtainedScore, _that.maxScore,
            _that.description, _that.level, _that.percentage);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String gunaName, int obtainedScore, int maxScore,
            String description, CompatibilityLevel level, double percentage)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GunaScore():
        return $default(_that.gunaName, _that.obtainedScore, _that.maxScore,
            _that.description, _that.level, _that.percentage);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String gunaName, int obtainedScore, int maxScore,
            String description, CompatibilityLevel level, double percentage)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _GunaScore() when $default != null:
        return $default(_that.gunaName, _that.obtainedScore, _that.maxScore,
            _that.description, _that.level, _that.percentage);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _GunaScore implements GunaScore {
  const _GunaScore(
      {required this.gunaName,
      required this.obtainedScore,
      required this.maxScore,
      required this.description,
      required this.level,
      required this.percentage});
  factory _GunaScore.fromJson(Map<String, dynamic> json) =>
      _$GunaScoreFromJson(json);

  @override
  final String gunaName;
  @override
  final int obtainedScore;
  @override
  final int maxScore;
  @override
  final String description;
  @override
  final CompatibilityLevel level;
  @override
  final double percentage;

  /// Create a copy of GunaScore
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$GunaScoreCopyWith<_GunaScore> get copyWith =>
      __$GunaScoreCopyWithImpl<_GunaScore>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$GunaScoreToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _GunaScore &&
            (identical(other.gunaName, gunaName) ||
                other.gunaName == gunaName) &&
            (identical(other.obtainedScore, obtainedScore) ||
                other.obtainedScore == obtainedScore) &&
            (identical(other.maxScore, maxScore) ||
                other.maxScore == maxScore) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.level, level) || other.level == level) &&
            (identical(other.percentage, percentage) ||
                other.percentage == percentage));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gunaName, obtainedScore,
      maxScore, description, level, percentage);

  @override
  String toString() {
    return 'GunaScore(gunaName: $gunaName, obtainedScore: $obtainedScore, maxScore: $maxScore, description: $description, level: $level, percentage: $percentage)';
  }
}

/// @nodoc
abstract mixin class _$GunaScoreCopyWith<$Res>
    implements $GunaScoreCopyWith<$Res> {
  factory _$GunaScoreCopyWith(
          _GunaScore value, $Res Function(_GunaScore) _then) =
      __$GunaScoreCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String gunaName,
      int obtainedScore,
      int maxScore,
      String description,
      CompatibilityLevel level,
      double percentage});
}

/// @nodoc
class __$GunaScoreCopyWithImpl<$Res> implements _$GunaScoreCopyWith<$Res> {
  __$GunaScoreCopyWithImpl(this._self, this._then);

  final _GunaScore _self;
  final $Res Function(_GunaScore) _then;

  /// Create a copy of GunaScore
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gunaName = null,
    Object? obtainedScore = null,
    Object? maxScore = null,
    Object? description = null,
    Object? level = null,
    Object? percentage = null,
  }) {
    return _then(_GunaScore(
      gunaName: null == gunaName
          ? _self.gunaName
          : gunaName // ignore: cast_nullable_to_non_nullable
              as String,
      obtainedScore: null == obtainedScore
          ? _self.obtainedScore
          : obtainedScore // ignore: cast_nullable_to_non_nullable
              as int,
      maxScore: null == maxScore
          ? _self.maxScore
          : maxScore // ignore: cast_nullable_to_non_nullable
              as int,
      description: null == description
          ? _self.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      level: null == level
          ? _self.level
          : level // ignore: cast_nullable_to_non_nullable
              as CompatibilityLevel,
      percentage: null == percentage
          ? _self.percentage
          : percentage // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
mixin _$AstrologyReport {
  String get id;
  String get userId;
  String get type;
  String get title;
  String get content;
  Map<String, dynamic> get data;
  DateTime get generatedAt;
  String? get astrologerId;
  String? get astrologerName;
  bool get isPremium;
  double? get price;

  /// Create a copy of AstrologyReport
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $AstrologyReportCopyWith<AstrologyReport> get copyWith =>
      _$AstrologyReportCopyWithImpl<AstrologyReport>(
          this as AstrologyReport, _$identity);

  /// Serializes this AstrologyReport to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is AstrologyReport &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            const DeepCollectionEquality().equals(other.data, data) &&
            (identical(other.generatedAt, generatedAt) ||
                other.generatedAt == generatedAt) &&
            (identical(other.astrologerId, astrologerId) ||
                other.astrologerId == astrologerId) &&
            (identical(other.astrologerName, astrologerName) ||
                other.astrologerName == astrologerName) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.price, price) || other.price == price));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      type,
      title,
      content,
      const DeepCollectionEquality().hash(data),
      generatedAt,
      astrologerId,
      astrologerName,
      isPremium,
      price);

  @override
  String toString() {
    return 'AstrologyReport(id: $id, userId: $userId, type: $type, title: $title, content: $content, data: $data, generatedAt: $generatedAt, astrologerId: $astrologerId, astrologerName: $astrologerName, isPremium: $isPremium, price: $price)';
  }
}

/// @nodoc
abstract mixin class $AstrologyReportCopyWith<$Res> {
  factory $AstrologyReportCopyWith(
          AstrologyReport value, $Res Function(AstrologyReport) _then) =
      _$AstrologyReportCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String type,
      String title,
      String content,
      Map<String, dynamic> data,
      DateTime generatedAt,
      String? astrologerId,
      String? astrologerName,
      bool isPremium,
      double? price});
}

/// @nodoc
class _$AstrologyReportCopyWithImpl<$Res>
    implements $AstrologyReportCopyWith<$Res> {
  _$AstrologyReportCopyWithImpl(this._self, this._then);

  final AstrologyReport _self;
  final $Res Function(AstrologyReport) _then;

  /// Create a copy of AstrologyReport
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? title = null,
    Object? content = null,
    Object? data = null,
    Object? generatedAt = null,
    Object? astrologerId = freezed,
    Object? astrologerName = freezed,
    Object? isPremium = null,
    Object? price = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      generatedAt: null == generatedAt
          ? _self.generatedAt
          : generatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      astrologerId: freezed == astrologerId
          ? _self.astrologerId
          : astrologerId // ignore: cast_nullable_to_non_nullable
              as String?,
      astrologerName: freezed == astrologerName
          ? _self.astrologerName
          : astrologerName // ignore: cast_nullable_to_non_nullable
              as String?,
      isPremium: null == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      price: freezed == price
          ? _self.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// Adds pattern-matching-related methods to [AstrologyReport].
extension AstrologyReportPatterns on AstrologyReport {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_AstrologyReport value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstrologyReport() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_AstrologyReport value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstrologyReport():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_AstrologyReport value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstrologyReport() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String type,
            String title,
            String content,
            Map<String, dynamic> data,
            DateTime generatedAt,
            String? astrologerId,
            String? astrologerName,
            bool isPremium,
            double? price)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _AstrologyReport() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.type,
            _that.title,
            _that.content,
            _that.data,
            _that.generatedAt,
            _that.astrologerId,
            _that.astrologerName,
            _that.isPremium,
            _that.price);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String type,
            String title,
            String content,
            Map<String, dynamic> data,
            DateTime generatedAt,
            String? astrologerId,
            String? astrologerName,
            bool isPremium,
            double? price)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstrologyReport():
        return $default(
            _that.id,
            _that.userId,
            _that.type,
            _that.title,
            _that.content,
            _that.data,
            _that.generatedAt,
            _that.astrologerId,
            _that.astrologerName,
            _that.isPremium,
            _that.price);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String userId,
            String type,
            String title,
            String content,
            Map<String, dynamic> data,
            DateTime generatedAt,
            String? astrologerId,
            String? astrologerName,
            bool isPremium,
            double? price)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _AstrologyReport() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.type,
            _that.title,
            _that.content,
            _that.data,
            _that.generatedAt,
            _that.astrologerId,
            _that.astrologerName,
            _that.isPremium,
            _that.price);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _AstrologyReport implements AstrologyReport {
  const _AstrologyReport(
      {required this.id,
      required this.userId,
      required this.type,
      required this.title,
      required this.content,
      required final Map<String, dynamic> data,
      required this.generatedAt,
      this.astrologerId,
      this.astrologerName,
      required this.isPremium,
      this.price})
      : _data = data;
  factory _AstrologyReport.fromJson(Map<String, dynamic> json) =>
      _$AstrologyReportFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String type;
  @override
  final String title;
  @override
  final String content;
  final Map<String, dynamic> _data;
  @override
  Map<String, dynamic> get data {
    if (_data is EqualUnmodifiableMapView) return _data;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_data);
  }

  @override
  final DateTime generatedAt;
  @override
  final String? astrologerId;
  @override
  final String? astrologerName;
  @override
  final bool isPremium;
  @override
  final double? price;

  /// Create a copy of AstrologyReport
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$AstrologyReportCopyWith<_AstrologyReport> get copyWith =>
      __$AstrologyReportCopyWithImpl<_AstrologyReport>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$AstrologyReportToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _AstrologyReport &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.content, content) || other.content == content) &&
            const DeepCollectionEquality().equals(other._data, _data) &&
            (identical(other.generatedAt, generatedAt) ||
                other.generatedAt == generatedAt) &&
            (identical(other.astrologerId, astrologerId) ||
                other.astrologerId == astrologerId) &&
            (identical(other.astrologerName, astrologerName) ||
                other.astrologerName == astrologerName) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.price, price) || other.price == price));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      type,
      title,
      content,
      const DeepCollectionEquality().hash(_data),
      generatedAt,
      astrologerId,
      astrologerName,
      isPremium,
      price);

  @override
  String toString() {
    return 'AstrologyReport(id: $id, userId: $userId, type: $type, title: $title, content: $content, data: $data, generatedAt: $generatedAt, astrologerId: $astrologerId, astrologerName: $astrologerName, isPremium: $isPremium, price: $price)';
  }
}

/// @nodoc
abstract mixin class _$AstrologyReportCopyWith<$Res>
    implements $AstrologyReportCopyWith<$Res> {
  factory _$AstrologyReportCopyWith(
          _AstrologyReport value, $Res Function(_AstrologyReport) _then) =
      __$AstrologyReportCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String type,
      String title,
      String content,
      Map<String, dynamic> data,
      DateTime generatedAt,
      String? astrologerId,
      String? astrologerName,
      bool isPremium,
      double? price});
}

/// @nodoc
class __$AstrologyReportCopyWithImpl<$Res>
    implements _$AstrologyReportCopyWith<$Res> {
  __$AstrologyReportCopyWithImpl(this._self, this._then);

  final _AstrologyReport _self;
  final $Res Function(_AstrologyReport) _then;

  /// Create a copy of AstrologyReport
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? type = null,
    Object? title = null,
    Object? content = null,
    Object? data = null,
    Object? generatedAt = null,
    Object? astrologerId = freezed,
    Object? astrologerName = freezed,
    Object? isPremium = null,
    Object? price = freezed,
  }) {
    return _then(_AstrologyReport(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _self.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      content: null == content
          ? _self.content
          : content // ignore: cast_nullable_to_non_nullable
              as String,
      data: null == data
          ? _self._data
          : data // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      generatedAt: null == generatedAt
          ? _self.generatedAt
          : generatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      astrologerId: freezed == astrologerId
          ? _self.astrologerId
          : astrologerId // ignore: cast_nullable_to_non_nullable
              as String?,
      astrologerName: freezed == astrologerName
          ? _self.astrologerName
          : astrologerName // ignore: cast_nullable_to_non_nullable
              as String?,
      isPremium: null == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool,
      price: freezed == price
          ? _self.price
          : price // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
mixin _$KundaliSettings {
  bool get showKundali;
  bool get allowKundaliMatching;
  bool get requireKundaliForMatching;
  bool get showDoshas;
  bool get showPlanetaryPositions;
  bool get showHouses;
  bool get showAspects;
  String get preferredLanguage;
  bool get enableNotifications;

  /// Create a copy of KundaliSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $KundaliSettingsCopyWith<KundaliSettings> get copyWith =>
      _$KundaliSettingsCopyWithImpl<KundaliSettings>(
          this as KundaliSettings, _$identity);

  /// Serializes this KundaliSettings to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is KundaliSettings &&
            (identical(other.showKundali, showKundali) ||
                other.showKundali == showKundali) &&
            (identical(other.allowKundaliMatching, allowKundaliMatching) ||
                other.allowKundaliMatching == allowKundaliMatching) &&
            (identical(other.requireKundaliForMatching,
                    requireKundaliForMatching) ||
                other.requireKundaliForMatching == requireKundaliForMatching) &&
            (identical(other.showDoshas, showDoshas) ||
                other.showDoshas == showDoshas) &&
            (identical(other.showPlanetaryPositions, showPlanetaryPositions) ||
                other.showPlanetaryPositions == showPlanetaryPositions) &&
            (identical(other.showHouses, showHouses) ||
                other.showHouses == showHouses) &&
            (identical(other.showAspects, showAspects) ||
                other.showAspects == showAspects) &&
            (identical(other.preferredLanguage, preferredLanguage) ||
                other.preferredLanguage == preferredLanguage) &&
            (identical(other.enableNotifications, enableNotifications) ||
                other.enableNotifications == enableNotifications));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showKundali,
      allowKundaliMatching,
      requireKundaliForMatching,
      showDoshas,
      showPlanetaryPositions,
      showHouses,
      showAspects,
      preferredLanguage,
      enableNotifications);

  @override
  String toString() {
    return 'KundaliSettings(showKundali: $showKundali, allowKundaliMatching: $allowKundaliMatching, requireKundaliForMatching: $requireKundaliForMatching, showDoshas: $showDoshas, showPlanetaryPositions: $showPlanetaryPositions, showHouses: $showHouses, showAspects: $showAspects, preferredLanguage: $preferredLanguage, enableNotifications: $enableNotifications)';
  }
}

/// @nodoc
abstract mixin class $KundaliSettingsCopyWith<$Res> {
  factory $KundaliSettingsCopyWith(
          KundaliSettings value, $Res Function(KundaliSettings) _then) =
      _$KundaliSettingsCopyWithImpl;
  @useResult
  $Res call(
      {bool showKundali,
      bool allowKundaliMatching,
      bool requireKundaliForMatching,
      bool showDoshas,
      bool showPlanetaryPositions,
      bool showHouses,
      bool showAspects,
      String preferredLanguage,
      bool enableNotifications});
}

/// @nodoc
class _$KundaliSettingsCopyWithImpl<$Res>
    implements $KundaliSettingsCopyWith<$Res> {
  _$KundaliSettingsCopyWithImpl(this._self, this._then);

  final KundaliSettings _self;
  final $Res Function(KundaliSettings) _then;

  /// Create a copy of KundaliSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showKundali = null,
    Object? allowKundaliMatching = null,
    Object? requireKundaliForMatching = null,
    Object? showDoshas = null,
    Object? showPlanetaryPositions = null,
    Object? showHouses = null,
    Object? showAspects = null,
    Object? preferredLanguage = null,
    Object? enableNotifications = null,
  }) {
    return _then(_self.copyWith(
      showKundali: null == showKundali
          ? _self.showKundali
          : showKundali // ignore: cast_nullable_to_non_nullable
              as bool,
      allowKundaliMatching: null == allowKundaliMatching
          ? _self.allowKundaliMatching
          : allowKundaliMatching // ignore: cast_nullable_to_non_nullable
              as bool,
      requireKundaliForMatching: null == requireKundaliForMatching
          ? _self.requireKundaliForMatching
          : requireKundaliForMatching // ignore: cast_nullable_to_non_nullable
              as bool,
      showDoshas: null == showDoshas
          ? _self.showDoshas
          : showDoshas // ignore: cast_nullable_to_non_nullable
              as bool,
      showPlanetaryPositions: null == showPlanetaryPositions
          ? _self.showPlanetaryPositions
          : showPlanetaryPositions // ignore: cast_nullable_to_non_nullable
              as bool,
      showHouses: null == showHouses
          ? _self.showHouses
          : showHouses // ignore: cast_nullable_to_non_nullable
              as bool,
      showAspects: null == showAspects
          ? _self.showAspects
          : showAspects // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredLanguage: null == preferredLanguage
          ? _self.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      enableNotifications: null == enableNotifications
          ? _self.enableNotifications
          : enableNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// Adds pattern-matching-related methods to [KundaliSettings].
extension KundaliSettingsPatterns on KundaliSettings {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_KundaliSettings value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KundaliSettings() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_KundaliSettings value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliSettings():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_KundaliSettings value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliSettings() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            bool showKundali,
            bool allowKundaliMatching,
            bool requireKundaliForMatching,
            bool showDoshas,
            bool showPlanetaryPositions,
            bool showHouses,
            bool showAspects,
            String preferredLanguage,
            bool enableNotifications)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _KundaliSettings() when $default != null:
        return $default(
            _that.showKundali,
            _that.allowKundaliMatching,
            _that.requireKundaliForMatching,
            _that.showDoshas,
            _that.showPlanetaryPositions,
            _that.showHouses,
            _that.showAspects,
            _that.preferredLanguage,
            _that.enableNotifications);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            bool showKundali,
            bool allowKundaliMatching,
            bool requireKundaliForMatching,
            bool showDoshas,
            bool showPlanetaryPositions,
            bool showHouses,
            bool showAspects,
            String preferredLanguage,
            bool enableNotifications)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliSettings():
        return $default(
            _that.showKundali,
            _that.allowKundaliMatching,
            _that.requireKundaliForMatching,
            _that.showDoshas,
            _that.showPlanetaryPositions,
            _that.showHouses,
            _that.showAspects,
            _that.preferredLanguage,
            _that.enableNotifications);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool showKundali,
            bool allowKundaliMatching,
            bool requireKundaliForMatching,
            bool showDoshas,
            bool showPlanetaryPositions,
            bool showHouses,
            bool showAspects,
            String preferredLanguage,
            bool enableNotifications)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _KundaliSettings() when $default != null:
        return $default(
            _that.showKundali,
            _that.allowKundaliMatching,
            _that.requireKundaliForMatching,
            _that.showDoshas,
            _that.showPlanetaryPositions,
            _that.showHouses,
            _that.showAspects,
            _that.preferredLanguage,
            _that.enableNotifications);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _KundaliSettings implements KundaliSettings {
  const _KundaliSettings(
      {required this.showKundali,
      required this.allowKundaliMatching,
      required this.requireKundaliForMatching,
      required this.showDoshas,
      required this.showPlanetaryPositions,
      required this.showHouses,
      required this.showAspects,
      required this.preferredLanguage,
      required this.enableNotifications});
  factory _KundaliSettings.fromJson(Map<String, dynamic> json) =>
      _$KundaliSettingsFromJson(json);

  @override
  final bool showKundali;
  @override
  final bool allowKundaliMatching;
  @override
  final bool requireKundaliForMatching;
  @override
  final bool showDoshas;
  @override
  final bool showPlanetaryPositions;
  @override
  final bool showHouses;
  @override
  final bool showAspects;
  @override
  final String preferredLanguage;
  @override
  final bool enableNotifications;

  /// Create a copy of KundaliSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$KundaliSettingsCopyWith<_KundaliSettings> get copyWith =>
      __$KundaliSettingsCopyWithImpl<_KundaliSettings>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$KundaliSettingsToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _KundaliSettings &&
            (identical(other.showKundali, showKundali) ||
                other.showKundali == showKundali) &&
            (identical(other.allowKundaliMatching, allowKundaliMatching) ||
                other.allowKundaliMatching == allowKundaliMatching) &&
            (identical(other.requireKundaliForMatching,
                    requireKundaliForMatching) ||
                other.requireKundaliForMatching == requireKundaliForMatching) &&
            (identical(other.showDoshas, showDoshas) ||
                other.showDoshas == showDoshas) &&
            (identical(other.showPlanetaryPositions, showPlanetaryPositions) ||
                other.showPlanetaryPositions == showPlanetaryPositions) &&
            (identical(other.showHouses, showHouses) ||
                other.showHouses == showHouses) &&
            (identical(other.showAspects, showAspects) ||
                other.showAspects == showAspects) &&
            (identical(other.preferredLanguage, preferredLanguage) ||
                other.preferredLanguage == preferredLanguage) &&
            (identical(other.enableNotifications, enableNotifications) ||
                other.enableNotifications == enableNotifications));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      showKundali,
      allowKundaliMatching,
      requireKundaliForMatching,
      showDoshas,
      showPlanetaryPositions,
      showHouses,
      showAspects,
      preferredLanguage,
      enableNotifications);

  @override
  String toString() {
    return 'KundaliSettings(showKundali: $showKundali, allowKundaliMatching: $allowKundaliMatching, requireKundaliForMatching: $requireKundaliForMatching, showDoshas: $showDoshas, showPlanetaryPositions: $showPlanetaryPositions, showHouses: $showHouses, showAspects: $showAspects, preferredLanguage: $preferredLanguage, enableNotifications: $enableNotifications)';
  }
}

/// @nodoc
abstract mixin class _$KundaliSettingsCopyWith<$Res>
    implements $KundaliSettingsCopyWith<$Res> {
  factory _$KundaliSettingsCopyWith(
          _KundaliSettings value, $Res Function(_KundaliSettings) _then) =
      __$KundaliSettingsCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool showKundali,
      bool allowKundaliMatching,
      bool requireKundaliForMatching,
      bool showDoshas,
      bool showPlanetaryPositions,
      bool showHouses,
      bool showAspects,
      String preferredLanguage,
      bool enableNotifications});
}

/// @nodoc
class __$KundaliSettingsCopyWithImpl<$Res>
    implements _$KundaliSettingsCopyWith<$Res> {
  __$KundaliSettingsCopyWithImpl(this._self, this._then);

  final _KundaliSettings _self;
  final $Res Function(_KundaliSettings) _then;

  /// Create a copy of KundaliSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? showKundali = null,
    Object? allowKundaliMatching = null,
    Object? requireKundaliForMatching = null,
    Object? showDoshas = null,
    Object? showPlanetaryPositions = null,
    Object? showHouses = null,
    Object? showAspects = null,
    Object? preferredLanguage = null,
    Object? enableNotifications = null,
  }) {
    return _then(_KundaliSettings(
      showKundali: null == showKundali
          ? _self.showKundali
          : showKundali // ignore: cast_nullable_to_non_nullable
              as bool,
      allowKundaliMatching: null == allowKundaliMatching
          ? _self.allowKundaliMatching
          : allowKundaliMatching // ignore: cast_nullable_to_non_nullable
              as bool,
      requireKundaliForMatching: null == requireKundaliForMatching
          ? _self.requireKundaliForMatching
          : requireKundaliForMatching // ignore: cast_nullable_to_non_nullable
              as bool,
      showDoshas: null == showDoshas
          ? _self.showDoshas
          : showDoshas // ignore: cast_nullable_to_non_nullable
              as bool,
      showPlanetaryPositions: null == showPlanetaryPositions
          ? _self.showPlanetaryPositions
          : showPlanetaryPositions // ignore: cast_nullable_to_non_nullable
              as bool,
      showHouses: null == showHouses
          ? _self.showHouses
          : showHouses // ignore: cast_nullable_to_non_nullable
              as bool,
      showAspects: null == showAspects
          ? _self.showAspects
          : showAspects // ignore: cast_nullable_to_non_nullable
              as bool,
      preferredLanguage: null == preferredLanguage
          ? _self.preferredLanguage
          : preferredLanguage // ignore: cast_nullable_to_non_nullable
              as String,
      enableNotifications: null == enableNotifications
          ? _self.enableNotifications
          : enableNotifications // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

// dart format on
