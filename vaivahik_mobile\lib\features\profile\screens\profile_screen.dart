import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../app/theme.dart';
import '../../../core/animations/app_animations.dart';
import '../../../core/responsive/responsive_helper.dart';
import '../../../core/widgets/enhanced_ui_components.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppAnimations.slow,
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppGradients.backgroundGradient,
        ),
        child: ResponsiveContainer(
          child: CustomScrollView(
            slivers: [
              // Enhanced App Bar
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                backgroundColor: Colors.transparent,
                elevation: 0,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: const BoxDecoration(
                      gradient: AppGradients.primaryGradient,
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                    ),
                    child: AppAnimations.cardEntranceAnimation(
                      animation: _animationController,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const SizedBox(height: 40), // Status bar padding
                            // Profile Avatar with Animation
                            Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                gradient: AppGradients.accentGradient,
                                borderRadius: BorderRadius.circular(50),
                                boxShadow: AppShadows.elevatedShadow,
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                  width: 3,
                                ),
                              ),
                              child: const Icon(
                                Icons.person,
                                size: 50,
                                color: Colors.white,
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'Your Profile',
                              style: AppTextStyles.h2.copyWith(color: Colors.white),
                            ),
                            Text(
                              'Manage your matrimony profile',
                              style: AppTextStyles.bodyMedium.copyWith(
                                color: Colors.white.withValues(alpha: 0.9),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.edit, color: Colors.white),
                    onPressed: () {
                      Navigator.of(context).pushNamed('/edit-profile');
                    },
                  ),
                ],
              ),

              // Profile Content
              SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.all(ResponsiveSpacing.md(context)),
                  child: Column(
                    children: [
                      SizedBox(height: ResponsiveSpacing.lg(context)),

                      // Profile Stats Cards
                      AppAnimations.cardEntranceAnimation(
                        animation: _animationController,
                        delay: 1,
                        child: _buildProfileStats(),
                      ),

                      SizedBox(height: ResponsiveSpacing.lg(context)),

                      // Profile Actions
                      AppAnimations.cardEntranceAnimation(
                        animation: _animationController,
                        delay: 2,
                        child: _buildProfileActions(),
                      ),

                      SizedBox(height: ResponsiveSpacing.lg(context)),

                      // Quick Settings
                      AppAnimations.cardEntranceAnimation(
                        animation: _animationController,
                        delay: 3,
                        child: _buildQuickSettings(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileStats() {
    return Row(
      children: [
        Expanded(
          child: EnhancedCard(
            child: Column(
              children: [
                const Text('Profile Views', style: AppTextStyles.labelMedium),
                const SizedBox(height: 8),
                Text('127', style: AppTextStyles.h2.copyWith(color: AppColors.primary)),
              ],
            ),
          ),
        ),
        SizedBox(width: ResponsiveSpacing.sm(context)),
        Expanded(
          child: EnhancedCard(
            child: Column(
              children: [
                const Text('Interests', style: AppTextStyles.labelMedium),
                const SizedBox(height: 8),
                Text('23', style: AppTextStyles.h2.copyWith(color: AppColors.secondary)),
              ],
            ),
          ),
        ),
        SizedBox(width: ResponsiveSpacing.sm(context)),
        Expanded(
          child: EnhancedCard(
            isPremium: true,
            child: Column(
              children: [
                const Text('Matches', style: AppTextStyles.labelMedium),
                const SizedBox(height: 8),
                Text('45', style: AppTextStyles.h2.copyWith(color: AppColors.accent)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProfileActions() {
    return Column(
      children: [
        EnhancedButton(
          text: 'Complete Profile',
          type: ButtonType.primary,
          size: ButtonSize.large,
          isFullWidth: true,
          icon: const Icon(Icons.person_add, size: 20),
          onPressed: () {
            // Navigate to profile completion
          },
        ),
        SizedBox(height: ResponsiveSpacing.md(context)),
        Row(
          children: [
            Expanded(
              child: EnhancedButton(
                text: 'Edit Photos',
                type: ButtonType.secondary,
                size: ButtonSize.medium,
                icon: const Icon(Icons.photo_camera, size: 16),
                onPressed: () {
                  // Navigate to photo management
                },
              ),
            ),
            SizedBox(width: ResponsiveSpacing.sm(context)),
            Expanded(
              child: EnhancedButton(
                text: 'Privacy',
                type: ButtonType.accent,
                size: ButtonSize.medium,
                icon: const Icon(Icons.security, size: 16),
                onPressed: () {
                  // Navigate to privacy settings
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickSettings() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Quick Settings', style: AppTextStyles.h3),
          SizedBox(height: ResponsiveSpacing.md(context)),

          _buildSettingItem(
            icon: Icons.notifications,
            title: 'Notifications',
            subtitle: 'Manage your notification preferences',
            onTap: () {},
          ),

          _buildSettingItem(
            icon: Icons.privacy_tip,
            title: 'Privacy Controls',
            subtitle: 'Control who can see your profile',
            onTap: () {},
          ),

          _buildSettingItem(
            icon: Icons.star,
            title: 'Premium Features',
            subtitle: 'Upgrade to premium membership',
            onTap: () {},
            isPremium: true,
          ),

          _buildSettingItem(
            icon: Icons.help,
            title: 'Help & Support',
            subtitle: 'Get help with your account',
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isPremium = false,
  }) {
    return ListTile(
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          gradient: isPremium ? AppGradients.accentGradient : AppGradients.primaryGradient,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(icon, color: Colors.white, size: 20),
      ),
      title: Text(title, style: AppTextStyles.labelLarge),
      subtitle: Text(subtitle, style: AppTextStyles.bodySmall),
      trailing: isPremium
          ? const Icon(Icons.star, color: AppColors.accent, size: 20)
          : const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }
}
