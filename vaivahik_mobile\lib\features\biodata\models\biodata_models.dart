class BiodataTemplate {
  final String id;
  final String name;
  final String description;
  final BiodataType type;
  final String category;
  final String genderOrientation;
  final String targetAudience;
  final String templateHtml;
  final String templateCss;
  final String designFile;
  final String? previewImage;
  final String? thumbnail;
  final String headerText;
  final String footerText;
  final Map<String, dynamic> defaultSettings;
  final List<String> requiredFields;
  final List<String> optionalFields;
  final bool isActive;
  final bool isPremium;
  final double price;
  final double? originalPrice;
  final int? discountPercent;
  final double? discountedPrice;
  final String currency;
  final int purchaseCount;
  final int downloadCount;
  final double revenue;
  final DateTime createdAt;
  final DateTime updatedAt;

  const BiodataTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.category,
    required this.genderOrientation,
    required this.targetAudience,
    required this.templateHtml,
    required this.templateCss,
    required this.designFile,
    this.previewImage,
    this.thumbnail,
    required this.headerText,
    required this.footerText,
    required this.defaultSettings,
    required this.requiredFields,
    required this.optionalFields,
    required this.isActive,
    required this.isPremium,
    required this.price,
    this.originalPrice,
    this.discountPercent,
    this.discountedPrice,
    required this.currency,
    required this.purchaseCount,
    required this.downloadCount,
    required this.revenue,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BiodataTemplate.fromJson(Map<String, dynamic> json) {
    return BiodataTemplate(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      type: BiodataType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => BiodataType.traditional,
      ),
      category: json['category']?.toString() ?? 'traditional',
      genderOrientation: json['genderOrientation']?.toString() ?? json['targetGender']?.toString() ?? 'male',
      targetAudience: json['targetAudience']?.toString() ?? 'General audience',
      templateHtml: json['templateHtml']?.toString() ?? '',
      templateCss: json['templateCss']?.toString() ?? '',
      designFile: json['designFile']?.toString() ?? '',
      previewImage: json['previewImage']?.toString(),
      thumbnail: json['thumbnail']?.toString(),
      headerText: json['headerText']?.toString() ?? '॥ श्री गणेशाय नमः ॥',
      footerText: json['footerText']?.toString() ?? 'Powered by Vaivahik - The Premier Maratha Matrimony Platform',
      defaultSettings: Map<String, dynamic>.from(json['defaultSettings'] ?? {}),
      requiredFields: List<String>.from(json['requiredFields'] ?? []),
      optionalFields: List<String>.from(json['optionalFields'] ?? []),
      isActive: json['isActive'] ?? true,
      isPremium: json['isPremium'] ?? false,
      price: (json['price'] ?? json['discountedPrice'] ?? 0).toDouble(),
      originalPrice: json['originalPrice']?.toDouble(),
      discountPercent: json['discountPercent']?.toInt(),
      discountedPrice: json['discountedPrice']?.toDouble(),
      currency: json['currency']?.toString() ?? 'INR',
      purchaseCount: json['purchaseCount']?.toInt() ?? 0,
      downloadCount: json['downloadCount']?.toInt() ?? 0,
      revenue: (json['revenue'] ?? 0).toDouble(),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(json['updatedAt']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'category': category,
      'genderOrientation': genderOrientation,
      'targetAudience': targetAudience,
      'templateHtml': templateHtml,
      'templateCss': templateCss,
      'designFile': designFile,
      'previewImage': previewImage,
      'thumbnail': thumbnail,
      'headerText': headerText,
      'footerText': footerText,
      'defaultSettings': defaultSettings,
      'requiredFields': requiredFields,
      'optionalFields': optionalFields,
      'isActive': isActive,
      'isPremium': isPremium,
      'price': price,
      'originalPrice': originalPrice,
      'discountPercent': discountPercent,
      'discountedPrice': discountedPrice,
      'currency': currency,
      'purchaseCount': purchaseCount,
      'downloadCount': downloadCount,
      'revenue': revenue,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}

enum BiodataType {
  traditional,
  modern,
  elegant,
  classic,
  premium,
  royal,
  minimalist,
  simple,
  detailed
}

class GeneratedBiodata {
  final String id;
  final String userId;
  final String templateId;
  final BiodataTemplate template;
  final Map<String, dynamic> userData;
  final BiodataSettings settings;
  final String generatedHtml;
  final String? pdfUrl;
  final BiodataStatus status;
  final DateTime createdAt;
  final DateTime? lastModified;
  final int downloadCount;
  final int shareCount;

  const GeneratedBiodata({
    required this.id,
    required this.userId,
    required this.templateId,
    required this.template,
    required this.userData,
    required this.settings,
    required this.generatedHtml,
    this.pdfUrl,
    required this.status,
    required this.createdAt,
    this.lastModified,
    required this.downloadCount,
    required this.shareCount,
  });

  factory GeneratedBiodata.fromJson(Map<String, dynamic> json) {
    return GeneratedBiodata(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      templateId: json['templateId']?.toString() ?? '',
      template: BiodataTemplate.fromJson(json['template'] ?? {}),
      userData: Map<String, dynamic>.from(json['userData'] ?? {}),
      settings: BiodataSettings.fromJson(json['settings'] ?? {}),
      generatedHtml: json['generatedHtml']?.toString() ?? '',
      pdfUrl: json['pdfUrl']?.toString(),
      status: BiodataStatus.values.firstWhere(
        (e) => e.name == json['status']?.toString(),
        orElse: () => BiodataStatus.draft,
      ),
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      lastModified: json['lastModified'] != null 
          ? DateTime.tryParse(json['lastModified'].toString())
          : null,
      downloadCount: json['downloadCount']?.toInt() ?? 0,
      shareCount: json['shareCount']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'templateId': templateId,
      'template': template.toJson(),
      'userData': userData,
      'settings': settings.toJson(),
      'generatedHtml': generatedHtml,
      'pdfUrl': pdfUrl,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified?.toIso8601String(),
      'downloadCount': downloadCount,
      'shareCount': shareCount,
    };
  }
}

enum BiodataStatus {
  draft,
  generated,
  published,
  archived
}

class BiodataSettings {
  final bool includePhoto;
  final bool includeKundali;
  final bool includeContactDetails;
  final bool includeFamilyDetails;
  final bool includeEducationDetails;
  final bool includeCareerDetails;
  final bool includeExpectations;
  final bool includeHobbies;
  final String language;
  final String fontFamily;
  final int fontSize;
  final String colorScheme;
  final bool watermark;
  final String? customWatermark;
  final Map<String, bool> sectionVisibility;
  final Map<String, int> sectionOrder;

  const BiodataSettings({
    required this.includePhoto,
    required this.includeKundali,
    required this.includeContactDetails,
    required this.includeFamilyDetails,
    required this.includeEducationDetails,
    required this.includeCareerDetails,
    required this.includeExpectations,
    required this.includeHobbies,
    required this.language,
    required this.fontFamily,
    required this.fontSize,
    required this.colorScheme,
    required this.watermark,
    this.customWatermark,
    required this.sectionVisibility,
    required this.sectionOrder,
  });

  factory BiodataSettings.fromJson(Map<String, dynamic> json) {
    return BiodataSettings(
      includePhoto: json['includePhoto'] ?? true,
      includeKundali: json['includeKundali'] ?? true,
      includeContactDetails: json['includeContactDetails'] ?? true,
      includeFamilyDetails: json['includeFamilyDetails'] ?? true,
      includeEducationDetails: json['includeEducationDetails'] ?? true,
      includeCareerDetails: json['includeCareerDetails'] ?? true,
      includeExpectations: json['includeExpectations'] ?? true,
      includeHobbies: json['includeHobbies'] ?? true,
      language: json['language']?.toString() ?? 'en',
      fontFamily: json['fontFamily']?.toString() ?? 'Arial',
      fontSize: json['fontSize']?.toInt() ?? 12,
      colorScheme: json['colorScheme']?.toString() ?? 'default',
      watermark: json['watermark'] ?? true,
      customWatermark: json['customWatermark']?.toString(),
      sectionVisibility: Map<String, bool>.from(json['sectionVisibility'] ?? {}),
      sectionOrder: Map<String, int>.from(json['sectionOrder'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'includePhoto': includePhoto,
      'includeKundali': includeKundali,
      'includeContactDetails': includeContactDetails,
      'includeFamilyDetails': includeFamilyDetails,
      'includeEducationDetails': includeEducationDetails,
      'includeCareerDetails': includeCareerDetails,
      'includeExpectations': includeExpectations,
      'includeHobbies': includeHobbies,
      'language': language,
      'fontFamily': fontFamily,
      'fontSize': fontSize,
      'colorScheme': colorScheme,
      'watermark': watermark,
      'customWatermark': customWatermark,
      'sectionVisibility': sectionVisibility,
      'sectionOrder': sectionOrder,
    };
  }

  BiodataSettings copyWith({
    bool? includePhoto,
    bool? includeKundali,
    bool? includeContactDetails,
    bool? includeFamilyDetails,
    bool? includeEducationDetails,
    bool? includeCareerDetails,
    bool? includeExpectations,
    bool? includeHobbies,
    String? language,
    String? fontFamily,
    int? fontSize,
    String? colorScheme,
    bool? watermark,
    String? customWatermark,
    Map<String, bool>? sectionVisibility,
    Map<String, int>? sectionOrder,
  }) {
    return BiodataSettings(
      includePhoto: includePhoto ?? this.includePhoto,
      includeKundali: includeKundali ?? this.includeKundali,
      includeContactDetails: includeContactDetails ?? this.includeContactDetails,
      includeFamilyDetails: includeFamilyDetails ?? this.includeFamilyDetails,
      includeEducationDetails: includeEducationDetails ?? this.includeEducationDetails,
      includeCareerDetails: includeCareerDetails ?? this.includeCareerDetails,
      includeExpectations: includeExpectations ?? this.includeExpectations,
      includeHobbies: includeHobbies ?? this.includeHobbies,
      language: language ?? this.language,
      fontFamily: fontFamily ?? this.fontFamily,
      fontSize: fontSize ?? this.fontSize,
      colorScheme: colorScheme ?? this.colorScheme,
      watermark: watermark ?? this.watermark,
      customWatermark: customWatermark ?? this.customWatermark,
      sectionVisibility: sectionVisibility ?? this.sectionVisibility,
      sectionOrder: sectionOrder ?? this.sectionOrder,
    );
  }
}

class BiodataField {
  final String key;
  final String label;
  final BiodataFieldType type;
  final bool isRequired;
  final bool isVisible;
  final int order;
  final String? defaultValue;
  final List<String>? options;
  final Map<String, dynamic>? validation;
  final String? section;

  const BiodataField({
    required this.key,
    required this.label,
    required this.type,
    required this.isRequired,
    required this.isVisible,
    required this.order,
    this.defaultValue,
    this.options,
    this.validation,
    this.section,
  });

  factory BiodataField.fromJson(Map<String, dynamic> json) {
    return BiodataField(
      key: json['key']?.toString() ?? '',
      label: json['label']?.toString() ?? '',
      type: BiodataFieldType.values.firstWhere(
        (e) => e.name == json['type']?.toString(),
        orElse: () => BiodataFieldType.text,
      ),
      isRequired: json['isRequired'] ?? false,
      isVisible: json['isVisible'] ?? true,
      order: json['order']?.toInt() ?? 0,
      defaultValue: json['defaultValue']?.toString(),
      options: json['options'] != null ? List<String>.from(json['options']) : null,
      validation: json['validation'] as Map<String, dynamic>?,
      section: json['section']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'label': label,
      'type': type.name,
      'isRequired': isRequired,
      'isVisible': isVisible,
      'order': order,
      'defaultValue': defaultValue,
      'options': options,
      'validation': validation,
      'section': section,
    };
  }
}

enum BiodataFieldType {
  text,
  number,
  email,
  phone,
  date,
  dropdown,
  multiselect,
  textarea,
  image,
  boolean
}

class BiodataShare {
  final String id;
  final String biodataId;
  final String sharedBy;
  final String? sharedWith;
  final ShareMethod method;
  final String? shareUrl;
  final DateTime sharedAt;
  final DateTime? expiresAt;
  final bool isActive;
  final int viewCount;
  final DateTime? lastViewed;

  const BiodataShare({
    required this.id,
    required this.biodataId,
    required this.sharedBy,
    this.sharedWith,
    required this.method,
    this.shareUrl,
    required this.sharedAt,
    this.expiresAt,
    required this.isActive,
    required this.viewCount,
    this.lastViewed,
  });

  factory BiodataShare.fromJson(Map<String, dynamic> json) {
    return BiodataShare(
      id: json['id']?.toString() ?? '',
      biodataId: json['biodataId']?.toString() ?? '',
      sharedBy: json['sharedBy']?.toString() ?? '',
      sharedWith: json['sharedWith']?.toString(),
      method: ShareMethod.values.firstWhere(
        (e) => e.name == json['method']?.toString(),
        orElse: () => ShareMethod.link,
      ),
      shareUrl: json['shareUrl']?.toString(),
      sharedAt: DateTime.tryParse(json['sharedAt']?.toString() ?? '') ?? DateTime.now(),
      expiresAt: json['expiresAt'] != null 
          ? DateTime.tryParse(json['expiresAt'].toString())
          : null,
      isActive: json['isActive'] ?? true,
      viewCount: json['viewCount']?.toInt() ?? 0,
      lastViewed: json['lastViewed'] != null 
          ? DateTime.tryParse(json['lastViewed'].toString())
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'biodataId': biodataId,
      'sharedBy': sharedBy,
      'sharedWith': sharedWith,
      'method': method.name,
      'shareUrl': shareUrl,
      'sharedAt': sharedAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'isActive': isActive,
      'viewCount': viewCount,
      'lastViewed': lastViewed?.toIso8601String(),
    };
  }

  bool get isExpired {
    return expiresAt != null && expiresAt!.isBefore(DateTime.now());
  }
}

enum ShareMethod {
  link,
  email,
  whatsapp,
  telegram,
  download
}

class BiodataAnalytics {
  final String biodataId;
  final int totalViews;
  final int uniqueViews;
  final int downloads;
  final int shares;
  final Map<String, int> viewsBySource;
  final Map<String, int> viewsByDevice;
  final Map<String, int> viewsByLocation;
  final DateTime? lastViewed;
  final double averageViewDuration;
  final List<String> popularSections;

  const BiodataAnalytics({
    required this.biodataId,
    required this.totalViews,
    required this.uniqueViews,
    required this.downloads,
    required this.shares,
    required this.viewsBySource,
    required this.viewsByDevice,
    required this.viewsByLocation,
    this.lastViewed,
    required this.averageViewDuration,
    required this.popularSections,
  });

  factory BiodataAnalytics.fromJson(Map<String, dynamic> json) {
    return BiodataAnalytics(
      biodataId: json['biodataId']?.toString() ?? '',
      totalViews: json['totalViews']?.toInt() ?? 0,
      uniqueViews: json['uniqueViews']?.toInt() ?? 0,
      downloads: json['downloads']?.toInt() ?? 0,
      shares: json['shares']?.toInt() ?? 0,
      viewsBySource: Map<String, int>.from(json['viewsBySource'] ?? {}),
      viewsByDevice: Map<String, int>.from(json['viewsByDevice'] ?? {}),
      viewsByLocation: Map<String, int>.from(json['viewsByLocation'] ?? {}),
      lastViewed: json['lastViewed'] != null 
          ? DateTime.tryParse(json['lastViewed'].toString())
          : null,
      averageViewDuration: json['averageViewDuration']?.toDouble() ?? 0.0,
      popularSections: List<String>.from(json['popularSections'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'biodataId': biodataId,
      'totalViews': totalViews,
      'uniqueViews': uniqueViews,
      'downloads': downloads,
      'shares': shares,
      'viewsBySource': viewsBySource,
      'viewsByDevice': viewsByDevice,
      'viewsByLocation': viewsByLocation,
      'lastViewed': lastViewed?.toIso8601String(),
      'averageViewDuration': averageViewDuration,
      'popularSections': popularSections,
    };
  }

  double get engagementRate {
    if (totalViews == 0) return 0.0;
    return ((downloads + shares) / totalViews) * 100;
  }
}
