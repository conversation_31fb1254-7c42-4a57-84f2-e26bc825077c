#!/usr/bin/env node

/**
 * Test Kundali Matching System
 * Tests the comprehensive kundali matching API with various birth details
 */

require('dotenv').config();
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8000/api';

// Test data for kundali matching
const testUsers = [
  {
    id: 'test-user-1',
    birthDate: '1995-03-15',
    birthTime: '10:30',
    birthPlace: 'Mumbai, Maharashtra, India',
    profile: {
      fullName: '<PERSON><PERSON>',
      gender: 'male',
      age: 28,
      height: '5.8',
      education: 'Software Engineer',
      profession: 'IT Professional'
    }
  },
  {
    id: 'test-user-2',
    birthDate: '1997-07-22',
    birthTime: '14:45',
    birthPlace: 'Pune, Maharashtra, India',
    profile: {
      fullName: '<PERSON><PERSON>',
      gender: 'female',
      age: 26,
      height: '5.4',
      education: 'MBA Finance',
      profession: 'Financial Analyst'
    }
  },
  {
    id: 'test-user-3',
    birthDate: '1994-11-08',
    birthTime: '08:15',
    birthPlace: 'Nashik, Maharashtra, India',
    profile: {
      fullName: '<PERSON><PERSON><PERSON>',
      gender: 'male',
      age: 29,
      height: '5.10',
      education: 'CA',
      profession: 'Chartered Accountant'
    }
  },
  {
    id: 'test-user-4',
    birthDate: '1996-12-03',
    birthTime: '18:20',
    birthPlace: 'Kolhapur, Maharashtra, India',
    profile: {
      fullName: 'Sneha Kulkarni',
      gender: 'female',
      age: 27,
      height: '5.5',
      education: 'Doctor',
      profession: 'Medical Professional'
    }
  }
];

async function testKundaliMatching() {
  console.log('🕉️  Testing Kundali Matching System...\n');
  
  try {
    // Test 1: Basic Kundali Matching
    console.log('1️⃣ Testing Basic Kundali Matching...');
    
    const basicMatch = await axios.post(`${API_BASE_URL}/premium/kundli-matching`, {
      user1: testUsers[0],
      user2: testUsers[1],
      options: {
        includeMLScore: false,
        includeDetailedAnalysis: false,
        includeRemedies: false,
        includeAuspiciousDates: false
      }
    });
    
    console.log(`✅ Basic matching successful`);
    console.log(`   Score: ${basicMatch.data.overallScore}/36 Gunas`);
    console.log(`   Compatibility: ${basicMatch.data.compatibility}`);
    console.log(`   Recommendation: ${basicMatch.data.recommendation}\n`);
    
    // Test 2: Advanced Kundali Matching with ML
    console.log('2️⃣ Testing Advanced Kundali Matching with ML...');
    
    const advancedMatch = await axios.post(`${API_BASE_URL}/premium/kundli-matching`, {
      user1: testUsers[2],
      user2: testUsers[3],
      options: {
        includeMLScore: true,
        includeDetailedAnalysis: true,
        includeRemedies: true,
        includeAuspiciousDates: true
      }
    });
    
    console.log(`✅ Advanced matching successful`);
    console.log(`   Traditional Score: ${advancedMatch.data.overallScore}/36 Gunas`);
    console.log(`   ML Score: ${advancedMatch.data.mlCompatibilityScore}%`);
    console.log(`   Combined Score: ${advancedMatch.data.combinedScore}%`);
    console.log(`   Compatibility: ${advancedMatch.data.compatibility}`);
    console.log(`   Detailed Analysis: ${advancedMatch.data.detailedAnalysis ? 'Included' : 'Not included'}`);
    console.log(`   Remedies: ${advancedMatch.data.remedies ? 'Included' : 'Not included'}`);
    console.log(`   Auspicious Dates: ${advancedMatch.data.auspiciousDates ? 'Included' : 'Not included'}\n`);
    
    // Test 3: Multiple Matching Combinations
    console.log('3️⃣ Testing Multiple Matching Combinations...');
    
    const combinations = [
      [testUsers[0], testUsers[2]], // Male-Male (should handle gracefully)
      [testUsers[1], testUsers[3]], // Female-Female (should handle gracefully)
      [testUsers[0], testUsers[3]], // Different combination
      [testUsers[1], testUsers[2]]  // Different combination
    ];
    
    for (let i = 0; i < combinations.length; i++) {
      try {
        const [user1, user2] = combinations[i];
        const result = await axios.post(`${API_BASE_URL}/premium/kundli-matching`, {
          user1,
          user2,
          options: {
            includeMLScore: true,
            includeDetailedAnalysis: false,
            includeRemedies: false,
            includeAuspiciousDates: false
          }
        });
        
        console.log(`   Combination ${i + 1}: ${result.data.overallScore}/36 Gunas (${result.data.compatibility})`);
      } catch (error) {
        console.log(`   Combination ${i + 1}: Error - ${error.response?.data?.message || error.message}`);
      }
    }
    
    console.log('\n4️⃣ Testing Error Handling...');
    
    // Test 4: Invalid Data Handling
    try {
      await axios.post(`${API_BASE_URL}/premium/kundli-matching`, {
        user1: { ...testUsers[0], birthDate: 'invalid-date' },
        user2: testUsers[1]
      });
    } catch (error) {
      console.log(`✅ Invalid date handling: ${error.response?.status} - ${error.response?.data?.message}`);
    }
    
    // Test 5: Missing Data Handling
    try {
      await axios.post(`${API_BASE_URL}/premium/kundli-matching`, {
        user1: { ...testUsers[0], birthTime: null },
        user2: testUsers[1]
      });
    } catch (error) {
      console.log(`✅ Missing birth time handling: ${error.response?.status} - ${error.response?.data?.message}`);
    }
    
    console.log('\n🎉 Kundali Matching System is working correctly!');
    console.log('\n📊 Test Summary:');
    console.log('✅ Basic matching functionality');
    console.log('✅ Advanced ML integration');
    console.log('✅ Multiple user combinations');
    console.log('✅ Error handling and validation');
    console.log('✅ API response structure');
    
    return true;
    
  } catch (error) {
    console.log('\n❌ Kundali Matching test failed:');
    console.log(`Error: ${error.message}`);
    
    if (error.response) {
      console.log(`Status: ${error.response.status}`);
      console.log(`Response: ${JSON.stringify(error.response.data, null, 2)}`);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection Issues:');
      console.log('1. Make sure backend server is running on port 8000');
      console.log('2. Check if API endpoint exists');
      console.log('3. Verify database connection');
    }
    
    return false;
  }
}

// Run the test
testKundaliMatching().then(success => {
  if (success) {
    console.log('\n🕉️  Kundali Matching System is production-ready!');
    console.log('\n📝 Next steps:');
    console.log('1. Integrate with user authentication');
    console.log('2. Add premium feature gating');
    console.log('3. Implement caching for performance');
    console.log('4. Add detailed logging and analytics');
  } else {
    console.log('\n❌ Kundali Matching System needs fixes.');
  }
  
  process.exit(success ? 0 : 1);
});
