/// 🎁 Referral System Models
/// Using existing website backend API structure
library;

class ReferralProgram {
  final String id;
  final String name;
  final String? description;
  final String status;
  final DateTime startDate;
  final DateTime? endDate;
  final String referrerRewardType;
  final double referrerRewardAmount;
  final String refereeRewardType;
  final double refereeRewardAmount;
  final int? maxReferralsPerUser;
  final String conversionRequirement;
  final String? termsAndConditions;

  ReferralProgram({
    required this.id,
    required this.name,
    this.description,
    required this.status,
    required this.startDate,
    this.endDate,
    required this.referrerRewardType,
    required this.referrerRewardAmount,
    required this.refereeRewardType,
    required this.refereeRewardAmount,
    this.maxReferralsPerUser,
    required this.conversionRequirement,
    this.termsAndConditions,
  });

  factory ReferralProgram.fromJson(Map<String, dynamic> json) {
    return ReferralProgram(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      status: json['status'] ?? 'active',
      startDate: DateTime.parse(json['startDate'] ?? DateTime.now().toIso8601String()),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      referrerRewardType: json['referrerRewardType'] ?? 'cash',
      referrerRewardAmount: (json['referrerRewardAmount'] ?? 0).toDouble(),
      refereeRewardType: json['refereeRewardType'] ?? 'cash',
      refereeRewardAmount: (json['refereeRewardAmount'] ?? 0).toDouble(),
      maxReferralsPerUser: json['maxReferralsPerUser'],
      conversionRequirement: json['conversionRequirement'] ?? 'none',
      termsAndConditions: json['termsAndConditions'],
    );
  }
}

class ReferralData {
  final String referralCode;
  final String referralLink;
  final ReferralProgram program;
  final bool canRefer;
  final int? remainingReferrals;
  final List<ReferralHistory> referralHistory;
  final double totalRewardsEarned;
  final ReferralStats stats;

  ReferralData({
    required this.referralCode,
    required this.referralLink,
    required this.program,
    required this.canRefer,
    this.remainingReferrals,
    required this.referralHistory,
    required this.totalRewardsEarned,
    required this.stats,
  });

  factory ReferralData.fromJson(Map<String, dynamic> json) {
    return ReferralData(
      referralCode: json['referralCode'] ?? '',
      referralLink: json['referralLink'] ?? '',
      program: ReferralProgram.fromJson(json['program'] ?? {}),
      canRefer: json['canRefer'] ?? true,
      remainingReferrals: json['remainingReferrals'],
      referralHistory: (json['referralHistory'] as List? ?? [])
          .map((item) => ReferralHistory.fromJson(item))
          .toList(),
      totalRewardsEarned: (json['totalRewardsEarned'] ?? 0).toDouble(),
      stats: ReferralStats.fromJson(json['stats'] ?? {}),
    );
  }
}

class ReferralHistory {
  final String id;
  final DateTime date;
  final String status;
  final String rewardStatus;
  final String programName;
  final String rewardType;
  final double rewardAmount;

  ReferralHistory({
    required this.id,
    required this.date,
    required this.status,
    required this.rewardStatus,
    required this.programName,
    required this.rewardType,
    required this.rewardAmount,
  });

  factory ReferralHistory.fromJson(Map<String, dynamic> json) {
    return ReferralHistory(
      id: json['id'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      status: json['status'] ?? 'pending',
      rewardStatus: json['rewardStatus'] ?? 'pending',
      programName: json['programName'] ?? '',
      rewardType: json['rewardType'] ?? 'cash',
      rewardAmount: (json['rewardAmount'] ?? 0).toDouble(),
    );
  }
}

class ReferralStats {
  final int totalReferrals;
  final int successfulReferrals;
  final int pendingReferrals;
  final double totalEarnings;

  ReferralStats({
    required this.totalReferrals,
    required this.successfulReferrals,
    required this.pendingReferrals,
    required this.totalEarnings,
  });

  factory ReferralStats.fromJson(Map<String, dynamic> json) {
    return ReferralStats(
      totalReferrals: json['totalReferrals'] ?? 0,
      successfulReferrals: json['successfulReferrals'] ?? 0,
      pendingReferrals: json['pendingReferrals'] ?? 0,
      totalEarnings: (json['totalEarnings'] ?? 0).toDouble(),
    );
  }
}

class ReferralInvite {
  final String? email;
  final String? phone;
  final String? message;

  ReferralInvite({
    this.email,
    this.phone,
    this.message,
  });

  Map<String, dynamic> toJson() {
    return {
      if (email != null) 'email': email,
      if (phone != null) 'phone': phone,
      if (message != null) 'message': message,
    };
  }
}

class ReferralReward {
  final String id;
  final String rewardType;
  final double rewardAmount;
  final String status;
  final DateTime createdAt;
  final Map<String, dynamic>? transactionDetails;

  ReferralReward({
    required this.id,
    required this.rewardType,
    required this.rewardAmount,
    required this.status,
    required this.createdAt,
    this.transactionDetails,
  });

  factory ReferralReward.fromJson(Map<String, dynamic> json) {
    return ReferralReward(
      id: json['id'] ?? '',
      rewardType: json['rewardType'] ?? 'cash',
      rewardAmount: (json['rewardAmount'] ?? 0).toDouble(),
      status: json['status'] ?? 'pending',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      transactionDetails: json['transactionDetails'],
    );
  }
}

// Enum for reward types
enum RewardType {
  cash,
  subscriptionDays,
  premiumFeatures;

  String get displayName {
    switch (this) {
      case RewardType.cash:
        return 'Cash Reward';
      case RewardType.subscriptionDays:
        return 'Premium Days';
      case RewardType.premiumFeatures:
        return 'Premium Features';
    }
  }

  String get icon {
    switch (this) {
      case RewardType.cash:
        return '💰';
      case RewardType.subscriptionDays:
        return '⭐';
      case RewardType.premiumFeatures:
        return '🎁';
    }
  }
}

// Enum for referral status
enum ReferralStatus {
  pending,
  completed,
  rewarded;

  String get displayName {
    switch (this) {
      case ReferralStatus.pending:
        return 'Pending';
      case ReferralStatus.completed:
        return 'Completed';
      case ReferralStatus.rewarded:
        return 'Rewarded';
    }
  }

  String get description {
    switch (this) {
      case ReferralStatus.pending:
        return 'Waiting for friend to join';
      case ReferralStatus.completed:
        return 'Friend joined successfully';
      case ReferralStatus.rewarded:
        return 'Reward processed';
    }
  }
}
