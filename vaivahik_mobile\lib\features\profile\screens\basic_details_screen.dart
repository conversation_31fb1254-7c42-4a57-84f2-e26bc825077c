import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/widgets/premium_widgets.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../core/api/api_client.dart';
import '../../../app/theme.dart';

class BasicDetailsScreen extends StatefulWidget {
  const BasicDetailsScreen({super.key});

  @override
  State<BasicDetailsScreen> createState() => _BasicDetailsScreenState();
}

class _BasicDetailsScreenState extends State<BasicDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _fullNameController = TextEditingController();
  final _birthPlaceController = TextEditingController();
  
  String? selectedGender;
  String? selectedProfileFor;
  String? selectedHeight;
  DateTime? selectedDate;
  TimeOfDay? selectedBirthTime;
  
  bool isLoading = false;

  final List<String> genderOptions = ['Male', 'Female'];
  final List<String> profileForOptions = [
    'Self',
    'Son',
    'Daughter',
    'Brother',
    'Sister',
    'Friend',
    'Relative'
  ];
  
  final List<String> heightOptions = [
    '4\'6" - 137 cm',
    '4\'7" - 140 cm',
    '4\'8" - 142 cm',
    '4\'9" - 145 cm',
    '4\'10" - 147 cm',
    '4\'11" - 150 cm',
    '5\'0" - 152 cm',
    '5\'1" - 155 cm',
    '5\'2" - 157 cm',
    '5\'3" - 160 cm',
    '5\'4" - 163 cm',
    '5\'5" - 165 cm',
    '5\'6" - 168 cm',
    '5\'7" - 170 cm',
    '5\'8" - 173 cm',
    '5\'9" - 175 cm',
    '5\'10" - 178 cm',
    '5\'11" - 180 cm',
    '6\'0" - 183 cm',
    '6\'1" - 185 cm',
    '6\'2" - 188 cm',
    '6\'3" - 191 cm',
    '6\'4" - 193 cm',
    '6\'5" - 196 cm',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Basic Details',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              GlassmorphicCard(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.person_outline,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Basic Information',
                              style: TextStyle(
                                color: AppTheme.textColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Tell us about yourself',
                              style: TextStyle(
                                color: AppTheme.textColor.withValues(alpha: 0.7),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
              
              const SizedBox(height: 24),
              
              // Form Fields
              _buildFormSection([
                CustomTextField(
                  controller: _fullNameController,
                  label: 'Full Name',
                  hint: 'Enter your full name',
                  prefixIcon: const Icon(Icons.person),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your full name';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdownField(
                  label: 'Profile For',
                  hint: 'Select profile for',
                  value: selectedProfileFor,
                  items: profileForOptions,
                  onChanged: (value) => setState(() => selectedProfileFor = value),
                  prefixIcon: Icons.family_restroom,
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdownField(
                  label: 'Gender',
                  hint: 'Select gender',
                  value: selectedGender,
                  items: genderOptions,
                  onChanged: (value) => setState(() => selectedGender = value),
                  prefixIcon: Icons.wc,
                ),
                
                const SizedBox(height: 16),
                
                _buildDateField(),
                
                const SizedBox(height: 16),
                
                _buildTimeField(),
                
                const SizedBox(height: 16),
                
                CustomTextField(
                  controller: _birthPlaceController,
                  label: 'Birth Place',
                  hint: 'Enter your birth place',
                  prefixIcon: const Icon(Icons.location_on),
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdownField(
                  label: 'Height',
                  hint: 'Select your height',
                  value: selectedHeight,
                  items: heightOptions,
                  onChanged: (value) => setState(() => selectedHeight = value),
                  prefixIcon: Icons.height,
                ),
              ]),
              
              const SizedBox(height: 32),
              
              // Save Button
              PremiumGradientButton(
                text: isLoading ? 'Saving...' : 'Save & Continue',
                onPressed: isLoading ? null : _saveBasicDetails,
                width: double.infinity,
              ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormSection(List<Widget> children) {
    return GlassmorphicCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: children,
        ),
      ),
    ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData prefixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.cardColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            hint: Text(
              hint,
              style: TextStyle(
                color: AppTheme.textColor.withValues(alpha: 0.5),
              ),
            ),
            decoration: InputDecoration(
              prefixIcon: Icon(prefixIcon, color: AppTheme.primaryColor),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            dropdownColor: AppTheme.cardColor,
            style: TextStyle(color: AppTheme.textColor),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date of Birth',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppTheme.cardColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                Text(
                  selectedDate != null
                      ? '${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}'
                      : 'Select date of birth',
                  style: TextStyle(
                    color: selectedDate != null 
                        ? AppTheme.textColor 
                        : AppTheme.textColor.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Birth Time (Optional)',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: _selectTime,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: AppTheme.cardColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.primaryColor.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.access_time, color: AppTheme.primaryColor),
                const SizedBox(width: 12),
                Text(
                  selectedBirthTime != null
                      ? selectedBirthTime!.format(context)
                      : 'Select birth time',
                  style: TextStyle(
                    color: selectedBirthTime != null 
                        ? AppTheme.textColor 
                        : AppTheme.textColor.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime(1950),
      lastDate: DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: AppTheme.primaryColor,
              surface: AppTheme.cardColor,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedBirthTime ?? TimeOfDay.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.dark(
              primary: AppTheme.primaryColor,
              surface: AppTheme.cardColor,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedBirthTime) {
      setState(() {
        selectedBirthTime = picked;
      });
    }
  }

  Future<void> _saveBasicDetails() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (selectedGender == null || selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please fill all required fields'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      // Real API call to save basic details
      final apiClient = ApiClient();
      final response = await apiClient.post('/profile/basic-details', {
        'fullName': _fullNameController.text,
        'gender': selectedGender,
        'profileFor': selectedProfileFor,
        'dateOfBirth': selectedDate?.toIso8601String(),
        'birthTime': selectedBirthTime?.format(context),
        'birthPlace': _birthPlaceController.text,
        'height': selectedHeight,
      });

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Failed to save basic details');
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Basic details saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _birthPlaceController.dispose();
    super.dispose();
  }
}
