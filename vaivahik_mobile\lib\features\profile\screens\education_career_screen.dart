import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/widgets/premium_widgets.dart';
import '../../../core/widgets/custom_text_field.dart';
import '../../../app/theme.dart';

class EducationCareerScreen extends StatefulWidget {
  const EducationCareerScreen({super.key});

  @override
  State<EducationCareerScreen> createState() => _EducationCareerScreenState();
}

class _EducationCareerScreenState extends State<EducationCareerScreen> {
  final _formKey = GlobalKey<FormState>();
  final _occupationController = TextEditingController();
  final _workLocationController = TextEditingController();
  
  String? selectedEducation;
  String? selectedIncomeRange;
  
  bool isLoading = false;

  final List<String> educationOptions = [
    'High School',
    'Diploma',
    'Bachelor\'s Degree',
    'Master\'s Degree',
    'PhD',
    'Professional Degree',
    'Trade School',
    'Other',
  ];
  
  final List<String> incomeRangeOptions = [
    'Below ₹2 Lakh',
    '₹2-5 Lakh',
    '₹5-10 Lakh',
    '₹10-15 Lakh',
    '₹15-25 Lakh',
    '₹25-50 Lakh',
    '₹50 Lakh - 1 Crore',
    'Above ₹1 Crore',
    'Prefer not to say',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Education & Career',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              GlassmorphicCard(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: AppTheme.primaryGradient,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.school_outlined,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Professional Details',
                              style: TextStyle(
                                color: AppTheme.textColor,
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Your education and career information',
                              style: TextStyle(
                                color: AppTheme.textColor.withValues(alpha: 0.7),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
              
              const SizedBox(height: 24),
              
              // Education Section
              _buildFormSection(
                title: 'Education',
                icon: Icons.school,
                children: [
                  _buildDropdownField(
                    label: 'Highest Education',
                    hint: 'Select your education level',
                    value: selectedEducation,
                    items: educationOptions,
                    onChanged: (value) => setState(() => selectedEducation = value),
                    prefixIcon: Icons.school,
                  ),
                ],
              ),
              
              const SizedBox(height: 20),
              
              // Career Section
              _buildFormSection(
                title: 'Career',
                icon: Icons.work,
                children: [
                  CustomTextField(
                    controller: _occupationController,
                    label: 'Occupation',
                    hint: 'Enter your occupation/job title',
                    prefixIcon: const Icon(Icons.work),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter your occupation';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  CustomTextField(
                    controller: _workLocationController,
                    label: 'Work Location',
                    hint: 'Enter your work location',
                    prefixIcon: const Icon(Icons.location_city),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildDropdownField(
                    label: 'Annual Income',
                    hint: 'Select your income range',
                    value: selectedIncomeRange,
                    items: incomeRangeOptions,
                    onChanged: (value) => setState(() => selectedIncomeRange = value),
                    prefixIcon: Icons.currency_rupee,
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
              
              // Save Button
              PremiumGradientButton(
                text: isLoading ? 'Saving...' : 'Save & Continue',
                onPressed: isLoading ? null : _saveEducationCareer,
                width: double.infinity,
              ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return GlassmorphicCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData prefixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.cardColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            hint: Text(
              hint,
              style: TextStyle(
                color: AppTheme.textColor.withValues(alpha: 0.5),
              ),
            ),
            decoration: InputDecoration(
              prefixIcon: Icon(prefixIcon, color: AppTheme.primaryColor),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            dropdownColor: AppTheme.cardColor,
            style: TextStyle(color: AppTheme.textColor),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Future<void> _saveEducationCareer() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      // Implement API call to save education and career details
      await _saveEducationCareerToAPI();
      await Future.delayed(const Duration(seconds: 2)); // Mock API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Education & career details saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _saveEducationCareerToAPI() async {
    // Simulate API call to save education and career information
    print('Saving education and career information to API...');
    print('Education: $selectedEducation');
    print('Occupation: ${_occupationController.text}');
    print('Work Location: ${_workLocationController.text}');
    print('Annual Income: $selectedIncomeRange');
    // In a real implementation, this would make HTTP request to backend
    await Future.delayed(const Duration(milliseconds: 500));
    print('Education and career information saved successfully');
  }

  @override
  void dispose() {
    _occupationController.dispose();
    _workLocationController.dispose();
    super.dispose();
  }
}
