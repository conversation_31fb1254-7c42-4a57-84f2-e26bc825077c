import '../api/api_client.dart';
import '../models/user_model.dart';

// Feature access levels matching website's middleware
enum AccessLevel {
  basic,
  verified,
  premium,
}

// Feature types matching website's configuration
enum FeatureType {
  // Basic features (available to all)
  profileView,
  basicSearch,
  
  // Verified features (require phone verification)
  advancedSearch,
  profileContact,
  sendInterest,
  
  // Premium features (require subscription)
  unlimitedProfileViews,
  prioritySupport,
  profileBoost,
  kundaliMatching,
  chatWithMatches,
  seeWhoLikedYou,
  adFreeExperience,
  advancedFilters,
}

// Feature usage limits matching website's configuration
class FeatureLimits {
  final int profileViewsPerDay;
  final int interestsPerDay;
  final int searchesPerDay;
  final int chatMessagesPerDay;
  final bool hasAdvancedSearch;
  final bool hasKundaliMatching;
  final bool hasProfileBoost;
  final bool hasAdFreeExperience;

  const FeatureLimits({
    required this.profileViewsPerDay,
    required this.interestsPerDay,
    required this.searchesPerDay,
    required this.chatMessagesPerDay,
    required this.hasAdvancedSearch,
    required this.hasKundaliMatching,
    required this.hasProfileBoost,
    required this.hasAdFreeExperience,
  });

  factory FeatureLimits.basic() {
    return const FeatureLimits(
      profileViewsPerDay: 10,
      interestsPerDay: 5,
      searchesPerDay: 10,
      chatMessagesPerDay: 0,
      hasAdvancedSearch: false,
      hasKundaliMatching: false,
      hasProfileBoost: false,
      hasAdFreeExperience: false,
    );
  }

  factory FeatureLimits.verified() {
    return const FeatureLimits(
      profileViewsPerDay: 25,
      interestsPerDay: 10,
      searchesPerDay: 25,
      chatMessagesPerDay: 10,
      hasAdvancedSearch: true,
      hasKundaliMatching: false,
      hasProfileBoost: false,
      hasAdFreeExperience: false,
    );
  }

  factory FeatureLimits.premium() {
    return const FeatureLimits(
      profileViewsPerDay: -1, // Unlimited
      interestsPerDay: -1, // Unlimited
      searchesPerDay: -1, // Unlimited
      chatMessagesPerDay: -1, // Unlimited
      hasAdvancedSearch: true,
      hasKundaliMatching: true,
      hasProfileBoost: true,
      hasAdFreeExperience: true,
    );
  }
}

// Feature usage tracking
class FeatureUsage {
  final int profileViewsToday;
  final int interestsToday;
  final int searchesToday;
  final int chatMessagesToday;
  final DateTime lastReset;

  const FeatureUsage({
    required this.profileViewsToday,
    required this.interestsToday,
    required this.searchesToday,
    required this.chatMessagesToday,
    required this.lastReset,
  });

  factory FeatureUsage.fromJson(Map<String, dynamic> json) {
    return FeatureUsage(
      profileViewsToday: json['profileViewsToday'] ?? 0,
      interestsToday: json['interestsToday'] ?? 0,
      searchesToday: json['searchesToday'] ?? 0,
      chatMessagesToday: json['chatMessagesToday'] ?? 0,
      lastReset: DateTime.parse(json['lastReset'] ?? DateTime.now().toIso8601String()),
    );
  }
}

// Feature access result
class FeatureAccessResult {
  final bool hasAccess;
  final String? reason;
  final AccessLevel requiredLevel;
  final int? remainingUsage;
  final int? totalLimit;

  const FeatureAccessResult({
    required this.hasAccess,
    this.reason,
    required this.requiredLevel,
    this.remainingUsage,
    this.totalLimit,
  });

  factory FeatureAccessResult.allowed({
    required AccessLevel requiredLevel,
    int? remainingUsage,
    int? totalLimit,
  }) {
    return FeatureAccessResult(
      hasAccess: true,
      requiredLevel: requiredLevel,
      remainingUsage: remainingUsage,
      totalLimit: totalLimit,
    );
  }

  factory FeatureAccessResult.denied({
    required String reason,
    required AccessLevel requiredLevel,
    int? remainingUsage,
    int? totalLimit,
  }) {
    return FeatureAccessResult(
      hasAccess: false,
      reason: reason,
      requiredLevel: requiredLevel,
      remainingUsage: remainingUsage,
      totalLimit: totalLimit,
    );
  }
}

class FeatureAccessService {
  static final FeatureAccessService _instance = FeatureAccessService._internal();
  factory FeatureAccessService() => _instance;
  FeatureAccessService._internal();

  final ApiClient _apiClient = ApiClient();

  // Feature configuration matching website's middleware
  static const Map<FeatureType, AccessLevel> _featureAccessMap = {
    // Basic features
    FeatureType.profileView: AccessLevel.basic,
    FeatureType.basicSearch: AccessLevel.basic,
    
    // Verified features
    FeatureType.advancedSearch: AccessLevel.verified,
    FeatureType.profileContact: AccessLevel.verified,
    FeatureType.sendInterest: AccessLevel.verified,
    
    // Premium features
    FeatureType.unlimitedProfileViews: AccessLevel.premium,
    FeatureType.prioritySupport: AccessLevel.premium,
    FeatureType.profileBoost: AccessLevel.premium,
    FeatureType.kundaliMatching: AccessLevel.premium,
    FeatureType.chatWithMatches: AccessLevel.premium,
    FeatureType.seeWhoLikedYou: AccessLevel.premium,
    FeatureType.adFreeExperience: AccessLevel.premium,
    FeatureType.advancedFilters: AccessLevel.premium,
  };

  // Get user's access level
  AccessLevel getUserAccessLevel(UserModel user) {
    if (user.isPremium) {
      return AccessLevel.premium;
    } else if (user.isVerified) {
      return AccessLevel.verified;
    } else {
      return AccessLevel.basic;
    }
  }

  // Get feature limits based on user's access level
  FeatureLimits getFeatureLimits(UserModel user) {
    final accessLevel = getUserAccessLevel(user);
    
    switch (accessLevel) {
      case AccessLevel.basic:
        return FeatureLimits.basic();
      case AccessLevel.verified:
        return FeatureLimits.verified();
      case AccessLevel.premium:
        return FeatureLimits.premium();
    }
  }

  // Check if user has access to a specific feature
  Future<FeatureAccessResult> checkFeatureAccess(
    FeatureType feature,
    UserModel user,
  ) async {
    final requiredLevel = _featureAccessMap[feature] ?? AccessLevel.premium;
    final userLevel = getUserAccessLevel(user);
    final limits = getFeatureLimits(user);

    // Check access level
    if (!_hasRequiredAccessLevel(userLevel, requiredLevel)) {
      return FeatureAccessResult.denied(
        reason: _getAccessDeniedMessage(requiredLevel),
        requiredLevel: requiredLevel,
      );
    }

    // Check usage limits for specific features
    try {
      final usage = await getFeatureUsage();
      
      switch (feature) {
        case FeatureType.profileView:
          if (limits.profileViewsPerDay > 0 && 
              usage.profileViewsToday >= limits.profileViewsPerDay) {
            return FeatureAccessResult.denied(
              reason: 'Daily profile view limit reached',
              requiredLevel: requiredLevel,
              remainingUsage: 0,
              totalLimit: limits.profileViewsPerDay,
            );
          }
          return FeatureAccessResult.allowed(
            requiredLevel: requiredLevel,
            remainingUsage: limits.profileViewsPerDay > 0 
                ? limits.profileViewsPerDay - usage.profileViewsToday 
                : -1,
            totalLimit: limits.profileViewsPerDay,
          );
          
        case FeatureType.sendInterest:
          if (limits.interestsPerDay > 0 && 
              usage.interestsToday >= limits.interestsPerDay) {
            return FeatureAccessResult.denied(
              reason: 'Daily interest limit reached',
              requiredLevel: requiredLevel,
              remainingUsage: 0,
              totalLimit: limits.interestsPerDay,
            );
          }
          return FeatureAccessResult.allowed(
            requiredLevel: requiredLevel,
            remainingUsage: limits.interestsPerDay > 0 
                ? limits.interestsPerDay - usage.interestsToday 
                : -1,
            totalLimit: limits.interestsPerDay,
          );
          
        case FeatureType.chatWithMatches:
          if (limits.chatMessagesPerDay > 0 && 
              usage.chatMessagesToday >= limits.chatMessagesPerDay) {
            return FeatureAccessResult.denied(
              reason: 'Daily chat message limit reached',
              requiredLevel: requiredLevel,
              remainingUsage: 0,
              totalLimit: limits.chatMessagesPerDay,
            );
          }
          return FeatureAccessResult.allowed(
            requiredLevel: requiredLevel,
            remainingUsage: limits.chatMessagesPerDay > 0 
                ? limits.chatMessagesPerDay - usage.chatMessagesToday 
                : -1,
            totalLimit: limits.chatMessagesPerDay,
          );
          
        default:
          return FeatureAccessResult.allowed(requiredLevel: requiredLevel);
      }
    } catch (e) {
      // If we can't fetch usage, allow access but log the error
      return FeatureAccessResult.allowed(requiredLevel: requiredLevel);
    }
  }

  // Get current feature usage from backend
  Future<FeatureUsage> getFeatureUsage() async {
    try {
      final response = await _apiClient.get('/users/feature-usage');
      
      if (response['success'] == true) {
        return FeatureUsage.fromJson(response['usage']);
      } else {
        throw Exception('Failed to fetch feature usage');
      }
    } catch (e) {
      throw Exception('Error fetching feature usage: $e');
    }
  }

  // Track feature usage
  Future<void> trackFeatureUsage(FeatureType feature) async {
    try {
      await _apiClient.post('/users/track-feature-usage', {
        'feature': feature.toString().split('.').last,
      });
    } catch (e) {
      // Log error but don't throw to avoid blocking user actions
      print('Error tracking feature usage: $e');
    }
  }

  // Helper methods
  bool _hasRequiredAccessLevel(AccessLevel userLevel, AccessLevel requiredLevel) {
    return userLevel.index >= requiredLevel.index;
  }

  String _getAccessDeniedMessage(AccessLevel requiredLevel) {
    switch (requiredLevel) {
      case AccessLevel.verified:
        return 'Please verify your phone number to access this feature';
      case AccessLevel.premium:
        return 'This feature requires a premium subscription';
      default:
        return 'Access denied';
    }
  }

  // Get upgrade message for feature access
  String getUpgradeMessage(FeatureType feature) {
    final requiredLevel = _featureAccessMap[feature] ?? AccessLevel.premium;
    
    switch (requiredLevel) {
      case AccessLevel.verified:
        return 'Verify your phone number to unlock this feature';
      case AccessLevel.premium:
        return 'Upgrade to Premium to unlock this feature';
      default:
        return 'Feature not available';
    }
  }
}
