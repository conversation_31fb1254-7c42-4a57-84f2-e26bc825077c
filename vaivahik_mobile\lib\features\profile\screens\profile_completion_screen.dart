import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/widgets/premium_widgets.dart';
import '../../../app/theme.dart';
import 'basic_details_screen.dart';
import 'education_career_screen.dart';
import 'location_details_screen.dart';
import 'family_details_screen.dart';
import 'lifestyle_habits_screen.dart';
import 'about_me_screen.dart';
import 'photo_upload_screen.dart';

class ProfileCompletionScreen extends StatefulWidget {
  const ProfileCompletionScreen({super.key});

  @override
  State<ProfileCompletionScreen> createState() => _ProfileCompletionScreenState();
}

class _ProfileCompletionScreenState extends State<ProfileCompletionScreen> {
  int completionPercentage = 15; // Mock data - will be fetched from API

  final List<ProfileSection> sections = [
    ProfileSection(
      title: 'Basic Details',
      subtitle: 'Name, age, height, gender',
      icon: Icons.person_outline,
      isCompleted: false,
      route: '/basic-details',
      screen: const BasicDetailsScreen(),
    ),
    ProfileSection(
      title: 'Education & Career',
      subtitle: 'Education, occupation, income',
      icon: Icons.school_outlined,
      isCompleted: false,
      route: '/education-career',
      screen: const EducationCareerScreen(),
    ),
    ProfileSection(
      title: 'Location Details',
      subtitle: 'Current city, native place',
      icon: Icons.location_on_outlined,
      isCompleted: false,
      route: '/location-details',
      screen: const LocationDetailsScreen(),
    ),
    ProfileSection(
      title: 'Family Details',
      subtitle: 'Family background, siblings',
      icon: Icons.family_restroom_outlined,
      isCompleted: false,
      route: '/family-details',
      screen: const FamilyDetailsScreen(),
    ),
    ProfileSection(
      title: 'Lifestyle & Habits',
      subtitle: 'Diet, hobbies, interests',
      icon: Icons.favorite_outline,
      isCompleted: false,
      route: '/lifestyle-habits',
      screen: const LifestyleHabitsScreen(),
    ),
    ProfileSection(
      title: 'About Me',
      subtitle: 'Tell about yourself',
      icon: Icons.edit_outlined,
      isCompleted: false,
      route: '/about-me',
      screen: const AboutMeScreen(),
    ),
    ProfileSection(
      title: 'Profile Photos',
      subtitle: 'Upload your best photos',
      icon: Icons.photo_camera_outlined,
      isCompleted: false,
      route: '/photo-upload',
      screen: const PhotoUploadScreen(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Complete Your Profile',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress Card
            GlassmorphicCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: AppTheme.primaryGradient,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.trending_up,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Profile Completion',
                                style: TextStyle(
                                  color: AppTheme.textColor,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '$completionPercentage% Complete',
                                style: TextStyle(
                                  color: AppTheme.textColor.withValues(alpha: 0.7),
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '$completionPercentage%',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: LinearProgressIndicator(
                        value: completionPercentage / 100,
                        backgroundColor: AppTheme.textColor.withValues(alpha: 0.1),
                        valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                        minHeight: 8,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Complete your profile to get better matches and increase visibility',
                      style: TextStyle(
                        color: AppTheme.textColor.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
            
            const SizedBox(height: 24),
            
            // Sections List
            Text(
              'Profile Sections',
              style: TextStyle(
                color: AppTheme.textColor,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sections.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final section = sections[index];
                return _buildSectionCard(section, index);
              },
            ),
            
            const SizedBox(height: 32),
            
            // Complete Profile Button
            PremiumGradientButton(
              text: 'Save & Continue',
              onPressed: () {
                // Navigate to home or dashboard
                Navigator.pushNamedAndRemoveUntil(
                  context, 
                  '/home', 
                  (route) => false,
                );
              },
              width: double.infinity,
            ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(ProfileSection section, int index) {
    return GlassmorphicCard(
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: section.isCompleted 
                ? AppTheme.successGradient 
                : AppTheme.primaryGradient.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            section.isCompleted ? Icons.check : section.icon,
            color: Colors.white,
            size: 24,
          ),
        ),
        title: Text(
          section.title,
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          section.subtitle,
          style: TextStyle(
            color: AppTheme.textColor.withValues(alpha: 0.6),
            fontSize: 14,
          ),
        ),
        trailing: Icon(
          Icons.arrow_forward_ios,
          color: AppTheme.primaryColor,
          size: 16,
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => section.screen),
          );
        },
      ),
    ).animate(delay: (index * 100).ms).fadeIn(duration: 600.ms).slideX(begin: 0.3, end: 0);
  }
}

class ProfileSection {
  final String title;
  final String subtitle;
  final IconData icon;
  final bool isCompleted;
  final String route;
  final Widget screen;

  ProfileSection({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.isCompleted,
    required this.route,
    required this.screen,
  });
}
