#!/usr/bin/env node

/**
 * Comprehensive System Integration Test
 * Tests all major systems and their integration
 */

require('dotenv').config();
const axios = require('axios');

const API_BASE_URL = 'http://localhost:8000/api';

async function testSystemIntegration() {
  console.log('🔧 Running Comprehensive System Integration Tests...\n');
  
  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };
  
  // Test 1: Health Check
  console.log('1️⃣ Testing System Health...');
  try {
    const health = await axios.get(`${API_BASE_URL}/health`);
    if (health.data.success) {
      console.log('✅ System health check passed');
      results.passed++;
      results.tests.push({ name: 'Health Check', status: 'PASSED' });
    }
  } catch (error) {
    console.log('❌ System health check failed');
    results.failed++;
    results.tests.push({ name: 'Health Check', status: 'FAILED', error: error.message });
  }
  
  // Test 2: Database Connectivity
  console.log('\n2️⃣ Testing Database Connectivity...');
  try {
    // Test user endpoint (requires DB)
    const users = await axios.get(`${API_BASE_URL}/users/profiles`);
    console.log('✅ Database connectivity working');
    results.passed++;
    results.tests.push({ name: 'Database Connectivity', status: 'PASSED' });
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Database connectivity working (auth required)');
      results.passed++;
      results.tests.push({ name: 'Database Connectivity', status: 'PASSED' });
    } else {
      console.log('❌ Database connectivity failed');
      results.failed++;
      results.tests.push({ name: 'Database Connectivity', status: 'FAILED', error: error.message });
    }
  }
  
  // Test 3: Kundali Matching System
  console.log('\n3️⃣ Testing Kundali Matching System...');
  try {
    const kundaliTest = await axios.post(`${API_BASE_URL}/premium/kundli-matching`, {
      user1: {
        id: 'test1',
        birthDate: '1995-03-15',
        birthTime: '10:30',
        birthPlace: 'Mumbai, Maharashtra, India',
        profile: { fullName: 'Test User 1', age: 28 }
      },
      user2: {
        id: 'test2',
        birthDate: '1997-07-22',
        birthTime: '14:45',
        birthPlace: 'Pune, Maharashtra, India',
        profile: { fullName: 'Test User 2', age: 26 }
      },
      options: { includeMLScore: true }
    });
    
    if (kundaliTest.data.success && kundaliTest.data.overallScore) {
      console.log(`✅ Kundali matching working (Score: ${kundaliTest.data.overallScore}/36)`);
      results.passed++;
      results.tests.push({ name: 'Kundali Matching', status: 'PASSED' });
    }
  } catch (error) {
    console.log('❌ Kundali matching failed');
    results.failed++;
    results.tests.push({ name: 'Kundali Matching', status: 'FAILED', error: error.message });
  }
  
  // Test 4: Google Places API
  console.log('\n4️⃣ Testing Google Places API...');
  try {
    const placesTest = await axios.get(`https://maps.googleapis.com/maps/api/place/textsearch/json?query=Mumbai&key=${process.env.GOOGLE_PLACES_API_KEY}`);
    if (placesTest.data.status === 'OK') {
      console.log('✅ Google Places API working');
      results.passed++;
      results.tests.push({ name: 'Google Places API', status: 'PASSED' });
    }
  } catch (error) {
    console.log('❌ Google Places API failed');
    results.failed++;
    results.tests.push({ name: 'Google Places API', status: 'FAILED', error: error.message });
  }
  
  // Test 5: Firebase FCM (Configuration Check)
  console.log('\n5️⃣ Testing Firebase Configuration...');
  try {
    const requiredVars = ['FIREBASE_PROJECT_ID', 'FIREBASE_PRIVATE_KEY', 'FIREBASE_CLIENT_EMAIL'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length === 0) {
      console.log('✅ Firebase configuration complete');
      results.passed++;
      results.tests.push({ name: 'Firebase Configuration', status: 'PASSED' });
    } else {
      console.log(`❌ Firebase configuration incomplete: ${missingVars.join(', ')}`);
      results.failed++;
      results.tests.push({ name: 'Firebase Configuration', status: 'FAILED', error: `Missing: ${missingVars.join(', ')}` });
    }
  } catch (error) {
    console.log('❌ Firebase configuration check failed');
    results.failed++;
    results.tests.push({ name: 'Firebase Configuration', status: 'FAILED', error: error.message });
  }
  
  // Test 6: Email Configuration
  console.log('\n6️⃣ Testing Email Configuration...');
  try {
    const emailVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS'];
    const missingEmailVars = emailVars.filter(varName => !process.env[varName]);
    
    if (missingEmailVars.length === 0) {
      console.log('✅ Email configuration complete');
      results.passed++;
      results.tests.push({ name: 'Email Configuration', status: 'PASSED' });
    } else {
      console.log(`⚠️  Email configuration incomplete: ${missingEmailVars.join(', ')}`);
      results.passed++; // Not critical for basic functionality
      results.tests.push({ name: 'Email Configuration', status: 'WARNING', error: `Missing: ${missingEmailVars.join(', ')}` });
    }
  } catch (error) {
    console.log('❌ Email configuration check failed');
    results.failed++;
    results.tests.push({ name: 'Email Configuration', status: 'FAILED', error: error.message });
  }
  
  // Test 7: SMS Configuration (MSG91)
  console.log('\n7️⃣ Testing SMS Configuration...');
  try {
    const smsVars = ['MSG91_API_KEY', 'MSG91_SENDER_ID', 'MSG91_DLT_TEMPLATE_ID'];
    const missingSmsVars = smsVars.filter(varName => !process.env[varName]);
    
    if (missingSmsVars.length === 0) {
      console.log('✅ SMS configuration complete');
      results.passed++;
      results.tests.push({ name: 'SMS Configuration', status: 'PASSED' });
    } else {
      console.log(`❌ SMS configuration incomplete: ${missingSmsVars.join(', ')}`);
      results.failed++;
      results.tests.push({ name: 'SMS Configuration', status: 'FAILED', error: `Missing: ${missingSmsVars.join(', ')}` });
    }
  } catch (error) {
    console.log('❌ SMS configuration check failed');
    results.failed++;
    results.tests.push({ name: 'SMS Configuration', status: 'FAILED', error: error.message });
  }
  
  // Test 8: Payment Gateway Configuration
  console.log('\n8️⃣ Testing Payment Gateway Configuration...');
  try {
    const paymentVars = ['RAZORPAY_KEY_ID', 'RAZORPAY_KEY_SECRET'];
    const missingPaymentVars = paymentVars.filter(varName => !process.env[varName]);
    
    if (missingPaymentVars.length === 0) {
      console.log('✅ Payment gateway configuration complete');
      results.passed++;
      results.tests.push({ name: 'Payment Gateway Configuration', status: 'PASSED' });
    } else {
      console.log(`❌ Payment gateway configuration incomplete: ${missingPaymentVars.join(', ')}`);
      results.failed++;
      results.tests.push({ name: 'Payment Gateway Configuration', status: 'FAILED', error: `Missing: ${missingPaymentVars.join(', ')}` });
    }
  } catch (error) {
    console.log('❌ Payment gateway configuration check failed');
    results.failed++;
    results.tests.push({ name: 'Payment Gateway Configuration', status: 'FAILED', error: error.message });
  }
  
  // Test 9: Security Configuration
  console.log('\n9️⃣ Testing Security Configuration...');
  try {
    const securityVars = ['JWT_SECRET', 'ADMIN_JWT_SECRET'];
    const missingSecurityVars = securityVars.filter(varName => !process.env[varName]);
    
    if (missingSecurityVars.length === 0) {
      console.log('✅ Security configuration complete');
      results.passed++;
      results.tests.push({ name: 'Security Configuration', status: 'PASSED' });
    } else {
      console.log(`❌ Security configuration incomplete: ${missingSecurityVars.join(', ')}`);
      results.failed++;
      results.tests.push({ name: 'Security Configuration', status: 'FAILED', error: `Missing: ${missingSecurityVars.join(', ')}` });
    }
  } catch (error) {
    console.log('❌ Security configuration check failed');
    results.failed++;
    results.tests.push({ name: 'Security Configuration', status: 'FAILED', error: error.message });
  }
  
  // Test 10: Public Content API
  console.log('\n🔟 Testing Public Content API...');
  try {
    const content = await axios.get(`${API_BASE_URL}/content/privacy-policy`);
    if (content.data) {
      console.log('✅ Public content API working');
      results.passed++;
      results.tests.push({ name: 'Public Content API', status: 'PASSED' });
    }
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('⚠️  Public content API accessible but content not found');
      results.passed++;
      results.tests.push({ name: 'Public Content API', status: 'WARNING', error: 'Content not found' });
    } else {
      console.log('❌ Public content API failed');
      results.failed++;
      results.tests.push({ name: 'Public Content API', status: 'FAILED', error: error.message });
    }
  }
  
  // Final Results
  console.log('\n📊 System Integration Test Results:');
  console.log('=====================================');
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`📈 Success Rate: ${Math.round((results.passed / (results.passed + results.failed)) * 100)}%`);
  
  console.log('\n📋 Detailed Results:');
  results.tests.forEach((test, index) => {
    const icon = test.status === 'PASSED' ? '✅' : test.status === 'WARNING' ? '⚠️' : '❌';
    console.log(`${index + 1}. ${icon} ${test.name}: ${test.status}`);
    if (test.error) {
      console.log(`   Error: ${test.error}`);
    }
  });
  
  const isSuccess = results.failed === 0;
  
  if (isSuccess) {
    console.log('\n🎉 All critical systems are working correctly!');
    console.log('\n🚀 System is ready for production deployment!');
  } else {
    console.log('\n⚠️  Some systems need attention before production deployment.');
  }
  
  return isSuccess;
}

// Run the test
testSystemIntegration().then(success => {
  process.exit(success ? 0 : 1);
});
