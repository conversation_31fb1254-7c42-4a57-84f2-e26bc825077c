import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/api/api_client.dart';
import '../models/shortlist_model.dart';

/// 📋 SHORTLIST SERVICE - Complete Shortlist Management
/// Features: Add/Remove, Notes, Filters, Stats, Bulk Operations
class ShortlistService {
  final ApiClient _apiClient;

  ShortlistService(this._apiClient);

  /// 📋 Get Shortlisted Profiles
  Future<ShortlistResponse> getShortlistedProfiles({
    int page = 1,
    int limit = 20,
    ShortlistFilters? filters,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
      };

      // Add filters to query params
      if (filters != null) {
        if (filters.searchQuery?.isNotEmpty == true) {
          queryParams['search'] = filters.searchQuery;
        }
        if (filters.interestSent != null) {
          queryParams['interestSent'] = filters.interestSent;
        }
        if (filters.interestStatus?.isNotEmpty == true) {
          queryParams['interestStatus'] = filters.interestStatus;
        }
        if (filters.location?.isNotEmpty == true) {
          queryParams['location'] = filters.location;
        }
        if (filters.education?.isNotEmpty == true) {
          queryParams['education'] = filters.education;
        }
        if (filters.occupation?.isNotEmpty == true) {
          queryParams['occupation'] = filters.occupation;
        }
        if (filters.minAge != null) {
          queryParams['minAge'] = filters.minAge;
        }
        if (filters.maxAge != null) {
          queryParams['maxAge'] = filters.maxAge;
        }
        if (filters.minHeight != null) {
          queryParams['minHeight'] = filters.minHeight;
        }
        if (filters.maxHeight != null) {
          queryParams['maxHeight'] = filters.maxHeight;
        }
        if (filters.religion?.isNotEmpty == true) {
          queryParams['religion'] = filters.religion;
        }
        if (filters.caste?.isNotEmpty == true) {
          queryParams['caste'] = filters.caste;
        }
        if (filters.maritalStatus?.isNotEmpty == true) {
          queryParams['maritalStatus'] = filters.maritalStatus;
        }
        if (filters.isVerified != null) {
          queryParams['isVerified'] = filters.isVerified;
        }
        if (filters.isOnline != null) {
          queryParams['isOnline'] = filters.isOnline;
        }
        if (filters.sortBy?.isNotEmpty == true) {
          queryParams['sortBy'] = filters.sortBy;
        }
        if (filters.sortAscending != null) {
          queryParams['sortAscending'] = filters.sortAscending;
        }
      }

      final response = await _apiClient.get('/user/shortlist', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> shortlistData = response['data'] ?? [];
        final items = shortlistData.map((item) => ShortlistItem.fromJson(item)).toList();
        
        return ShortlistResponse(
          success: true,
          data: items,
          total: response['total'] ?? items.length,
          page: page,
          limit: limit,
          hasMore: items.length >= limit,
        );
      } else {
        throw ApiException(response['message'] ?? 'Failed to fetch shortlisted profiles');
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Error fetching shortlisted profiles: $e');
    }
  }

  /// ➕ Add Profile to Shortlist
  Future<ShortlistActionResult> addToShortlist(AddToShortlistRequest request) async {
    try {
      final response = await _apiClient.post('/user/shortlist', {
        'profileId': request.profileId,
        if (request.note?.isNotEmpty == true) 'note': request.note,
      });

      if (response['success'] == true) {
        return ShortlistActionResult(
          success: true,
          message: response['message'] ?? 'Profile added to shortlist successfully',
          shortlistId: response['shortlistId']?.toString(),
        );
      } else {
        throw ApiException(response['message'] ?? 'Failed to add profile to shortlist');
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Error adding profile to shortlist: $e');
    }
  }

  /// 🗑️ Remove Profile from Shortlist
  Future<ShortlistActionResult> removeFromShortlist(RemoveFromShortlistRequest request) async {
    try {
      final response = await _apiClient.delete('/user/shortlist/${request.shortlistId}');

      if (response['success'] == true) {
        return ShortlistActionResult(
          success: true,
          message: response['message'] ?? 'Profile removed from shortlist successfully',
          shortlistId: request.shortlistId,
        );
      } else {
        throw ApiException(response['message'] ?? 'Failed to remove profile from shortlist');
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Error removing profile from shortlist: $e');
    }
  }

  /// ✏️ Update Shortlist Note
  Future<ShortlistActionResult> updateShortlistNote(UpdateShortlistNoteRequest request) async {
    try {
      final response = await _apiClient.put('/user/shortlist/${request.shortlistId}', {
        'note': request.note ?? '',
      });

      if (response['success'] == true) {
        return ShortlistActionResult(
          success: true,
          message: response['message'] ?? 'Note updated successfully',
          shortlistId: request.shortlistId,
        );
      } else {
        throw ApiException(response['message'] ?? 'Failed to update note');
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Error updating shortlist note: $e');
    }
  }

  /// 📊 Get Shortlist Statistics
  Future<ShortlistStats> getShortlistStats() async {
    try {
      final response = await _apiClient.get('/user/shortlist/stats');

      if (response['success'] == true) {
        final statsData = response['data'] ?? response['stats'] ?? {};
        return ShortlistStats.fromJson(statsData);
      } else {
        throw ApiException(response['message'] ?? 'Failed to fetch shortlist statistics');
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Error fetching shortlist statistics: $e');
    }
  }

  /// 🔍 Check if Profile is Shortlisted
  Future<bool> isProfileShortlisted(String profileId) async {
    try {
      final response = await _apiClient.get('/user/shortlist/check/$profileId');

      if (response['success'] == true) {
        return response['isShortlisted'] ?? false;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// 📋 Get Shortlist by Profile ID
  Future<ShortlistItem?> getShortlistByProfileId(String profileId) async {
    try {
      final response = await _apiClient.get('/user/shortlist/profile/$profileId');

      if (response['success'] == true && response['data'] != null) {
        return ShortlistItem.fromJson(response['data']);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  /// 🗑️ Bulk Remove from Shortlist
  Future<ShortlistActionResult> bulkRemoveFromShortlist(List<String> shortlistIds) async {
    try {
      final response = await _apiClient.post('/user/shortlist/bulk-remove', {
        'shortlistIds': shortlistIds,
      });

      if (response['success'] == true) {
        return ShortlistActionResult(
          success: true,
          message: response['message'] ?? 'Profiles removed from shortlist successfully',
        );
      } else {
        throw ApiException(response['message'] ?? 'Failed to remove profiles from shortlist');
      }
    } catch (e) {
      if (e is ApiException) rethrow;
      throw ApiException('Error removing profiles from shortlist: $e');
    }
  }

  /// 💝 Send Interest to Shortlisted Profile
  Future<bool> sendInterestToShortlistedProfile(String profileId, {String? message}) async {
    try {
      final response = await _apiClient.post('/user/interests/send', {
        'targetUserId': profileId,
        if (message?.isNotEmpty == true) 'message': message,
      });

      return response['success'] == true;
    } catch (e) {
      throw ApiException('Error sending interest: $e');
    }
  }

  /// 📱 Get Shortlist Quick Actions
  Future<List<String>> getShortlistQuickActions() async {
    try {
      final response = await _apiClient.get('/user/shortlist/quick-actions');

      if (response['success'] == true) {
        final List<dynamic> actions = response['actions'] ?? [];
        return actions.map((action) => action.toString()).toList();
      } else {
        return ['Send Interest', 'View Profile', 'Call', 'Remove'];
      }
    } catch (e) {
      return ['Send Interest', 'View Profile', 'Call', 'Remove'];
    }
  }

  /// 🔄 Sync Shortlist with Server
  Future<bool> syncShortlist() async {
    try {
      final response = await _apiClient.post('/user/shortlist/sync', {
        'timestamp': DateTime.now().toIso8601String(),
      });

      return response['success'] == true;
    } catch (e) {
      return false;
    }
  }

  /// 📤 Export Shortlist Data
  Future<String?> exportShortlistData({String format = 'json'}) async {
    try {
      final response = await _apiClient.get('/user/shortlist/export', queryParams: {
        'format': format,
      });

      if (response['success'] == true) {
        return response['data']?.toString();
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }
}

/// 🏭 SHORTLIST SERVICE PROVIDER
final shortlistServiceProvider = Provider<ShortlistService>((ref) {
  return ShortlistService(ApiClient());
});
