import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/feature_access_service.dart';
import '../providers/feature_access_provider.dart';
import '../theme/app_theme.dart';
import '../../features/premium/screens/premium_plans_screen.dart';

// Widget that wraps content with feature access control
class FeatureAccessWidget extends ConsumerWidget {
  final FeatureType feature;
  final Widget child;
  final Widget? fallback;
  final bool showUpgradePrompt;
  final String? customMessage;
  final VoidCallback? onAccessDenied;

  const FeatureAccessWidget({
    super.key,
    required this.feature,
    required this.child,
    this.fallback,
    this.showUpgradePrompt = true,
    this.customMessage,
    this.onAccessDenied,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final featureAccessAsync = ref.watch(featureAccessCheckProvider(feature));

    return featureAccessAsync.when(
      data: (accessResult) {
        if (accessResult.hasAccess) {
          return child;
        } else {
          if (fallback != null) {
            return fallback!;
          } else if (showUpgradePrompt) {
            return _buildUpgradePrompt(context, ref, accessResult);
          } else {
            return const SizedBox.shrink();
          }
        }
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => child, // Show content on error to avoid blocking
    );
  }

  Widget _buildUpgradePrompt(BuildContext context, WidgetRef ref, FeatureAccessResult accessResult) {
    final notifier = ref.read(featureAccessProvider.notifier);
    final message = customMessage ?? accessResult.reason ?? notifier.getUpgradeMessage(feature);

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border.all(color: Colors.orange.shade200),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getFeatureIcon(feature),
            size: 48,
            color: Colors.orange.shade600,
          ),
          const SizedBox(height: 12),
          Text(
            _getFeatureTitle(feature),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              if (accessResult.requiredLevel == AccessLevel.verified)
                ElevatedButton(
                  onPressed: () {
                    // Navigate to verification screen
                    Navigator.pushNamed(context, '/verification');
                    if (onAccessDenied != null) onAccessDenied!();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Verify Phone'),
                ),
              if (accessResult.requiredLevel == AccessLevel.premium)
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const PremiumPlansScreen(),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Upgrade to Premium'),
                ),
              TextButton(
                onPressed: () {
                  if (onAccessDenied != null) onAccessDenied!();
                },
                child: const Text('Maybe Later'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getFeatureIcon(FeatureType feature) {
    switch (feature) {
      case FeatureType.profileView:
        return Icons.visibility;
      case FeatureType.advancedSearch:
        return Icons.search;
      case FeatureType.chatWithMatches:
        return Icons.chat;
      case FeatureType.kundaliMatching:
        return Icons.stars;
      case FeatureType.profileBoost:
        return Icons.trending_up;
      case FeatureType.sendInterest:
        return Icons.favorite;
      case FeatureType.seeWhoLikedYou:
        return Icons.favorite_border;
      case FeatureType.prioritySupport:
        return Icons.support_agent;
      case FeatureType.adFreeExperience:
        return Icons.block;
      default:
        return Icons.lock;
    }
  }

  String _getFeatureTitle(FeatureType feature) {
    switch (feature) {
      case FeatureType.profileView:
        return 'Profile Views';
      case FeatureType.advancedSearch:
        return 'Advanced Search';
      case FeatureType.chatWithMatches:
        return 'Chat with Matches';
      case FeatureType.kundaliMatching:
        return 'Kundali Matching';
      case FeatureType.profileBoost:
        return 'Profile Boost';
      case FeatureType.sendInterest:
        return 'Send Interest';
      case FeatureType.seeWhoLikedYou:
        return 'See Who Liked You';
      case FeatureType.prioritySupport:
        return 'Priority Support';
      case FeatureType.adFreeExperience:
        return 'Ad-Free Experience';
      case FeatureType.unlimitedProfileViews:
        return 'Unlimited Profile Views';
      case FeatureType.advancedFilters:
        return 'Advanced Filters';
      default:
        return 'Premium Feature';
    }
  }
}

// Usage tracking wrapper widget
class FeatureUsageTracker extends ConsumerWidget {
  final FeatureType feature;
  final Widget child;
  final bool trackOnTap;

  const FeatureUsageTracker({
    super.key,
    required this.feature,
    required this.child,
    this.trackOnTap = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (trackOnTap) {
      return GestureDetector(
        onTap: () {
          _trackUsage(ref);
        },
        child: child,
      );
    } else {
      // Track usage immediately
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _trackUsage(ref);
      });
      return child;
    }
  }

  void _trackUsage(WidgetRef ref) {
    final notifier = ref.read(featureAccessProvider.notifier);
    notifier.trackUsage(feature);
  }
}

// Feature usage indicator widget
class FeatureUsageIndicator extends ConsumerWidget {
  final FeatureType feature;
  final bool showPercentage;

  const FeatureUsageIndicator({
    super.key,
    required this.feature,
    this.showPercentage = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final featureAccessState = ref.watch(featureAccessProvider);
    final notifier = ref.read(featureAccessProvider.notifier);

    if (featureAccessState.isLoading) {
      return const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(strokeWidth: 2),
      );
    }

    final remaining = notifier.getRemainingUsage(feature);
    final percentage = notifier.getUsagePercentage(feature);

    if (remaining == -1) {
      // Unlimited usage
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.green.shade100,
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Text(
          'Unlimited',
          style: TextStyle(
            fontSize: 12,
            color: Colors.green,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    Color indicatorColor;
    if (percentage < 0.5) {
      indicatorColor = Colors.green;
    } else if (percentage < 0.8) {
      indicatorColor = Colors.orange;
    } else {
      indicatorColor = Colors.red;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: indicatorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: indicatorColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            showPercentage 
                ? '${(percentage * 100).toInt()}%'
                : '$remaining left',
            style: TextStyle(
              fontSize: 12,
              color: indicatorColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
