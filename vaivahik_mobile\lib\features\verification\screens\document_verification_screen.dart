import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../app/theme.dart';
import '../../../core/api/api_client.dart';

/// 🛡️ DOCUMENT VERIFICATION SYSTEM - World-Class Security
/// Features: Camera Integration, Document Scanning, Verification Status, Admin Approval

class DocumentVerificationScreen extends ConsumerStatefulWidget {
  const DocumentVerificationScreen({super.key});

  @override
  ConsumerState<DocumentVerificationScreen> createState() => _DocumentVerificationScreenState();
}

class _DocumentVerificationScreenState extends ConsumerState<DocumentVerificationScreen> 
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  final ImagePicker _picker = ImagePicker();
  
  final List<DocumentType> _documentTypes = [
    const DocumentType(
      id: 'id_proof',
      title: 'ID Proof',
      subtitle: 'Aadhaar, PAN, Passport, Driving License',
      icon: Icons.credit_card,
      color: Colors.blue,
      isRequired: true,
    ),
    const DocumentType(
      id: 'education',
      title: 'Education Certificate',
      subtitle: 'Degree, Diploma, Marksheet',
      icon: Icons.school,
      color: Colors.green,
      isRequired: true,
    ),
    const DocumentType(
      id: 'income_proof',
      title: 'Income Proof',
      subtitle: 'Salary Slip, ITR, Bank Statement',
      icon: Icons.account_balance_wallet,
      color: Colors.orange,
      isRequired: false,
    ),
    const DocumentType(
      id: 'address_proof',
      title: 'Address Proof',
      subtitle: 'Utility Bill, Rent Agreement',
      icon: Icons.home,
      color: Colors.purple,
      isRequired: false,
    ),
    const DocumentType(
      id: 'photo_id',
      title: 'Photo with ID',
      subtitle: 'Selfie holding your ID proof',
      icon: Icons.camera_alt,
      color: Colors.red,
      isRequired: true,
    ),
  ];

  Map<String, DocumentStatus> _documentStatuses = {};
  final Map<String, File?> _uploadedFiles = {};
  bool _isLoading = false; // ignore: unused_field

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animationController.forward();
    _loadDocumentStatuses();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadDocumentStatuses() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/verification/documents');
      
      if (response['success'] == true) {
        final documents = response['data']['documents'] as List;
        setState(() {
          _documentStatuses = {
            for (var doc in documents)
              doc['type']: DocumentStatus.fromString(doc['status'])
          };
        });
      }
    } catch (e) {
      print('Error loading document statuses: $e');
    }
  }

  Future<void> _pickDocument(DocumentType docType) async {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildImageSourceSelector(docType),
    );
  }

  Widget _buildImageSourceSelector(DocumentType docType) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Upload ${docType.title}',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildSourceOption(
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  onTap: () => _captureImage(docType, ImageSource.camera),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSourceOption(
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: () => _captureImage(docType, ImageSource.gallery),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
        ],
      ),
    ).animate()
     .slideY(begin: 1, curve: Curves.elasticOut)
     .fadeIn();
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.border),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              size: 32,
              color: AppColors.primary,
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _captureImage(DocumentType docType, ImageSource source) async {
    Navigator.of(context).pop(); // Close bottom sheet
    
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );
      
      if (image != null) {
        setState(() {
          _uploadedFiles[docType.id] = File(image.path);
        });
        
        await _uploadDocument(docType, File(image.path));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error capturing image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _uploadDocument(DocumentType docType, File file) async {
    setState(() {
      _isLoading = true;
      _documentStatuses[docType.id] = DocumentStatus.uploading;
    });

    try {
      final apiClient = ApiClient();
      
      // Create multipart request
      final formData = {
        'type': docType.id,
        'title': docType.title,
        'file': file,
      };
      
      final response = await apiClient.postMultipart('/verification/upload', formData);
      
      if (response['success'] == true) {
        setState(() {
          _documentStatuses[docType.id] = DocumentStatus.pending;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${docType.title} uploaded successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception(response['message'] ?? 'Upload failed');
      }
    } catch (e) {
      setState(() {
        _documentStatuses[docType.id] = DocumentStatus.failed;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Upload failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Document Verification'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _loadDocumentStatuses,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: _buildDocumentList(),
          ),
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    final completedDocs = _documentStatuses.values
        .where((status) => status == DocumentStatus.approved)
        .length;
    final totalRequired = _documentTypes.where((doc) => doc.isRequired).length;
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.verified_user,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Verification Status',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '$completedDocs of $totalRequired required documents verified',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: totalRequired > 0 ? completedDocs / totalRequired : 0,
            backgroundColor: Colors.white.withValues(alpha: 0.3),
            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ],
      ),
    ).animate(controller: _animationController)
     .slideY(begin: -0.5, curve: Curves.elasticOut)
     .fadeIn();
  }

  Widget _buildDocumentList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _documentTypes.length,
      itemBuilder: (context, index) {
        final docType = _documentTypes[index];
        final status = _documentStatuses[docType.id] ?? DocumentStatus.notUploaded;
        
        return _buildDocumentCard(docType, status, index);
      },
    );
  }

  Widget _buildDocumentCard(DocumentType docType, DocumentStatus status, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
        border: Border.all(
          color: _getStatusColor(status).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: docType.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            docType.icon,
            color: docType.color,
            size: 24,
          ),
        ),
        title: Row(
          children: [
            Expanded(
              child: Text(
                docType.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (docType.isRequired)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  'Required',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              docType.subtitle,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 8),
            _buildStatusChip(status),
          ],
        ),
        trailing: _buildActionButton(docType, status),
        onTap: status == DocumentStatus.notUploaded || status == DocumentStatus.failed
            ? () => _pickDocument(docType)
            : null,
      ),
    ).animate()
     .slideX(begin: 1, delay: (index * 100).ms)
     .fadeIn(delay: (index * 100).ms);
  }

  Widget _buildStatusChip(DocumentStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getStatusColor(status).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getStatusIcon(status),
            size: 12,
            color: _getStatusColor(status),
          ),
          const SizedBox(width: 4),
          Text(
            _getStatusText(status),
            style: TextStyle(
              color: _getStatusColor(status),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(DocumentType docType, DocumentStatus status) {
    switch (status) {
      case DocumentStatus.notUploaded:
      case DocumentStatus.failed:
        return IconButton(
          onPressed: () => _pickDocument(docType),
          icon: Icon(
            Icons.upload_file,
            color: docType.color,
          ),
        );
      case DocumentStatus.uploading:
        return const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        );
      case DocumentStatus.pending:
        return Icon(
          Icons.schedule,
          color: Colors.orange[600],
        );
      case DocumentStatus.approved:
        return Icon(
          Icons.check_circle,
          color: Colors.green[600],
        );
      case DocumentStatus.rejected:
        return IconButton(
          onPressed: () => _pickDocument(docType),
          icon: const Icon(
            Icons.refresh,
            color: Colors.red,
          ),
        );
    }
  }

  Widget _buildBottomActions() {
    final allRequiredUploaded = _documentTypes
        .where((doc) => doc.isRequired)
        .every((doc) => _documentStatuses[doc.id] != null && 
               _documentStatuses[doc.id] != DocumentStatus.notUploaded);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (!allRequiredUploaded)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.orange[700],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Please upload all required documents for verification',
                      style: TextStyle(
                        color: Colors.orange[700],
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: allRequiredUploaded ? _submitForVerification : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                allRequiredUploaded 
                    ? 'Submit for Verification'
                    : 'Upload Required Documents',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate()
     .slideY(begin: 1, delay: 600.ms)
     .fadeIn(delay: 600.ms);
  }

  Future<void> _submitForVerification() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.post('/verification/submit', {});
      
      if (response['success'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Documents submitted for verification!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error submitting documents: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Color _getStatusColor(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.notUploaded:
        return Colors.grey;
      case DocumentStatus.uploading:
        return Colors.blue;
      case DocumentStatus.pending:
        return Colors.orange;
      case DocumentStatus.approved:
        return Colors.green;
      case DocumentStatus.rejected:
      case DocumentStatus.failed:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.notUploaded:
        return Icons.upload_file;
      case DocumentStatus.uploading:
        return Icons.cloud_upload;
      case DocumentStatus.pending:
        return Icons.schedule;
      case DocumentStatus.approved:
        return Icons.check_circle;
      case DocumentStatus.rejected:
      case DocumentStatus.failed:
        return Icons.error;
    }
  }

  String _getStatusText(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.notUploaded:
        return 'Not Uploaded';
      case DocumentStatus.uploading:
        return 'Uploading...';
      case DocumentStatus.pending:
        return 'Under Review';
      case DocumentStatus.approved:
        return 'Verified';
      case DocumentStatus.rejected:
        return 'Rejected';
      case DocumentStatus.failed:
        return 'Upload Failed';
    }
  }
}

class DocumentType {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final bool isRequired;

  const DocumentType({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.isRequired,
  });
}

enum DocumentStatus {
  notUploaded,
  uploading,
  pending,
  approved,
  rejected,
  failed;

  static DocumentStatus fromString(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return DocumentStatus.pending;
      case 'approved':
      case 'verified':
        return DocumentStatus.approved;
      case 'rejected':
        return DocumentStatus.rejected;
      case 'uploading':
        return DocumentStatus.uploading;
      case 'failed':
        return DocumentStatus.failed;
      default:
        return DocumentStatus.notUploaded;
    }
  }
}
