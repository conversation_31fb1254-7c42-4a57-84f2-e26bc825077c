import 'package:freezed_annotation/freezed_annotation.dart';

part 'verification_models.freezed.dart';
part 'verification_models.g.dart';

// ignore_for_file: non_abstract_class_inherits_abstract_member

// Document Types
enum DocumentType {
  @JsonValue('AADHAR_CARD')
  aadharCard('AADHAR_CARD', 'Aadhar Card', '📄'),
  @JsonValue('PAN_CARD')
  panCard('PAN_CARD', 'PAN Card', '🆔'),
  @JsonValue('VOTER_ID')
  voterId('VOTER_ID', 'Voter ID', '🗳️'),
  @JsonValue('PASSPORT')
  passport('PASSPORT', 'Passport', '📘'),
  @JsonValue('DRIVING_LICENSE')
  drivingLicense('DRIVING_LICENSE', 'Driving License', '🚗'),
  @JsonValue('OTHER')
  other('OTHER', 'Other Document', '📋');

  const DocumentType(this.value, this.label, this.icon);
  final String value;
  final String label;
  final String icon;
}

// Document Status
enum DocumentStatus {
  @JsonValue('PENDING_REVIEW')
  pendingReview('PENDING_REVIEW', 'Pending Review', '⏳'),
  @JsonValue('APPROVED')
  approved('APPROVED', 'Approved', '✅'),
  @JsonValue('REJECTED')
  rejected('REJECTED', 'Rejected', '❌');

  const DocumentStatus(this.value, this.label, this.icon);
  final String value;
  final String label;
  final String icon;
}

// Verification Document Model
@freezed
class VerificationDocument with _$VerificationDocument {
  const factory VerificationDocument({
    required String id,
    required String userId,
    required DocumentType type,
    required String url,
    required String filename,
    required int filesize,
    required String mimeType,
    required DocumentStatus status,
    String? adminNotes,
    required DateTime uploadedAt,
    DateTime? reviewedAt,
    String? reviewedBy,
  }) = _VerificationDocument;

  factory VerificationDocument.fromJson(Map<String, dynamic> json) =>
      _$VerificationDocumentFromJson(json);
}

// Document Upload Request
@freezed
class DocumentUploadRequest with _$DocumentUploadRequest {
  const factory DocumentUploadRequest({
    required DocumentType documentType,
    required String filePath,
    required String filename,
  }) = _DocumentUploadRequest;

  factory DocumentUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentUploadRequestFromJson(json);
}

// Document Upload Response
@freezed
class DocumentUploadResponse with _$DocumentUploadResponse {
  const factory DocumentUploadResponse({
    required bool success,
    required String message,
    VerificationDocument? document,
    String? error,
  }) = _DocumentUploadResponse;

  factory DocumentUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentUploadResponseFromJson(json);
}

// Verification Status
@freezed
class VerificationStatus with _$VerificationStatus {
  const factory VerificationStatus({
    required bool isVerified,
    required String profileStatus,
    required List<VerificationDocument> documents,
    required int totalDocuments,
    required int approvedDocuments,
    required int pendingDocuments,
    required int rejectedDocuments,
    DateTime? lastUploadedAt,
    DateTime? lastReviewedAt,
  }) = _VerificationStatus;

  factory VerificationStatus.fromJson(Map<String, dynamic> json) =>
      _$VerificationStatusFromJson(json);
}

// Document Delete Request
@freezed
class DocumentDeleteRequest with _$DocumentDeleteRequest {
  const factory DocumentDeleteRequest({
    required String documentId,
  }) = _DocumentDeleteRequest;

  factory DocumentDeleteRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentDeleteRequestFromJson(json);
}

// Document Delete Response
@freezed
class DocumentDeleteResponse with _$DocumentDeleteResponse {
  const factory DocumentDeleteResponse({
    required bool success,
    required String message,
    String? error,
  }) = _DocumentDeleteResponse;

  factory DocumentDeleteResponse.fromJson(Map<String, dynamic> json) =>
      _$DocumentDeleteResponseFromJson(json);
}

// Verification Statistics
@freezed
class VerificationStats with _$VerificationStats {
  const factory VerificationStats({
    required int totalUploaded,
    required int approved,
    required int pending,
    required int rejected,
    required double completionPercentage,
    required List<DocumentType> missingDocuments,
    required List<DocumentType> requiredDocuments,
  }) = _VerificationStats;

  factory VerificationStats.fromJson(Map<String, dynamic> json) =>
      _$VerificationStatsFromJson(json);
}

// File Upload Progress
@freezed
class FileUploadProgress with _$FileUploadProgress {
  const factory FileUploadProgress({
    required String filename,
    required double progress,
    required bool isCompleted,
    required bool hasError,
    String? errorMessage,
  }) = _FileUploadProgress;

  factory FileUploadProgress.fromJson(Map<String, dynamic> json) =>
      _$FileUploadProgressFromJson(json);
}
