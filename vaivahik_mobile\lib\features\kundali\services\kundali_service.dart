import '../../../core/api/api_client.dart';
import '../models/kundali_models.dart';

class KundaliService {
  final ApiClient _apiClient;

  KundaliService(this._apiClient);

  // Generate kundali for a user
  Future<KundaliData> generateKundali({
    required String name,
    required DateTime birthDate,
    required String birthTime,
    required String birthPlace,
    required double latitude,
    required double longitude,
    required String timezone,
  }) async {
    try {
      final response = await _apiClient.post('/kundali/generate', {
        'name': name,
        'birthDate': birthDate.toIso8601String(),
        'birthTime': birthTime,
        'birthPlace': birthPlace,
        'latitude': latitude,
        'longitude': longitude,
        'timezone': timezone,
      });

      if (response['success'] == true) {
        return KundaliData.fromJson(response['kundali'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to generate kundali');
      }
    } catch (e) {
      throw Exception('Error generating kundali: $e');
    }
  }

  // Get user's kundali
  Future<KundaliData?> getUserKundali(String userId) async {
    try {
      final response = await _apiClient.get('/kundali/user/$userId');

      if (response['success'] == true && response['kundali'] != null) {
        return KundaliData.fromJson(response['kundali']);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get current user's kundali
  Future<KundaliData?> getMyKundali() async {
    try {
      final response = await _apiClient.get('/kundali/my');

      if (response['success'] == true && response['kundali'] != null) {
        return KundaliData.fromJson(response['kundali']);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Update kundali data
  Future<KundaliData> updateKundali(String kundaliId, Map<String, dynamic> updates) async {
    try {
      final response = await _apiClient.put('/kundali/$kundaliId', updates);

      if (response['success'] == true) {
        return KundaliData.fromJson(response['kundali'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update kundali');
      }
    } catch (e) {
      throw Exception('Error updating kundali: $e');
    }
  }

  // Perform kundali matching between two users
  Future<KundaliMatching> performKundaliMatching(String otherUserId) async {
    try {
      final response = await _apiClient.post('/kundali/match', {
        'otherUserId': otherUserId,
      });

      if (response['success'] == true) {
        return KundaliMatching.fromJson(response['matching'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to perform kundali matching');
      }
    } catch (e) {
      throw Exception('Error performing kundali matching: $e');
    }
  }

  // Get kundali matching result
  Future<KundaliMatching?> getKundaliMatching(String matchingId) async {
    try {
      final response = await _apiClient.get('/kundali/matching/$matchingId');

      if (response['success'] == true && response['matching'] != null) {
        return KundaliMatching.fromJson(response['matching']);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Get all kundali matchings for current user
  Future<List<KundaliMatching>> getMyKundaliMatchings({
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final response = await _apiClient.get('/kundali/matchings', queryParams: {
        'page': page,
        'limit': limit,
      });

      if (response['success'] == true) {
        final List<dynamic> matchingsData = response['matchings'] ?? response['data'] ?? [];
        return matchingsData.map((matching) => KundaliMatching.fromJson(matching)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch kundali matchings');
      }
    } catch (e) {
      throw Exception('Error fetching kundali matchings: $e');
    }
  }

  // Generate astrology report
  Future<AstrologyReport> generateAstrologyReport(String type, {Map<String, dynamic>? parameters}) async {
    try {
      final response = await _apiClient.post('/kundali/report', {
        'type': type,
        if (parameters != null) ...parameters,
      });

      if (response['success'] == true) {
        return AstrologyReport.fromJson(response['report'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to generate astrology report');
      }
    } catch (e) {
      throw Exception('Error generating astrology report: $e');
    }
  }

  // Get astrology reports
  Future<List<AstrologyReport>> getAstrologyReports({
    int page = 1,
    int limit = 20,
    String? type,
  }) async {
    try {
      final queryParams = {
        'page': page,
        'limit': limit,
        if (type != null) 'type': type,
      };

      final response = await _apiClient.get('/kundali/reports', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> reportsData = response['reports'] ?? response['data'] ?? [];
        return reportsData.map((report) => AstrologyReport.fromJson(report)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch astrology reports');
      }
    } catch (e) {
      throw Exception('Error fetching astrology reports: $e');
    }
  }

  // Get kundali settings
  Future<KundaliSettings> getKundaliSettings() async {
    try {
      final response = await _apiClient.get('/kundali/settings');

      if (response['success'] == true) {
        return KundaliSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch kundali settings');
      }
    } catch (e) {
      throw Exception('Error fetching kundali settings: $e');
    }
  }

  // Update kundali settings
  Future<KundaliSettings> updateKundaliSettings(KundaliSettings settings) async {
    try {
      final response = await _apiClient.put('/kundali/settings', settings.toJson());

      if (response['success'] == true) {
        return KundaliSettings.fromJson(response['settings'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update kundali settings');
      }
    } catch (e) {
      throw Exception('Error updating kundali settings: $e');
    }
  }

  // Check if kundali matching is available (premium feature)
  Future<bool> isKundaliMatchingAvailable() async {
    try {
      final response = await _apiClient.get('/kundali/availability');

      return response['success'] == true && response['available'] == true;
    } catch (e) {
      return false;
    }
  }

  // Get kundali compatibility score with another user
  Future<double> getCompatibilityScore(String otherUserId) async {
    try {
      final response = await _apiClient.get('/kundali/compatibility/$otherUserId');

      if (response['success'] == true) {
        return response['score']?.toDouble() ?? 0.0;
      } else {
        throw Exception(response['message'] ?? 'Failed to get compatibility score');
      }
    } catch (e) {
      throw Exception('Error getting compatibility score: $e');
    }
  }

  // Get dosha analysis
  Future<Map<String, dynamic>> getDoshaAnalysis(String kundaliId) async {
    try {
      final response = await _apiClient.get('/kundali/$kundaliId/doshas');

      if (response['success'] == true) {
        return response['analysis'] ?? response['data'] ?? {};
      } else {
        throw Exception(response['message'] ?? 'Failed to get dosha analysis');
      }
    } catch (e) {
      throw Exception('Error getting dosha analysis: $e');
    }
  }

  // Get planetary positions
  Future<Map<String, dynamic>> getPlanetaryPositions(String kundaliId) async {
    try {
      final response = await _apiClient.get('/kundali/$kundaliId/planets');

      if (response['success'] == true) {
        return response['positions'] ?? response['data'] ?? {};
      } else {
        throw Exception(response['message'] ?? 'Failed to get planetary positions');
      }
    } catch (e) {
      throw Exception('Error getting planetary positions: $e');
    }
  }

  // Get house analysis
  Future<Map<String, dynamic>> getHouseAnalysis(String kundaliId) async {
    try {
      final response = await _apiClient.get('/kundali/$kundaliId/houses');

      if (response['success'] == true) {
        return response['analysis'] ?? response['data'] ?? {};
      } else {
        throw Exception(response['message'] ?? 'Failed to get house analysis');
      }
    } catch (e) {
      throw Exception('Error getting house analysis: $e');
    }
  }

  // Generate kundali PDF
  Future<String> generateKundaliPDF(String kundaliId, {String language = 'en'}) async {
    try {
      final response = await _apiClient.get('/kundali/$kundaliId/pdf', queryParams: {
        'language': language,
      });

      if (response['success'] == true) {
        return response['downloadUrl'] ?? '';
      } else {
        throw Exception(response['message'] ?? 'Failed to generate kundali PDF');
      }
    } catch (e) {
      throw Exception('Error generating kundali PDF: $e');
    }
  }

  // Generate matching report PDF
  Future<String> generateMatchingReportPDF(String matchingId, {String language = 'en'}) async {
    try {
      final response = await _apiClient.get('/kundali/matching/$matchingId/pdf', queryParams: {
        'language': language,
      });

      if (response['success'] == true) {
        return response['downloadUrl'] ?? '';
      } else {
        throw Exception(response['message'] ?? 'Failed to generate matching report PDF');
      }
    } catch (e) {
      throw Exception('Error generating matching report PDF: $e');
    }
  }

  // Validate birth details for kundali generation
  Future<bool> validateBirthDetails({
    required DateTime birthDate,
    required String birthTime,
    required String birthPlace,
    required double latitude,
    required double longitude,
  }) async {
    try {
      final response = await _apiClient.post('/kundali/validate', {
        'birthDate': birthDate.toIso8601String(),
        'birthTime': birthTime,
        'birthPlace': birthPlace,
        'latitude': latitude,
        'longitude': longitude,
      });

      return response['success'] == true && response['valid'] == true;
    } catch (e) {
      return false;
    }
  }

  // Get birth place suggestions
  Future<List<Map<String, dynamic>>> getBirthPlaceSuggestions(String query) async {
    try {
      final response = await _apiClient.get('/kundali/places', queryParams: {
        'query': query,
      });

      if (response['success'] == true) {
        return List<Map<String, dynamic>>.from(response['places'] ?? response['data'] ?? []);
      } else {
        return [];
      }
    } catch (e) {
      return [];
    }
  }
}
