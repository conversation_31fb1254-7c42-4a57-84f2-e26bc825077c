import 'package:flutter/material.dart';

/// Responsive Helper - Adaptive design system for all screen sizes
/// Based on Material Design 3 breakpoints and website responsive patterns
class ResponsiveHelper {
  // Breakpoints - Material Design 3 standard
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 840;
  static const double desktopBreakpoint = 1200;
  static const double largeDesktopBreakpoint = 1600;
  
  // Screen Type Detection
  static ScreenType getScreenType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return ScreenType.mobile;
    } else if (width < tabletBreakpoint) {
      return ScreenType.tablet;
    } else if (width < desktopBreakpoint) {
      return ScreenType.desktop;
    } else {
      return ScreenType.largeDesktop;
    }
  }
  
  // Screen Size Checks
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < tabletBreakpoint;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= tabletBreakpoint;
  }
  
  // Responsive Values
  static T responsive<T>({
    required BuildContext context,
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    final screenType = getScreenType(context);
    
    switch (screenType) {
      case ScreenType.mobile:
        return mobile;
      case ScreenType.tablet:
        return tablet ?? mobile;
      case ScreenType.desktop:
        return desktop ?? tablet ?? mobile;
      case ScreenType.largeDesktop:
        return largeDesktop ?? desktop ?? tablet ?? mobile;
    }
  }
  
  // Responsive Padding
  static EdgeInsets responsivePadding(BuildContext context) {
    return responsive<EdgeInsets>(
      context: context,
      mobile: const EdgeInsets.all(16),
      tablet: const EdgeInsets.all(24),
      desktop: const EdgeInsets.all(32),
    );
  }
  
  // Responsive Margin
  static EdgeInsets responsiveMargin(BuildContext context) {
    return responsive<EdgeInsets>(
      context: context,
      mobile: const EdgeInsets.all(8),
      tablet: const EdgeInsets.all(12),
      desktop: const EdgeInsets.all(16),
    );
  }
  
  // Responsive Font Size
  static double responsiveFontSize(BuildContext context, double baseFontSize) {
    return responsive<double>(
      context: context,
      mobile: baseFontSize,
      tablet: baseFontSize * 1.1,
      desktop: baseFontSize * 1.2,
    );
  }
  
  // Responsive Grid Columns
  static int responsiveGridColumns(BuildContext context) {
    return responsive<int>(
      context: context,
      mobile: 1,
      tablet: 2,
      desktop: 3,
      largeDesktop: 4,
    );
  }
  
  // Responsive Card Width
  static double responsiveCardWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    return responsive<double>(
      context: context,
      mobile: screenWidth - 32, // Full width with padding
      tablet: (screenWidth - 48) / 2, // Two columns
      desktop: (screenWidth - 64) / 3, // Three columns
      largeDesktop: (screenWidth - 80) / 4, // Four columns
    );
  }
  
  // Responsive Container Max Width
  static double responsiveMaxWidth(BuildContext context) {
    return responsive<double>(
      context: context,
      mobile: double.infinity,
      tablet: 800,
      desktop: 1200,
      largeDesktop: 1400,
    );
  }
  
  // Responsive App Bar Height
  static double responsiveAppBarHeight(BuildContext context) {
    return responsive<double>(
      context: context,
      mobile: kToolbarHeight,
      tablet: kToolbarHeight + 8,
      desktop: kToolbarHeight + 16,
    );
  }
  
  // Responsive Bottom Navigation Height
  static double responsiveBottomNavHeight(BuildContext context) {
    return responsive<double>(
      context: context,
      mobile: 60,
      tablet: 70,
      desktop: 80,
    );
  }
}

// Screen Type Enum
enum ScreenType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

// Responsive Widget Builder
class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, ScreenType screenType) builder;
  
  const ResponsiveBuilder({
    super.key,
    required this.builder,
  });
  
  @override
  Widget build(BuildContext context) {
    final screenType = ResponsiveHelper.getScreenType(context);
    return builder(context, screenType);
  }
}

// Responsive Layout Widget
class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? largeDesktop;
  
  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });
  
  @override
  Widget build(BuildContext context) {
    return ResponsiveHelper.responsive<Widget>(
      context: context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      largeDesktop: largeDesktop,
    );
  }
}

// Responsive Grid View
class ResponsiveGridView extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final EdgeInsets padding;
  
  const ResponsiveGridView({
    super.key,
    required this.children,
    this.spacing = 16,
    this.runSpacing = 16,
    this.padding = const EdgeInsets.all(16),
  });
  
  @override
  Widget build(BuildContext context) {
    final columns = ResponsiveHelper.responsiveGridColumns(context);
    
    return Padding(
      padding: padding,
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: columns,
          crossAxisSpacing: spacing,
          mainAxisSpacing: runSpacing,
          childAspectRatio: 1.0,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}

// Responsive Container
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final double? maxWidth;
  
  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.maxWidth,
  });
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxWidth: maxWidth ?? ResponsiveHelper.responsiveMaxWidth(context),
      ),
      padding: padding ?? ResponsiveHelper.responsivePadding(context),
      margin: margin ?? ResponsiveHelper.responsiveMargin(context),
      child: child,
    );
  }
}

// Responsive Text
class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  
  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });
  
  @override
  Widget build(BuildContext context) {
    final responsiveStyle = style?.copyWith(
      fontSize: style?.fontSize != null
          ? ResponsiveHelper.responsiveFontSize(context, style!.fontSize!)
          : null,
    );
    
    return Text(
      text,
      style: responsiveStyle,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }
}

// Responsive Spacing
class ResponsiveSpacing {
  static double xs(BuildContext context) => ResponsiveHelper.responsive(
        context: context,
        mobile: 4,
        tablet: 6,
        desktop: 8,
      );
  
  static double sm(BuildContext context) => ResponsiveHelper.responsive(
        context: context,
        mobile: 8,
        tablet: 12,
        desktop: 16,
      );
  
  static double md(BuildContext context) => ResponsiveHelper.responsive(
        context: context,
        mobile: 16,
        tablet: 20,
        desktop: 24,
      );
  
  static double lg(BuildContext context) => ResponsiveHelper.responsive(
        context: context,
        mobile: 24,
        tablet: 32,
        desktop: 40,
      );
  
  static double xl(BuildContext context) => ResponsiveHelper.responsive(
        context: context,
        mobile: 32,
        tablet: 48,
        desktop: 64,
      );
}
