import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../api/api_client.dart';
import '../services/matching_service.dart';

/// 🔍 Advanced Search Service
/// Matches website's exact search functionality and backend API
/// Uses same filters as website registration form and search components

class AdvancedSearchFilters {
  // Basic filters (matching website's basic search)
  final String? gender;
  final int? minAge;
  final int? maxAge;
  final String? city;

  // Advanced filters (matching website's advanced search API)
  final String? religion;
  final String? caste;
  final String? subCaste;
  final List<String>? education;
  final List<String>? occupation;
  final String? incomeRange;
  final List<String>? maritalStatus;
  final double? heightFrom; // in feet
  final double? heightTo; // in feet
  final String? diet;
  final String? smoking;
  final String? drinking;
  final String? gotra;
  final String? manglik;

  // Additional website filters
  final String? motherTongue;
  final bool? withPhoto;
  final String? profileCreatedWithin;
  final String? nativePlace;

  const AdvancedSearchFilters({
    this.gender,
    this.minAge,
    this.maxAge,
    this.city,
    this.religion,
    this.caste,
    this.subCaste,
    this.education,
    this.occupation,
    this.incomeRange,
    this.maritalStatus,
    this.heightFrom,
    this.heightTo,
    this.diet,
    this.smoking,
    this.drinking,
    this.gotra,
    this.manglik,
    this.motherTongue,
    this.withPhoto,
    this.profileCreatedWithin,
    this.nativePlace,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = {};

    // Basic filters (matching website's backend API)
    if (gender != null) json['gender'] = gender;
    if (minAge != null) json['minAge'] = minAge;
    if (maxAge != null) json['maxAge'] = maxAge;
    if (city != null) json['city'] = city;

    // Advanced filters (matching website's advanced search API)
    if (religion != null) json['religion'] = religion;
    if (caste != null) json['caste'] = caste;
    if (subCaste != null) json['subCaste'] = subCaste;
    if (education != null) json['education'] = education;
    if (occupation != null) json['occupation'] = occupation;
    if (incomeRange != null) json['incomeRange'] = incomeRange;
    if (maritalStatus != null) json['maritalStatus'] = maritalStatus;
    if (heightFrom != null) json['heightFrom'] = heightFrom;
    if (heightTo != null) json['heightTo'] = heightTo;
    if (diet != null) json['diet'] = diet;
    if (smoking != null) json['smoking'] = smoking;
    if (drinking != null) json['drinking'] = drinking;
    if (gotra != null) json['gotra'] = gotra;
    if (manglik != null) json['manglik'] = manglik;

    // Additional website filters
    if (motherTongue != null) json['motherTongue'] = motherTongue;
    if (withPhoto != null) json['withPhoto'] = withPhoto;
    if (profileCreatedWithin != null) json['profileCreatedWithin'] = profileCreatedWithin;
    if (nativePlace != null) json['nativePlace'] = nativePlace;

    return json;
  }

  AdvancedSearchFilters copyWith({
    String? gender,
    int? minAge,
    int? maxAge,
    String? city,
    String? religion,
    String? caste,
    String? subCaste,
    List<String>? education,
    List<String>? occupation,
    String? incomeRange,
    List<String>? maritalStatus,
    double? heightFrom,
    double? heightTo,
    String? diet,
    String? smoking,
    String? drinking,
    String? gotra,
    String? manglik,
    String? motherTongue,
    bool? withPhoto,
    String? profileCreatedWithin,
    String? nativePlace,
  }) {
    return AdvancedSearchFilters(
      gender: gender ?? this.gender,
      minAge: minAge ?? this.minAge,
      maxAge: maxAge ?? this.maxAge,
      city: city ?? this.city,
      religion: religion ?? this.religion,
      caste: caste ?? this.caste,
      subCaste: subCaste ?? this.subCaste,
      education: education ?? this.education,
      occupation: occupation ?? this.occupation,
      incomeRange: incomeRange ?? this.incomeRange,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      heightFrom: heightFrom ?? this.heightFrom,
      heightTo: heightTo ?? this.heightTo,
      diet: diet ?? this.diet,
      smoking: smoking ?? this.smoking,
      drinking: drinking ?? this.drinking,
      gotra: gotra ?? this.gotra,
      manglik: manglik ?? this.manglik,
      motherTongue: motherTongue ?? this.motherTongue,
      withPhoto: withPhoto ?? this.withPhoto,
      profileCreatedWithin: profileCreatedWithin ?? this.profileCreatedWithin,
      nativePlace: nativePlace ?? this.nativePlace,
    );
  }
}

class SavedSearch {
  final String id;
  final String name;
  final AdvancedSearchFilters filters;
  final DateTime createdAt;
  final DateTime lastUsed;
  final int resultCount;
  final bool isActive;

  SavedSearch({
    required this.id,
    required this.name,
    required this.filters,
    required this.createdAt,
    required this.lastUsed,
    required this.resultCount,
    this.isActive = true,
  });

  factory SavedSearch.fromJson(Map<String, dynamic> json) {
    return SavedSearch(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      filters: const AdvancedSearchFilters(), // Parse from json['filters']
      createdAt: DateTime.parse(json['createdAt']),
      lastUsed: DateTime.parse(json['lastUsed']),
      resultCount: json['resultCount'] ?? 0,
      isActive: json['isActive'] ?? true,
    );
  }
}

class AdvancedSearchService {
  final ApiClient _apiClient = ApiClient();

  // Perform advanced search using website's exact backend API
  Future<List<MatchResult>> advancedSearch({
    required AdvancedSearchFilters filters,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
        ...filters.toJson().map((k, v) => MapEntry(k, v?.toString())),
      };

      // Use website's exact API endpoint
      final response = await _apiClient.get('/users/advanced-search', queryParams: queryParams);

      if (response['success'] == true) {
        final profiles = response['profiles'] as List;
        return profiles.map((profile) => MatchResult.fromJson(profile)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to perform advanced search');
      }
    } catch (e) {
      throw Exception('Error performing advanced search: $e');
    }
  }

  // Basic search functionality matching website's API
  Future<List<MatchResult>> basicSearch({
    String? gender,
    int? minAge,
    int? maxAge,
    String? city,
    int page = 1,
    int limit = 20,
  }) async {
    try {
      final queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
        if (gender != null) 'gender': gender,
        if (minAge != null) 'minAge': minAge.toString(),
        if (maxAge != null) 'maxAge': maxAge.toString(),
        if (city != null) 'city': city,
      };

      // Use website's exact API endpoint
      final response = await _apiClient.get('/users/search', queryParams: queryParams);

      if (response['success'] == true) {
        final profiles = response['profiles'] as List;
        return profiles.map((profile) => MatchResult.fromJson(profile)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to perform basic search');
      }
    } catch (e) {
      throw Exception('Error performing basic search: $e');
    }
  }

  // Get search suggestions based on partial input
  Future<Map<String, List<String>>> getSearchSuggestions(String query) async {
    try {
      final response = await _apiClient.get('/search/suggestions', queryParams: {
        'query': query,
      });
      
      if (response['success'] == true) {
        final suggestions = response['suggestions'] as Map<String, dynamic>;
        return suggestions.map((k, v) => MapEntry(k, List<String>.from(v)));
      } else {
        return {};
      }
    } catch (e) {
      return {};
    }
  }

  // Save search for future use
  Future<bool> saveSearch({
    required String name,
    required AdvancedSearchFilters filters,
  }) async {
    try {
      final response = await _apiClient.post('/search/save', {
        'name': name,
        'filters': filters.toJson(),
      });
      
      return response['success'] == true;
    } catch (e) {
      throw Exception('Error saving search: $e');
    }
  }

  // Get saved searches
  Future<List<SavedSearch>> getSavedSearches() async {
    try {
      final response = await _apiClient.get('/search/saved');
      
      if (response['success'] == true) {
        final searches = response['searches'] as List;
        return searches.map((search) => SavedSearch.fromJson(search)).toList();
      } else {
        return [];
      }
    } catch (e) {
      throw Exception('Error getting saved searches: $e');
    }
  }

  // Delete saved search
  Future<bool> deleteSavedSearch(String searchId) async {
    try {
      final response = await _apiClient.delete('/search/saved/$searchId');
      return response['success'] == true;
    } catch (e) {
      throw Exception('Error deleting saved search: $e');
    }
  }

  // Get filter options (for dropdowns, etc.)
  Future<Map<String, List<String>>> getFilterOptions() async {
    try {
      final response = await _apiClient.get('/search/filter-options');
      
      if (response['success'] == true) {
        final options = response['options'] as Map<String, dynamic>;
        return options.map((k, v) => MapEntry(k, List<String>.from(v)));
      } else {
        return {};
      }
    } catch (e) {
      throw Exception('Error getting filter options: $e');
    }
  }
}

// Riverpod providers
final advancedSearchServiceProvider = Provider<AdvancedSearchService>((ref) {
  return AdvancedSearchService();
});

final advancedSearchProvider = FutureProvider.family<List<MatchResult>, Map<String, dynamic>>((ref, params) async {
  final searchService = ref.read(advancedSearchServiceProvider);
  final filters = params['filters'] as AdvancedSearchFilters;
  final page = params['page'] as int? ?? 1;
  final limit = params['limit'] as int? ?? 20;
  
  return searchService.advancedSearch(
    filters: filters,
    page: page,
    limit: limit,
  );
});

final savedSearchesProvider = FutureProvider<List<SavedSearch>>((ref) async {
  final searchService = ref.read(advancedSearchServiceProvider);
  return searchService.getSavedSearches();
});

final searchSuggestionsProvider = FutureProvider.family<Map<String, List<String>>, String>((ref, query) async {
  final searchService = ref.read(advancedSearchServiceProvider);
  return searchService.getSearchSuggestions(query);
});

final filterOptionsProvider = FutureProvider<Map<String, List<String>>>((ref) async {
  final searchService = ref.read(advancedSearchServiceProvider);
  return searchService.getFilterOptions();
});
