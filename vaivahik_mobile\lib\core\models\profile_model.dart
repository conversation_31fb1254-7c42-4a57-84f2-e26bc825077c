class ProfileModel {
  final String id;
  final String userId;
  final String? fullName;
  final String? gender;
  final String? profileFor;
  final DateTime? dateOfBirth;
  final String? birthTime;
  final String? birthPlace;
  final String? height;
  final String? city;
  final String? state;
  final String? country;
  final String? nativePlace;
  final String? education;
  final String? occupation;
  final String? incomeRange;
  final String? workLocation;
  final String? maritalStatus;
  final String? religion;
  final String? caste;
  final String? subCaste;
  final String? motherTongue;
  final String? complexion;
  final String? bloodGroup;
  final String? diet;
  final String? drinking;
  final String? smoking;
  final String? exerciseHabits;
  final String? fatherName;
  final String? motherName;
  final String? uncleName;
  final String? familyType;
  final String? familyValues;
  final int? totalSiblings;
  final int? marriedSiblings;
  final String? familyContact;
  final String? aboutMe;
  final String? partnerPreferences;
  final String? hobbies;
  final String? interests;
  final double? latitude;
  final double? longitude;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ProfileModel({
    required this.id,
    required this.userId,
    this.fullName,
    this.gender,
    this.profileFor,
    this.dateOfBirth,
    this.birthTime,
    this.birthPlace,
    this.height,
    this.city,
    this.state,
    this.country,
    this.nativePlace,
    this.education,
    this.occupation,
    this.incomeRange,
    this.workLocation,
    this.maritalStatus,
    this.religion,
    this.caste,
    this.subCaste,
    this.motherTongue,
    this.complexion,
    this.bloodGroup,
    this.diet,
    this.drinking,
    this.smoking,
    this.exerciseHabits,
    this.fatherName,
    this.motherName,
    this.uncleName,
    this.familyType,
    this.familyValues,
    this.totalSiblings,
    this.marriedSiblings,
    this.familyContact,
    this.aboutMe,
    this.partnerPreferences,
    this.hobbies,
    this.interests,
    this.latitude,
    this.longitude,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      fullName: json['fullName'],
      gender: json['gender'],
      profileFor: json['profileFor'],
      dateOfBirth: json['dateOfBirth'] != null 
          ? DateTime.parse(json['dateOfBirth']) 
          : null,
      birthTime: json['birthTime'],
      birthPlace: json['birthPlace'],
      height: json['height'],
      city: json['city'],
      state: json['state'],
      country: json['country'],
      nativePlace: json['nativePlace'],
      education: json['education'],
      occupation: json['occupation'],
      incomeRange: json['incomeRange'],
      workLocation: json['workLocation'],
      maritalStatus: json['maritalStatus'],
      religion: json['religion'],
      caste: json['caste'],
      subCaste: json['subCaste'],
      motherTongue: json['motherTongue'],
      complexion: json['complexion'],
      bloodGroup: json['bloodGroup'],
      diet: json['diet'],
      drinking: json['drinking'],
      smoking: json['smoking'],
      exerciseHabits: json['exerciseHabits'],
      fatherName: json['fatherName'],
      motherName: json['motherName'],
      uncleName: json['uncleName'],
      familyType: json['familyType'],
      familyValues: json['familyValues'],
      totalSiblings: json['totalSiblings'],
      marriedSiblings: json['marriedSiblings'],
      familyContact: json['familyContact'],
      aboutMe: json['aboutMe'],
      partnerPreferences: json['partnerPreferences'],
      hobbies: json['hobbies'],
      interests: json['interests'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'fullName': fullName,
      'gender': gender,
      'profileFor': profileFor,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'birthTime': birthTime,
      'birthPlace': birthPlace,
      'height': height,
      'city': city,
      'state': state,
      'country': country,
      'nativePlace': nativePlace,
      'education': education,
      'occupation': occupation,
      'incomeRange': incomeRange,
      'workLocation': workLocation,
      'maritalStatus': maritalStatus,
      'religion': religion,
      'caste': caste,
      'subCaste': subCaste,
      'motherTongue': motherTongue,
      'complexion': complexion,
      'bloodGroup': bloodGroup,
      'diet': diet,
      'drinking': drinking,
      'smoking': smoking,
      'exerciseHabits': exerciseHabits,
      'fatherName': fatherName,
      'motherName': motherName,
      'uncleName': uncleName,
      'familyType': familyType,
      'familyValues': familyValues,
      'totalSiblings': totalSiblings,
      'marriedSiblings': marriedSiblings,
      'familyContact': familyContact,
      'aboutMe': aboutMe,
      'partnerPreferences': partnerPreferences,
      'hobbies': hobbies,
      'interests': interests,
      'latitude': latitude,
      'longitude': longitude,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Helper methods
  int get age {
    if (dateOfBirth != null) {
      final now = DateTime.now();
      final age = now.year - dateOfBirth!.year;
      if (now.month < dateOfBirth!.month || 
          (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
        return age - 1;
      }
      return age;
    }
    return 0;
  }

  String get displayLocation {
    if (city != null && state != null) {
      return '$city, $state';
    } else if (city != null) {
      return city!;
    } else if (state != null) {
      return state!;
    }
    return 'Location not specified';
  }

  List<String> get hobbiesList {
    if (hobbies != null && hobbies!.isNotEmpty) {
      return hobbies!.split(',').map((e) => e.trim()).toList();
    }
    return [];
  }

  List<String> get interestsList {
    if (interests != null && interests!.isNotEmpty) {
      return interests!.split(',').map((e) => e.trim()).toList();
    }
    return [];
  }

  bool get hasBasicDetails {
    return fullName != null && 
           gender != null && 
           dateOfBirth != null && 
           height != null;
  }

  bool get hasEducationCareer {
    return education != null && occupation != null;
  }

  bool get hasLocationDetails {
    return city != null && state != null;
  }

  bool get hasFamilyDetails {
    return fatherName != null && motherName != null;
  }

  bool get hasAboutMe {
    return aboutMe != null && aboutMe!.isNotEmpty;
  }

  int get completionPercentage {
    int completedSections = 0;
    int totalSections = 6;

    if (hasBasicDetails) completedSections++;
    if (hasEducationCareer) completedSections++;
    if (hasLocationDetails) completedSections++;
    if (hasFamilyDetails) completedSections++;
    if (hasAboutMe) completedSections++;
    if (diet != null || drinking != null || smoking != null) completedSections++;

    return ((completedSections / totalSections) * 100).round();
  }
}

class PhotoModel {
  final String id;
  final String userId;
  final String url;
  final String visibility;
  final bool isProfilePic;
  final String status;
  final DateTime uploadedAt;

  const PhotoModel({
    required this.id,
    required this.userId,
    required this.url,
    required this.visibility,
    required this.isProfilePic,
    required this.status,
    required this.uploadedAt,
  });

  factory PhotoModel.fromJson(Map<String, dynamic> json) {
    return PhotoModel(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      url: json['url'] ?? '',
      visibility: json['visibility'] ?? 'PUBLIC',
      isProfilePic: json['isProfilePic'] ?? false,
      status: json['status'] ?? 'ACTIVE',
      uploadedAt: json['uploadedAt'] != null 
          ? DateTime.parse(json['uploadedAt']) 
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'url': url,
      'visibility': visibility,
      'isProfilePic': isProfilePic,
      'status': status,
      'uploadedAt': uploadedAt.toIso8601String(),
    };
  }

  static PhotoModel empty() {
    return PhotoModel(
      id: '',
      userId: '',
      url: '',
      visibility: 'PUBLIC',
      isProfilePic: false,
      status: 'ACTIVE',
      uploadedAt: DateTime.now(),
    );
  }
}
