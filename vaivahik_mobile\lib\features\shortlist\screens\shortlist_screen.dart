import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../app/theme.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../../../core/widgets/empty_state_widget.dart';
import '../providers/shortlist_provider.dart';
import '../models/shortlist_model.dart';
import '../widgets/shortlist_card.dart';
import '../widgets/shortlist_stats_card.dart';
import '../widgets/shortlist_filters_dialog.dart';
import '../widgets/add_note_dialog.dart';

/// 📋 SHORTLIST SCREEN - Main Shortlist Management Interface
/// Features: View/Manage Shortlisted Profiles, Filters, Stats, Actions
class ShortlistScreen extends ConsumerStatefulWidget {
  const ShortlistScreen({super.key});

  @override
  ConsumerState<ShortlistScreen> createState() => _ShortlistScreenState();
}

class _ShortlistScreenState extends ConsumerState<ShortlistScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _tabController = TabController(length: 4, vsync: this);
    
    // Load initial data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(shortlistProvider.notifier).loadShortlist(refresh: true);
      ref.read(shortlistStatsProvider.notifier).loadStats();
    });
    
    // Setup scroll listener for pagination
    _scrollController.addListener(_onScroll);
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      ref.read(shortlistProvider.notifier).loadShortlist();
    }
  }

  @override
  Widget build(BuildContext context) {
    final shortlistState = ref.watch(shortlistProvider);
    final statsAsync = ref.watch(shortlistStatsProvider);
    
    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          _buildAppBar(innerBoxIsScrolled),
          _buildStatsSection(statsAsync),
          _buildTabBar(),
        ],
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildAllTab(shortlistState),
            _buildInterestSentTab(shortlistState),
            _buildNoInterestTab(shortlistState),
            _buildMutualInterestTab(shortlistState),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  SliverAppBar _buildAppBar(bool innerBoxIsScrolled) {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'My Shortlist',
          style: AppTextStyles.h2.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primary,
                AppColors.primary.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Stack(
            children: [
              Positioned(
                right: -50,
                top: -50,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                  ),
                ),
              ),
              Positioned(
                left: -30,
                bottom: -30,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.05),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFiltersDialog,
        ),
        IconButton(
          icon: const Icon(Icons.sort),
          onPressed: _showSortDialog,
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'refresh',
              child: ListTile(
                leading: Icon(Icons.refresh),
                title: Text('Refresh'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('Export'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'clear_all',
              child: ListTile(
                leading: Icon(Icons.clear_all),
                title: Text('Clear All'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatsSection(AsyncValue<ShortlistStats> statsAsync) {
    return SliverToBoxAdapter(
      child: Container(
        margin: const EdgeInsets.all(16),
        child: statsAsync.when(
          data: (stats) => ShortlistStatsCard(stats: stats),
          loading: () => const LoadingWidget(size: 40),
          error: (error, _) => CustomErrorWidget(
            message: error.toString(),
            onRetry: () => ref.read(shortlistStatsProvider.notifier).loadStats(),
          ),
        ),
      ).animate(controller: _animationController)
       .slideX(begin: -1, delay: 200.ms, curve: Curves.elasticOut)
       .fadeIn(delay: 200.ms),
    );
  }

  Widget _buildTabBar() {
    return SliverPersistentHeader(
      pinned: true,
      delegate: _TabBarDelegate(
        TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          indicatorWeight: 3,
          labelStyle: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: AppTextStyles.bodyMedium,
          tabs: const [
            Tab(text: 'All'),
            Tab(text: 'Interest Sent'),
            Tab(text: 'No Interest'),
            Tab(text: 'Mutual'),
          ],
        ),
      ),
    );
  }

  Widget _buildAllTab(ShortlistUIState state) {
    return _buildShortlistContent(state, ShortlistViewMode.all);
  }

  Widget _buildInterestSentTab(ShortlistUIState state) {
    return _buildShortlistContent(state, ShortlistViewMode.interestSent);
  }

  Widget _buildNoInterestTab(ShortlistUIState state) {
    return _buildShortlistContent(state, ShortlistViewMode.noInterest);
  }

  Widget _buildMutualInterestTab(ShortlistUIState state) {
    return _buildShortlistContent(state, ShortlistViewMode.mutualInterest);
  }

  Widget _buildShortlistContent(ShortlistUIState state, ShortlistViewMode mode) {
    if (state.isRefreshing) {
      return const Center(child: LoadingWidget());
    }

    if (state.error != null) {
      return CustomErrorWidget(
        message: state.error!,
        onRetry: () => ref.read(shortlistProvider.notifier).refreshShortlist(),
      );
    }

    final filteredItems = _getFilteredItems(state.items, mode);

    if (filteredItems.isEmpty) {
      return EmptyStateWidget(
        title: _getEmptyTitle(mode),
        message: _getEmptySubtitle(mode),
        icon: Icons.favorite_border,
        actionText: 'Browse Profiles',
        onAction: () => context.go('/search'),
      );
    }

    return RefreshIndicator(
      onRefresh: () => ref.read(shortlistProvider.notifier).refreshShortlist(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredItems.length + (state.isLoading ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= filteredItems.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: LoadingWidget(size: 40),
              ),
            );
          }

          final item = filteredItems[index];
          return ShortlistCard(
            item: item,
            onTap: () => _viewProfile(item),
            onRemove: () => _removeFromShortlist(item),
            onEditNote: () => _editNote(item),
            onSendInterest: () => _sendInterest(item),
            onCall: () => _callProfile(item),
          ).animate(delay: (index * 100).ms)
           .slideX(begin: 1, curve: Curves.easeOut)
           .fadeIn();
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () => context.go('/search'),
      backgroundColor: AppColors.primary,
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('Add More'),
    ).animate(controller: _animationController)
     .scale(delay: 800.ms, curve: Curves.elasticOut)
     .fadeIn(delay: 800.ms);
  }

  List<ShortlistItem> _getFilteredItems(List<ShortlistItem> items, ShortlistViewMode mode) {
    switch (mode) {
      case ShortlistViewMode.all:
        return items;
      case ShortlistViewMode.interestSent:
        return items.where((item) => item.interestSent).toList();
      case ShortlistViewMode.noInterest:
        return items.where((item) => !item.interestSent).toList();
      case ShortlistViewMode.mutualInterest:
        return items.where((item) => item.hasMutualInterest).toList();
    }
  }

  String _getEmptyTitle(ShortlistViewMode mode) {
    switch (mode) {
      case ShortlistViewMode.all:
        return 'No Shortlisted Profiles';
      case ShortlistViewMode.interestSent:
        return 'No Interests Sent';
      case ShortlistViewMode.noInterest:
        return 'All Interests Sent';
      case ShortlistViewMode.mutualInterest:
        return 'No Mutual Interests';
    }
  }

  String _getEmptySubtitle(ShortlistViewMode mode) {
    switch (mode) {
      case ShortlistViewMode.all:
        return 'Start browsing profiles and add interesting ones to your shortlist';
      case ShortlistViewMode.interestSent:
        return 'Send interests to your shortlisted profiles to connect';
      case ShortlistViewMode.noInterest:
        return 'Great! You\'ve sent interests to all your shortlisted profiles';
      case ShortlistViewMode.mutualInterest:
        return 'When someone accepts your interest, they\'ll appear here';
    }
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => ShortlistFiltersDialog(
        currentFilters: ref.read(shortlistProvider).filters,
        onApply: (filters) {
          ref.read(shortlistProvider.notifier).applyFilters(filters);
        },
        onClear: () {
          ref.read(shortlistProvider.notifier).clearFilters();
        },
      ),
    );
  }

  void _showSortDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort By'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: ShortlistSortOption.values.map((option) {
            return RadioListTile<String>(
              title: Text(option.label),
              value: option.value,
              groupValue: ref.read(shortlistProvider).sortBy,
              onChanged: (value) {
                if (value != null) {
                  ref.read(shortlistProvider.notifier).updateSort(value, false);
                  Navigator.pop(context);
                }
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'refresh':
        ref.read(shortlistProvider.notifier).refreshShortlist();
        ref.read(shortlistStatsProvider.notifier).refreshStats();
        break;
      case 'export':
        _exportShortlist();
        break;
      case 'clear_all':
        _showClearAllDialog();
        break;
    }
  }

  void _viewProfile(ShortlistItem item) {
    context.go('/profile/${item.profile.id}');
  }

  void _removeFromShortlist(ShortlistItem item) {
    ref.read(shortlistProvider.notifier).removeFromShortlist(item.id);
  }

  void _editNote(ShortlistItem item) {
    showDialog(
      context: context,
      builder: (context) => AddNoteDialog(
        initialNote: item.note,
        onSave: (note) {
          ref.read(shortlistProvider.notifier).updateNote(item.id, note);
        },
      ),
    );
  }

  void _sendInterest(ShortlistItem item) {
    ref.read(shortlistProvider.notifier).sendInterest(item.profile.id);
  }

  void _callProfile(ShortlistItem item) {
    // Implement calling functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Calling ${item.fullName}...')),
    );
  }

  void _exportShortlist() {
    // Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Exporting shortlist...')),
    );
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Shortlist'),
        content: const Text('Are you sure you want to remove all profiles from your shortlist?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Implement clear all functionality
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

class _TabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar tabBar;

  _TabBarDelegate(this.tabBar);

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: Colors.white,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) => false;
}
