import 'dart:async';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'storage_service.dart';
import '../api/api_client.dart';

class ChatMessage {
  final String id;
  final String senderId;
  final String receiverId;
  final String message;
  final String type; // text, image, voice, video
  final DateTime timestamp;
  final bool isRead;
  final bool isDelivered;
  final Map<String, dynamic>? metadata;
  final String? replyToId;
  final bool isDeleted;
  final DateTime? editedAt;

  ChatMessage({
    required this.id,
    required this.senderId,
    required this.receiverId,
    required this.message,
    this.type = 'text',
    required this.timestamp,
    this.isRead = false,
    this.isDelivered = false,
    this.metadata,
    this.replyToId,
    this.isDeleted = false,
    this.editedAt,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] ?? '',
      senderId: json['senderId'] ?? '',
      receiverId: json['receiverId'] ?? '',
      message: json['message'] ?? json['content'] ?? '',
      type: json['type'] ?? 'text',
      timestamp: DateTime.parse(json['timestamp']),
      isRead: json['isRead'] ?? false,
      isDelivered: json['isDelivered'] ?? false,
      metadata: json['metadata'],
      replyToId: json['replyToId'],
      isDeleted: json['isDeleted'] ?? false,
      editedAt: json['editedAt'] != null ? DateTime.parse(json['editedAt']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'senderId': senderId,
      'receiverId': receiverId,
      'message': message,
      'content': message, // For API compatibility
      'type': type,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
      'isDelivered': isDelivered,
      'metadata': metadata,
      'replyToId': replyToId,
      'isDeleted': isDeleted,
      'editedAt': editedAt?.toIso8601String(),
    };
  }
}

class ChatRoom {
  final String id;
  final List<String> participants;
  final ChatMessage? lastMessage;
  final int unreadCount;
  final DateTime lastActivity;
  final bool isActive;
  final Map<String, dynamic>? settings;
  final bool isPremiumChat;

  const ChatRoom({
    required this.id,
    required this.participants,
    this.lastMessage,
    this.unreadCount = 0,
    required this.lastActivity,
    this.isActive = true,
    this.settings,
    this.isPremiumChat = false,
  });

  factory ChatRoom.fromJson(Map<String, dynamic> json) {
    return ChatRoom(
      id: json['id'] ?? '',
      participants: List<String>.from(json['participants'] ?? []),
      lastMessage: json['lastMessage'] != null
          ? ChatMessage.fromJson(json['lastMessage'])
          : null,
      unreadCount: json['unreadCount'] ?? 0,
      lastActivity: DateTime.parse(json['lastActivity']),
      isActive: json['isActive'] ?? true,
      settings: json['settings'],
      isPremiumChat: json['isPremiumChat'] ?? false,
    );
  }
}

class TypingIndicator {
  final String userId;
  final String roomId;
  final DateTime timestamp;
  final bool isTyping;

  const TypingIndicator({
    required this.userId,
    required this.roomId,
    required this.timestamp,
    this.isTyping = true,
  });

  factory TypingIndicator.fromJson(Map<String, dynamic> json) {
    return TypingIndicator(
      userId: json['userId'] ?? '',
      roomId: json['roomId'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      isTyping: json['isTyping'] ?? true,
    );
  }
}

class OnlineStatus {
  final String userId;
  final bool isOnline;
  final DateTime lastSeen;

  const OnlineStatus({
    required this.userId,
    required this.isOnline,
    required this.lastSeen,
  });

  factory OnlineStatus.fromJson(Map<String, dynamic> json) {
    return OnlineStatus(
      userId: json['userId'] ?? '',
      isOnline: json['isOnline'] ?? false,
      lastSeen: DateTime.parse(json['lastSeen']),
    );
  }
}

class ChatService {
  static io.Socket? _socket;
  static String? _currentUserId;
  static final List<ChatMessage> _messages = [];
  static final List<Function(ChatMessage)> _messageListeners = [];
  static final List<Function(String, bool)> _typingListeners = [];
  static final List<Function(String, bool)> _onlineStatusListeners = [];

  static Future<void> initialize() async {
    try {
      final token = await StorageService.getSecureString(StorageService.keyAuthToken);
      final userId = StorageService.getString(StorageService.keyUserId);
      
      if (token == null || userId == null) {
        throw Exception('User not authenticated');
      }

      _currentUserId = userId;

      _socket = io.io('http://localhost:8000', <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
        'auth': {
          'token': token,
          'userId': userId,
        },
      });

      _setupSocketListeners();
      _socket!.connect();
    } catch (e) {
      print('Chat service initialization failed: $e');
    }
  }

  static void _setupSocketListeners() {
    _socket!.on('connect', (_) {
      print('Connected to chat server');
    });

    _socket!.on('disconnect', (_) {
      print('Disconnected from chat server');
    });

    // Enhanced message handling matching website's chat handler
    _socket!.on('newMessage', (data) {
      final message = ChatMessage.fromJson(data);
      _messages.add(message);

      // Notify all listeners
      for (var listener in _messageListeners) {
        listener(message);
      }
    });

    _socket!.on('message', (data) {
      final message = ChatMessage.fromJson(data);
      _messages.add(message);

      // Notify all listeners
      for (final listener in _messageListeners) {
        listener(message);
      }
    });

    _socket!.on('typing', (data) {
      final userId = data['userId'] as String;
      final isTyping = data['isTyping'] as bool;
      
      for (final listener in _typingListeners) {
        listener(userId, isTyping);
      }
    });

    _socket!.on('user_online', (data) {
      final userId = data['userId'] as String;
      
      for (final listener in _onlineStatusListeners) {
        listener(userId, true);
      }
    });

    _socket!.on('user_offline', (data) {
      final userId = data['userId'] as String;
      
      for (final listener in _onlineStatusListeners) {
        listener(userId, false);
      }
    });

    // Additional Socket.IO events matching website's chat handler
    _socket!.on('stopTyping', (data) {
      final userId = data['userId'] as String;

      for (final listener in _typingListeners) {
        listener(userId, false);
      }
    });

    _socket!.on('messageRead', (data) {
      // Handle read receipts
      print('Message read: $data');
    });

    _socket!.on('messageDelivered', (data) {
      // Handle delivery receipts
      print('Message delivered: $data');
    });

    _socket!.on('userOnline', (data) {
      final userId = data['userId'] as String;

      for (final listener in _onlineStatusListeners) {
        listener(userId, true);
      }
    });

    _socket!.on('userOffline', (data) {
      final userId = data['userId'] as String;

      for (final listener in _onlineStatusListeners) {
        listener(userId, false);
      }
    });

    _socket!.on('error', (error) {
      print('Socket error: $error');
    });
  }

  static void sendMessage({
    required String receiverId,
    required String message,
    String type = 'text',
    Map<String, dynamic>? metadata,
    String? replyToId,
  }) {
    if (_socket == null || !_socket!.connected) {
      throw Exception('Not connected to chat server');
    }

    final messageData = {
      'receiverId': receiverId,
      'message': message,
      'content': message, // For API compatibility
      'type': type,
      'metadata': metadata,
      'replyToId': replyToId,
      'timestamp': DateTime.now().toIso8601String(),
    };

    // Use website's Socket.IO event name
    _socket!.emit('sendMessage', messageData);
  }

  static void joinRoom(String roomId) {
    if (_socket == null || !_socket!.connected) return;
    _socket!.emit('join_room', {'roomId': roomId});
  }

  static void leaveRoom(String roomId) {
    if (_socket == null || !_socket!.connected) return;
    _socket!.emit('leave_room', {'roomId': roomId});
  }

  static void sendTypingIndicator(String receiverId, bool isTyping) {
    if (_socket == null || !_socket!.connected) return;

    _socket!.emit(isTyping ? 'typing' : 'stopTyping', {
      'receiverId': receiverId,
    });
  }

  // Mark messages as read
  static void markAsRead({
    required String otherUserId,
    required List<String> messageIds,
  }) {
    if (_socket == null || !_socket!.connected) return;

    _socket!.emit('markAsRead', {
      'otherUserId': otherUserId,
      'messageIds': messageIds,
    });
  }

  // Get chat history using API
  static Future<List<ChatMessage>> getChatHistory({
    required String otherUserId,
    int page = 1,
    int limit = 50,
  }) async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/chat/history/$otherUserId', queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      });

      if (response['success'] == true) {
        final messages = response['messages'] as List;
        return messages.map((msg) => ChatMessage.fromJson(msg)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting chat history: $e');
      return [];
    }
  }

  // Get chat rooms using API
  static Future<List<ChatRoom>> getChatRooms() async {
    try {
      final apiClient = ApiClient();
      final response = await apiClient.get('/chat/rooms');

      if (response['success'] == true) {
        final rooms = response['rooms'] as List;
        return rooms.map((room) => ChatRoom.fromJson(room)).toList();
      }
      return [];
    } catch (e) {
      print('Error getting chat rooms: $e');
      return [];
    }
  }

  static void markMessageAsRead(String messageId) {
    if (_socket == null || !_socket!.connected) return;
    
    _socket!.emit('mark_read', {'messageId': messageId});
  }

  static void addMessageListener(Function(ChatMessage) listener) {
    _messageListeners.add(listener);
  }

  static void removeMessageListener(Function(ChatMessage) listener) {
    _messageListeners.remove(listener);
  }

  static void addTypingListener(Function(String, bool) listener) {
    _typingListeners.add(listener);
  }

  static void removeTypingListener(Function(String, bool) listener) {
    _typingListeners.remove(listener);
  }

  static void addOnlineStatusListener(Function(String, bool) listener) {
    _onlineStatusListeners.add(listener);
  }

  static void removeOnlineStatusListener(Function(String, bool) listener) {
    _onlineStatusListeners.remove(listener);
  }

  static List<ChatMessage> getMessages() {
    return List.from(_messages);
  }

  static void disconnect() {
    _socket?.disconnect();
    _socket = null;
    _currentUserId = null;
    _messages.clear();
    _messageListeners.clear();
    _typingListeners.clear();
    _onlineStatusListeners.clear();
  }

  static bool get isConnected => _socket?.connected ?? false;
  static String? get currentUserId => _currentUserId;
}

// Riverpod providers for chat functionality
final chatServiceProvider = Provider<ChatService>((ref) {
  return ChatService();
});

final chatMessagesProvider = StateNotifierProvider<ChatMessagesNotifier, List<ChatMessage>>((ref) {
  return ChatMessagesNotifier();
});

class ChatMessagesNotifier extends StateNotifier<List<ChatMessage>> {
  ChatMessagesNotifier() : super([]) {
    ChatService.addMessageListener(_onNewMessage);
  }

  void _onNewMessage(ChatMessage message) {
    state = [...state, message];
  }

  void sendMessage({
    required String receiverId,
    required String message,
    String type = 'text',
    Map<String, dynamic>? metadata,
  }) {
    ChatService.sendMessage(
      receiverId: receiverId,
      message: message,
      type: type,
      metadata: metadata,
    );
  }

  @override
  void dispose() {
    ChatService.removeMessageListener(_onNewMessage);
    super.dispose();
  }
}
