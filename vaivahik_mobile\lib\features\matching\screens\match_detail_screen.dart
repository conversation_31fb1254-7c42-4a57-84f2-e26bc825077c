import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../calling/widgets/smart_call_button.dart';

class MatchDetailScreen extends ConsumerStatefulWidget {
  final String matchId;

  const MatchDetailScreen({
    super.key,
    required this.matchId,
  });

  @override
  ConsumerState<MatchDetailScreen> createState() => _MatchDetailScreenState();
}

class _MatchDetailScreenState extends ConsumerState<MatchDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Match Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.favorite_border),
            onPressed: () {
              // Toggle like/unlike match
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Profile liked!')),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Share profile via system share
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening share options...')),
              );
            },
          ),
        ],
      ),
      body: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircleAvatar(
            radius: 60,
            child: Icon(Icons.person, size: 60),
          ),
          const SizedBox(height: 16),
          Text(
            'Match Details: ${widget.matchId}',
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text('Detailed profile information will be shown here...'),
          const SizedBox(height: 32),
          Column(
            children: [
              // First row - Interest and Chat
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // Send interest to the match
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Interest sent successfully!')),
                        );
                      },
                      icon: const Icon(Icons.favorite),
                      label: const Text('Send Interest'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        // Start chat with the match
                        Navigator.of(context).pushNamed('/chat', arguments: widget.matchId);
                      },
                      icon: const Icon(Icons.chat),
                      label: const Text('Chat'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Second row - Call button
              SmartCallButton(
                targetUserId: widget.matchId,
                targetUserName: 'Match User', // In real app, get from match data
                showLabel: true,
                onCallInitiated: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Call initiated...')),
                  );
                },
              ),
            ],
          ),
        ],
      ),
      ),
    );
  }
}
