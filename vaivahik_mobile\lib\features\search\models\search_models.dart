class SearchFilters {
  final AgeRange? ageRange;
  final HeightRange? heightRange;
  final List<String>? locations;
  final List<String>? educations;
  final List<String>? occupations;
  final List<String>? incomes;
  final List<String>? religions;
  final List<String>? castes;
  final List<String>? subCastes;
  final List<String>? motherTongues;
  final MaritalStatus? maritalStatus;
  final List<String>? dietPreferences;
  final List<String>? smokingHabits;
  final List<String>? drinkingHabits;
  final bool? hasPhoto;
  final bool? isVerified;
  final bool? isPremium;
  final bool? isOnline;
  final DateTime? lastActiveWithin;
  final List<String>? hobbies;
  final List<String>? interests;
  final KundaliFilters? kundaliFilters;
  final FamilyFilters? familyFilters;
  final LocationFilters? locationFilters;
  final String? keyword;
  final SortBy sortBy;
  final SortOrder sortOrder;

  const SearchFilters({
    this.ageRange,
    this.heightRange,
    this.locations,
    this.educations,
    this.occupations,
    this.incomes,
    this.religions,
    this.castes,
    this.subCastes,
    this.motherTongues,
    this.maritalStatus,
    this.dietPreferences,
    this.smokingHabits,
    this.drinkingHabits,
    this.hasPhoto,
    this.isVerified,
    this.isPremium,
    this.isOnline,
    this.lastActiveWithin,
    this.hobbies,
    this.interests,
    this.kundaliFilters,
    this.familyFilters,
    this.locationFilters,
    this.keyword,
    this.sortBy = SortBy.relevance,
    this.sortOrder = SortOrder.desc,
  });

  factory SearchFilters.fromJson(Map<String, dynamic> json) {
    return SearchFilters(
      ageRange: json['ageRange'] != null ? AgeRange.fromJson(json['ageRange']) : null,
      heightRange: json['heightRange'] != null ? HeightRange.fromJson(json['heightRange']) : null,
      locations: json['locations'] != null ? List<String>.from(json['locations']) : null,
      educations: json['educations'] != null ? List<String>.from(json['educations']) : null,
      occupations: json['occupations'] != null ? List<String>.from(json['occupations']) : null,
      incomes: json['incomes'] != null ? List<String>.from(json['incomes']) : null,
      religions: json['religions'] != null ? List<String>.from(json['religions']) : null,
      castes: json['castes'] != null ? List<String>.from(json['castes']) : null,
      subCastes: json['subCastes'] != null ? List<String>.from(json['subCastes']) : null,
      motherTongues: json['motherTongues'] != null ? List<String>.from(json['motherTongues']) : null,
      maritalStatus: json['maritalStatus'] != null 
          ? MaritalStatus.values.firstWhere((e) => e.name == json['maritalStatus'])
          : null,
      dietPreferences: json['dietPreferences'] != null ? List<String>.from(json['dietPreferences']) : null,
      smokingHabits: json['smokingHabits'] != null ? List<String>.from(json['smokingHabits']) : null,
      drinkingHabits: json['drinkingHabits'] != null ? List<String>.from(json['drinkingHabits']) : null,
      hasPhoto: json['hasPhoto'],
      isVerified: json['isVerified'],
      isPremium: json['isPremium'],
      isOnline: json['isOnline'],
      lastActiveWithin: json['lastActiveWithin'] != null 
          ? DateTime.tryParse(json['lastActiveWithin'].toString())
          : null,
      hobbies: json['hobbies'] != null ? List<String>.from(json['hobbies']) : null,
      interests: json['interests'] != null ? List<String>.from(json['interests']) : null,
      kundaliFilters: json['kundaliFilters'] != null ? KundaliFilters.fromJson(json['kundaliFilters']) : null,
      familyFilters: json['familyFilters'] != null ? FamilyFilters.fromJson(json['familyFilters']) : null,
      locationFilters: json['locationFilters'] != null ? LocationFilters.fromJson(json['locationFilters']) : null,
      keyword: json['keyword']?.toString(),
      sortBy: SortBy.values.firstWhere(
        (e) => e.name == json['sortBy']?.toString(),
        orElse: () => SortBy.relevance,
      ),
      sortOrder: SortOrder.values.firstWhere(
        (e) => e.name == json['sortOrder']?.toString(),
        orElse: () => SortOrder.desc,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ageRange': ageRange?.toJson(),
      'heightRange': heightRange?.toJson(),
      'locations': locations,
      'educations': educations,
      'occupations': occupations,
      'incomes': incomes,
      'religions': religions,
      'castes': castes,
      'subCastes': subCastes,
      'motherTongues': motherTongues,
      'maritalStatus': maritalStatus?.name,
      'dietPreferences': dietPreferences,
      'smokingHabits': smokingHabits,
      'drinkingHabits': drinkingHabits,
      'hasPhoto': hasPhoto,
      'isVerified': isVerified,
      'isPremium': isPremium,
      'isOnline': isOnline,
      'lastActiveWithin': lastActiveWithin?.toIso8601String(),
      'hobbies': hobbies,
      'interests': interests,
      'kundaliFilters': kundaliFilters?.toJson(),
      'familyFilters': familyFilters?.toJson(),
      'locationFilters': locationFilters?.toJson(),
      'keyword': keyword,
      'sortBy': sortBy.name,
      'sortOrder': sortOrder.name,
    };
  }

  SearchFilters copyWith({
    AgeRange? ageRange,
    HeightRange? heightRange,
    List<String>? locations,
    List<String>? educations,
    List<String>? occupations,
    List<String>? incomes,
    List<String>? religions,
    List<String>? castes,
    List<String>? subCastes,
    List<String>? motherTongues,
    MaritalStatus? maritalStatus,
    List<String>? dietPreferences,
    List<String>? smokingHabits,
    List<String>? drinkingHabits,
    bool? hasPhoto,
    bool? isVerified,
    bool? isPremium,
    bool? isOnline,
    DateTime? lastActiveWithin,
    List<String>? hobbies,
    List<String>? interests,
    KundaliFilters? kundaliFilters,
    FamilyFilters? familyFilters,
    LocationFilters? locationFilters,
    String? keyword,
    SortBy? sortBy,
    SortOrder? sortOrder,
  }) {
    return SearchFilters(
      ageRange: ageRange ?? this.ageRange,
      heightRange: heightRange ?? this.heightRange,
      locations: locations ?? this.locations,
      educations: educations ?? this.educations,
      occupations: occupations ?? this.occupations,
      incomes: incomes ?? this.incomes,
      religions: religions ?? this.religions,
      castes: castes ?? this.castes,
      subCastes: subCastes ?? this.subCastes,
      motherTongues: motherTongues ?? this.motherTongues,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      dietPreferences: dietPreferences ?? this.dietPreferences,
      smokingHabits: smokingHabits ?? this.smokingHabits,
      drinkingHabits: drinkingHabits ?? this.drinkingHabits,
      hasPhoto: hasPhoto ?? this.hasPhoto,
      isVerified: isVerified ?? this.isVerified,
      isPremium: isPremium ?? this.isPremium,
      isOnline: isOnline ?? this.isOnline,
      lastActiveWithin: lastActiveWithin ?? this.lastActiveWithin,
      hobbies: hobbies ?? this.hobbies,
      interests: interests ?? this.interests,
      kundaliFilters: kundaliFilters ?? this.kundaliFilters,
      familyFilters: familyFilters ?? this.familyFilters,
      locationFilters: locationFilters ?? this.locationFilters,
      keyword: keyword ?? this.keyword,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  bool get isEmpty {
    return ageRange == null &&
        heightRange == null &&
        (locations?.isEmpty ?? true) &&
        (educations?.isEmpty ?? true) &&
        (occupations?.isEmpty ?? true) &&
        (incomes?.isEmpty ?? true) &&
        (religions?.isEmpty ?? true) &&
        (castes?.isEmpty ?? true) &&
        (subCastes?.isEmpty ?? true) &&
        (motherTongues?.isEmpty ?? true) &&
        maritalStatus == null &&
        (dietPreferences?.isEmpty ?? true) &&
        (smokingHabits?.isEmpty ?? true) &&
        (drinkingHabits?.isEmpty ?? true) &&
        hasPhoto == null &&
        isVerified == null &&
        isPremium == null &&
        isOnline == null &&
        lastActiveWithin == null &&
        (hobbies?.isEmpty ?? true) &&
        (interests?.isEmpty ?? true) &&
        kundaliFilters == null &&
        familyFilters == null &&
        locationFilters == null &&
        (keyword?.isEmpty ?? true);
  }
}

class AgeRange {
  final int min;
  final int max;

  const AgeRange({required this.min, required this.max});

  factory AgeRange.fromJson(Map<String, dynamic> json) {
    return AgeRange(
      min: json['min']?.toInt() ?? 18,
      max: json['max']?.toInt() ?? 60,
    );
  }

  Map<String, dynamic> toJson() {
    return {'min': min, 'max': max};
  }
}

class HeightRange {
  final double min;
  final double max;

  const HeightRange({required this.min, required this.max});

  factory HeightRange.fromJson(Map<String, dynamic> json) {
    return HeightRange(
      min: json['min']?.toDouble() ?? 4.5,
      max: json['max']?.toDouble() ?? 6.5,
    );
  }

  Map<String, dynamic> toJson() {
    return {'min': min, 'max': max};
  }
}

class KundaliFilters {
  final bool? hasMangalDosha;
  final bool? hasKalSarpaDosha;
  final bool? hasPitruDosha;
  final int? minCompatibilityScore;
  final List<String>? preferredGunas;

  const KundaliFilters({
    this.hasMangalDosha,
    this.hasKalSarpaDosha,
    this.hasPitruDosha,
    this.minCompatibilityScore,
    this.preferredGunas,
  });

  factory KundaliFilters.fromJson(Map<String, dynamic> json) {
    return KundaliFilters(
      hasMangalDosha: json['hasMangalDosha'],
      hasKalSarpaDosha: json['hasKalSarpaDosha'],
      hasPitruDosha: json['hasPitruDosha'],
      minCompatibilityScore: json['minCompatibilityScore']?.toInt(),
      preferredGunas: json['preferredGunas'] != null 
          ? List<String>.from(json['preferredGunas'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'hasMangalDosha': hasMangalDosha,
      'hasKalSarpaDosha': hasKalSarpaDosha,
      'hasPitruDosha': hasPitruDosha,
      'minCompatibilityScore': minCompatibilityScore,
      'preferredGunas': preferredGunas,
    };
  }
}

class FamilyFilters {
  final List<String>? familyTypes;
  final List<String>? familyValues;
  final List<String>? familyStatuses;
  final int? minSiblings;
  final int? maxSiblings;

  const FamilyFilters({
    this.familyTypes,
    this.familyValues,
    this.familyStatuses,
    this.minSiblings,
    this.maxSiblings,
  });

  factory FamilyFilters.fromJson(Map<String, dynamic> json) {
    return FamilyFilters(
      familyTypes: json['familyTypes'] != null 
          ? List<String>.from(json['familyTypes'])
          : null,
      familyValues: json['familyValues'] != null 
          ? List<String>.from(json['familyValues'])
          : null,
      familyStatuses: json['familyStatuses'] != null 
          ? List<String>.from(json['familyStatuses'])
          : null,
      minSiblings: json['minSiblings']?.toInt(),
      maxSiblings: json['maxSiblings']?.toInt(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'familyTypes': familyTypes,
      'familyValues': familyValues,
      'familyStatuses': familyStatuses,
      'minSiblings': minSiblings,
      'maxSiblings': maxSiblings,
    };
  }
}

class LocationFilters {
  final List<String>? countries;
  final List<String>? states;
  final List<String>? cities;
  final double? radiusKm;
  final double? latitude;
  final double? longitude;

  const LocationFilters({
    this.countries,
    this.states,
    this.cities,
    this.radiusKm,
    this.latitude,
    this.longitude,
  });

  factory LocationFilters.fromJson(Map<String, dynamic> json) {
    return LocationFilters(
      countries: json['countries'] != null 
          ? List<String>.from(json['countries'])
          : null,
      states: json['states'] != null 
          ? List<String>.from(json['states'])
          : null,
      cities: json['cities'] != null 
          ? List<String>.from(json['cities'])
          : null,
      radiusKm: json['radiusKm']?.toDouble(),
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'countries': countries,
      'states': states,
      'cities': cities,
      'radiusKm': radiusKm,
      'latitude': latitude,
      'longitude': longitude,
    };
  }
}

enum MaritalStatus {
  neverMarried,
  divorced,
  widowed,
  separated
}

enum SortBy {
  relevance,
  lastActive,
  newest,
  age,
  height,
  compatibility,
  premium,
  verified
}

enum SortOrder {
  asc,
  desc
}

class SavedSearch {
  final String id;
  final String userId;
  final String name;
  final SearchFilters filters;
  final bool isActive;
  final DateTime createdAt;
  final DateTime lastUsed;
  final int usageCount;

  const SavedSearch({
    required this.id,
    required this.userId,
    required this.name,
    required this.filters,
    required this.isActive,
    required this.createdAt,
    required this.lastUsed,
    required this.usageCount,
  });

  factory SavedSearch.fromJson(Map<String, dynamic> json) {
    return SavedSearch(
      id: json['id']?.toString() ?? '',
      userId: json['userId']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      filters: SearchFilters.fromJson(json['filters'] ?? {}),
      isActive: json['isActive'] ?? true,
      createdAt: DateTime.tryParse(json['createdAt']?.toString() ?? '') ?? DateTime.now(),
      lastUsed: DateTime.tryParse(json['lastUsed']?.toString() ?? '') ?? DateTime.now(),
      usageCount: json['usageCount']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'filters': filters.toJson(),
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'lastUsed': lastUsed.toIso8601String(),
      'usageCount': usageCount,
    };
  }
}

class SearchResult {
  final List<SearchUser> users;
  final int totalCount;
  final int currentPage;
  final int totalPages;
  final bool hasMore;
  final SearchFilters appliedFilters;
  final String? searchId;
  final DateTime searchTime;

  const SearchResult({
    required this.users,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
    required this.hasMore,
    required this.appliedFilters,
    this.searchId,
    required this.searchTime,
  });

  factory SearchResult.fromJson(Map<String, dynamic> json) {
    List<SearchUser> usersList = [];
    if (json['users'] != null) {
      usersList = (json['users'] as List)
          .map((user) => SearchUser.fromJson(user))
          .toList();
    }

    return SearchResult(
      users: usersList,
      totalCount: json['totalCount']?.toInt() ?? 0,
      currentPage: json['currentPage']?.toInt() ?? 1,
      totalPages: json['totalPages']?.toInt() ?? 1,
      hasMore: json['hasMore'] ?? false,
      appliedFilters: SearchFilters.fromJson(json['appliedFilters'] ?? {}),
      searchId: json['searchId']?.toString(),
      searchTime: DateTime.tryParse(json['searchTime']?.toString() ?? '') ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'users': users.map((user) => user.toJson()).toList(),
      'totalCount': totalCount,
      'currentPage': currentPage,
      'totalPages': totalPages,
      'hasMore': hasMore,
      'appliedFilters': appliedFilters.toJson(),
      'searchId': searchId,
      'searchTime': searchTime.toIso8601String(),
    };
  }
}

class SearchUser {
  final String id;
  final String name;
  final int age;
  final double height;
  final String location;
  final String education;
  final String occupation;
  final String? profilePhoto;
  final bool isVerified;
  final bool isPremium;
  final bool isOnline;
  final DateTime? lastActive;
  final double? compatibilityScore;
  final int matchPercentage;

  const SearchUser({
    required this.id,
    required this.name,
    required this.age,
    required this.height,
    required this.location,
    required this.education,
    required this.occupation,
    this.profilePhoto,
    required this.isVerified,
    required this.isPremium,
    required this.isOnline,
    this.lastActive,
    this.compatibilityScore,
    required this.matchPercentage,
  });

  factory SearchUser.fromJson(Map<String, dynamic> json) {
    return SearchUser(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      age: json['age']?.toInt() ?? 0,
      height: json['height']?.toDouble() ?? 0.0,
      location: json['location']?.toString() ?? '',
      education: json['education']?.toString() ?? '',
      occupation: json['occupation']?.toString() ?? '',
      profilePhoto: json['profilePhoto']?.toString(),
      isVerified: json['isVerified'] ?? false,
      isPremium: json['isPremium'] ?? false,
      isOnline: json['isOnline'] ?? false,
      lastActive: json['lastActive'] != null 
          ? DateTime.tryParse(json['lastActive'].toString())
          : null,
      compatibilityScore: json['compatibilityScore']?.toDouble(),
      matchPercentage: json['matchPercentage']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'height': height,
      'location': location,
      'education': education,
      'occupation': occupation,
      'profilePhoto': profilePhoto,
      'isVerified': isVerified,
      'isPremium': isPremium,
      'isOnline': isOnline,
      'lastActive': lastActive?.toIso8601String(),
      'compatibilityScore': compatibilityScore,
      'matchPercentage': matchPercentage,
    };
  }
}
