import 'package:flutter/material.dart';

/// App Animations - Consistent animation system matching website
/// Based on vaivahik-nextjs animation patterns and modern UI standards
class AppAnimations {
  // Animation Durations - Consistent timing
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration normal = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  static const Duration verySlow = Duration(milliseconds: 800);
  
  // Animation Curves - Natural motion
  static const Curve easeInOut = Curves.easeInOut;
  static const Curve easeOut = Curves.easeOut;
  static const Curve easeIn = Curves.easeIn;
  static const Curve elasticOut = Curves.elasticOut;
  static const Curve bounceOut = Curves.bounceOut;
  
  // Page Transition Animations
  static Widget slideTransition({
    required Widget child,
    required Animation<double> animation,
    SlideDirection direction = SlideDirection.right,
  }) {
    Offset begin;
    switch (direction) {
      case SlideDirection.left:
        begin = const Offset(-1.0, 0.0);
        break;
      case SlideDirection.right:
        begin = const Offset(1.0, 0.0);
        break;
      case SlideDirection.up:
        begin = const Offset(0.0, -1.0);
        break;
      case SlideDirection.down:
        begin = const Offset(0.0, 1.0);
        break;
    }
    
    return SlideTransition(
      position: Tween<Offset>(
        begin: begin,
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: easeOut,
      )),
      child: child,
    );
  }
  
  static Widget fadeTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return FadeTransition(
      opacity: animation,
      child: child,
    );
  }
  
  static Widget scaleTransition({
    required Widget child,
    required Animation<double> animation,
  }) {
    return ScaleTransition(
      scale: Tween<double>(
        begin: 0.8,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: animation,
        curve: elasticOut,
      )),
      child: child,
    );
  }
  
  // Card Animation - Entrance animation for cards
  static Widget cardEntranceAnimation({
    required Widget child,
    required Animation<double> animation,
    int delay = 0,
  }) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final delayedAnimation = Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: animation,
          curve: Interval(
            delay * 0.1,
            1.0,
            curve: easeOut,
          ),
        ));
        
        return Transform.translate(
          offset: Offset(0, 50 * (1 - delayedAnimation.value)),
          child: Opacity(
            opacity: delayedAnimation.value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }
  
  // Button Press Animation
  static Widget buttonPressAnimation({
    required Widget child,
    required VoidCallback onPressed,
  }) {
    return TweenAnimationBuilder<double>(
      duration: fast,
      tween: Tween<double>(begin: 1.0, end: 1.0),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: GestureDetector(
            onTapDown: (_) {
              // Scale down on press
            },
            onTapUp: (_) {
              onPressed();
            },
            onTapCancel: () {
              // Scale back up on cancel
            },
            child: child,
          ),
        );
      },
      child: child,
    );
  }
  
  // Shimmer Loading Animation
  static Widget shimmerLoading({
    required Widget child,
    bool isLoading = true,
  }) {
    if (!isLoading) return child;
    
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1500),
      tween: Tween<double>(begin: -1.0, end: 1.0),
      builder: (context, value, child) {
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: const [
                Colors.transparent,
                Colors.white54,
                Colors.transparent,
              ],
              stops: [
                (value - 0.3).clamp(0.0, 1.0),
                value.clamp(0.0, 1.0),
                (value + 0.3).clamp(0.0, 1.0),
              ],
            ).createShader(bounds);
          },
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Floating Animation - For floating elements
  static Widget floatingAnimation({
    required Widget child,
    Duration duration = const Duration(seconds: 3),
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween<double>(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 10 * (0.5 - (value * 2 - 1).abs())),
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Pulse Animation - For attention-grabbing elements
  static Widget pulseAnimation({
    required Widget child,
    Duration duration = const Duration(seconds: 2),
  }) {
    return TweenAnimationBuilder<double>(
      duration: duration,
      tween: Tween<double>(begin: 1.0, end: 1.1),
      builder: (context, scale, child) {
        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
      child: child,
    );
  }
  
  // Staggered List Animation
  static Widget staggeredListAnimation({
    required Widget child,
    required Animation<double> animation,
    required int index,
    int itemCount = 1,
  }) {
    final interval = 1.0 / itemCount;
    final start = index * interval;
    final end = (start + interval).clamp(0.0, 1.0);
    
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final curvedAnimation = CurvedAnimation(
          parent: animation,
          curve: Interval(start, end, curve: easeOut),
        );
        
        return Transform.translate(
          offset: Offset(0, 50 * (1 - curvedAnimation.value)),
          child: Opacity(
            opacity: curvedAnimation.value,
            child: child,
          ),
        );
      },
      child: child,
    );
  }
}

// Slide Direction Enum
enum SlideDirection { left, right, up, down }

// Custom Page Route with Animation
class AnimatedPageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final AnimationType animationType;
  final Duration duration;
  
  AnimatedPageRoute({
    required this.child,
    this.animationType = AnimationType.slide,
    this.duration = AppAnimations.normal,
  }) : super(
    transitionDuration: duration,
    reverseTransitionDuration: duration,
    pageBuilder: (context, animation, secondaryAnimation) => child,
  );
  
  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    switch (animationType) {
      case AnimationType.slide:
        return AppAnimations.slideTransition(
          child: child,
          animation: animation,
        );
      case AnimationType.fade:
        return AppAnimations.fadeTransition(
          child: child,
          animation: animation,
        );
      case AnimationType.scale:
        return AppAnimations.scaleTransition(
          child: child,
          animation: animation,
        );
    }
  }
}

// Animation Type Enum
enum AnimationType { slide, fade, scale }

// Hero Animation Helper
class HeroAnimationHelper {
  static Widget heroAnimation({
    required String tag,
    required Widget child,
  }) {
    return Hero(
      tag: tag,
      child: Material(
        color: Colors.transparent,
        child: child,
      ),
    );
  }
}
