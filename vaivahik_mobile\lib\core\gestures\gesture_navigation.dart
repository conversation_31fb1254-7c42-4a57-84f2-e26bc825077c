import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// 👆 GESTURE NAVIGATION SYSTEM - Intuitive Touch Interactions
/// Features: Swipe navigation, pinch gestures, drag interactions, haptic feedback
/// Provides natural and intuitive gesture-based navigation throughout the app

/// Advanced Swipe Navigation Widget
class SwipeNavigationWrapper extends StatefulWidget {
  final Widget child;
  final VoidCallback? onSwipeLeft;
  final VoidCallback? onSwipeRight;
  final VoidCallback? onSwipeUp;
  final VoidCallback? onSwipeDown;
  final double sensitivity;
  final bool enableHapticFeedback;
  final bool enableVisualFeedback;

  const SwipeNavigationWrapper({
    super.key,
    required this.child,
    this.onSwipeLeft,
    this.onSwipeRight,
    this.onSwipeUp,
    this.onSwipeDown,
    this.sensitivity = 100.0,
    this.enableHapticFeedback = true,
    this.enableVisualFeedback = true,
  });

  @override
  State<SwipeNavigationWrapper> createState() => _SwipeNavigationWrapperState();
}

class _SwipeNavigationWrapperState extends State<SwipeNavigationWrapper>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _slideAnimation;

  Offset _startPosition = Offset.zero;
  Offset _currentPosition = Offset.zero;
  bool _isGestureActive = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.translate(
              offset: _slideAnimation.value,
              child: widget.child,
            ),
          );
        },
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    _startPosition = details.localPosition;
    _isGestureActive = true;
    
    if (widget.enableVisualFeedback) {
      _animationController.forward();
    }
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isGestureActive) return;
    
    _currentPosition = details.localPosition;
    
    if (widget.enableVisualFeedback) {
      final offset = _currentPosition - _startPosition;
      final normalizedOffset = Offset(
        offset.dx / context.size!.width,
        offset.dy / context.size!.height,
      );
      
      _slideAnimation = Tween<Offset>(
        begin: Offset.zero,
        end: normalizedOffset * 0.1, // Subtle movement
      ).animate(_animationController);
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isGestureActive) return;
    
    _isGestureActive = false;
    final offset = _currentPosition - _startPosition;
    final distance = offset.distance;
    
    if (distance > widget.sensitivity) {
      final direction = _getSwipeDirection(offset);
      _handleSwipe(direction);
    }
    
    if (widget.enableVisualFeedback) {
      _animationController.reverse();
    }
  }

  SwipeDirection _getSwipeDirection(Offset offset) {
    final dx = offset.dx.abs();
    final dy = offset.dy.abs();
    
    if (dx > dy) {
      return offset.dx > 0 ? SwipeDirection.right : SwipeDirection.left;
    } else {
      return offset.dy > 0 ? SwipeDirection.down : SwipeDirection.up;
    }
  }

  void _handleSwipe(SwipeDirection direction) {
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
    
    switch (direction) {
      case SwipeDirection.left:
        widget.onSwipeLeft?.call();
        break;
      case SwipeDirection.right:
        widget.onSwipeRight?.call();
        break;
      case SwipeDirection.up:
        widget.onSwipeUp?.call();
        break;
      case SwipeDirection.down:
        widget.onSwipeDown?.call();
        break;
    }
  }
}

enum SwipeDirection { left, right, up, down }

/// Pinch to Zoom Widget
class PinchToZoomWidget extends StatefulWidget {
  final Widget child;
  final double minScale;
  final double maxScale;
  final bool enableRotation;
  final VoidCallback? onZoomStart;
  final VoidCallback? onZoomEnd;

  const PinchToZoomWidget({
    super.key,
    required this.child,
    this.minScale = 0.5,
    this.maxScale = 3.0,
    this.enableRotation = false,
    this.onZoomStart,
    this.onZoomEnd,
  });

  @override
  State<PinchToZoomWidget> createState() => _PinchToZoomWidgetState();
}

class _PinchToZoomWidgetState extends State<PinchToZoomWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Matrix4> _animation;

  Matrix4 _transform = Matrix4.identity();

  bool _isZooming = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onScaleStart: _onScaleStart,
      onScaleUpdate: _onScaleUpdate,
      onScaleEnd: _onScaleEnd,
      onDoubleTap: _onDoubleTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          final transform = _animation.value ?? _transform;
          return Transform(
            transform: transform,
            alignment: Alignment.center,
            child: widget.child,
          );
        },
      ),
    );
  }

  void _onScaleStart(ScaleStartDetails details) {
    _isZooming = true;
    widget.onZoomStart?.call();
    HapticFeedback.selectionClick();
  }

  void _onScaleUpdate(ScaleUpdateDetails details) {
    if (!_isZooming) return;

    final scale = details.scale.clamp(widget.minScale, widget.maxScale);
    final translation = details.focalPoint - details.localFocalPoint;

    setState(() {
      _transform = Matrix4.identity()
        ..translate(translation.dx, translation.dy)
        ..scale(scale);

      if (widget.enableRotation) {
        _transform.rotateZ(details.rotation);
      }
    });
  }

  void _onScaleEnd(ScaleEndDetails details) {
    _isZooming = false;
    widget.onZoomEnd?.call();
    HapticFeedback.selectionClick();
  }

  void _onDoubleTap() {
    final isZoomedIn = _transform.getMaxScaleOnAxis() > 1.0;
    final targetTransform = isZoomedIn ? Matrix4.identity() : Matrix4.identity()..scale(2.0);

    _animation = Matrix4Tween(
      begin: _transform,
      end: targetTransform,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward(from: 0).then((_) {
      setState(() {
        _transform = targetTransform;
      });
    });

    HapticFeedback.mediumImpact();
  }
}

/// Drag to Dismiss Widget
class DragToDismissWrapper extends StatefulWidget {
  final Widget child;
  final VoidCallback? onDismiss;
  final DismissDirection direction;
  final double dismissThreshold;
  final bool enableHapticFeedback;

  const DragToDismissWrapper({
    super.key,
    required this.child,
    this.onDismiss,
    this.direction = DismissDirection.vertical,
    this.dismissThreshold = 0.3,
    this.enableHapticFeedback = true,
  });

  @override
  State<DragToDismissWrapper> createState() => _DragToDismissWrapperState();
}

class _DragToDismissWrapperState extends State<DragToDismissWrapper>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  Offset _dragOffset = Offset.zero;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.translate(
              offset: _slideAnimation.value + _dragOffset,
              child: widget.child,
            ),
          );
        },
      ),
    );
  }

  void _onPanStart(DragStartDetails details) {
    _isDragging = true;
    _animationController.stop();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isDragging) return;

    setState(() {
      switch (widget.direction) {
        case DismissDirection.vertical:
          _dragOffset = Offset(0, details.delta.dy + _dragOffset.dy);
          break;
        case DismissDirection.horizontal:
          _dragOffset = Offset(details.delta.dx + _dragOffset.dx, 0);
          break;
        case DismissDirection.endToStart:
          if (details.delta.dx < 0) {
            _dragOffset = Offset(details.delta.dx + _dragOffset.dx, 0);
          }
          break;
        case DismissDirection.startToEnd:
          if (details.delta.dx > 0) {
            _dragOffset = Offset(details.delta.dx + _dragOffset.dx, 0);
          }
          break;
        case DismissDirection.up:
          if (details.delta.dy < 0) {
            _dragOffset = Offset(0, details.delta.dy + _dragOffset.dy);
          }
          break;
        case DismissDirection.down:
          if (details.delta.dy > 0) {
            _dragOffset = Offset(0, details.delta.dy + _dragOffset.dy);
          }
          break;
        case DismissDirection.none:
          break;
      }
    });

    // Provide haptic feedback when approaching dismiss threshold
    final progress = _getDismissProgress();
    if (progress > widget.dismissThreshold && widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
  }

  void _onPanEnd(DragEndDetails details) {
    _isDragging = false;
    final progress = _getDismissProgress();

    if (progress > widget.dismissThreshold) {
      _dismissWidget();
    } else {
      _resetPosition();
    }
  }

  double _getDismissProgress() {
    final screenSize = MediaQuery.of(context).size;
    switch (widget.direction) {
      case DismissDirection.vertical:
      case DismissDirection.up:
      case DismissDirection.down:
        return _dragOffset.dy.abs() / screenSize.height;
      case DismissDirection.horizontal:
      case DismissDirection.endToStart:
      case DismissDirection.startToEnd:
        return _dragOffset.dx.abs() / screenSize.width;
      case DismissDirection.none:
        return 0.0;
    }
  }

  void _dismissWidget() {
    final screenSize = MediaQuery.of(context).size;
    Offset targetOffset;

    switch (widget.direction) {
      case DismissDirection.vertical:
        targetOffset = Offset(0, _dragOffset.dy > 0 ? screenSize.height : -screenSize.height);
        break;
      case DismissDirection.horizontal:
        targetOffset = Offset(_dragOffset.dx > 0 ? screenSize.width : -screenSize.width, 0);
        break;
      case DismissDirection.up:
        targetOffset = Offset(0, -screenSize.height);
        break;
      case DismissDirection.down:
        targetOffset = Offset(0, screenSize.height);
        break;
      case DismissDirection.startToEnd:
        targetOffset = Offset(screenSize.width, 0);
        break;
      case DismissDirection.endToStart:
        targetOffset = Offset(-screenSize.width, 0);
        break;
      case DismissDirection.none:
        targetOffset = Offset.zero;
        break;
    }

    _slideAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: targetOffset,
    ).animate(_animationController);

    _animationController.forward().then((_) {
      widget.onDismiss?.call();
    });

    if (widget.enableHapticFeedback) {
      HapticFeedback.mediumImpact();
    }
  }

  void _resetPosition() {
    _slideAnimation = Tween<Offset>(
      begin: _dragOffset,
      end: Offset.zero,
    ).animate(_animationController);

    _animationController.forward().then((_) {
      setState(() {
        _dragOffset = Offset.zero;
      });
    });
  }
}

/// Pull to Refresh Widget
class PullToRefreshWrapper extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final double triggerDistance;
  final bool enableHapticFeedback;

  const PullToRefreshWrapper({
    super.key,
    required this.child,
    required this.onRefresh,
    this.triggerDistance = 100.0,
    this.enableHapticFeedback = true,
  });

  @override
  State<PullToRefreshWrapper> createState() => _PullToRefreshWrapperState();
}

class _PullToRefreshWrapperState extends State<PullToRefreshWrapper>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;

  double _pullDistance = 0.0;
  bool _isRefreshing = false;
  bool _hasTriggered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 1),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: _onScrollNotification,
      child: Stack(
        children: [
          widget.child,
          if (_pullDistance > 0)
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: _pullDistance.clamp(0.0, widget.triggerDistance),
                alignment: Alignment.center,
                child: AnimatedBuilder(
                  animation: _rotationAnimation,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _rotationAnimation.value * 2 * math.pi,
                      child: Icon(
                        Icons.refresh,
                        size: 24,
                        color: _pullDistance >= widget.triggerDistance
                            ? Colors.blue
                            : Colors.grey,
                      ),
                    );
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  bool _onScrollNotification(ScrollNotification notification) {
    if (notification is ScrollUpdateNotification) {
      if (notification.metrics.pixels < 0 && !_isRefreshing) {
        setState(() {
          _pullDistance = -notification.metrics.pixels;
        });

        if (_pullDistance >= widget.triggerDistance && !_hasTriggered) {
          _hasTriggered = true;
          if (widget.enableHapticFeedback) {
            HapticFeedback.mediumImpact();
          }
        }
      }
    } else if (notification is ScrollEndNotification) {
      if (_pullDistance >= widget.triggerDistance && !_isRefreshing) {
        _triggerRefresh();
      } else {
        _resetPull();
      }
    }

    return false;
  }

  void _triggerRefresh() async {
    setState(() {
      _isRefreshing = true;
    });

    _animationController.repeat();

    try {
      await widget.onRefresh();
    } finally {
      _animationController.stop();
      _resetPull();
      setState(() {
        _isRefreshing = false;
        _hasTriggered = false;
      });
    }
  }

  void _resetPull() {
    setState(() {
      _pullDistance = 0.0;
    });
  }
}
