import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/services/chat_service.dart';
import '../../../core/api/api_client.dart';
import '../../../core/widgets/feature_access_widget.dart';
import '../../../core/services/feature_access_service.dart';

class ChatScreen extends ConsumerStatefulWidget {
  final String chatId;
  final String userName;
  final String receiverId;

  const ChatScreen({
    super.key,
    required this.chatId,
    required this.userName,
    required this.receiverId,
  });

  @override
  ConsumerState<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends ConsumerState<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize chat service and join room
    ChatService.initialize();
    ChatService.joinRoom(widget.chatId);
  }

  @override
  void dispose() {
    ChatService.leaveRoom(widget.chatId);
    _messageController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isEmpty) return;

    // Wrap with feature usage tracking
    FeatureUsageTracker(
      feature: FeatureType.chatWithMatches,
      trackOnTap: true,
      child: Container(), // Empty container since we're just tracking
    );

    ChatService.sendMessage(
      receiverId: widget.receiverId,
      message: message,
    );

    _messageController.clear();
  }

  void _makeVoiceCall() async {
    // Get user's phone number from profile
    try {
      final response = await ApiClient().get('/profile/${widget.receiverId}');
      if (response['success'] == true) {
        final phoneNumber = response['data']['user']['phone'];
        if (phoneNumber != null && phoneNumber.isNotEmpty) {
          final phoneUrl = Uri.parse('tel:$phoneNumber');
          if (await canLaunchUrl(phoneUrl)) {
            await launchUrl(phoneUrl);
          }
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Phone number not available')),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Unable to make call')),
        );
      }
    }
  }

  void _makeVideoCall() {
    // Implement video call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Video call feature coming soon!')),
    );
  }

  void _showChatOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.block),
              title: const Text('Block User'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.report),
              title: const Text('Report User'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.delete),
              title: const Text('Clear Chat'),
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
      title: Text(widget.userName),
      actions: [
        IconButton(
          icon: const Icon(Icons.call),
          onPressed: _makeVoiceCall,
        ),
        IconButton(
          icon: const Icon(Icons.videocam),
          onPressed: _makeVideoCall,
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: _showChatOptions,
        ),
      ],
    ),
      body: Column(
      children: [
        const Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.chat, size: 80, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'Chat Screen',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('Messages will appear here...'),
              ],
            ),
          ),
        ),
        FeatureAccessWidget(
          feature: FeatureType.chatWithMatches,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              border: Border(
                top: BorderSide(color: Colors.grey[300]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: const InputDecoration(
                      hintText: 'Type a message...',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  onPressed: _sendMessage,
                  icon: const Icon(Icons.send),
                  color: Colors.blue,
                ),
              ],
            ),
          ),
        ),
      ],
      ),
    );
  }
}
