/**
 * Debug Kundali System Issues
 */

const VedicAstrologyService = require('../src/services/vedicAstrology.service');
const ManglikDoshaService = require('../src/services/manglikDosha.service');
const ComprehensiveKundaliService = require('../src/services/comprehensiveKundali.service');

async function debugKundali() {
  console.log('🔍 Debugging Kundali System...\n');

  const vedicService = new VedicAstrologyService();
  const manglikService = new ManglikDoshaService();
  const kundaliService = new ComprehensiveKundaliService();

  // Test Manglik detection
  console.log('1. Testing Manglik dosha detection...');
  try {
    const doshaResult = manglikService.detectManglikDosha('1990-05-15', '14:30', 'Mumbai, India');
    console.log('   Dosha Result:', JSON.stringify(doshaResult, null, 2));
  } catch (error) {
    console.log('   Error:', error.message);
  }

  // Test comprehensive matching
  console.log('\n2. Testing comprehensive kundali matching...');
  try {
    const user1 = {
      id: 'test1',
      name: 'Test User 1',
      birthDate: '1990-05-15',
      birthTime: '14:30',
      birthPlace: 'Mumbai, India',
      timezone: 'IST'
    };

    const user2 = {
      id: 'test2',
      name: 'Test User 2',
      birthDate: '1992-08-20',
      birthTime: '10:15',
      birthPlace: 'Delhi, India',
      timezone: 'IST'
    };

    const result = await kundaliService.generateCompleteKundaliMatch(user1, user2, {
      includeCharts: true,
      includeRemedies: true
    });
    
    console.log('   Match Result Keys:', Object.keys(result));
    if (result.overallCompatibility) {
      console.log('   Overall Score:', result.overallCompatibility.score);
    }
    if (result.ashtakootAnalysis) {
      console.log('   Ashtakoot Score:', result.ashtakootAnalysis.totalScore);
    }
  } catch (error) {
    console.log('   Error:', error.message);
    console.log('   Stack:', error.stack);
  }

  // Test Manglik compatibility
  console.log('\n3. Testing Manglik compatibility...');
  try {
    const person1Dosha = { isManglik: true, intensity: 50 };
    const person2Dosha = { isManglik: false, intensity: 0 };
    
    const compatibility = manglikService.checkManglikCompatibility(person1Dosha, person2Dosha);
    console.log('   Compatibility Result:', JSON.stringify(compatibility, null, 2));
  } catch (error) {
    console.log('   Error:', error.message);
  }
}

debugKundali().catch(console.error);
