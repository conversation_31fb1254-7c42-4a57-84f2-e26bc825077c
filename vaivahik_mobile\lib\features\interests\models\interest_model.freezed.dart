// GENERATED CODE - DO NOT MODIFY BY HAND
// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'interest_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$InterestModel {
  String get id;
  String get userId;
  String get targetUserId;
  String? get message;
  String get status;
  String? get responseMessage;
  DateTime? get respondedAt;
  DateTime get createdAt;
  DateTime? get updatedAt;
  UserProfileModel? get user;
  UserProfileModel? get targetUser;

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InterestModelCopyWith<InterestModel> get copyWith =>
      _$InterestModelCopyWithImpl<InterestModel>(
          this as InterestModel, _$identity);

  /// Serializes this InterestModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InterestModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            (identical(other.respondedAt, respondedAt) ||
                other.respondedAt == respondedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.targetUser, targetUser) ||
                other.targetUser == targetUser));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      targetUserId,
      message,
      status,
      responseMessage,
      respondedAt,
      createdAt,
      updatedAt,
      user,
      targetUser);

  @override
  String toString() {
    return 'InterestModel(id: $id, userId: $userId, targetUserId: $targetUserId, message: $message, status: $status, responseMessage: $responseMessage, respondedAt: $respondedAt, createdAt: $createdAt, updatedAt: $updatedAt, user: $user, targetUser: $targetUser)';
  }
}

/// @nodoc
abstract mixin class $InterestModelCopyWith<$Res> {
  factory $InterestModelCopyWith(
          InterestModel value, $Res Function(InterestModel) _then) =
      _$InterestModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String userId,
      String targetUserId,
      String? message,
      String status,
      String? responseMessage,
      DateTime? respondedAt,
      DateTime createdAt,
      DateTime? updatedAt,
      UserProfileModel? user,
      UserProfileModel? targetUser});

  $UserProfileModelCopyWith<$Res>? get user;
  $UserProfileModelCopyWith<$Res>? get targetUser;
}

/// @nodoc
class _$InterestModelCopyWithImpl<$Res>
    implements $InterestModelCopyWith<$Res> {
  _$InterestModelCopyWithImpl(this._self, this._then);

  final InterestModel _self;
  final $Res Function(InterestModel) _then;

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? targetUserId = null,
    Object? message = freezed,
    Object? status = null,
    Object? responseMessage = freezed,
    Object? respondedAt = freezed,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? user = freezed,
    Object? targetUser = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      responseMessage: freezed == responseMessage
          ? _self.responseMessage
          : responseMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      respondedAt: freezed == respondedAt
          ? _self.respondedAt
          : respondedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserProfileModel?,
      targetUser: freezed == targetUser
          ? _self.targetUser
          : targetUser // ignore: cast_nullable_to_non_nullable
              as UserProfileModel?,
    ));
  }

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserProfileModelCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $UserProfileModelCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserProfileModelCopyWith<$Res>? get targetUser {
    if (_self.targetUser == null) {
      return null;
    }

    return $UserProfileModelCopyWith<$Res>(_self.targetUser!, (value) {
      return _then(_self.copyWith(targetUser: value));
    });
  }
}

/// Adds pattern-matching-related methods to [InterestModel].
extension InterestModelPatterns on InterestModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_InterestModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_InterestModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_InterestModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String targetUserId,
            String? message,
            String status,
            String? responseMessage,
            DateTime? respondedAt,
            DateTime createdAt,
            DateTime? updatedAt,
            UserProfileModel? user,
            UserProfileModel? targetUser)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestModel() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.targetUserId,
            _that.message,
            _that.status,
            _that.responseMessage,
            _that.respondedAt,
            _that.createdAt,
            _that.updatedAt,
            _that.user,
            _that.targetUser);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String userId,
            String targetUserId,
            String? message,
            String status,
            String? responseMessage,
            DateTime? respondedAt,
            DateTime createdAt,
            DateTime? updatedAt,
            UserProfileModel? user,
            UserProfileModel? targetUser)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestModel():
        return $default(
            _that.id,
            _that.userId,
            _that.targetUserId,
            _that.message,
            _that.status,
            _that.responseMessage,
            _that.respondedAt,
            _that.createdAt,
            _that.updatedAt,
            _that.user,
            _that.targetUser);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String userId,
            String targetUserId,
            String? message,
            String status,
            String? responseMessage,
            DateTime? respondedAt,
            DateTime createdAt,
            DateTime? updatedAt,
            UserProfileModel? user,
            UserProfileModel? targetUser)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestModel() when $default != null:
        return $default(
            _that.id,
            _that.userId,
            _that.targetUserId,
            _that.message,
            _that.status,
            _that.responseMessage,
            _that.respondedAt,
            _that.createdAt,
            _that.updatedAt,
            _that.user,
            _that.targetUser);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _InterestModel implements InterestModel {
  const _InterestModel(
      {required this.id,
      required this.userId,
      required this.targetUserId,
      this.message,
      this.status = 'PENDING',
      this.responseMessage,
      this.respondedAt,
      required this.createdAt,
      this.updatedAt,
      this.user,
      this.targetUser});
  factory _InterestModel.fromJson(Map<String, dynamic> json) =>
      _$InterestModelFromJson(json);

  @override
  final String id;
  @override
  final String userId;
  @override
  final String targetUserId;
  @override
  final String? message;
  @override
  @JsonKey()
  final String status;
  @override
  final String? responseMessage;
  @override
  final DateTime? respondedAt;
  @override
  final DateTime createdAt;
  @override
  final DateTime? updatedAt;
  @override
  final UserProfileModel? user;
  @override
  final UserProfileModel? targetUser;

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InterestModelCopyWith<_InterestModel> get copyWith =>
      __$InterestModelCopyWithImpl<_InterestModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InterestModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InterestModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            (identical(other.respondedAt, respondedAt) ||
                other.respondedAt == respondedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.user, user) || other.user == user) &&
            (identical(other.targetUser, targetUser) ||
                other.targetUser == targetUser));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      targetUserId,
      message,
      status,
      responseMessage,
      respondedAt,
      createdAt,
      updatedAt,
      user,
      targetUser);

  @override
  String toString() {
    return 'InterestModel(id: $id, userId: $userId, targetUserId: $targetUserId, message: $message, status: $status, responseMessage: $responseMessage, respondedAt: $respondedAt, createdAt: $createdAt, updatedAt: $updatedAt, user: $user, targetUser: $targetUser)';
  }
}

/// @nodoc
abstract mixin class _$InterestModelCopyWith<$Res>
    implements $InterestModelCopyWith<$Res> {
  factory _$InterestModelCopyWith(
          _InterestModel value, $Res Function(_InterestModel) _then) =
      __$InterestModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String userId,
      String targetUserId,
      String? message,
      String status,
      String? responseMessage,
      DateTime? respondedAt,
      DateTime createdAt,
      DateTime? updatedAt,
      UserProfileModel? user,
      UserProfileModel? targetUser});

  @override
  $UserProfileModelCopyWith<$Res>? get user;
  @override
  $UserProfileModelCopyWith<$Res>? get targetUser;
}

/// @nodoc
class __$InterestModelCopyWithImpl<$Res>
    implements _$InterestModelCopyWith<$Res> {
  __$InterestModelCopyWithImpl(this._self, this._then);

  final _InterestModel _self;
  final $Res Function(_InterestModel) _then;

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? userId = null,
    Object? targetUserId = null,
    Object? message = freezed,
    Object? status = null,
    Object? responseMessage = freezed,
    Object? respondedAt = freezed,
    Object? createdAt = null,
    Object? updatedAt = freezed,
    Object? user = freezed,
    Object? targetUser = freezed,
  }) {
    return _then(_InterestModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      userId: null == userId
          ? _self.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String,
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as String,
      responseMessage: freezed == responseMessage
          ? _self.responseMessage
          : responseMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      respondedAt: freezed == respondedAt
          ? _self.respondedAt
          : respondedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: null == createdAt
          ? _self.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: freezed == updatedAt
          ? _self.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      user: freezed == user
          ? _self.user
          : user // ignore: cast_nullable_to_non_nullable
              as UserProfileModel?,
      targetUser: freezed == targetUser
          ? _self.targetUser
          : targetUser // ignore: cast_nullable_to_non_nullable
              as UserProfileModel?,
    ));
  }

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserProfileModelCopyWith<$Res>? get user {
    if (_self.user == null) {
      return null;
    }

    return $UserProfileModelCopyWith<$Res>(_self.user!, (value) {
      return _then(_self.copyWith(user: value));
    });
  }

  /// Create a copy of InterestModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UserProfileModelCopyWith<$Res>? get targetUser {
    if (_self.targetUser == null) {
      return null;
    }

    return $UserProfileModelCopyWith<$Res>(_self.targetUser!, (value) {
      return _then(_self.copyWith(targetUser: value));
    });
  }
}

/// @nodoc
mixin _$UserProfileModel {
  String get id;
  String get firstName;
  String? get lastName;
  int? get age;
  String? get city;
  String? get state;
  String? get occupation;
  String? get profilePicUrl;
  String? get education;
  int? get height;
  String? get religion;
  String? get caste;
  String? get motherTongue;
  bool? get isVerified;
  bool? get isPremium;
  DateTime? get lastSeen;
  bool? get isOnline;

  /// Create a copy of UserProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UserProfileModelCopyWith<UserProfileModel> get copyWith =>
      _$UserProfileModelCopyWithImpl<UserProfileModel>(
          this as UserProfileModel, _$identity);

  /// Serializes this UserProfileModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UserProfileModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.profilePicUrl, profilePicUrl) ||
                other.profilePicUrl == profilePicUrl) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            (identical(other.caste, caste) || other.caste == caste) &&
            (identical(other.motherTongue, motherTongue) ||
                other.motherTongue == motherTongue) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.lastSeen, lastSeen) ||
                other.lastSeen == lastSeen) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      age,
      city,
      state,
      occupation,
      profilePicUrl,
      education,
      height,
      religion,
      caste,
      motherTongue,
      isVerified,
      isPremium,
      lastSeen,
      isOnline);

  @override
  String toString() {
    return 'UserProfileModel(id: $id, firstName: $firstName, lastName: $lastName, age: $age, city: $city, state: $state, occupation: $occupation, profilePicUrl: $profilePicUrl, education: $education, height: $height, religion: $religion, caste: $caste, motherTongue: $motherTongue, isVerified: $isVerified, isPremium: $isPremium, lastSeen: $lastSeen, isOnline: $isOnline)';
  }
}

/// @nodoc
abstract mixin class $UserProfileModelCopyWith<$Res> {
  factory $UserProfileModelCopyWith(
          UserProfileModel value, $Res Function(UserProfileModel) _then) =
      _$UserProfileModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String firstName,
      String? lastName,
      int? age,
      String? city,
      String? state,
      String? occupation,
      String? profilePicUrl,
      String? education,
      int? height,
      String? religion,
      String? caste,
      String? motherTongue,
      bool? isVerified,
      bool? isPremium,
      DateTime? lastSeen,
      bool? isOnline});
}

/// @nodoc
class _$UserProfileModelCopyWithImpl<$Res>
    implements $UserProfileModelCopyWith<$Res> {
  _$UserProfileModelCopyWithImpl(this._self, this._then);

  final UserProfileModel _self;
  final $Res Function(UserProfileModel) _then;

  /// Create a copy of UserProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? firstName = null,
    Object? lastName = freezed,
    Object? age = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? occupation = freezed,
    Object? profilePicUrl = freezed,
    Object? education = freezed,
    Object? height = freezed,
    Object? religion = freezed,
    Object? caste = freezed,
    Object? motherTongue = freezed,
    Object? isVerified = freezed,
    Object? isPremium = freezed,
    Object? lastSeen = freezed,
    Object? isOnline = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _self.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePicUrl: freezed == profilePicUrl
          ? _self.profilePicUrl
          : profilePicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _self.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      religion: freezed == religion
          ? _self.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      caste: freezed == caste
          ? _self.caste
          : caste // ignore: cast_nullable_to_non_nullable
              as String?,
      motherTongue: freezed == motherTongue
          ? _self.motherTongue
          : motherTongue // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: freezed == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isPremium: freezed == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSeen: freezed == lastSeen
          ? _self.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isOnline: freezed == isOnline
          ? _self.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// Adds pattern-matching-related methods to [UserProfileModel].
extension UserProfileModelPatterns on UserProfileModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_UserProfileModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserProfileModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_UserProfileModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserProfileModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_UserProfileModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserProfileModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String firstName,
            String? lastName,
            int? age,
            String? city,
            String? state,
            String? occupation,
            String? profilePicUrl,
            String? education,
            int? height,
            String? religion,
            String? caste,
            String? motherTongue,
            bool? isVerified,
            bool? isPremium,
            DateTime? lastSeen,
            bool? isOnline)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _UserProfileModel() when $default != null:
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.age,
            _that.city,
            _that.state,
            _that.occupation,
            _that.profilePicUrl,
            _that.education,
            _that.height,
            _that.religion,
            _that.caste,
            _that.motherTongue,
            _that.isVerified,
            _that.isPremium,
            _that.lastSeen,
            _that.isOnline);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String firstName,
            String? lastName,
            int? age,
            String? city,
            String? state,
            String? occupation,
            String? profilePicUrl,
            String? education,
            int? height,
            String? religion,
            String? caste,
            String? motherTongue,
            bool? isVerified,
            bool? isPremium,
            DateTime? lastSeen,
            bool? isOnline)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserProfileModel():
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.age,
            _that.city,
            _that.state,
            _that.occupation,
            _that.profilePicUrl,
            _that.education,
            _that.height,
            _that.religion,
            _that.caste,
            _that.motherTongue,
            _that.isVerified,
            _that.isPremium,
            _that.lastSeen,
            _that.isOnline);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String firstName,
            String? lastName,
            int? age,
            String? city,
            String? state,
            String? occupation,
            String? profilePicUrl,
            String? education,
            int? height,
            String? religion,
            String? caste,
            String? motherTongue,
            bool? isVerified,
            bool? isPremium,
            DateTime? lastSeen,
            bool? isOnline)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _UserProfileModel() when $default != null:
        return $default(
            _that.id,
            _that.firstName,
            _that.lastName,
            _that.age,
            _that.city,
            _that.state,
            _that.occupation,
            _that.profilePicUrl,
            _that.education,
            _that.height,
            _that.religion,
            _that.caste,
            _that.motherTongue,
            _that.isVerified,
            _that.isPremium,
            _that.lastSeen,
            _that.isOnline);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _UserProfileModel implements UserProfileModel {
  const _UserProfileModel(
      {required this.id,
      required this.firstName,
      this.lastName,
      this.age,
      this.city,
      this.state,
      this.occupation,
      this.profilePicUrl,
      this.education,
      this.height,
      this.religion,
      this.caste,
      this.motherTongue,
      this.isVerified,
      this.isPremium,
      this.lastSeen,
      this.isOnline});
  factory _UserProfileModel.fromJson(Map<String, dynamic> json) =>
      _$UserProfileModelFromJson(json);

  @override
  final String id;
  @override
  final String firstName;
  @override
  final String? lastName;
  @override
  final int? age;
  @override
  final String? city;
  @override
  final String? state;
  @override
  final String? occupation;
  @override
  final String? profilePicUrl;
  @override
  final String? education;
  @override
  final int? height;
  @override
  final String? religion;
  @override
  final String? caste;
  @override
  final String? motherTongue;
  @override
  final bool? isVerified;
  @override
  final bool? isPremium;
  @override
  final DateTime? lastSeen;
  @override
  final bool? isOnline;

  /// Create a copy of UserProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UserProfileModelCopyWith<_UserProfileModel> get copyWith =>
      __$UserProfileModelCopyWithImpl<_UserProfileModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UserProfileModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UserProfileModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.firstName, firstName) ||
                other.firstName == firstName) &&
            (identical(other.lastName, lastName) ||
                other.lastName == lastName) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.city, city) || other.city == city) &&
            (identical(other.state, state) || other.state == state) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.profilePicUrl, profilePicUrl) ||
                other.profilePicUrl == profilePicUrl) &&
            (identical(other.education, education) ||
                other.education == education) &&
            (identical(other.height, height) || other.height == height) &&
            (identical(other.religion, religion) ||
                other.religion == religion) &&
            (identical(other.caste, caste) || other.caste == caste) &&
            (identical(other.motherTongue, motherTongue) ||
                other.motherTongue == motherTongue) &&
            (identical(other.isVerified, isVerified) ||
                other.isVerified == isVerified) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.lastSeen, lastSeen) ||
                other.lastSeen == lastSeen) &&
            (identical(other.isOnline, isOnline) ||
                other.isOnline == isOnline));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      firstName,
      lastName,
      age,
      city,
      state,
      occupation,
      profilePicUrl,
      education,
      height,
      religion,
      caste,
      motherTongue,
      isVerified,
      isPremium,
      lastSeen,
      isOnline);

  @override
  String toString() {
    return 'UserProfileModel(id: $id, firstName: $firstName, lastName: $lastName, age: $age, city: $city, state: $state, occupation: $occupation, profilePicUrl: $profilePicUrl, education: $education, height: $height, religion: $religion, caste: $caste, motherTongue: $motherTongue, isVerified: $isVerified, isPremium: $isPremium, lastSeen: $lastSeen, isOnline: $isOnline)';
  }
}

/// @nodoc
abstract mixin class _$UserProfileModelCopyWith<$Res>
    implements $UserProfileModelCopyWith<$Res> {
  factory _$UserProfileModelCopyWith(
          _UserProfileModel value, $Res Function(_UserProfileModel) _then) =
      __$UserProfileModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String firstName,
      String? lastName,
      int? age,
      String? city,
      String? state,
      String? occupation,
      String? profilePicUrl,
      String? education,
      int? height,
      String? religion,
      String? caste,
      String? motherTongue,
      bool? isVerified,
      bool? isPremium,
      DateTime? lastSeen,
      bool? isOnline});
}

/// @nodoc
class __$UserProfileModelCopyWithImpl<$Res>
    implements _$UserProfileModelCopyWith<$Res> {
  __$UserProfileModelCopyWithImpl(this._self, this._then);

  final _UserProfileModel _self;
  final $Res Function(_UserProfileModel) _then;

  /// Create a copy of UserProfileModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? firstName = null,
    Object? lastName = freezed,
    Object? age = freezed,
    Object? city = freezed,
    Object? state = freezed,
    Object? occupation = freezed,
    Object? profilePicUrl = freezed,
    Object? education = freezed,
    Object? height = freezed,
    Object? religion = freezed,
    Object? caste = freezed,
    Object? motherTongue = freezed,
    Object? isVerified = freezed,
    Object? isPremium = freezed,
    Object? lastSeen = freezed,
    Object? isOnline = freezed,
  }) {
    return _then(_UserProfileModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      firstName: null == firstName
          ? _self.firstName
          : firstName // ignore: cast_nullable_to_non_nullable
              as String,
      lastName: freezed == lastName
          ? _self.lastName
          : lastName // ignore: cast_nullable_to_non_nullable
              as String?,
      age: freezed == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int?,
      city: freezed == city
          ? _self.city
          : city // ignore: cast_nullable_to_non_nullable
              as String?,
      state: freezed == state
          ? _self.state
          : state // ignore: cast_nullable_to_non_nullable
              as String?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      profilePicUrl: freezed == profilePicUrl
          ? _self.profilePicUrl
          : profilePicUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
      height: freezed == height
          ? _self.height
          : height // ignore: cast_nullable_to_non_nullable
              as int?,
      religion: freezed == religion
          ? _self.religion
          : religion // ignore: cast_nullable_to_non_nullable
              as String?,
      caste: freezed == caste
          ? _self.caste
          : caste // ignore: cast_nullable_to_non_nullable
              as String?,
      motherTongue: freezed == motherTongue
          ? _self.motherTongue
          : motherTongue // ignore: cast_nullable_to_non_nullable
              as String?,
      isVerified: freezed == isVerified
          ? _self.isVerified
          : isVerified // ignore: cast_nullable_to_non_nullable
              as bool?,
      isPremium: freezed == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool?,
      lastSeen: freezed == lastSeen
          ? _self.lastSeen
          : lastSeen // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      isOnline: freezed == isOnline
          ? _self.isOnline
          : isOnline // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
mixin _$SendInterestRequest {
  String get targetUserId;
  String? get message;

  /// Create a copy of SendInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SendInterestRequestCopyWith<SendInterestRequest> get copyWith =>
      _$SendInterestRequestCopyWithImpl<SendInterestRequest>(
          this as SendInterestRequest, _$identity);

  /// Serializes this SendInterestRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SendInterestRequest &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, targetUserId, message);

  @override
  String toString() {
    return 'SendInterestRequest(targetUserId: $targetUserId, message: $message)';
  }
}

/// @nodoc
abstract mixin class $SendInterestRequestCopyWith<$Res> {
  factory $SendInterestRequestCopyWith(
          SendInterestRequest value, $Res Function(SendInterestRequest) _then) =
      _$SendInterestRequestCopyWithImpl;
  @useResult
  $Res call({String targetUserId, String? message});
}

/// @nodoc
class _$SendInterestRequestCopyWithImpl<$Res>
    implements $SendInterestRequestCopyWith<$Res> {
  _$SendInterestRequestCopyWithImpl(this._self, this._then);

  final SendInterestRequest _self;
  final $Res Function(SendInterestRequest) _then;

  /// Create a copy of SendInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? targetUserId = null,
    Object? message = freezed,
  }) {
    return _then(_self.copyWith(
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [SendInterestRequest].
extension SendInterestRequestPatterns on SendInterestRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_SendInterestRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SendInterestRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_SendInterestRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendInterestRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_SendInterestRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendInterestRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String targetUserId, String? message)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _SendInterestRequest() when $default != null:
        return $default(_that.targetUserId, _that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String targetUserId, String? message) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendInterestRequest():
        return $default(_that.targetUserId, _that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String targetUserId, String? message)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _SendInterestRequest() when $default != null:
        return $default(_that.targetUserId, _that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _SendInterestRequest implements SendInterestRequest {
  const _SendInterestRequest({required this.targetUserId, this.message});
  factory _SendInterestRequest.fromJson(Map<String, dynamic> json) =>
      _$SendInterestRequestFromJson(json);

  @override
  final String targetUserId;
  @override
  final String? message;

  /// Create a copy of SendInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SendInterestRequestCopyWith<_SendInterestRequest> get copyWith =>
      __$SendInterestRequestCopyWithImpl<_SendInterestRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SendInterestRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SendInterestRequest &&
            (identical(other.targetUserId, targetUserId) ||
                other.targetUserId == targetUserId) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, targetUserId, message);

  @override
  String toString() {
    return 'SendInterestRequest(targetUserId: $targetUserId, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$SendInterestRequestCopyWith<$Res>
    implements $SendInterestRequestCopyWith<$Res> {
  factory _$SendInterestRequestCopyWith(_SendInterestRequest value,
          $Res Function(_SendInterestRequest) _then) =
      __$SendInterestRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String targetUserId, String? message});
}

/// @nodoc
class __$SendInterestRequestCopyWithImpl<$Res>
    implements _$SendInterestRequestCopyWith<$Res> {
  __$SendInterestRequestCopyWithImpl(this._self, this._then);

  final _SendInterestRequest _self;
  final $Res Function(_SendInterestRequest) _then;

  /// Create a copy of SendInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? targetUserId = null,
    Object? message = freezed,
  }) {
    return _then(_SendInterestRequest(
      targetUserId: null == targetUserId
          ? _self.targetUserId
          : targetUserId // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$RespondInterestRequest {
  String get interestId;
  String get response; // 'ACCEPTED' or 'REJECTED'
  String? get message;

  /// Create a copy of RespondInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RespondInterestRequestCopyWith<RespondInterestRequest> get copyWith =>
      _$RespondInterestRequestCopyWithImpl<RespondInterestRequest>(
          this as RespondInterestRequest, _$identity);

  /// Serializes this RespondInterestRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RespondInterestRequest &&
            (identical(other.interestId, interestId) ||
                other.interestId == interestId) &&
            (identical(other.response, response) ||
                other.response == response) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, interestId, response, message);

  @override
  String toString() {
    return 'RespondInterestRequest(interestId: $interestId, response: $response, message: $message)';
  }
}

/// @nodoc
abstract mixin class $RespondInterestRequestCopyWith<$Res> {
  factory $RespondInterestRequestCopyWith(RespondInterestRequest value,
          $Res Function(RespondInterestRequest) _then) =
      _$RespondInterestRequestCopyWithImpl;
  @useResult
  $Res call({String interestId, String response, String? message});
}

/// @nodoc
class _$RespondInterestRequestCopyWithImpl<$Res>
    implements $RespondInterestRequestCopyWith<$Res> {
  _$RespondInterestRequestCopyWithImpl(this._self, this._then);

  final RespondInterestRequest _self;
  final $Res Function(RespondInterestRequest) _then;

  /// Create a copy of RespondInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? interestId = null,
    Object? response = null,
    Object? message = freezed,
  }) {
    return _then(_self.copyWith(
      interestId: null == interestId
          ? _self.interestId
          : interestId // ignore: cast_nullable_to_non_nullable
              as String,
      response: null == response
          ? _self.response
          : response // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [RespondInterestRequest].
extension RespondInterestRequestPatterns on RespondInterestRequest {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_RespondInterestRequest value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RespondInterestRequest() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_RespondInterestRequest value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RespondInterestRequest():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_RespondInterestRequest value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RespondInterestRequest() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(String interestId, String response, String? message)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _RespondInterestRequest() when $default != null:
        return $default(_that.interestId, _that.response, _that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(String interestId, String response, String? message)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RespondInterestRequest():
        return $default(_that.interestId, _that.response, _that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(String interestId, String response, String? message)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _RespondInterestRequest() when $default != null:
        return $default(_that.interestId, _that.response, _that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _RespondInterestRequest implements RespondInterestRequest {
  const _RespondInterestRequest(
      {required this.interestId, required this.response, this.message});
  factory _RespondInterestRequest.fromJson(Map<String, dynamic> json) =>
      _$RespondInterestRequestFromJson(json);

  @override
  final String interestId;
  @override
  final String response;
// 'ACCEPTED' or 'REJECTED'
  @override
  final String? message;

  /// Create a copy of RespondInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RespondInterestRequestCopyWith<_RespondInterestRequest> get copyWith =>
      __$RespondInterestRequestCopyWithImpl<_RespondInterestRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RespondInterestRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RespondInterestRequest &&
            (identical(other.interestId, interestId) ||
                other.interestId == interestId) &&
            (identical(other.response, response) ||
                other.response == response) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, interestId, response, message);

  @override
  String toString() {
    return 'RespondInterestRequest(interestId: $interestId, response: $response, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$RespondInterestRequestCopyWith<$Res>
    implements $RespondInterestRequestCopyWith<$Res> {
  factory _$RespondInterestRequestCopyWith(_RespondInterestRequest value,
          $Res Function(_RespondInterestRequest) _then) =
      __$RespondInterestRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String interestId, String response, String? message});
}

/// @nodoc
class __$RespondInterestRequestCopyWithImpl<$Res>
    implements _$RespondInterestRequestCopyWith<$Res> {
  __$RespondInterestRequestCopyWithImpl(this._self, this._then);

  final _RespondInterestRequest _self;
  final $Res Function(_RespondInterestRequest) _then;

  /// Create a copy of RespondInterestRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? interestId = null,
    Object? response = null,
    Object? message = freezed,
  }) {
    return _then(_RespondInterestRequest(
      interestId: null == interestId
          ? _self.interestId
          : interestId // ignore: cast_nullable_to_non_nullable
              as String,
      response: null == response
          ? _self.response
          : response // ignore: cast_nullable_to_non_nullable
              as String,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$ActivityStatsModel {
  int get interestsSent;
  int get interestsReceived;
  int get profileViews;
  int get totalMatches;
  int get acceptedInterests;
  int get pendingInterests;
  int get declinedInterests;
  List<int> get weeklyViews;
  List<int> get weeklyInterests;
  double get successRate;
  int get todayViews;
  int get todayInterests;

  /// Create a copy of ActivityStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ActivityStatsModelCopyWith<ActivityStatsModel> get copyWith =>
      _$ActivityStatsModelCopyWithImpl<ActivityStatsModel>(
          this as ActivityStatsModel, _$identity);

  /// Serializes this ActivityStatsModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ActivityStatsModel &&
            (identical(other.interestsSent, interestsSent) ||
                other.interestsSent == interestsSent) &&
            (identical(other.interestsReceived, interestsReceived) ||
                other.interestsReceived == interestsReceived) &&
            (identical(other.profileViews, profileViews) ||
                other.profileViews == profileViews) &&
            (identical(other.totalMatches, totalMatches) ||
                other.totalMatches == totalMatches) &&
            (identical(other.acceptedInterests, acceptedInterests) ||
                other.acceptedInterests == acceptedInterests) &&
            (identical(other.pendingInterests, pendingInterests) ||
                other.pendingInterests == pendingInterests) &&
            (identical(other.declinedInterests, declinedInterests) ||
                other.declinedInterests == declinedInterests) &&
            const DeepCollectionEquality()
                .equals(other.weeklyViews, weeklyViews) &&
            const DeepCollectionEquality()
                .equals(other.weeklyInterests, weeklyInterests) &&
            (identical(other.successRate, successRate) ||
                other.successRate == successRate) &&
            (identical(other.todayViews, todayViews) ||
                other.todayViews == todayViews) &&
            (identical(other.todayInterests, todayInterests) ||
                other.todayInterests == todayInterests));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      interestsSent,
      interestsReceived,
      profileViews,
      totalMatches,
      acceptedInterests,
      pendingInterests,
      declinedInterests,
      const DeepCollectionEquality().hash(weeklyViews),
      const DeepCollectionEquality().hash(weeklyInterests),
      successRate,
      todayViews,
      todayInterests);

  @override
  String toString() {
    return 'ActivityStatsModel(interestsSent: $interestsSent, interestsReceived: $interestsReceived, profileViews: $profileViews, totalMatches: $totalMatches, acceptedInterests: $acceptedInterests, pendingInterests: $pendingInterests, declinedInterests: $declinedInterests, weeklyViews: $weeklyViews, weeklyInterests: $weeklyInterests, successRate: $successRate, todayViews: $todayViews, todayInterests: $todayInterests)';
  }
}

/// @nodoc
abstract mixin class $ActivityStatsModelCopyWith<$Res> {
  factory $ActivityStatsModelCopyWith(
          ActivityStatsModel value, $Res Function(ActivityStatsModel) _then) =
      _$ActivityStatsModelCopyWithImpl;
  @useResult
  $Res call(
      {int interestsSent,
      int interestsReceived,
      int profileViews,
      int totalMatches,
      int acceptedInterests,
      int pendingInterests,
      int declinedInterests,
      List<int> weeklyViews,
      List<int> weeklyInterests,
      double successRate,
      int todayViews,
      int todayInterests});
}

/// @nodoc
class _$ActivityStatsModelCopyWithImpl<$Res>
    implements $ActivityStatsModelCopyWith<$Res> {
  _$ActivityStatsModelCopyWithImpl(this._self, this._then);

  final ActivityStatsModel _self;
  final $Res Function(ActivityStatsModel) _then;

  /// Create a copy of ActivityStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? interestsSent = null,
    Object? interestsReceived = null,
    Object? profileViews = null,
    Object? totalMatches = null,
    Object? acceptedInterests = null,
    Object? pendingInterests = null,
    Object? declinedInterests = null,
    Object? weeklyViews = null,
    Object? weeklyInterests = null,
    Object? successRate = null,
    Object? todayViews = null,
    Object? todayInterests = null,
  }) {
    return _then(_self.copyWith(
      interestsSent: null == interestsSent
          ? _self.interestsSent
          : interestsSent // ignore: cast_nullable_to_non_nullable
              as int,
      interestsReceived: null == interestsReceived
          ? _self.interestsReceived
          : interestsReceived // ignore: cast_nullable_to_non_nullable
              as int,
      profileViews: null == profileViews
          ? _self.profileViews
          : profileViews // ignore: cast_nullable_to_non_nullable
              as int,
      totalMatches: null == totalMatches
          ? _self.totalMatches
          : totalMatches // ignore: cast_nullable_to_non_nullable
              as int,
      acceptedInterests: null == acceptedInterests
          ? _self.acceptedInterests
          : acceptedInterests // ignore: cast_nullable_to_non_nullable
              as int,
      pendingInterests: null == pendingInterests
          ? _self.pendingInterests
          : pendingInterests // ignore: cast_nullable_to_non_nullable
              as int,
      declinedInterests: null == declinedInterests
          ? _self.declinedInterests
          : declinedInterests // ignore: cast_nullable_to_non_nullable
              as int,
      weeklyViews: null == weeklyViews
          ? _self.weeklyViews
          : weeklyViews // ignore: cast_nullable_to_non_nullable
              as List<int>,
      weeklyInterests: null == weeklyInterests
          ? _self.weeklyInterests
          : weeklyInterests // ignore: cast_nullable_to_non_nullable
              as List<int>,
      successRate: null == successRate
          ? _self.successRate
          : successRate // ignore: cast_nullable_to_non_nullable
              as double,
      todayViews: null == todayViews
          ? _self.todayViews
          : todayViews // ignore: cast_nullable_to_non_nullable
              as int,
      todayInterests: null == todayInterests
          ? _self.todayInterests
          : todayInterests // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// Adds pattern-matching-related methods to [ActivityStatsModel].
extension ActivityStatsModelPatterns on ActivityStatsModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_ActivityStatsModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActivityStatsModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_ActivityStatsModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivityStatsModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_ActivityStatsModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivityStatsModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            int interestsSent,
            int interestsReceived,
            int profileViews,
            int totalMatches,
            int acceptedInterests,
            int pendingInterests,
            int declinedInterests,
            List<int> weeklyViews,
            List<int> weeklyInterests,
            double successRate,
            int todayViews,
            int todayInterests)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _ActivityStatsModel() when $default != null:
        return $default(
            _that.interestsSent,
            _that.interestsReceived,
            _that.profileViews,
            _that.totalMatches,
            _that.acceptedInterests,
            _that.pendingInterests,
            _that.declinedInterests,
            _that.weeklyViews,
            _that.weeklyInterests,
            _that.successRate,
            _that.todayViews,
            _that.todayInterests);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            int interestsSent,
            int interestsReceived,
            int profileViews,
            int totalMatches,
            int acceptedInterests,
            int pendingInterests,
            int declinedInterests,
            List<int> weeklyViews,
            List<int> weeklyInterests,
            double successRate,
            int todayViews,
            int todayInterests)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivityStatsModel():
        return $default(
            _that.interestsSent,
            _that.interestsReceived,
            _that.profileViews,
            _that.totalMatches,
            _that.acceptedInterests,
            _that.pendingInterests,
            _that.declinedInterests,
            _that.weeklyViews,
            _that.weeklyInterests,
            _that.successRate,
            _that.todayViews,
            _that.todayInterests);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            int interestsSent,
            int interestsReceived,
            int profileViews,
            int totalMatches,
            int acceptedInterests,
            int pendingInterests,
            int declinedInterests,
            List<int> weeklyViews,
            List<int> weeklyInterests,
            double successRate,
            int todayViews,
            int todayInterests)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _ActivityStatsModel() when $default != null:
        return $default(
            _that.interestsSent,
            _that.interestsReceived,
            _that.profileViews,
            _that.totalMatches,
            _that.acceptedInterests,
            _that.pendingInterests,
            _that.declinedInterests,
            _that.weeklyViews,
            _that.weeklyInterests,
            _that.successRate,
            _that.todayViews,
            _that.todayInterests);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _ActivityStatsModel implements ActivityStatsModel {
  const _ActivityStatsModel(
      {this.interestsSent = 0,
      this.interestsReceived = 0,
      this.profileViews = 0,
      this.totalMatches = 0,
      this.acceptedInterests = 0,
      this.pendingInterests = 0,
      this.declinedInterests = 0,
      final List<int> weeklyViews = const [],
      final List<int> weeklyInterests = const [],
      this.successRate = 0.0,
      this.todayViews = 0,
      this.todayInterests = 0})
      : _weeklyViews = weeklyViews,
        _weeklyInterests = weeklyInterests;
  factory _ActivityStatsModel.fromJson(Map<String, dynamic> json) =>
      _$ActivityStatsModelFromJson(json);

  @override
  @JsonKey()
  final int interestsSent;
  @override
  @JsonKey()
  final int interestsReceived;
  @override
  @JsonKey()
  final int profileViews;
  @override
  @JsonKey()
  final int totalMatches;
  @override
  @JsonKey()
  final int acceptedInterests;
  @override
  @JsonKey()
  final int pendingInterests;
  @override
  @JsonKey()
  final int declinedInterests;
  final List<int> _weeklyViews;
  @override
  @JsonKey()
  List<int> get weeklyViews {
    if (_weeklyViews is EqualUnmodifiableListView) return _weeklyViews;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weeklyViews);
  }

  final List<int> _weeklyInterests;
  @override
  @JsonKey()
  List<int> get weeklyInterests {
    if (_weeklyInterests is EqualUnmodifiableListView) return _weeklyInterests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_weeklyInterests);
  }

  @override
  @JsonKey()
  final double successRate;
  @override
  @JsonKey()
  final int todayViews;
  @override
  @JsonKey()
  final int todayInterests;

  /// Create a copy of ActivityStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ActivityStatsModelCopyWith<_ActivityStatsModel> get copyWith =>
      __$ActivityStatsModelCopyWithImpl<_ActivityStatsModel>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ActivityStatsModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ActivityStatsModel &&
            (identical(other.interestsSent, interestsSent) ||
                other.interestsSent == interestsSent) &&
            (identical(other.interestsReceived, interestsReceived) ||
                other.interestsReceived == interestsReceived) &&
            (identical(other.profileViews, profileViews) ||
                other.profileViews == profileViews) &&
            (identical(other.totalMatches, totalMatches) ||
                other.totalMatches == totalMatches) &&
            (identical(other.acceptedInterests, acceptedInterests) ||
                other.acceptedInterests == acceptedInterests) &&
            (identical(other.pendingInterests, pendingInterests) ||
                other.pendingInterests == pendingInterests) &&
            (identical(other.declinedInterests, declinedInterests) ||
                other.declinedInterests == declinedInterests) &&
            const DeepCollectionEquality()
                .equals(other._weeklyViews, _weeklyViews) &&
            const DeepCollectionEquality()
                .equals(other._weeklyInterests, _weeklyInterests) &&
            (identical(other.successRate, successRate) ||
                other.successRate == successRate) &&
            (identical(other.todayViews, todayViews) ||
                other.todayViews == todayViews) &&
            (identical(other.todayInterests, todayInterests) ||
                other.todayInterests == todayInterests));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      interestsSent,
      interestsReceived,
      profileViews,
      totalMatches,
      acceptedInterests,
      pendingInterests,
      declinedInterests,
      const DeepCollectionEquality().hash(_weeklyViews),
      const DeepCollectionEquality().hash(_weeklyInterests),
      successRate,
      todayViews,
      todayInterests);

  @override
  String toString() {
    return 'ActivityStatsModel(interestsSent: $interestsSent, interestsReceived: $interestsReceived, profileViews: $profileViews, totalMatches: $totalMatches, acceptedInterests: $acceptedInterests, pendingInterests: $pendingInterests, declinedInterests: $declinedInterests, weeklyViews: $weeklyViews, weeklyInterests: $weeklyInterests, successRate: $successRate, todayViews: $todayViews, todayInterests: $todayInterests)';
  }
}

/// @nodoc
abstract mixin class _$ActivityStatsModelCopyWith<$Res>
    implements $ActivityStatsModelCopyWith<$Res> {
  factory _$ActivityStatsModelCopyWith(
          _ActivityStatsModel value, $Res Function(_ActivityStatsModel) _then) =
      __$ActivityStatsModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int interestsSent,
      int interestsReceived,
      int profileViews,
      int totalMatches,
      int acceptedInterests,
      int pendingInterests,
      int declinedInterests,
      List<int> weeklyViews,
      List<int> weeklyInterests,
      double successRate,
      int todayViews,
      int todayInterests});
}

/// @nodoc
class __$ActivityStatsModelCopyWithImpl<$Res>
    implements _$ActivityStatsModelCopyWith<$Res> {
  __$ActivityStatsModelCopyWithImpl(this._self, this._then);

  final _ActivityStatsModel _self;
  final $Res Function(_ActivityStatsModel) _then;

  /// Create a copy of ActivityStatsModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? interestsSent = null,
    Object? interestsReceived = null,
    Object? profileViews = null,
    Object? totalMatches = null,
    Object? acceptedInterests = null,
    Object? pendingInterests = null,
    Object? declinedInterests = null,
    Object? weeklyViews = null,
    Object? weeklyInterests = null,
    Object? successRate = null,
    Object? todayViews = null,
    Object? todayInterests = null,
  }) {
    return _then(_ActivityStatsModel(
      interestsSent: null == interestsSent
          ? _self.interestsSent
          : interestsSent // ignore: cast_nullable_to_non_nullable
              as int,
      interestsReceived: null == interestsReceived
          ? _self.interestsReceived
          : interestsReceived // ignore: cast_nullable_to_non_nullable
              as int,
      profileViews: null == profileViews
          ? _self.profileViews
          : profileViews // ignore: cast_nullable_to_non_nullable
              as int,
      totalMatches: null == totalMatches
          ? _self.totalMatches
          : totalMatches // ignore: cast_nullable_to_non_nullable
              as int,
      acceptedInterests: null == acceptedInterests
          ? _self.acceptedInterests
          : acceptedInterests // ignore: cast_nullable_to_non_nullable
              as int,
      pendingInterests: null == pendingInterests
          ? _self.pendingInterests
          : pendingInterests // ignore: cast_nullable_to_non_nullable
              as int,
      declinedInterests: null == declinedInterests
          ? _self.declinedInterests
          : declinedInterests // ignore: cast_nullable_to_non_nullable
              as int,
      weeklyViews: null == weeklyViews
          ? _self._weeklyViews
          : weeklyViews // ignore: cast_nullable_to_non_nullable
              as List<int>,
      weeklyInterests: null == weeklyInterests
          ? _self._weeklyInterests
          : weeklyInterests // ignore: cast_nullable_to_non_nullable
              as List<int>,
      successRate: null == successRate
          ? _self.successRate
          : successRate // ignore: cast_nullable_to_non_nullable
              as double,
      todayViews: null == todayViews
          ? _self.todayViews
          : todayViews // ignore: cast_nullable_to_non_nullable
              as int,
      todayInterests: null == todayInterests
          ? _self.todayInterests
          : todayInterests // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$InterestActivityModel {
  String get id;
  String get profileId;
  String get profileName;
  String? get profilePhoto;
  int get age;
  String get location;
  InterestType get type;
  InterestStatus get status;
  DateTime get timestamp;
  String? get message;
  String? get responseMessage;
  bool? get isRead;
  bool? get isPremium;
  String? get occupation;
  String? get education;

  /// Create a copy of InterestActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InterestActivityModelCopyWith<InterestActivityModel> get copyWith =>
      _$InterestActivityModelCopyWithImpl<InterestActivityModel>(
          this as InterestActivityModel, _$identity);

  /// Serializes this InterestActivityModel to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InterestActivityModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.profileId, profileId) ||
                other.profileId == profileId) &&
            (identical(other.profileName, profileName) ||
                other.profileName == profileName) &&
            (identical(other.profilePhoto, profilePhoto) ||
                other.profilePhoto == profilePhoto) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.education, education) ||
                other.education == education));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      profileId,
      profileName,
      profilePhoto,
      age,
      location,
      type,
      status,
      timestamp,
      message,
      responseMessage,
      isRead,
      isPremium,
      occupation,
      education);

  @override
  String toString() {
    return 'InterestActivityModel(id: $id, profileId: $profileId, profileName: $profileName, profilePhoto: $profilePhoto, age: $age, location: $location, type: $type, status: $status, timestamp: $timestamp, message: $message, responseMessage: $responseMessage, isRead: $isRead, isPremium: $isPremium, occupation: $occupation, education: $education)';
  }
}

/// @nodoc
abstract mixin class $InterestActivityModelCopyWith<$Res> {
  factory $InterestActivityModelCopyWith(InterestActivityModel value,
          $Res Function(InterestActivityModel) _then) =
      _$InterestActivityModelCopyWithImpl;
  @useResult
  $Res call(
      {String id,
      String profileId,
      String profileName,
      String? profilePhoto,
      int age,
      String location,
      InterestType type,
      InterestStatus status,
      DateTime timestamp,
      String? message,
      String? responseMessage,
      bool? isRead,
      bool? isPremium,
      String? occupation,
      String? education});
}

/// @nodoc
class _$InterestActivityModelCopyWithImpl<$Res>
    implements $InterestActivityModelCopyWith<$Res> {
  _$InterestActivityModelCopyWithImpl(this._self, this._then);

  final InterestActivityModel _self;
  final $Res Function(InterestActivityModel) _then;

  /// Create a copy of InterestActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? profileId = null,
    Object? profileName = null,
    Object? profilePhoto = freezed,
    Object? age = null,
    Object? location = null,
    Object? type = null,
    Object? status = null,
    Object? timestamp = null,
    Object? message = freezed,
    Object? responseMessage = freezed,
    Object? isRead = freezed,
    Object? isPremium = freezed,
    Object? occupation = freezed,
    Object? education = freezed,
  }) {
    return _then(_self.copyWith(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      profileId: null == profileId
          ? _self.profileId
          : profileId // ignore: cast_nullable_to_non_nullable
              as String,
      profileName: null == profileName
          ? _self.profileName
          : profileName // ignore: cast_nullable_to_non_nullable
              as String,
      profilePhoto: freezed == profilePhoto
          ? _self.profilePhoto
          : profilePhoto // ignore: cast_nullable_to_non_nullable
              as String?,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int,
      location: null == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as InterestType,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as InterestStatus,
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      responseMessage: freezed == responseMessage
          ? _self.responseMessage
          : responseMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isRead: freezed == isRead
          ? _self.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool?,
      isPremium: freezed == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// Adds pattern-matching-related methods to [InterestActivityModel].
extension InterestActivityModelPatterns on InterestActivityModel {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_InterestActivityModel value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestActivityModel() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_InterestActivityModel value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestActivityModel():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_InterestActivityModel value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestActivityModel() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            String id,
            String profileId,
            String profileName,
            String? profilePhoto,
            int age,
            String location,
            InterestType type,
            InterestStatus status,
            DateTime timestamp,
            String? message,
            String? responseMessage,
            bool? isRead,
            bool? isPremium,
            String? occupation,
            String? education)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestActivityModel() when $default != null:
        return $default(
            _that.id,
            _that.profileId,
            _that.profileName,
            _that.profilePhoto,
            _that.age,
            _that.location,
            _that.type,
            _that.status,
            _that.timestamp,
            _that.message,
            _that.responseMessage,
            _that.isRead,
            _that.isPremium,
            _that.occupation,
            _that.education);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            String id,
            String profileId,
            String profileName,
            String? profilePhoto,
            int age,
            String location,
            InterestType type,
            InterestStatus status,
            DateTime timestamp,
            String? message,
            String? responseMessage,
            bool? isRead,
            bool? isPremium,
            String? occupation,
            String? education)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestActivityModel():
        return $default(
            _that.id,
            _that.profileId,
            _that.profileName,
            _that.profilePhoto,
            _that.age,
            _that.location,
            _that.type,
            _that.status,
            _that.timestamp,
            _that.message,
            _that.responseMessage,
            _that.isRead,
            _that.isPremium,
            _that.occupation,
            _that.education);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            String id,
            String profileId,
            String profileName,
            String? profilePhoto,
            int age,
            String location,
            InterestType type,
            InterestStatus status,
            DateTime timestamp,
            String? message,
            String? responseMessage,
            bool? isRead,
            bool? isPremium,
            String? occupation,
            String? education)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestActivityModel() when $default != null:
        return $default(
            _that.id,
            _that.profileId,
            _that.profileName,
            _that.profilePhoto,
            _that.age,
            _that.location,
            _that.type,
            _that.status,
            _that.timestamp,
            _that.message,
            _that.responseMessage,
            _that.isRead,
            _that.isPremium,
            _that.occupation,
            _that.education);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _InterestActivityModel implements InterestActivityModel {
  const _InterestActivityModel(
      {required this.id,
      required this.profileId,
      required this.profileName,
      this.profilePhoto,
      required this.age,
      required this.location,
      required this.type,
      required this.status,
      required this.timestamp,
      this.message,
      this.responseMessage,
      this.isRead,
      this.isPremium,
      this.occupation,
      this.education});
  factory _InterestActivityModel.fromJson(Map<String, dynamic> json) =>
      _$InterestActivityModelFromJson(json);

  @override
  final String id;
  @override
  final String profileId;
  @override
  final String profileName;
  @override
  final String? profilePhoto;
  @override
  final int age;
  @override
  final String location;
  @override
  final InterestType type;
  @override
  final InterestStatus status;
  @override
  final DateTime timestamp;
  @override
  final String? message;
  @override
  final String? responseMessage;
  @override
  final bool? isRead;
  @override
  final bool? isPremium;
  @override
  final String? occupation;
  @override
  final String? education;

  /// Create a copy of InterestActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InterestActivityModelCopyWith<_InterestActivityModel> get copyWith =>
      __$InterestActivityModelCopyWithImpl<_InterestActivityModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InterestActivityModelToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InterestActivityModel &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.profileId, profileId) ||
                other.profileId == profileId) &&
            (identical(other.profileName, profileName) ||
                other.profileName == profileName) &&
            (identical(other.profilePhoto, profilePhoto) ||
                other.profilePhoto == profilePhoto) &&
            (identical(other.age, age) || other.age == age) &&
            (identical(other.location, location) ||
                other.location == location) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp) &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.responseMessage, responseMessage) ||
                other.responseMessage == responseMessage) &&
            (identical(other.isRead, isRead) || other.isRead == isRead) &&
            (identical(other.isPremium, isPremium) ||
                other.isPremium == isPremium) &&
            (identical(other.occupation, occupation) ||
                other.occupation == occupation) &&
            (identical(other.education, education) ||
                other.education == education));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      profileId,
      profileName,
      profilePhoto,
      age,
      location,
      type,
      status,
      timestamp,
      message,
      responseMessage,
      isRead,
      isPremium,
      occupation,
      education);

  @override
  String toString() {
    return 'InterestActivityModel(id: $id, profileId: $profileId, profileName: $profileName, profilePhoto: $profilePhoto, age: $age, location: $location, type: $type, status: $status, timestamp: $timestamp, message: $message, responseMessage: $responseMessage, isRead: $isRead, isPremium: $isPremium, occupation: $occupation, education: $education)';
  }
}

/// @nodoc
abstract mixin class _$InterestActivityModelCopyWith<$Res>
    implements $InterestActivityModelCopyWith<$Res> {
  factory _$InterestActivityModelCopyWith(_InterestActivityModel value,
          $Res Function(_InterestActivityModel) _then) =
      __$InterestActivityModelCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String id,
      String profileId,
      String profileName,
      String? profilePhoto,
      int age,
      String location,
      InterestType type,
      InterestStatus status,
      DateTime timestamp,
      String? message,
      String? responseMessage,
      bool? isRead,
      bool? isPremium,
      String? occupation,
      String? education});
}

/// @nodoc
class __$InterestActivityModelCopyWithImpl<$Res>
    implements _$InterestActivityModelCopyWith<$Res> {
  __$InterestActivityModelCopyWithImpl(this._self, this._then);

  final _InterestActivityModel _self;
  final $Res Function(_InterestActivityModel) _then;

  /// Create a copy of InterestActivityModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? id = null,
    Object? profileId = null,
    Object? profileName = null,
    Object? profilePhoto = freezed,
    Object? age = null,
    Object? location = null,
    Object? type = null,
    Object? status = null,
    Object? timestamp = null,
    Object? message = freezed,
    Object? responseMessage = freezed,
    Object? isRead = freezed,
    Object? isPremium = freezed,
    Object? occupation = freezed,
    Object? education = freezed,
  }) {
    return _then(_InterestActivityModel(
      id: null == id
          ? _self.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      profileId: null == profileId
          ? _self.profileId
          : profileId // ignore: cast_nullable_to_non_nullable
              as String,
      profileName: null == profileName
          ? _self.profileName
          : profileName // ignore: cast_nullable_to_non_nullable
              as String,
      profilePhoto: freezed == profilePhoto
          ? _self.profilePhoto
          : profilePhoto // ignore: cast_nullable_to_non_nullable
              as String?,
      age: null == age
          ? _self.age
          : age // ignore: cast_nullable_to_non_nullable
              as int,
      location: null == location
          ? _self.location
          : location // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _self.type
          : type // ignore: cast_nullable_to_non_nullable
              as InterestType,
      status: null == status
          ? _self.status
          : status // ignore: cast_nullable_to_non_nullable
              as InterestStatus,
      timestamp: null == timestamp
          ? _self.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as DateTime,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
      responseMessage: freezed == responseMessage
          ? _self.responseMessage
          : responseMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      isRead: freezed == isRead
          ? _self.isRead
          : isRead // ignore: cast_nullable_to_non_nullable
              as bool?,
      isPremium: freezed == isPremium
          ? _self.isPremium
          : isPremium // ignore: cast_nullable_to_non_nullable
              as bool?,
      occupation: freezed == occupation
          ? _self.occupation
          : occupation // ignore: cast_nullable_to_non_nullable
              as String?,
      education: freezed == education
          ? _self.education
          : education // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$InterestListResponse {
  bool get success;
  InterestListData get data;
  String? get message;

  /// Create a copy of InterestListResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InterestListResponseCopyWith<InterestListResponse> get copyWith =>
      _$InterestListResponseCopyWithImpl<InterestListResponse>(
          this as InterestListResponse, _$identity);

  /// Serializes this InterestListResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InterestListResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, message);

  @override
  String toString() {
    return 'InterestListResponse(success: $success, data: $data, message: $message)';
  }
}

/// @nodoc
abstract mixin class $InterestListResponseCopyWith<$Res> {
  factory $InterestListResponseCopyWith(InterestListResponse value,
          $Res Function(InterestListResponse) _then) =
      _$InterestListResponseCopyWithImpl;
  @useResult
  $Res call({bool success, InterestListData data, String? message});

  $InterestListDataCopyWith<$Res> get data;
}

/// @nodoc
class _$InterestListResponseCopyWithImpl<$Res>
    implements $InterestListResponseCopyWith<$Res> {
  _$InterestListResponseCopyWithImpl(this._self, this._then);

  final InterestListResponse _self;
  final $Res Function(InterestListResponse) _then;

  /// Create a copy of InterestListResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = null,
    Object? message = freezed,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as InterestListData,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of InterestListResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InterestListDataCopyWith<$Res> get data {
    return $InterestListDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// Adds pattern-matching-related methods to [InterestListResponse].
extension InterestListResponsePatterns on InterestListResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_InterestListResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestListResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_InterestListResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_InterestListResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool success, InterestListData data, String? message)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestListResponse() when $default != null:
        return $default(_that.success, _that.data, _that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool success, InterestListData data, String? message)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListResponse():
        return $default(_that.success, _that.data, _that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(bool success, InterestListData data, String? message)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListResponse() when $default != null:
        return $default(_that.success, _that.data, _that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _InterestListResponse implements InterestListResponse {
  const _InterestListResponse(
      {required this.success, required this.data, this.message});
  factory _InterestListResponse.fromJson(Map<String, dynamic> json) =>
      _$InterestListResponseFromJson(json);

  @override
  final bool success;
  @override
  final InterestListData data;
  @override
  final String? message;

  /// Create a copy of InterestListResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InterestListResponseCopyWith<_InterestListResponse> get copyWith =>
      __$InterestListResponseCopyWithImpl<_InterestListResponse>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InterestListResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InterestListResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, message);

  @override
  String toString() {
    return 'InterestListResponse(success: $success, data: $data, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$InterestListResponseCopyWith<$Res>
    implements $InterestListResponseCopyWith<$Res> {
  factory _$InterestListResponseCopyWith(_InterestListResponse value,
          $Res Function(_InterestListResponse) _then) =
      __$InterestListResponseCopyWithImpl;
  @override
  @useResult
  $Res call({bool success, InterestListData data, String? message});

  @override
  $InterestListDataCopyWith<$Res> get data;
}

/// @nodoc
class __$InterestListResponseCopyWithImpl<$Res>
    implements _$InterestListResponseCopyWith<$Res> {
  __$InterestListResponseCopyWithImpl(this._self, this._then);

  final _InterestListResponse _self;
  final $Res Function(_InterestListResponse) _then;

  /// Create a copy of InterestListResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? data = null,
    Object? message = freezed,
  }) {
    return _then(_InterestListResponse(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as InterestListData,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of InterestListResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InterestListDataCopyWith<$Res> get data {
    return $InterestListDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// @nodoc
mixin _$InterestListData {
  List<InterestModel> get received;
  List<InterestModel> get sent;
  int get totalReceived;
  int get totalSent;
  int get pendingReceived;
  int get pendingSent;

  /// Create a copy of InterestListData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InterestListDataCopyWith<InterestListData> get copyWith =>
      _$InterestListDataCopyWithImpl<InterestListData>(
          this as InterestListData, _$identity);

  /// Serializes this InterestListData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InterestListData &&
            const DeepCollectionEquality().equals(other.received, received) &&
            const DeepCollectionEquality().equals(other.sent, sent) &&
            (identical(other.totalReceived, totalReceived) ||
                other.totalReceived == totalReceived) &&
            (identical(other.totalSent, totalSent) ||
                other.totalSent == totalSent) &&
            (identical(other.pendingReceived, pendingReceived) ||
                other.pendingReceived == pendingReceived) &&
            (identical(other.pendingSent, pendingSent) ||
                other.pendingSent == pendingSent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(received),
      const DeepCollectionEquality().hash(sent),
      totalReceived,
      totalSent,
      pendingReceived,
      pendingSent);

  @override
  String toString() {
    return 'InterestListData(received: $received, sent: $sent, totalReceived: $totalReceived, totalSent: $totalSent, pendingReceived: $pendingReceived, pendingSent: $pendingSent)';
  }
}

/// @nodoc
abstract mixin class $InterestListDataCopyWith<$Res> {
  factory $InterestListDataCopyWith(
          InterestListData value, $Res Function(InterestListData) _then) =
      _$InterestListDataCopyWithImpl;
  @useResult
  $Res call(
      {List<InterestModel> received,
      List<InterestModel> sent,
      int totalReceived,
      int totalSent,
      int pendingReceived,
      int pendingSent});
}

/// @nodoc
class _$InterestListDataCopyWithImpl<$Res>
    implements $InterestListDataCopyWith<$Res> {
  _$InterestListDataCopyWithImpl(this._self, this._then);

  final InterestListData _self;
  final $Res Function(InterestListData) _then;

  /// Create a copy of InterestListData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? received = null,
    Object? sent = null,
    Object? totalReceived = null,
    Object? totalSent = null,
    Object? pendingReceived = null,
    Object? pendingSent = null,
  }) {
    return _then(_self.copyWith(
      received: null == received
          ? _self.received
          : received // ignore: cast_nullable_to_non_nullable
              as List<InterestModel>,
      sent: null == sent
          ? _self.sent
          : sent // ignore: cast_nullable_to_non_nullable
              as List<InterestModel>,
      totalReceived: null == totalReceived
          ? _self.totalReceived
          : totalReceived // ignore: cast_nullable_to_non_nullable
              as int,
      totalSent: null == totalSent
          ? _self.totalSent
          : totalSent // ignore: cast_nullable_to_non_nullable
              as int,
      pendingReceived: null == pendingReceived
          ? _self.pendingReceived
          : pendingReceived // ignore: cast_nullable_to_non_nullable
              as int,
      pendingSent: null == pendingSent
          ? _self.pendingSent
          : pendingSent // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// Adds pattern-matching-related methods to [InterestListData].
extension InterestListDataPatterns on InterestListData {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_InterestListData value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestListData() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_InterestListData value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListData():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_InterestListData value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListData() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            List<InterestModel> received,
            List<InterestModel> sent,
            int totalReceived,
            int totalSent,
            int pendingReceived,
            int pendingSent)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestListData() when $default != null:
        return $default(_that.received, _that.sent, _that.totalReceived,
            _that.totalSent, _that.pendingReceived, _that.pendingSent);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            List<InterestModel> received,
            List<InterestModel> sent,
            int totalReceived,
            int totalSent,
            int pendingReceived,
            int pendingSent)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListData():
        return $default(_that.received, _that.sent, _that.totalReceived,
            _that.totalSent, _that.pendingReceived, _that.pendingSent);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            List<InterestModel> received,
            List<InterestModel> sent,
            int totalReceived,
            int totalSent,
            int pendingReceived,
            int pendingSent)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestListData() when $default != null:
        return $default(_that.received, _that.sent, _that.totalReceived,
            _that.totalSent, _that.pendingReceived, _that.pendingSent);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _InterestListData implements InterestListData {
  const _InterestListData(
      {final List<InterestModel> received = const [],
      final List<InterestModel> sent = const [],
      this.totalReceived = 0,
      this.totalSent = 0,
      this.pendingReceived = 0,
      this.pendingSent = 0})
      : _received = received,
        _sent = sent;
  factory _InterestListData.fromJson(Map<String, dynamic> json) =>
      _$InterestListDataFromJson(json);

  final List<InterestModel> _received;
  @override
  @JsonKey()
  List<InterestModel> get received {
    if (_received is EqualUnmodifiableListView) return _received;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_received);
  }

  final List<InterestModel> _sent;
  @override
  @JsonKey()
  List<InterestModel> get sent {
    if (_sent is EqualUnmodifiableListView) return _sent;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sent);
  }

  @override
  @JsonKey()
  final int totalReceived;
  @override
  @JsonKey()
  final int totalSent;
  @override
  @JsonKey()
  final int pendingReceived;
  @override
  @JsonKey()
  final int pendingSent;

  /// Create a copy of InterestListData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InterestListDataCopyWith<_InterestListData> get copyWith =>
      __$InterestListDataCopyWithImpl<_InterestListData>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InterestListDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InterestListData &&
            const DeepCollectionEquality().equals(other._received, _received) &&
            const DeepCollectionEquality().equals(other._sent, _sent) &&
            (identical(other.totalReceived, totalReceived) ||
                other.totalReceived == totalReceived) &&
            (identical(other.totalSent, totalSent) ||
                other.totalSent == totalSent) &&
            (identical(other.pendingReceived, pendingReceived) ||
                other.pendingReceived == pendingReceived) &&
            (identical(other.pendingSent, pendingSent) ||
                other.pendingSent == pendingSent));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_received),
      const DeepCollectionEquality().hash(_sent),
      totalReceived,
      totalSent,
      pendingReceived,
      pendingSent);

  @override
  String toString() {
    return 'InterestListData(received: $received, sent: $sent, totalReceived: $totalReceived, totalSent: $totalSent, pendingReceived: $pendingReceived, pendingSent: $pendingSent)';
  }
}

/// @nodoc
abstract mixin class _$InterestListDataCopyWith<$Res>
    implements $InterestListDataCopyWith<$Res> {
  factory _$InterestListDataCopyWith(
          _InterestListData value, $Res Function(_InterestListData) _then) =
      __$InterestListDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<InterestModel> received,
      List<InterestModel> sent,
      int totalReceived,
      int totalSent,
      int pendingReceived,
      int pendingSent});
}

/// @nodoc
class __$InterestListDataCopyWithImpl<$Res>
    implements _$InterestListDataCopyWith<$Res> {
  __$InterestListDataCopyWithImpl(this._self, this._then);

  final _InterestListData _self;
  final $Res Function(_InterestListData) _then;

  /// Create a copy of InterestListData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? received = null,
    Object? sent = null,
    Object? totalReceived = null,
    Object? totalSent = null,
    Object? pendingReceived = null,
    Object? pendingSent = null,
  }) {
    return _then(_InterestListData(
      received: null == received
          ? _self._received
          : received // ignore: cast_nullable_to_non_nullable
              as List<InterestModel>,
      sent: null == sent
          ? _self._sent
          : sent // ignore: cast_nullable_to_non_nullable
              as List<InterestModel>,
      totalReceived: null == totalReceived
          ? _self.totalReceived
          : totalReceived // ignore: cast_nullable_to_non_nullable
              as int,
      totalSent: null == totalSent
          ? _self.totalSent
          : totalSent // ignore: cast_nullable_to_non_nullable
              as int,
      pendingReceived: null == pendingReceived
          ? _self.pendingReceived
          : pendingReceived // ignore: cast_nullable_to_non_nullable
              as int,
      pendingSent: null == pendingSent
          ? _self.pendingSent
          : pendingSent // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
mixin _$InterestDashboardResponse {
  bool get success;
  InterestDashboardData get data;
  String? get message;

  /// Create a copy of InterestDashboardResponse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InterestDashboardResponseCopyWith<InterestDashboardResponse> get copyWith =>
      _$InterestDashboardResponseCopyWithImpl<InterestDashboardResponse>(
          this as InterestDashboardResponse, _$identity);

  /// Serializes this InterestDashboardResponse to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InterestDashboardResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, message);

  @override
  String toString() {
    return 'InterestDashboardResponse(success: $success, data: $data, message: $message)';
  }
}

/// @nodoc
abstract mixin class $InterestDashboardResponseCopyWith<$Res> {
  factory $InterestDashboardResponseCopyWith(InterestDashboardResponse value,
          $Res Function(InterestDashboardResponse) _then) =
      _$InterestDashboardResponseCopyWithImpl;
  @useResult
  $Res call({bool success, InterestDashboardData data, String? message});

  $InterestDashboardDataCopyWith<$Res> get data;
}

/// @nodoc
class _$InterestDashboardResponseCopyWithImpl<$Res>
    implements $InterestDashboardResponseCopyWith<$Res> {
  _$InterestDashboardResponseCopyWithImpl(this._self, this._then);

  final InterestDashboardResponse _self;
  final $Res Function(InterestDashboardResponse) _then;

  /// Create a copy of InterestDashboardResponse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? success = null,
    Object? data = null,
    Object? message = freezed,
  }) {
    return _then(_self.copyWith(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as InterestDashboardData,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of InterestDashboardResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InterestDashboardDataCopyWith<$Res> get data {
    return $InterestDashboardDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// Adds pattern-matching-related methods to [InterestDashboardResponse].
extension InterestDashboardResponsePatterns on InterestDashboardResponse {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_InterestDashboardResponse value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardResponse() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_InterestDashboardResponse value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardResponse():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_InterestDashboardResponse value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardResponse() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(bool success, InterestDashboardData data, String? message)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardResponse() when $default != null:
        return $default(_that.success, _that.data, _that.message);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(bool success, InterestDashboardData data, String? message)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardResponse():
        return $default(_that.success, _that.data, _that.message);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            bool success, InterestDashboardData data, String? message)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardResponse() when $default != null:
        return $default(_that.success, _that.data, _that.message);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _InterestDashboardResponse implements InterestDashboardResponse {
  const _InterestDashboardResponse(
      {required this.success, required this.data, this.message});
  factory _InterestDashboardResponse.fromJson(Map<String, dynamic> json) =>
      _$InterestDashboardResponseFromJson(json);

  @override
  final bool success;
  @override
  final InterestDashboardData data;
  @override
  final String? message;

  /// Create a copy of InterestDashboardResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InterestDashboardResponseCopyWith<_InterestDashboardResponse>
      get copyWith =>
          __$InterestDashboardResponseCopyWithImpl<_InterestDashboardResponse>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InterestDashboardResponseToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InterestDashboardResponse &&
            (identical(other.success, success) || other.success == success) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.message, message) || other.message == message));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, success, data, message);

  @override
  String toString() {
    return 'InterestDashboardResponse(success: $success, data: $data, message: $message)';
  }
}

/// @nodoc
abstract mixin class _$InterestDashboardResponseCopyWith<$Res>
    implements $InterestDashboardResponseCopyWith<$Res> {
  factory _$InterestDashboardResponseCopyWith(_InterestDashboardResponse value,
          $Res Function(_InterestDashboardResponse) _then) =
      __$InterestDashboardResponseCopyWithImpl;
  @override
  @useResult
  $Res call({bool success, InterestDashboardData data, String? message});

  @override
  $InterestDashboardDataCopyWith<$Res> get data;
}

/// @nodoc
class __$InterestDashboardResponseCopyWithImpl<$Res>
    implements _$InterestDashboardResponseCopyWith<$Res> {
  __$InterestDashboardResponseCopyWithImpl(this._self, this._then);

  final _InterestDashboardResponse _self;
  final $Res Function(_InterestDashboardResponse) _then;

  /// Create a copy of InterestDashboardResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? success = null,
    Object? data = null,
    Object? message = freezed,
  }) {
    return _then(_InterestDashboardResponse(
      success: null == success
          ? _self.success
          : success // ignore: cast_nullable_to_non_nullable
              as bool,
      data: null == data
          ? _self.data
          : data // ignore: cast_nullable_to_non_nullable
              as InterestDashboardData,
      message: freezed == message
          ? _self.message
          : message // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }

  /// Create a copy of InterestDashboardResponse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $InterestDashboardDataCopyWith<$Res> get data {
    return $InterestDashboardDataCopyWith<$Res>(_self.data, (value) {
      return _then(_self.copyWith(data: value));
    });
  }
}

/// @nodoc
mixin _$InterestDashboardData {
  ActivityStatsModel get stats;
  List<InterestActivityModel> get recentActivities;
  List<InterestModel> get pendingInterests;
  List<UserProfileModel> get suggestedProfiles;

  /// Create a copy of InterestDashboardData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $InterestDashboardDataCopyWith<InterestDashboardData> get copyWith =>
      _$InterestDashboardDataCopyWithImpl<InterestDashboardData>(
          this as InterestDashboardData, _$identity);

  /// Serializes this InterestDashboardData to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is InterestDashboardData &&
            (identical(other.stats, stats) || other.stats == stats) &&
            const DeepCollectionEquality()
                .equals(other.recentActivities, recentActivities) &&
            const DeepCollectionEquality()
                .equals(other.pendingInterests, pendingInterests) &&
            const DeepCollectionEquality()
                .equals(other.suggestedProfiles, suggestedProfiles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      stats,
      const DeepCollectionEquality().hash(recentActivities),
      const DeepCollectionEquality().hash(pendingInterests),
      const DeepCollectionEquality().hash(suggestedProfiles));

  @override
  String toString() {
    return 'InterestDashboardData(stats: $stats, recentActivities: $recentActivities, pendingInterests: $pendingInterests, suggestedProfiles: $suggestedProfiles)';
  }
}

/// @nodoc
abstract mixin class $InterestDashboardDataCopyWith<$Res> {
  factory $InterestDashboardDataCopyWith(InterestDashboardData value,
          $Res Function(InterestDashboardData) _then) =
      _$InterestDashboardDataCopyWithImpl;
  @useResult
  $Res call(
      {ActivityStatsModel stats,
      List<InterestActivityModel> recentActivities,
      List<InterestModel> pendingInterests,
      List<UserProfileModel> suggestedProfiles});

  $ActivityStatsModelCopyWith<$Res> get stats;
}

/// @nodoc
class _$InterestDashboardDataCopyWithImpl<$Res>
    implements $InterestDashboardDataCopyWith<$Res> {
  _$InterestDashboardDataCopyWithImpl(this._self, this._then);

  final InterestDashboardData _self;
  final $Res Function(InterestDashboardData) _then;

  /// Create a copy of InterestDashboardData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? stats = null,
    Object? recentActivities = null,
    Object? pendingInterests = null,
    Object? suggestedProfiles = null,
  }) {
    return _then(_self.copyWith(
      stats: null == stats
          ? _self.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as ActivityStatsModel,
      recentActivities: null == recentActivities
          ? _self.recentActivities
          : recentActivities // ignore: cast_nullable_to_non_nullable
              as List<InterestActivityModel>,
      pendingInterests: null == pendingInterests
          ? _self.pendingInterests
          : pendingInterests // ignore: cast_nullable_to_non_nullable
              as List<InterestModel>,
      suggestedProfiles: null == suggestedProfiles
          ? _self.suggestedProfiles
          : suggestedProfiles // ignore: cast_nullable_to_non_nullable
              as List<UserProfileModel>,
    ));
  }

  /// Create a copy of InterestDashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ActivityStatsModelCopyWith<$Res> get stats {
    return $ActivityStatsModelCopyWith<$Res>(_self.stats, (value) {
      return _then(_self.copyWith(stats: value));
    });
  }
}

/// Adds pattern-matching-related methods to [InterestDashboardData].
extension InterestDashboardDataPatterns on InterestDashboardData {
  /// A variant of `map` that fallback to returning `orElse`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>(
    TResult Function(_InterestDashboardData value)? $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardData() when $default != null:
        return $default(_that);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// Callbacks receives the raw object, upcasted.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case final Subclass2 value:
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult map<TResult extends Object?>(
    TResult Function(_InterestDashboardData value) $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardData():
        return $default(_that);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `map` that fallback to returning `null`.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case final Subclass value:
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>(
    TResult? Function(_InterestDashboardData value)? $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardData() when $default != null:
        return $default(_that);
      case _:
        return null;
    }
  }

  /// A variant of `when` that fallback to an `orElse` callback.
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return orElse();
  /// }
  /// ```

  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>(
    TResult Function(
            ActivityStatsModel stats,
            List<InterestActivityModel> recentActivities,
            List<InterestModel> pendingInterests,
            List<UserProfileModel> suggestedProfiles)?
        $default, {
    required TResult orElse(),
  }) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardData() when $default != null:
        return $default(_that.stats, _that.recentActivities,
            _that.pendingInterests, _that.suggestedProfiles);
      case _:
        return orElse();
    }
  }

  /// A `switch`-like method, using callbacks.
  ///
  /// As opposed to `map`, this offers destructuring.
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case Subclass2(:final field2):
  ///     return ...;
  /// }
  /// ```

  @optionalTypeArgs
  TResult when<TResult extends Object?>(
    TResult Function(
            ActivityStatsModel stats,
            List<InterestActivityModel> recentActivities,
            List<InterestModel> pendingInterests,
            List<UserProfileModel> suggestedProfiles)
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardData():
        return $default(_that.stats, _that.recentActivities,
            _that.pendingInterests, _that.suggestedProfiles);
      case _:
        throw StateError('Unexpected subclass');
    }
  }

  /// A variant of `when` that fallback to returning `null`
  ///
  /// It is equivalent to doing:
  /// ```dart
  /// switch (sealedClass) {
  ///   case Subclass(:final field):
  ///     return ...;
  ///   case _:
  ///     return null;
  /// }
  /// ```

  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>(
    TResult? Function(
            ActivityStatsModel stats,
            List<InterestActivityModel> recentActivities,
            List<InterestModel> pendingInterests,
            List<UserProfileModel> suggestedProfiles)?
        $default,
  ) {
    final _that = this;
    switch (_that) {
      case _InterestDashboardData() when $default != null:
        return $default(_that.stats, _that.recentActivities,
            _that.pendingInterests, _that.suggestedProfiles);
      case _:
        return null;
    }
  }
}

/// @nodoc
@JsonSerializable()
class _InterestDashboardData implements InterestDashboardData {
  const _InterestDashboardData(
      {required this.stats,
      final List<InterestActivityModel> recentActivities = const [],
      final List<InterestModel> pendingInterests = const [],
      final List<UserProfileModel> suggestedProfiles = const []})
      : _recentActivities = recentActivities,
        _pendingInterests = pendingInterests,
        _suggestedProfiles = suggestedProfiles;
  factory _InterestDashboardData.fromJson(Map<String, dynamic> json) =>
      _$InterestDashboardDataFromJson(json);

  @override
  final ActivityStatsModel stats;
  final List<InterestActivityModel> _recentActivities;
  @override
  @JsonKey()
  List<InterestActivityModel> get recentActivities {
    if (_recentActivities is EqualUnmodifiableListView)
      return _recentActivities;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recentActivities);
  }

  final List<InterestModel> _pendingInterests;
  @override
  @JsonKey()
  List<InterestModel> get pendingInterests {
    if (_pendingInterests is EqualUnmodifiableListView)
      return _pendingInterests;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_pendingInterests);
  }

  final List<UserProfileModel> _suggestedProfiles;
  @override
  @JsonKey()
  List<UserProfileModel> get suggestedProfiles {
    if (_suggestedProfiles is EqualUnmodifiableListView)
      return _suggestedProfiles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_suggestedProfiles);
  }

  /// Create a copy of InterestDashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$InterestDashboardDataCopyWith<_InterestDashboardData> get copyWith =>
      __$InterestDashboardDataCopyWithImpl<_InterestDashboardData>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$InterestDashboardDataToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _InterestDashboardData &&
            (identical(other.stats, stats) || other.stats == stats) &&
            const DeepCollectionEquality()
                .equals(other._recentActivities, _recentActivities) &&
            const DeepCollectionEquality()
                .equals(other._pendingInterests, _pendingInterests) &&
            const DeepCollectionEquality()
                .equals(other._suggestedProfiles, _suggestedProfiles));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      stats,
      const DeepCollectionEquality().hash(_recentActivities),
      const DeepCollectionEquality().hash(_pendingInterests),
      const DeepCollectionEquality().hash(_suggestedProfiles));

  @override
  String toString() {
    return 'InterestDashboardData(stats: $stats, recentActivities: $recentActivities, pendingInterests: $pendingInterests, suggestedProfiles: $suggestedProfiles)';
  }
}

/// @nodoc
abstract mixin class _$InterestDashboardDataCopyWith<$Res>
    implements $InterestDashboardDataCopyWith<$Res> {
  factory _$InterestDashboardDataCopyWith(_InterestDashboardData value,
          $Res Function(_InterestDashboardData) _then) =
      __$InterestDashboardDataCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ActivityStatsModel stats,
      List<InterestActivityModel> recentActivities,
      List<InterestModel> pendingInterests,
      List<UserProfileModel> suggestedProfiles});

  @override
  $ActivityStatsModelCopyWith<$Res> get stats;
}

/// @nodoc
class __$InterestDashboardDataCopyWithImpl<$Res>
    implements _$InterestDashboardDataCopyWith<$Res> {
  __$InterestDashboardDataCopyWithImpl(this._self, this._then);

  final _InterestDashboardData _self;
  final $Res Function(_InterestDashboardData) _then;

  /// Create a copy of InterestDashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? stats = null,
    Object? recentActivities = null,
    Object? pendingInterests = null,
    Object? suggestedProfiles = null,
  }) {
    return _then(_InterestDashboardData(
      stats: null == stats
          ? _self.stats
          : stats // ignore: cast_nullable_to_non_nullable
              as ActivityStatsModel,
      recentActivities: null == recentActivities
          ? _self._recentActivities
          : recentActivities // ignore: cast_nullable_to_non_nullable
              as List<InterestActivityModel>,
      pendingInterests: null == pendingInterests
          ? _self._pendingInterests
          : pendingInterests // ignore: cast_nullable_to_non_nullable
              as List<InterestModel>,
      suggestedProfiles: null == suggestedProfiles
          ? _self._suggestedProfiles
          : suggestedProfiles // ignore: cast_nullable_to_non_nullable
              as List<UserProfileModel>,
    ));
  }

  /// Create a copy of InterestDashboardData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ActivityStatsModelCopyWith<$Res> get stats {
    return $ActivityStatsModelCopyWith<$Res>(_self.stats, (value) {
      return _then(_self.copyWith(stats: value));
    });
  }
}

// dart format on
