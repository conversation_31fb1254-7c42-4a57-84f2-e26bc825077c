import '../api/api_client.dart';
import 'storage_service.dart';

// Mock classes for Firebase functionality (to be replaced when Firebase is enabled)
class MockRemoteMessage {
  final String? messageId;
  final Map<String, dynamic> data;
  final MockRemoteNotification? notification;

  MockRemoteMessage({this.messageId, this.data = const {}, this.notification});
}

class MockRemoteNotification {
  final String? title;
  final String? body;

  MockRemoteNotification({this.title, this.body});
}

class NotificationService {
  static String? _fcmToken;
  static final List<Function(MockRemoteMessage)> _messageHandlers = [];

  static Future<void> init() async {
    try {
      // Mock Firebase initialization (to be replaced when Firebase is enabled)
      print('Notification service initialized (mock mode)');

      // Generate mock FCM token
      _fcmToken = 'mock_fcm_token_${DateTime.now().millisecondsSinceEpoch}';
      print('Mock FCM Token: $_fcmToken');

      // Update token on server
      if (_fcmToken != null) {
        await updateToken(_fcmToken!);
      }

    } catch (e) {
      print('Error initializing notifications: $e');
    }
  }

  static Future<void> requestPermissions() async {
    try {
      // Mock permission request (to be replaced when Firebase is enabled)
      print('Mock: User granted notification permission');
    } catch (e) {
      print('Error requesting permissions: $e');
    }
  }

  static Future<void> subscribeToTopic(String topic) async {
    try {
      // Mock topic subscription (to be replaced when Firebase is enabled)
      print('Mock: Subscribed to topic: $topic');
    } catch (e) {
      print('Error subscribing to topic: $e');
    }
  }

  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      // Mock topic unsubscription (to be replaced when Firebase is enabled)
      print('Mock: Unsubscribed from topic: $topic');
    } catch (e) {
      print('Error unsubscribing from topic: $e');
    }
  }

  static Future<void> sendNotification({
    required String title,
    required String body,
    String? userId,
    Map<String, dynamic>? data,
  }) async {
    try {
      final apiClient = ApiClient();
      await apiClient.post('/notifications/send', {
        'title': title,
        'body': body,
        'userId': userId,
        'data': data,
      });
    } catch (e) {
      print('Error sending notification: $e');
    }
  }

  static Future<String?> getToken() async {
    try {
      return _fcmToken;
    } catch (e) {
      print('Error getting FCM token: $e');
      return null;
    }
  }

  static Future<void> updateToken(String token) async {
    try {
      final apiClient = ApiClient();
      await apiClient.post('/notifications/token', {
        'fcmToken': token,
      });

      // Store token locally
      await StorageService.setString('fcm_token', token);
    } catch (e) {
      print('Error updating FCM token: $e');
    }
  }

  static Future<void> handleBackgroundMessage(MockRemoteMessage message) async {
    print('Handling background message: ${message.messageId}');

    // Handle background message logic
    if (message.data.isNotEmpty) {
      final type = message.data['type'];
      switch (type) {
        case 'chat':
          // Handle chat message
          break;
        case 'match':
          // Handle new match
          break;
        case 'interest':
          // Handle interest received
          break;
        default:
          break;
      }
    }
  }

  static void handleForegroundMessage(MockRemoteMessage message) {
    print('Handling foreground message: ${message.messageId}');

    // Notify listeners
    for (final handler in _messageHandlers) {
      handler(message);
    }

    // Show local notification if needed
    if (message.notification != null) {
      print('Message title: ${message.notification!.title}');
      print('Message body: ${message.notification!.body}');
    }
  }

  static void handleNotificationTap(MockRemoteMessage message) {
    print('Notification tapped: ${message.messageId}');

    // Handle notification tap based on data
    if (message.data.isNotEmpty) {
      final type = message.data['type'];

      switch (type) {
        case 'chat':
          // Navigate to chat screen
          break;
        case 'match':
          // Navigate to matches screen
          break;
        case 'interest':
          // Navigate to interests screen
          break;
        default:
          break;
      }
    }
  }

  static void addMessageHandler(Function(MockRemoteMessage) handler) {
    _messageHandlers.add(handler);
  }

  static void removeMessageHandler(Function(MockRemoteMessage) handler) {
    _messageHandlers.remove(handler);
  }

  static String? get fcmToken => _fcmToken;
}

// Premium subscription management
class SubscriptionService {
  static final ApiClient _apiClient = ApiClient();

  static Future<Map<String, dynamic>?> getCurrentSubscription() async {
    try {
      final response = await _apiClient.get('/subscription/current');
      if (response['success'] == true) {
        return response['data'];
      }
      return null;
    } catch (e) {
      print('Error getting current subscription: $e');
      return null;
    }
  }

  static Future<List<Map<String, dynamic>>> getSubscriptionPlans() async {
    try {
      final response = await _apiClient.get('/subscription/plans');
      if (response['success'] == true) {
        return List<Map<String, dynamic>>.from(response['data']['plans']);
      }
      return [];
    } catch (e) {
      print('Error getting subscription plans: $e');
      return [];
    }
  }

  static Future<bool> subscribeToPlan(String planId, String paymentMethodId) async {
    try {
      final response = await _apiClient.post('/subscription/subscribe', {
        'planId': planId,
        'paymentMethodId': paymentMethodId,
      });
      
      return response['success'] == true;
    } catch (e) {
      print('Error subscribing to plan: $e');
      return false;
    }
  }

  static Future<bool> cancelSubscription() async {
    try {
      final response = await _apiClient.post('/subscription/cancel', {});
      return response['success'] == true;
    } catch (e) {
      print('Error canceling subscription: $e');
      return false;
    }
  }

  static Future<bool> isPremiumUser() async {
    try {
      final subscription = await getCurrentSubscription();
      return subscription != null && 
             subscription['status'] == 'active' &&
             DateTime.parse(subscription['expiresAt']).isAfter(DateTime.now());
    } catch (e) {
      print('Error checking premium status: $e');
      return false;
    }
  }
}
