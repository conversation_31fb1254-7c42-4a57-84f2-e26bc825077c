// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'kundali_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_KundaliData _$KundaliDataFromJson(Map<String, dynamic> json) => _KundaliData(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      birthDate: DateTime.parse(json['birthDate'] as String),
      birthTime: json['birthTime'] as String,
      birthPlace: json['birthPlace'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      timezone: json['timezone'] as String,
      planetaryPositions: json['planetaryPositions'] as Map<String, dynamic>,
      houses: json['houses'] as Map<String, dynamic>,
      aspects: json['aspects'] as Map<String, dynamic>,
      doshas:
          (json['doshas'] as List<dynamic>).map((e) => e as String).toList(),
      mangalDosha: json['mangalDosha'] as String?,
      kalSarpaDosha: json['kalSarpaDosha'] as String?,
      pitruDosha: json['pitruDosha'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      yogas: json['yogas'] as Map<String, dynamic>?,
      transits: json['transits'] as Map<String, dynamic>?,
      predictions: (json['predictions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      nakshatra: json['nakshatra'] as String?,
      rashi: json['rashi'] as String?,
      moonLongitude: (json['moonLongitude'] as num?)?.toDouble(),
      ascendant: json['ascendant'] as String?,
      isPremium: json['isPremium'] as bool?,
    );

Map<String, dynamic> _$KundaliDataToJson(_KundaliData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'name': instance.name,
      'birthDate': instance.birthDate.toIso8601String(),
      'birthTime': instance.birthTime,
      'birthPlace': instance.birthPlace,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'timezone': instance.timezone,
      'planetaryPositions': instance.planetaryPositions,
      'houses': instance.houses,
      'aspects': instance.aspects,
      'doshas': instance.doshas,
      'mangalDosha': instance.mangalDosha,
      'kalSarpaDosha': instance.kalSarpaDosha,
      'pitruDosha': instance.pitruDosha,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'yogas': instance.yogas,
      'transits': instance.transits,
      'predictions': instance.predictions,
      'nakshatra': instance.nakshatra,
      'rashi': instance.rashi,
      'moonLongitude': instance.moonLongitude,
      'ascendant': instance.ascendant,
      'isPremium': instance.isPremium,
    };

_KundaliMatching _$KundaliMatchingFromJson(Map<String, dynamic> json) =>
    _KundaliMatching(
      id: json['id'] as String,
      user1Id: json['user1Id'] as String,
      user2Id: json['user2Id'] as String,
      user1Kundali:
          KundaliData.fromJson(json['user1Kundali'] as Map<String, dynamic>),
      user2Kundali:
          KundaliData.fromJson(json['user2Kundali'] as Map<String, dynamic>),
      totalScore: (json['totalScore'] as num).toInt(),
      maxScore: (json['maxScore'] as num).toInt(),
      compatibilityPercentage:
          (json['compatibilityPercentage'] as num).toDouble(),
      gunaScores: (json['gunaScores'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, GunaScore.fromJson(e as Map<String, dynamic>)),
      ),
      strengths:
          (json['strengths'] as List<dynamic>).map((e) => e as String).toList(),
      challenges: (json['challenges'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      recommendations: (json['recommendations'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      status: $enumDecode(_$MatchingStatusEnumMap, json['status']),
      isPremiumFeature: json['isPremiumFeature'] as bool,
      createdAt: DateTime.parse(json['createdAt'] as String),
      astrologerNotes: json['astrologerNotes'] as String?,
    );

Map<String, dynamic> _$KundaliMatchingToJson(_KundaliMatching instance) =>
    <String, dynamic>{
      'id': instance.id,
      'user1Id': instance.user1Id,
      'user2Id': instance.user2Id,
      'user1Kundali': instance.user1Kundali,
      'user2Kundali': instance.user2Kundali,
      'totalScore': instance.totalScore,
      'maxScore': instance.maxScore,
      'compatibilityPercentage': instance.compatibilityPercentage,
      'gunaScores': instance.gunaScores,
      'strengths': instance.strengths,
      'challenges': instance.challenges,
      'recommendations': instance.recommendations,
      'status': _$MatchingStatusEnumMap[instance.status]!,
      'isPremiumFeature': instance.isPremiumFeature,
      'createdAt': instance.createdAt.toIso8601String(),
      'astrologerNotes': instance.astrologerNotes,
    };

const _$MatchingStatusEnumMap = {
  MatchingStatus.pending: 'pending',
  MatchingStatus.processing: 'processing',
  MatchingStatus.completed: 'completed',
  MatchingStatus.failed: 'failed',
};

_GunaScore _$GunaScoreFromJson(Map<String, dynamic> json) => _GunaScore(
      gunaName: json['gunaName'] as String,
      obtainedScore: (json['obtainedScore'] as num).toInt(),
      maxScore: (json['maxScore'] as num).toInt(),
      description: json['description'] as String,
      level: $enumDecode(_$CompatibilityLevelEnumMap, json['level']),
      percentage: (json['percentage'] as num).toDouble(),
    );

Map<String, dynamic> _$GunaScoreToJson(_GunaScore instance) =>
    <String, dynamic>{
      'gunaName': instance.gunaName,
      'obtainedScore': instance.obtainedScore,
      'maxScore': instance.maxScore,
      'description': instance.description,
      'level': _$CompatibilityLevelEnumMap[instance.level]!,
      'percentage': instance.percentage,
    };

const _$CompatibilityLevelEnumMap = {
  CompatibilityLevel.excellent: 'excellent',
  CompatibilityLevel.good: 'good',
  CompatibilityLevel.average: 'average',
  CompatibilityLevel.poor: 'poor',
};

_AstrologyReport _$AstrologyReportFromJson(Map<String, dynamic> json) =>
    _AstrologyReport(
      id: json['id'] as String,
      userId: json['userId'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      data: json['data'] as Map<String, dynamic>,
      generatedAt: DateTime.parse(json['generatedAt'] as String),
      astrologerId: json['astrologerId'] as String?,
      astrologerName: json['astrologerName'] as String?,
      isPremium: json['isPremium'] as bool,
      price: (json['price'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$AstrologyReportToJson(_AstrologyReport instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'type': instance.type,
      'title': instance.title,
      'content': instance.content,
      'data': instance.data,
      'generatedAt': instance.generatedAt.toIso8601String(),
      'astrologerId': instance.astrologerId,
      'astrologerName': instance.astrologerName,
      'isPremium': instance.isPremium,
      'price': instance.price,
    };

_KundaliSettings _$KundaliSettingsFromJson(Map<String, dynamic> json) =>
    _KundaliSettings(
      showKundali: json['showKundali'] as bool,
      allowKundaliMatching: json['allowKundaliMatching'] as bool,
      requireKundaliForMatching: json['requireKundaliForMatching'] as bool,
      showDoshas: json['showDoshas'] as bool,
      showPlanetaryPositions: json['showPlanetaryPositions'] as bool,
      showHouses: json['showHouses'] as bool,
      showAspects: json['showAspects'] as bool,
      preferredLanguage: json['preferredLanguage'] as String,
      enableNotifications: json['enableNotifications'] as bool,
    );

Map<String, dynamic> _$KundaliSettingsToJson(_KundaliSettings instance) =>
    <String, dynamic>{
      'showKundali': instance.showKundali,
      'allowKundaliMatching': instance.allowKundaliMatching,
      'requireKundaliForMatching': instance.requireKundaliForMatching,
      'showDoshas': instance.showDoshas,
      'showPlanetaryPositions': instance.showPlanetaryPositions,
      'showHouses': instance.showHouses,
      'showAspects': instance.showAspects,
      'preferredLanguage': instance.preferredLanguage,
      'enableNotifications': instance.enableNotifications,
    };
