import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BiodataScreen extends ConsumerWidget {
  const BiodataScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Biodata'),
        actions: [
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: () {
              // Download biodata as PDF
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Downloading biodata...')),
              );
              // This would integrate with PDF generation service
            },
          ),
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Share biodata via system share
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening share options...')),
              );
              // This would use share_plus package
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.description, size: 80, color: Colors.brown),
            SizedBox(height: 16),
            Text(
              'Biodata Screen',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('Your biodata will be displayed here...'),
          ],
        ),
      ),
    );
  }
}
