import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../core/widgets/premium_widgets.dart';
import '../../../app/theme.dart';

class LifestyleHabitsScreen extends StatefulWidget {
  const LifestyleHabitsScreen({super.key});

  @override
  State<LifestyleHabitsScreen> createState() => _LifestyleHabitsScreenState();
}

class _LifestyleHabitsScreenState extends State<LifestyleHabitsScreen> {
  String? selectedDiet;
  String? selectedDrinking;
  String? selectedSmoking;
  String? selectedExercise;
  
  bool isLoading = false;

  final List<String> dietOptions = [
    'Vegetarian',
    'Non-Vegetarian',
    'Vegan',
    'Jain Vegetarian',
  ];
  
  final List<String> drinkingOptions = [
    'Never',
    'Occasionally',
    'Socially',
    'Regularly',
  ];
  
  final List<String> smokingOptions = [
    'Never',
    'Occasionally',
    'Regularly',
    'Trying to quit',
  ];
  
  final List<String> exerciseOptions = [
    'Daily',
    'Few times a week',
    'Occasionally',
    'Never',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: AppTheme.primaryColor),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          'Lifestyle & Habits',
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Card
            GlassmorphicCard(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.favorite_outline,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Lifestyle & Habits',
                            style: TextStyle(
                              color: AppTheme.textColor,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Your lifestyle preferences',
                            style: TextStyle(
                              color: AppTheme.textColor.withValues(alpha: 0.7),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
            
            const SizedBox(height: 24),
            
            // Lifestyle Section
            _buildFormSection(
              title: 'Lifestyle Preferences',
              icon: Icons.restaurant,
              children: [
                _buildDropdownField(
                  label: 'Diet',
                  hint: 'Select your diet preference',
                  value: selectedDiet,
                  items: dietOptions,
                  onChanged: (value) => setState(() => selectedDiet = value),
                  prefixIcon: Icons.restaurant,
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdownField(
                  label: 'Drinking',
                  hint: 'Select drinking habit',
                  value: selectedDrinking,
                  items: drinkingOptions,
                  onChanged: (value) => setState(() => selectedDrinking = value),
                  prefixIcon: Icons.local_bar,
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdownField(
                  label: 'Smoking',
                  hint: 'Select smoking habit',
                  value: selectedSmoking,
                  items: smokingOptions,
                  onChanged: (value) => setState(() => selectedSmoking = value),
                  prefixIcon: Icons.smoking_rooms,
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdownField(
                  label: 'Exercise',
                  hint: 'Select exercise frequency',
                  value: selectedExercise,
                  items: exerciseOptions,
                  onChanged: (value) => setState(() => selectedExercise = value),
                  prefixIcon: Icons.fitness_center,
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // Save Button
            PremiumGradientButton(
              text: isLoading ? 'Saving...' : 'Save & Continue',
              onPressed: isLoading ? null : _saveLifestyleHabits,
              width: double.infinity,
            ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
          ],
        ),
      ),
    );
  }

  Widget _buildFormSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return GlassmorphicCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: AppTheme.primaryColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.textColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    ).animate(delay: 200.ms).fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildDropdownField({
    required String label,
    required String hint,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    required IconData prefixIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: AppTheme.textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: AppTheme.cardColor.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
            ),
          ),
          child: DropdownButtonFormField<String>(
            value: value,
            hint: Text(
              hint,
              style: TextStyle(
                color: AppTheme.textColor.withValues(alpha: 0.5),
              ),
            ),
            decoration: InputDecoration(
              prefixIcon: Icon(prefixIcon, color: AppTheme.primaryColor),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            dropdownColor: AppTheme.cardColor,
            style: TextStyle(color: AppTheme.textColor),
            items: items.map((item) {
              return DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Future<void> _saveLifestyleHabits() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Implement API call to save lifestyle habits
      await _saveLifestyleHabitsToAPI();
      await Future.delayed(const Duration(seconds: 2)); // Mock API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Lifestyle habits saved successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  Future<void> _saveLifestyleHabitsToAPI() async {
    // Simulate API call to save lifestyle and habits information
    print('Saving lifestyle and habits information to API...');
    print('Drinking: $selectedDrinking');
    print('Smoking: $selectedSmoking');
    print('Diet: $selectedDiet');
    print('Exercise: $selectedExercise');
    // In a real implementation, this would make HTTP request to backend
    await Future.delayed(const Duration(milliseconds: 500));
    print('Lifestyle and habits information saved successfully');
  }
}
