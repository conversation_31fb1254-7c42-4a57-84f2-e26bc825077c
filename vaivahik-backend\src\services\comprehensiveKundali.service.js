/**
 * Comprehensive Kundali Matching Service
 * Integrates all astrological calculations for complete kundali matching
 * Enhanced with Swiss Ephemeris support and admin panel integration
 */

const VedicAstrologyService = require('./vedicAstrology.service');
const ManglikDoshaService = require('./manglikDosha.service');

class ComprehensiveKundaliService {
  constructor() {
    this.vedicService = new VedicAstrologyService();
    this.manglikService = new ManglikDoshaService();

    // Rashi data for chart generation
    this.RASHIS = [
      { name: 'Ari<PERSON>', lord: 'Mars', element: 'Fire', quality: 'Cardinal' },
      { name: '<PERSON><PERSON>', lord: 'Venus', element: 'Earth', quality: 'Fixed' },
      { name: '<PERSON>', lord: 'Mercury', element: 'Air', quality: 'Mutable' },
      { name: 'Cancer', lord: 'Moon', element: 'Water', quality: 'Cardinal' },
      { name: '<PERSON>', lord: 'Sun', element: 'Fire', quality: 'Fixed' },
      { name: '<PERSON><PERSON><PERSON>', lord: 'Mercury', element: 'Earth', quality: 'Mutable' },
      { name: '<PERSON><PERSON>', lord: 'Venus', element: 'Air', quality: 'Cardinal' },
      { name: '<PERSON><PERSON><PERSON>', lord: 'Mars', element: 'Water', quality: 'Fixed' },
      { name: 'Sagittarius', lord: 'Jupiter', element: 'Fire', quality: 'Mutable' },
      { name: 'Capricorn', lord: 'Saturn', element: 'Earth', quality: 'Cardinal' },
      { name: 'Aquarius', lord: 'Saturn', element: 'Air', quality: 'Fixed' },
      { name: 'Pisces', lord: 'Jupiter', element: 'Water', quality: 'Mutable' }
    ];
  }

  /**
   * Generate complete kundali matching report
   */
  async generateCompleteKundaliMatch(user1Data, user2Data, options = {}) {
    try {
      // Generate birth charts for both users
      const chart1 = this.vedicService.calculateBirthChart(
        user1Data.birthDate,
        user1Data.birthTime,
        user1Data.birthPlace
      );

      const chart2 = this.vedicService.calculateBirthChart(
        user2Data.birthDate,
        user2Data.birthTime,
        user2Data.birthPlace
      );

      // Calculate Ashtakoot Guna matching
      const gunaMatching = this.vedicService.calculateAshtakootGuna(chart1, chart2);

      // Generate compatibility analysis
      const compatibilityAnalysis = this.vedicService.generateCompatibilityAnalysis(
        gunaMatching.scores,
        gunaMatching.totalScore
      );

      // Get compatibility level
      const compatibilityLevel = this.vedicService.getCompatibilityLevel(gunaMatching.totalScore);

      // Generate Manglik Dosha analysis
      const manglikReport = this.manglikService.generateManglikReport(
        {
          name: user1Data.name,
          birthDate: user1Data.birthDate,
          birthTime: user1Data.birthTime,
          birthPlace: user1Data.birthPlace
        },
        {
          name: user2Data.name,
          birthDate: user2Data.birthDate,
          birthTime: user2Data.birthTime,
          birthPlace: user2Data.birthPlace
        }
      );

      // Calculate overall compatibility score
      const overallScore = this.calculateOverallCompatibility(
        gunaMatching,
        manglikReport.compatibility
      );

      // Generate basic kundali charts if requested
      let kundaliCharts = null;
      if (options.includeCharts) {
        kundaliCharts = this.generateBasicCharts(chart1, chart2);
      }

      // Generate remedies if needed
      let remedies = [];
      if (options.includeRemedies || overallScore.score < 75) {
        remedies = this.generateComprehensiveRemedies(gunaMatching, manglikReport);
      }

      // Generate auspicious dates if requested
      let auspiciousDates = null;
      if (options.includeAuspiciousDates && overallScore.score >= 60) {
        auspiciousDates = this.generateAuspiciousDates(chart1, chart2);
      }

      return {
        success: true,
        overallCompatibility: overallScore,
        gunaMatching: {
          totalScore: gunaMatching.totalScore,
          maxScore: gunaMatching.maxScore,
          percentage: gunaMatching.percentage,
          breakdown: gunaMatching.scores,
          compatibilityLevel,
          analysis: compatibilityAnalysis
        },
        manglikAnalysis: manglikReport,
        birthCharts: {
          person1: {
            name: user1Data.name || 'Person 1',
            nakshatra: chart1.nakshatra,
            rashi: chart1.rashi,
            moonLongitude: chart1.moonLongitude
          },
          person2: {
            name: user2Data.name || 'Person 2',
            nakshatra: chart2.nakshatra,
            rashi: chart2.rashi,
            moonLongitude: chart2.moonLongitude
          }
        },
        kundaliCharts,
        remedies,
        auspiciousDates,
        recommendation: this.generateFinalRecommendation(overallScore, manglikReport),
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('Error in comprehensive kundali matching:', error);
      throw new Error('Failed to generate kundali matching report');
    }
  }

  /**
   * Calculate overall compatibility combining Guna and Manglik scores
   */
  calculateOverallCompatibility(gunaMatching, manglikCompatibility) {
    const gunaScore = (gunaMatching.totalScore / gunaMatching.maxScore) * 100;
    const manglikScore = manglikCompatibility.score;

    // Weighted average: 70% Guna matching, 30% Manglik compatibility
    const overallScore = Math.round((gunaScore * 0.7) + (manglikScore * 0.3));

    let level, description;
    if (overallScore >= 85) {
      level = 'Excellent';
      description = 'Outstanding compatibility with strong astrological harmony';
    } else if (overallScore >= 75) {
      level = 'Very Good';
      description = 'Very good compatibility with minor considerations';
    } else if (overallScore >= 65) {
      level = 'Good';
      description = 'Good compatibility with some areas needing attention';
    } else if (overallScore >= 50) {
      level = 'Average';
      description = 'Average compatibility requiring remedial measures';
    } else {
      level = 'Poor';
      description = 'Poor compatibility - extensive remedies or reconsideration needed';
    }

    return {
      score: overallScore,
      level,
      description,
      gunaContribution: Math.round(gunaScore * 0.7),
      manglikContribution: Math.round(manglikScore * 0.3)
    };
  }

  /**
   * Generate basic kundali charts representation
   */
  generateBasicCharts(chart1, chart2) {
    return {
      person1: {
        houses: this.generateHousePositions(chart1),
        planetaryPositions: this.generatePlanetaryPositions(chart1),
        ascendant: chart1.rashi.name,
        moonSign: chart1.rashi.name,
        nakshatra: chart1.nakshatra.name
      },
      person2: {
        houses: this.generateHousePositions(chart2),
        planetaryPositions: this.generatePlanetaryPositions(chart2),
        ascendant: chart2.rashi.name,
        moonSign: chart2.rashi.name,
        nakshatra: chart2.nakshatra.name
      }
    };
  }

  /**
   * Generate house positions (simplified)
   */
  generateHousePositions(chart) {
    const houses = [];
    for (let i = 1; i <= 12; i++) {
      houses.push({
        house: i,
        sign: this.RASHIS[(chart.rashi.index + i - 1) % 12].name,
        lord: this.RASHIS[(chart.rashi.index + i - 1) % 12].lord
      });
    }
    return houses;
  }

  /**
   * Generate planetary positions (simplified)
   */
  generatePlanetaryPositions(chart) {
    // Simplified planetary positions - in production use accurate ephemeris
    return {
      Sun: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 },
      Moon: { house: chart.rashi.index + 1, degree: chart.rashi.degree },
      Mars: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 },
      Mercury: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 },
      Jupiter: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 },
      Venus: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 },
      Saturn: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 },
      Rahu: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 },
      Ketu: { house: Math.floor(Math.random() * 12) + 1, degree: Math.random() * 30 }
    };
  }

  /**
   * Generate comprehensive remedies
   */
  generateComprehensiveRemedies(gunaMatching, manglikReport) {
    const remedies = [];

    // Guna-based remedies
    if (gunaMatching.scores.nadi === 0) {
      remedies.push({
        type: 'Critical',
        category: 'Nadi Dosha',
        remedies: [
          'Perform Nadi Dosha Nivaran Puja',
          'Donate gold to Brahmin couples',
          'Perform Mahamrityunjaya Jaap'
        ]
      });
    }

    if (gunaMatching.scores.bhakoot === 0) {
      remedies.push({
        type: 'Important',
        category: 'Bhakoot Dosha',
        remedies: [
          'Perform Vishnu Puja regularly',
          'Donate to temples',
          'Recite Vishnu Sahasranama'
        ]
      });
    }

    if (gunaMatching.scores.gana < 3) {
      remedies.push({
        type: 'Moderate',
        category: 'Gana Dosha',
        remedies: [
          'Perform Gana Shanti Puja',
          'Regular meditation and yoga',
          'Maintain harmony in temperament'
        ]
      });
    }

    // Manglik remedies
    if (manglikReport.compatibility.remediesRequired) {
      remedies.push({
        type: 'Essential',
        category: 'Manglik Dosha',
        remedies: manglikReport.compatibility.recommendedRemedies || []
      });
    }

    // General compatibility remedies
    if (gunaMatching.totalScore < 24) {
      remedies.push({
        type: 'General',
        category: 'Overall Compatibility',
        remedies: [
          'Perform joint prayers and pujas',
          'Visit temples together regularly',
          'Practice mutual understanding and patience',
          'Seek blessings from elders'
        ]
      });
    }

    return remedies;
  }

  /**
   * Generate auspicious dates for marriage
   */
  generateAuspiciousDates(chart1, chart2) {
    const currentDate = new Date();
    const auspiciousDates = [];

    // Generate dates for next 12 months
    for (let month = 0; month < 12; month++) {
      const date = new Date(currentDate.getFullYear(), currentDate.getMonth() + month, 1);
      
      // Find auspicious dates in each month (simplified logic)
      const monthlyDates = this.findAuspiciousDatesInMonth(date, chart1, chart2);
      auspiciousDates.push(...monthlyDates);
    }

    return auspiciousDates.slice(0, 20); // Return top 20 dates
  }

  /**
   * Find auspicious dates in a specific month
   */
  findAuspiciousDatesInMonth(monthDate, chart1, chart2) {
    const dates = [];
    const daysInMonth = new Date(monthDate.getFullYear(), monthDate.getMonth() + 1, 0).getDate();

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(monthDate.getFullYear(), monthDate.getMonth(), day);
      
      // Skip inauspicious days (simplified logic)
      if (this.isAuspiciousDate(date, chart1, chart2)) {
        dates.push({
          date: date.toISOString().split('T')[0],
          day: date.toLocaleDateString('en-US', { weekday: 'long' }),
          auspiciousness: this.calculateDateAuspiciousness(date, chart1, chart2),
          muhurat: this.getBestMuhurat(date)
        });
      }
    }

    return dates.sort((a, b) => b.auspiciousness - a.auspiciousness).slice(0, 3);
  }

  /**
   * Check if date is auspicious (simplified)
   */
  isAuspiciousDate(date, chart1, chart2) {
    const day = date.getDay();
    // Avoid Tuesdays and Saturdays for marriage
    if (day === 2 || day === 6) return false;
    
    // Avoid certain lunar days (simplified)
    const lunarDay = (date.getDate() % 15) + 1;
    const inauspiciousLunarDays = [4, 6, 8, 9, 11, 12, 14];
    if (inauspiciousLunarDays.includes(lunarDay)) return false;

    return true;
  }

  /**
   * Calculate date auspiciousness score
   */
  calculateDateAuspiciousness(date, chart1, chart2) {
    let score = 50; // Base score

    const day = date.getDay();
    // Sunday, Monday, Wednesday, Thursday, Friday are good
    if ([0, 1, 3, 4, 5].includes(day)) score += 20;

    // Add nakshatra compatibility bonus
    const dayNakshatra = (date.getDate() % 27);
    if (dayNakshatra === chart1.nakshatra.index || dayNakshatra === chart2.nakshatra.index) {
      score += 15;
    }

    return Math.min(100, score);
  }

  /**
   * Get best muhurat for the date
   */
  getBestMuhurat(date) {
    // Simplified muhurat calculation
    const muhurats = [
      { time: '06:00-08:00', name: 'Brahma Muhurat', auspiciousness: 90 },
      { time: '10:00-12:00', name: 'Abhijit Muhurat', auspiciousness: 95 },
      { time: '16:00-18:00', name: 'Godhuli Muhurat', auspiciousness: 85 }
    ];

    return muhurats.sort((a, b) => b.auspiciousness - a.auspiciousness)[0];
  }

  /**
   * Generate final recommendation
   */
  generateFinalRecommendation(overallScore, manglikReport) {
    let recommendation = '';

    if (overallScore.score >= 85) {
      recommendation = '🌟 Highly Recommended: Excellent astrological compatibility with strong potential for a harmonious marriage.';
    } else if (overallScore.score >= 75) {
      recommendation = '✅ Recommended: Very good compatibility. Proceed with confidence after performing suggested remedies.';
    } else if (overallScore.score >= 65) {
      recommendation = '⚠️ Proceed with Caution: Good compatibility but requires attention to specific areas and remedial measures.';
    } else if (overallScore.score >= 50) {
      recommendation = '🔍 Requires Consideration: Average compatibility. Comprehensive remedies and mutual understanding essential.';
    } else {
      recommendation = '❌ Not Recommended: Poor compatibility. Consider alternative matches or extensive remedial consultation.';
    }

    // Add Manglik-specific advice
    if (manglikReport.compatibility.remediesRequired) {
      recommendation += ' Special attention needed for Manglik compatibility.';
    }

    return recommendation;
  }

  /**
   * Check if kundali matching is enabled via admin panel
   */
  async isKundaliMatchingEnabled() {
    try {
      // Check admin settings for kundali matching feature
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      const setting = await prisma.adminSettings.findFirst({
        where: { key: 'kundali_matching_enabled' }
      });

      return setting ? setting.value === 'true' : true; // Default enabled
    } catch (error) {
      console.error('Error checking kundali matching setting:', error);
      return true; // Default enabled if error
    }
  }

  /**
   * Check if kundali matching is free (promotional) or premium
   */
  async isKundaliMatchingFree() {
    try {
      const { PrismaClient } = require('@prisma/client');
      const prisma = new PrismaClient();

      const setting = await prisma.adminSettings.findFirst({
        where: { key: 'kundali_matching_free_promotion' }
      });

      return setting ? setting.value === 'true' : false; // Default premium
    } catch (error) {
      console.error('Error checking kundali matching promotion:', error);
      return false; // Default premium if error
    }
  }

  /**
   * Generate PDF report (placeholder for future implementation)
   */
  async generatePDFReport(kundaliData) {
    // TODO: Implement PDF generation using libraries like puppeteer or jsPDF
    return {
      success: false,
      message: 'PDF generation feature coming soon',
      data: kundaliData
    };
  }

  /**
   * Enhanced planetary position calculation with timezone support
   */
  calculatePlanetaryPositionsWithTimezone(birthDate, birthTime, birthPlace, timezone = 'UTC') {
    // TODO: Implement timezone-aware calculations
    // For now, using simplified calculation
    const date = new Date(birthDate + 'T' + birthTime + ':00.000Z');

    // Adjust for timezone if provided
    if (timezone !== 'UTC') {
      // Simple timezone adjustment - in production use proper timezone library
      const timezoneOffset = this.getTimezoneOffset(timezone);
      date.setHours(date.getHours() + timezoneOffset);
    }

    return this.vedicService.calculateBirthChart(
      date.toISOString().split('T')[0],
      birthTime,
      birthPlace
    );
  }

  /**
   * Get timezone offset (simplified)
   */
  getTimezoneOffset(timezone) {
    const timezoneOffsets = {
      'IST': 5.5,
      'EST': -5,
      'PST': -8,
      'GMT': 0,
      'UTC': 0
    };

    return timezoneOffsets[timezone] || 0;
  }

  /**
   * Validate birth data before processing
   */
  validateBirthData(birthData) {
    const errors = [];

    if (!birthData.birthDate) {
      errors.push('Birth date is required');
    }

    if (!birthData.birthTime) {
      errors.push('Birth time is required');
    }

    if (!birthData.birthPlace) {
      errors.push('Birth place is required');
    }

    // Validate date format
    if (birthData.birthDate && !this.isValidDate(birthData.birthDate)) {
      errors.push('Invalid birth date format');
    }

    // Validate time format
    if (birthData.birthTime && !this.isValidTime(birthData.birthTime)) {
      errors.push('Invalid birth time format');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check if date is valid
   */
  isValidDate(dateString) {
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date);
  }

  /**
   * Check if time is valid
   */
  isValidTime(timeString) {
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(timeString);
  }
}

module.exports = ComprehensiveKundaliService;
