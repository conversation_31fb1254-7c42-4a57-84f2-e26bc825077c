const express = require('express');
const router = express.Router();
const { PrismaClient } = require('@prisma/client');

// Import kundali services for testing
const VedicAstrologyService = require('../../services/vedicAstrology.service');
const ManglikDoshaService = require('../../services/manglikDosha.service');
const ComprehensiveKundaliService = require('../../services/comprehensiveKundali.service');

const prisma = new PrismaClient();

/**
 * @route GET /api/admin/kundali-settings
 * @desc Get all kundali settings
 * @access Admin
 */
router.get('/kundali-settings', async (req, res) => {
  try {
    // Get all kundali-related settings
    const settings = await prisma.adminSettings.findMany({
      where: {
        category: 'kundali'
      }
    });

    // Convert to key-value object
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });

    res.json({
      success: true,
      settings: settingsObj
    });
  } catch (error) {
    console.error('Error fetching kundali settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch kundali settings'
    });
  }
});

/**
 * @route PUT /api/admin/kundali-settings
 * @desc Update kundali settings
 * @access Admin
 */
router.put('/kundali-settings', async (req, res) => {
  try {
    const { settings } = req.body;

    // Update each setting
    const updatePromises = Object.entries(settings).map(async ([key, value]) => {
      return prisma.adminSettings.upsert({
        where: { key },
        update: { 
          value: value.toString(),
          updatedAt: new Date()
        },
        create: {
          key,
          value: value.toString(),
          category: 'kundali',
          dataType: typeof value === 'boolean' ? 'boolean' : 
                   typeof value === 'number' ? 'number' : 'string'
        }
      });
    });

    await Promise.all(updatePromises);

    res.json({
      success: true,
      message: 'Kundali settings updated successfully'
    });
  } catch (error) {
    console.error('Error updating kundali settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update kundali settings'
    });
  }
});

/**
 * @route POST /api/admin/kundali-test
 * @desc Run comprehensive kundali system test
 * @access Admin
 */
router.post('/kundali-test', async (req, res) => {
  try {
    const vedicService = new VedicAstrologyService();
    const manglikService = new ManglikDoshaService();
    const kundaliService = new ComprehensiveKundaliService();

    const testResults = {
      tests: [],
      passed: 0,
      failed: 0,
      total: 0
    };

    // Test 1: Basic Nakshatra calculation
    try {
      const nakshatra = vedicService.getNakshatraFromLongitude(45);
      testResults.tests.push({
        name: 'Nakshatra Calculation',
        status: nakshatra && nakshatra.name ? 'PASS' : 'FAIL',
        details: nakshatra ? `Got: ${nakshatra.name}` : 'No result'
      });
      if (nakshatra && nakshatra.name) testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      testResults.tests.push({
        name: 'Nakshatra Calculation',
        status: 'FAIL',
        details: error.message
      });
      testResults.failed++;
    }

    // Test 2: Basic Rashi calculation
    try {
      const rashi = vedicService.getRashiFromLongitude(45);
      testResults.tests.push({
        name: 'Rashi Calculation',
        status: rashi && rashi.name ? 'PASS' : 'FAIL',
        details: rashi ? `Got: ${rashi.name}` : 'No result'
      });
      if (rashi && rashi.name) testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      testResults.tests.push({
        name: 'Rashi Calculation',
        status: 'FAIL',
        details: error.message
      });
      testResults.failed++;
    }

    // Test 3: Manglik dosha detection
    try {
      const doshaResult = manglikService.detectManglikDosha('1990-05-15', '14:30', 'Mumbai, India');
      testResults.tests.push({
        name: 'Manglik Dosha Detection',
        status: doshaResult && typeof doshaResult.isManglik === 'boolean' ? 'PASS' : 'FAIL',
        details: doshaResult ? `Manglik: ${doshaResult.isManglik}, Level: ${doshaResult.doshaLevelName}` : 'No result'
      });
      if (doshaResult && typeof doshaResult.isManglik === 'boolean') testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      testResults.tests.push({
        name: 'Manglik Dosha Detection',
        status: 'FAIL',
        details: error.message
      });
      testResults.failed++;
    }

    // Test 4: Complete kundali matching
    try {
      const user1 = {
        id: 'test1',
        name: 'Test User 1',
        birthDate: '1990-05-15',
        birthTime: '14:30',
        birthPlace: 'Mumbai, India'
      };

      const user2 = {
        id: 'test2',
        name: 'Test User 2',
        birthDate: '1992-08-20',
        birthTime: '10:15',
        birthPlace: 'Delhi, India'
      };

      const result = await kundaliService.generateCompleteKundaliMatch(user1, user2);
      testResults.tests.push({
        name: 'Complete Kundali Matching',
        status: result && result.overallCompatibility ? 'PASS' : 'FAIL',
        details: result ? `Score: ${result.overallCompatibility.score}%` : 'No result'
      });
      if (result && result.overallCompatibility) testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      testResults.tests.push({
        name: 'Complete Kundali Matching',
        status: 'FAIL',
        details: error.message
      });
      testResults.failed++;
    }

    // Test 5: Performance test
    const startTime = Date.now();
    try {
      const chart = vedicService.calculateBirthChart('1990-05-15', '14:30', 'Mumbai, India');
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      testResults.tests.push({
        name: 'Performance Test',
        status: executionTime < 5000 ? 'PASS' : 'FAIL',
        details: `Execution time: ${executionTime}ms`
      });
      if (executionTime < 5000) testResults.passed++;
      else testResults.failed++;
    } catch (error) {
      testResults.tests.push({
        name: 'Performance Test',
        status: 'FAIL',
        details: error.message
      });
      testResults.failed++;
    }

    testResults.total = testResults.passed + testResults.failed;
    testResults.successRate = Math.round((testResults.passed / testResults.total) * 100);
    testResults.success = testResults.successRate >= 80;

    res.json({
      success: true,
      ...testResults
    });
  } catch (error) {
    console.error('Error running kundali test:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to run kundali system test'
    });
  }
});

/**
 * @route GET /api/admin/kundali-stats
 * @desc Get kundali usage statistics
 * @access Admin
 */
router.get('/kundali-stats', async (req, res) => {
  try {
    // Get usage statistics (this would be from actual usage logs in production)
    const stats = {
      totalMatches: 0,
      dailyMatches: 0,
      premiumMatches: 0,
      freeMatches: 0,
      averageScore: 0,
      topCompatibilityPairs: []
    };

    // In production, you would query actual usage data from database
    // For now, return mock stats
    stats.totalMatches = 1250;
    stats.dailyMatches = 45;
    stats.premiumMatches = 890;
    stats.freeMatches = 360;
    stats.averageScore = 73.5;

    res.json({
      success: true,
      stats
    });
  } catch (error) {
    console.error('Error fetching kundali stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch kundali statistics'
    });
  }
});

module.exports = router;
