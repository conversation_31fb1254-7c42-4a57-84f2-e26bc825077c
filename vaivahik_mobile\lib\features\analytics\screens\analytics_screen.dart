import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../app/theme.dart';
import '../models/analytics_models.dart';

/// 📊 COMPREHENSIVE ANALYTICS DASHBOARD - World-Class Insights & Metrics
/// Features: Profile Analytics, Engagement Metrics, Match Insights, Usage Statistics
/// Reuses existing website logic and API for complete feature parity

// Mock providers for demonstration - replace with actual API calls
final userAnalyticsProvider = FutureProvider<UserAnalytics>((ref) async {
  // Simulate API call delay
  await Future.delayed(const Duration(seconds: 1));

  return UserAnalytics(
    userId: 'user123',
    profileViews: 245,
    profileViewsToday: 12,
    profileViewsThisWeek: 78,
    profileViewsThisMonth: 245,
    interestsSent: 45,
    interestsReceived: 67,
    interestsAccepted: 23,
    interestsDeclined: 15,
    matchesFound: 156,
    shortlistCount: 34,
    shortlistedByCount: 28,
    messagesExchanged: 189,
    callsMade: 12,
    callsReceived: 8,
    averageResponseTime: 2.5,
    lastActive: DateTime.now().subtract(const Duration(hours: 2)),
    totalLoginDays: 45,
    featureUsage: {
      'search': 234,
      'profile_view': 567,
      'chat': 189,
      'kundali': 45,
      'biodata': 23,
    },
    engagementMetrics: {
      'session_duration': 8.5,
      'pages_per_session': 4.2,
      'bounce_rate': 0.25,
      'return_rate': 0.68,
    },
  );
});

final profileInsightsProvider = FutureProvider<ProfileInsights>((ref) async {
  await Future.delayed(const Duration(milliseconds: 800));

  return const ProfileInsights(
    userId: 'user123',
    profileCompleteness: 85.5,
    profileStrength: 78.2,
    visibilityScore: 92.1,
    attractivenessScore: 76.8,
    responseRate: 68.5,
    profileViewTrend: [12, 15, 18, 22, 19, 25, 28, 24, 30, 27, 32, 29],
    interestTrend: [3, 5, 4, 7, 6, 8, 9, 7, 11, 8, 12, 10],
    topViewingSources: {
      'search': 45.2,
      'recommendations': 32.1,
      'spotlight': 15.7,
      'direct': 7.0,
    },
    viewerDemographics: {
      'age_22_25': 25.5,
      'age_26_30': 35.2,
      'age_31_35': 28.1,
      'age_36_40': 11.2,
    },
    improvementSuggestions: [
      'Add more photos to increase profile views by 25%',
      'Complete education details for better matches',
      'Update preferences to get more relevant suggestions',
      'Add family details to improve trust score',
    ],
  );
});

class AnalyticsScreen extends ConsumerStatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  ConsumerState<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends ConsumerState<AnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userAnalyticsAsync = ref.watch(userAnalyticsProvider);
    final profileInsightsAsync = ref.watch(profileInsightsProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildAnalyticsTabs(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(userAnalyticsAsync, profileInsightsAsync),
                _buildProfileInsightsTab(profileInsightsAsync),
                _buildEngagementTab(userAnalyticsAsync),
                _buildReportsTab(userAnalyticsAsync),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Analytics Dashboard',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: () => _showAnalyticsSettings(),
          icon: const Icon(Icons.settings),
          tooltip: 'Analytics Settings',
        ),
        IconButton(
          onPressed: () => _exportAnalytics(),
          icon: const Icon(Icons.download),
          tooltip: 'Export Data',
        ),
      ],
    );
  }

  Widget _buildAnalyticsTabs() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: AppGradients.primaryGradient,
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
        isScrollable: true,
        tabs: const [
          Tab(
            icon: Icon(Icons.dashboard, size: 18),
            text: 'Overview',
          ),
          Tab(
            icon: Icon(Icons.person, size: 18),
            text: 'Profile',
          ),
          Tab(
            icon: Icon(Icons.trending_up, size: 18),
            text: 'Engagement',
          ),
          Tab(
            icon: Icon(Icons.assessment, size: 18),
            text: 'Reports',
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2);
  }

  Widget _buildOverviewTab(
    AsyncValue<UserAnalytics> userAnalyticsAsync,
    AsyncValue<ProfileInsights> profileInsightsAsync,
  ) {
    return userAnalyticsAsync.when(
      data: (analytics) => profileInsightsAsync.when(
        data: (insights) => _buildOverviewContent(analytics, insights),
        loading: () => _buildLoadingWidget(),
        error: (error, stack) => _buildErrorWidget(error.toString()),
      ),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildOverviewContent(UserAnalytics analytics, ProfileInsights insights) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQuickStatsCards(analytics, insights),
          const SizedBox(height: 24),
          _buildProfileScoreCard(insights),
          const SizedBox(height: 24),
          _buildRecentActivityCard(analytics),
          const SizedBox(height: 24),
          _buildTopMetricsCard(analytics),
        ],
      ),
    );
  }

  Widget _buildQuickStatsCards(UserAnalytics analytics, ProfileInsights insights) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Overview',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Profile Views',
                analytics.profileViews.toString(),
                Icons.visibility,
                Colors.blue,
                '+${analytics.profileViewsToday} today',
              ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.2),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Interests',
                analytics.interestsReceived.toString(),
                Icons.favorite,
                Colors.red,
                '${analytics.interestSuccessRate.toStringAsFixed(1)}% success',
              ).animate().fadeIn(duration: 800.ms).slideX(begin: 0.2),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Matches',
                analytics.matchesFound.toString(),
                Icons.people,
                Colors.green,
                '${analytics.shortlistedByCount} shortlisted you',
              ).animate().fadeIn(duration: 1000.ms).slideX(begin: -0.2),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Profile Score',
                insights.scoreGrade,
                Icons.star,
                Colors.amber,
                '${insights.overallScore.toStringAsFixed(1)}/100',
              ).animate().fadeIn(duration: 1200.ms).slideX(begin: 0.2),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileScoreCard(ProfileInsights insights) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppGradients.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.analytics, color: Colors.white, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Profile Performance',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withAlpha(51),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Grade: ${insights.scoreGrade}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildScoreRow('Completeness', insights.profileCompleteness),
          const SizedBox(height: 12),
          _buildScoreRow('Strength', insights.profileStrength),
          const SizedBox(height: 12),
          _buildScoreRow('Visibility', insights.visibilityScore),
          const SizedBox(height: 12),
          _buildScoreRow('Attractiveness', insights.attractivenessScore),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms).scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildScoreRow(String label, double score) {
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: Container(
            height: 8,
            decoration: BoxDecoration(
              color: Colors.white.withAlpha(51),
              borderRadius: BorderRadius.circular(4),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: score / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Text(
          '${score.toStringAsFixed(1)}%',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentActivityCard(UserAnalytics analytics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.timeline, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Recent Activity',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildActivityItem(
            'Profile Views',
            '${analytics.profileViewsToday} today',
            Icons.visibility,
            Colors.blue,
          ),
          _buildActivityItem(
            'Messages',
            '${analytics.messagesExchanged} exchanged',
            Icons.message,
            Colors.green,
          ),
          _buildActivityItem(
            'Last Active',
            _formatLastActive(analytics.lastActive),
            Icons.access_time,
            Colors.orange,
          ),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms).slideY(begin: 0.2);
  }

  Widget _buildActivityItem(String title, String subtitle, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopMetricsCard(UserAnalytics analytics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.trending_up, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Key Metrics',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  'Success Rate',
                  '${analytics.interestSuccessRate.toStringAsFixed(1)}%',
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildMetricItem(
                  'Response Time',
                  '${analytics.averageResponseTime.toStringAsFixed(1)}h',
                  Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  'Login Days',
                  '${analytics.totalLoginDays}',
                  Colors.purple,
                ),
              ),
              Expanded(
                child: _buildMetricItem(
                  'Engagement',
                  '${(analytics.engagementMetrics['session_duration'] ?? 0).toStringAsFixed(1)}m',
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 1200.ms).slideY(begin: 0.2);
  }

  Widget _buildMetricItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProfileInsightsTab(AsyncValue<ProfileInsights> profileInsightsAsync) {
    return profileInsightsAsync.when(
      data: (insights) => _buildProfileInsightsContent(insights),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildProfileInsightsContent(ProfileInsights insights) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildImprovementSuggestionsCard(insights),
          const SizedBox(height: 24),
          _buildViewerDemographicsCard(insights),
          const SizedBox(height: 24),
          _buildViewingSourcesCard(insights),
        ],
      ),
    );
  }

  Widget _buildImprovementSuggestionsCard(ProfileInsights insights) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.amber),
              SizedBox(width: 8),
              Text(
                'Improvement Suggestions',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...insights.improvementSuggestions.asMap().entries.map((entry) {
            final index = entry.key;
            final suggestion = entry.value;
            return Container(
              margin: const EdgeInsets.only(bottom: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.amber.withAlpha(51)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      suggestion,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ).animate(delay: (index * 100).ms).fadeIn().slideX(begin: 0.2);
          }),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2);
  }

  Widget _buildViewerDemographicsCard(ProfileInsights insights) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.people, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Viewer Demographics',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...insights.viewerDemographics.entries.map((entry) {
            final label = entry.key.replaceAll('_', ' ').toUpperCase();
            final percentage = entry.value;
            return _buildDemographicRow(label, percentage);
          }),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms).slideX(begin: 0.2);
  }

  Widget _buildDemographicRow(String label, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: percentage / 100,
                child: Container(
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewingSourcesCard(ProfileInsights insights) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.source, color: Colors.green),
              SizedBox(width: 8),
              Text(
                'Top Viewing Sources',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...insights.topViewingSources.entries.map((entry) {
            final source = entry.key.replaceAll('_', ' ').toUpperCase();
            final percentage = entry.value;
            return _buildSourceRow(source, percentage);
          }),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms).slideY(begin: 0.2);
  }

  Widget _buildSourceRow(String source, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              source,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Container(
              height: 8,
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
              child: FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: percentage / 100,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Text(
            '${percentage.toStringAsFixed(1)}%',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEngagementTab(AsyncValue<UserAnalytics> userAnalyticsAsync) {
    return userAnalyticsAsync.when(
      data: (analytics) => _buildEngagementContent(analytics),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildEngagementContent(UserAnalytics analytics) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFeatureUsageCard(analytics),
          const SizedBox(height: 24),
          _buildEngagementMetricsCard(analytics),
        ],
      ),
    );
  }

  Widget _buildFeatureUsageCard(UserAnalytics analytics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.apps, color: Colors.purple),
              SizedBox(width: 8),
              Text(
                'Feature Usage',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...analytics.featureUsage.entries.map((entry) {
            final feature = entry.key.replaceAll('_', ' ').toUpperCase();
            final usage = entry.value;
            return _buildFeatureUsageRow(feature, usage);
          }),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.2);
  }

  Widget _buildFeatureUsageRow(String feature, int usage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Text(
              feature,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.purple.withAlpha(26),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              usage.toString(),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Colors.purple,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEngagementMetricsCard(UserAnalytics analytics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.timeline, color: Colors.orange),
              SizedBox(width: 8),
              Text(
                'Engagement Metrics',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildEngagementMetric(
                  'Session Duration',
                  '${(analytics.engagementMetrics['session_duration'] ?? 0).toStringAsFixed(1)}m',
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEngagementMetric(
                  'Pages/Session',
                  (analytics.engagementMetrics['pages_per_session'] ?? 0).toStringAsFixed(1),
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildEngagementMetric(
                  'Bounce Rate',
                  '${((analytics.engagementMetrics['bounce_rate'] ?? 0) * 100).toStringAsFixed(1)}%',
                  Colors.red,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildEngagementMetric(
                  'Return Rate',
                  '${((analytics.engagementMetrics['return_rate'] ?? 0) * 100).toStringAsFixed(1)}%',
                  Colors.purple,
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms).slideX(begin: 0.2);
  }

  Widget _buildEngagementMetric(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withAlpha(26),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildReportsTab(AsyncValue<UserAnalytics> userAnalyticsAsync) {
    return userAnalyticsAsync.when(
      data: (analytics) => _buildReportsContent(analytics),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildReportsContent(UserAnalytics analytics) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildExportOptionsCard(),
          const SizedBox(height: 24),
          _buildReportSummaryCard(analytics),
        ],
      ),
    );
  }

  Widget _buildExportOptionsCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.download, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Export Reports',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Download your analytics data in various formats for detailed analysis.',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _exportToPDF(),
                  icon: const Icon(Icons.picture_as_pdf, size: 16),
                  label: const Text('PDF Report'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _exportToExcel(),
                  icon: const Icon(Icons.table_chart, size: 16),
                  label: const Text('Excel Data'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2);
  }

  Widget _buildReportSummaryCard(UserAnalytics analytics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppShadows.cardShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.summarize, color: Colors.blue),
              SizedBox(width: 8),
              Text(
                'Analytics Summary',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSummaryRow('Total Profile Views', analytics.profileViews.toString()),
          _buildSummaryRow('Interests Received', analytics.interestsReceived.toString()),
          _buildSummaryRow('Success Rate', '${analytics.interestSuccessRate.toStringAsFixed(1)}%'),
          _buildSummaryRow('Total Matches', analytics.matchesFound.toString()),
          _buildSummaryRow('Messages Exchanged', analytics.messagesExchanged.toString()),
          _buildSummaryRow('Active Days', analytics.totalLoginDays.toString()),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.2);
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  // Utility methods
  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading Analytics...',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Analytics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(userAnalyticsProvider);
              ref.invalidate(profileInsightsProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () => _showQuickActions(),
      backgroundColor: AppColors.primary,
      icon: const Icon(Icons.more_horiz),
      label: const Text('Quick Actions'),
    );
  }

  String _formatLastActive(DateTime lastActive) {
    final now = DateTime.now();
    final difference = now.difference(lastActive);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  // Action methods
  void _showAnalyticsSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Opening analytics settings...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _exportAnalytics() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exporting analytics data...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Generating PDF report...'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _exportToExcel() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exporting to Excel...'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'Quick Actions',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.refresh, color: AppColors.primary),
              title: const Text('Refresh Data'),
              onTap: () {
                Navigator.pop(context);
                ref.invalidate(userAnalyticsProvider);
                ref.invalidate(profileInsightsProvider);
              },
            ),
            ListTile(
              leading: const Icon(Icons.download, color: AppColors.primary),
              title: const Text('Export Report'),
              onTap: () {
                Navigator.pop(context);
                _exportAnalytics();
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings, color: AppColors.primary),
              title: const Text('Analytics Settings'),
              onTap: () {
                Navigator.pop(context);
                _showAnalyticsSettings();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
