/**
 * Comprehensive Vedic Astrology Service
 * Authentic kundali matching system based on traditional Vedic astrology
 * No third-party dependencies - all calculations done in-house
 */

class VedicAstrologyService {
  constructor() {
    // 27 Nakshatras with their properties
    this.NAKSHATRAS = [
      { name: '<PERSON><PERSON><PERSON>', lord: '<PERSON><PERSON>', pada: [0, 13.33], gana: '<PERSON><PERSON>', yoni: 'Horse', varna: '<PERSON><PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON>', lord: '<PERSON>', pada: [13.33, 26.67], gana: '<PERSON><PERSON><PERSON>', yoni: '<PERSON>', varna: '<PERSON><PERSON><PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', lord: '<PERSON>', pada: [26.67, 40], gana: '<PERSON><PERSON><PERSON>', yoni: 'She<PERSON>', varna: '<PERSON><PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON>', lord: '<PERSON>', pada: [40, 53.33], gana: '<PERSON><PERSON><PERSON>', yoni: 'Ser<PERSON>', varna: '<PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', lord: '<PERSON>', pada: [53.33, 66.67], gana: '<PERSON><PERSON>', yoni: 'Serpent', varna: '<PERSON><PERSON>' },
      { name: '<PERSON><PERSON><PERSON>', lord: '<PERSON><PERSON>', pada: [66.67, 80], gana: '<PERSON><PERSON><PERSON>', yoni: '<PERSON>', varna: '<PERSON>' },
      { name: 'Punarvasu', lord: '<PERSON>', pada: [80, 93.33], gana: 'Deva', yoni: '<PERSON>', varna: 'Vaishya' },
      { name: 'Pushya', lord: '<PERSON>', pada: [93.33, 106.67], gana: '<PERSON>a', yoni: 'Sheep', varna: 'Brahmin' },
      { name: '<PERSON><PERSON>a', lord: 'Mercury', pada: [106.67, 120], gana: 'Rakshasa', yoni: 'Cat', varna: 'Mleccha' },
      { name: 'Magha', lord: 'Ketu', pada: [120, 133.33], gana: 'Rakshasa', yoni: 'Rat', varna: 'Shudra' },
      { name: 'Purva Phalguni', lord: 'Venus', pada: [133.33, 146.67], gana: 'Manushya', yoni: 'Rat', varna: 'Brahmin' },
      { name: 'Uttara Phalguni', lord: 'Sun', pada: [146.67, 160], gana: 'Manushya', yoni: 'Cow', varna: 'Kshatriya' },
      { name: 'Hasta', lord: 'Moon', pada: [160, 173.33], gana: 'Deva', yoni: 'Buffalo', varna: 'Vaishya' },
      { name: 'Chitra', lord: 'Mars', pada: [173.33, 186.67], gana: 'Rakshasa', yoni: 'Tiger', varna: 'Servant' },
      { name: 'Swati', lord: 'Rahu', pada: [186.67, 200], gana: 'Deva', yoni: 'Buffalo', varna: 'Butcher' },
      { name: 'Vishakha', lord: 'Jupiter', pada: [200, 213.33], gana: 'Rakshasa', yoni: 'Tiger', varna: 'Mleccha' },
      { name: 'Anuradha', lord: 'Saturn', pada: [213.33, 226.67], gana: 'Deva', yoni: 'Deer', varna: 'Shudra' },
      { name: 'Jyeshtha', lord: 'Mercury', pada: [226.67, 240], gana: 'Rakshasa', yoni: 'Deer', varna: 'Servant' },
      { name: 'Mula', lord: 'Ketu', pada: [240, 253.33], gana: 'Rakshasa', yoni: 'Dog', varna: 'Butcher' },
      { name: 'Purva Ashadha', lord: 'Venus', pada: [253.33, 266.67], gana: 'Manushya', yoni: 'Monkey', varna: 'Brahmin' },
      { name: 'Uttara Ashadha', lord: 'Sun', pada: [266.67, 280], gana: 'Manushya', yoni: 'Mongoose', varna: 'Kshatriya' },
      { name: 'Shravana', lord: 'Moon', pada: [280, 293.33], gana: 'Deva', yoni: 'Monkey', varna: 'Mleccha' },
      { name: 'Dhanishta', lord: 'Mars', pada: [293.33, 306.67], gana: 'Rakshasa', yoni: 'Lion', varna: 'Servant' },
      { name: 'Shatabhisha', lord: 'Rahu', pada: [306.67, 320], gana: 'Rakshasa', yoni: 'Horse', varna: 'Butcher' },
      { name: 'Purva Bhadrapada', lord: 'Jupiter', pada: [320, 333.33], gana: 'Manushya', yoni: 'Lion', varna: 'Brahmin' },
      { name: 'Uttara Bhadrapada', lord: 'Saturn', pada: [333.33, 346.67], gana: 'Manushya', yoni: 'Cow', varna: 'Kshatriya' },
      { name: 'Revati', lord: 'Mercury', pada: [346.67, 360], gana: 'Deva', yoni: 'Elephant', varna: 'Shudra' }
    ];

    // 12 Rashis (Zodiac Signs)
    this.RASHIS = [
      { name: 'Aries', lord: 'Mars', element: 'Fire', quality: 'Cardinal' },
      { name: 'Taurus', lord: 'Venus', element: 'Earth', quality: 'Fixed' },
      { name: 'Gemini', lord: 'Mercury', element: 'Air', quality: 'Mutable' },
      { name: 'Cancer', lord: 'Moon', element: 'Water', quality: 'Cardinal' },
      { name: 'Leo', lord: 'Sun', element: 'Fire', quality: 'Fixed' },
      { name: 'Virgo', lord: 'Mercury', element: 'Earth', quality: 'Mutable' },
      { name: 'Libra', lord: 'Venus', element: 'Air', quality: 'Cardinal' },
      { name: 'Scorpio', lord: 'Mars', element: 'Water', quality: 'Fixed' },
      { name: 'Sagittarius', lord: 'Jupiter', element: 'Fire', quality: 'Mutable' },
      { name: 'Capricorn', lord: 'Saturn', element: 'Earth', quality: 'Cardinal' },
      { name: 'Aquarius', lord: 'Saturn', element: 'Air', quality: 'Fixed' },
      { name: 'Pisces', lord: 'Jupiter', element: 'Water', quality: 'Mutable' }
    ];

    // Planetary friendship matrix
    this.PLANETARY_FRIENDSHIP = {
      'Sun': { friends: ['Moon', 'Mars', 'Jupiter'], neutral: ['Mercury'], enemies: ['Venus', 'Saturn', 'Rahu', 'Ketu'] },
      'Moon': { friends: ['Sun', 'Mercury'], neutral: ['Mars', 'Jupiter', 'Venus', 'Saturn'], enemies: ['Rahu', 'Ketu'] },
      'Mars': { friends: ['Sun', 'Moon', 'Jupiter'], neutral: ['Venus', 'Saturn'], enemies: ['Mercury', 'Rahu', 'Ketu'] },
      'Mercury': { friends: ['Sun', 'Venus'], neutral: ['Moon', 'Jupiter', 'Saturn'], enemies: ['Mars', 'Rahu', 'Ketu'] },
      'Jupiter': { friends: ['Sun', 'Moon', 'Mars'], neutral: ['Saturn'], enemies: ['Mercury', 'Venus', 'Rahu', 'Ketu'] },
      'Venus': { friends: ['Mercury', 'Saturn', 'Rahu', 'Ketu'], neutral: ['Mars', 'Jupiter'], enemies: ['Sun', 'Moon'] },
      'Saturn': { friends: ['Mercury', 'Venus', 'Rahu', 'Ketu'], neutral: ['Jupiter'], enemies: ['Sun', 'Moon', 'Mars'] },
      'Rahu': { friends: ['Venus', 'Saturn'], neutral: ['Mercury', 'Jupiter'], enemies: ['Sun', 'Moon', 'Mars'] },
      'Ketu': { friends: ['Venus', 'Saturn'], neutral: ['Mercury', 'Jupiter'], enemies: ['Sun', 'Moon', 'Mars'] }
    };

    // Yoni compatibility matrix
    this.YONI_COMPATIBILITY = {
      'Horse': { enemy: ['Buffalo'], neutral: ['Elephant', 'Sheep', 'Serpent', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Mongoose', 'Lion'], friend: ['Horse'] },
      'Elephant': { enemy: ['Lion'], neutral: ['Horse', 'Sheep', 'Serpent', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Mongoose', 'Buffalo'], friend: ['Elephant'] },
      'Sheep': { enemy: ['Monkey'], neutral: ['Horse', 'Elephant', 'Serpent', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Sheep'] },
      'Serpent': { enemy: ['Mongoose'], neutral: ['Horse', 'Elephant', 'Sheep', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Lion', 'Buffalo'], friend: ['Serpent'] },
      'Dog': { enemy: ['Sheep'], neutral: ['Horse', 'Elephant', 'Serpent', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Dog'] },
      'Cat': { enemy: ['Rat'], neutral: ['Horse', 'Elephant', 'Sheep', 'Serpent', 'Dog', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Cat'] },
      'Rat': { enemy: ['Cat'], neutral: ['Horse', 'Elephant', 'Sheep', 'Serpent', 'Dog', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Rat'] },
      'Cow': { enemy: ['Tiger'], neutral: ['Horse', 'Elephant', 'Sheep', 'Serpent', 'Dog', 'Cat', 'Rat', 'Deer', 'Monkey', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Cow'] },
      'Buffalo': { enemy: ['Horse'], neutral: ['Elephant', 'Sheep', 'Serpent', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Mongoose', 'Lion'], friend: ['Buffalo'] },
      'Tiger': { enemy: ['Cow'], neutral: ['Horse', 'Elephant', 'Sheep', 'Serpent', 'Dog', 'Cat', 'Rat', 'Deer', 'Monkey', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Tiger'] },
      'Deer': { enemy: ['Dog'], neutral: ['Horse', 'Elephant', 'Sheep', 'Serpent', 'Cat', 'Rat', 'Cow', 'Tiger', 'Monkey', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Deer'] },
      'Monkey': { enemy: ['Sheep'], neutral: ['Horse', 'Elephant', 'Serpent', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Mongoose', 'Lion', 'Buffalo'], friend: ['Monkey'] },
      'Mongoose': { enemy: ['Serpent'], neutral: ['Horse', 'Elephant', 'Sheep', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Lion', 'Buffalo'], friend: ['Mongoose'] },
      'Lion': { enemy: ['Elephant'], neutral: ['Horse', 'Sheep', 'Serpent', 'Dog', 'Cat', 'Rat', 'Cow', 'Tiger', 'Deer', 'Monkey', 'Mongoose', 'Buffalo'], friend: ['Lion'] }
    };
  }

  /**
   * Calculate Moon's longitude based on birth date and time
   * Simplified calculation for demonstration - in production use Swiss Ephemeris
   */
  calculateMoonLongitude(birthDate, birthTime, birthPlace) {
    const date = new Date(birthDate);
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    // Parse birth time
    const [hours, minutes] = birthTime.split(':').map(Number);
    const timeInHours = hours + minutes / 60;
    
    // Julian Day calculation (simplified)
    const a = Math.floor((14 - month) / 12);
    const y = year - a;
    const m = month + 12 * a - 3;
    const jd = day + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) + 1721119.5;
    
    // Add time component
    const julianDay = jd + (timeInHours - 12) / 24;
    
    // Simplified moon longitude calculation
    // In production, use accurate ephemeris data
    const T = (julianDay - 2451545.0) / 36525;
    const L0 = 218.3164477 + 481267.88123421 * T - 0.0015786 * T * T + T * T * T / 538841 - T * T * T * T / 65194000;
    
    // Normalize to 0-360 degrees
    let moonLongitude = L0 % 360;
    if (moonLongitude < 0) moonLongitude += 360;
    
    return moonLongitude;
  }

  /**
   * Get Nakshatra from Moon's longitude
   */
  getNakshatraFromLongitude(longitude) {
    const nakshatraIndex = Math.floor(longitude / 13.333333);
    const nakshatra = this.NAKSHATRAS[nakshatraIndex];
    const pada = Math.floor(((longitude % 13.333333) / 13.333333) * 4) + 1;
    
    return {
      ...nakshatra,
      index: nakshatraIndex,
      pada: pada,
      degree: longitude % 13.333333
    };
  }

  /**
   * Get Rashi (Moon Sign) from Moon's longitude
   */
  getRashiFromLongitude(longitude) {
    const rashiIndex = Math.floor(longitude / 30);
    const rashi = this.RASHIS[rashiIndex];
    
    return {
      ...rashi,
      index: rashiIndex,
      degree: longitude % 30
    };
  }

  /**
   * Calculate birth chart basics
   */
  calculateBirthChart(birthDate, birthTime, birthPlace) {
    const moonLongitude = this.calculateMoonLongitude(birthDate, birthTime, birthPlace);
    const nakshatra = this.getNakshatraFromLongitude(moonLongitude);
    const rashi = this.getRashiFromLongitude(moonLongitude);
    
    return {
      moonLongitude,
      nakshatra,
      rashi,
      birthDate,
      birthTime,
      birthPlace
    };
  }

  /**
   * Calculate Tara (Birth Star) compatibility
   * Based on count from bride's nakshatra to groom's nakshatra
   */
  calculateTaraScore(chart1, chart2) {
    const count = ((chart2.nakshatra.index - chart1.nakshatra.index + 27) % 27) + 1;

    // Tara classification
    const taraGroups = {
      janma: [1, 10, 19],      // Birth star - 0 points
      sampat: [2, 11, 20],     // Wealth - 3 points
      vipat: [3, 12, 21],      // Danger - 0 points
      kshema: [4, 13, 22],     // Welfare - 3 points
      pratyak: [5, 14, 23],    // Obstacle - 0 points
      sadhana: [6, 15, 24],    // Achievement - 3 points
      vadha: [7, 16, 25],      // Destruction - 0 points
      mitra: [8, 17, 26],      // Friend - 3 points
      ati_mitra: [9, 18, 27]   // Great friend - 3 points
    };

    for (const [group, positions] of Object.entries(taraGroups)) {
      if (positions.includes(count)) {
        return group.includes('sampat') || group.includes('kshema') ||
               group.includes('sadhana') || group.includes('mitra') ||
               group.includes('ati_mitra') ? 3 : 0;
      }
    }

    return 0;
  }

  /**
   * Calculate Varna (Caste) compatibility
   */
  calculateVarnaScore(chart1, chart2) {
    const varnaOrder = ['Brahmin', 'Kshatriya', 'Vaishya', 'Shudra'];
    const varna1Index = varnaOrder.indexOf(chart1.nakshatra.varna);
    const varna2Index = varnaOrder.indexOf(chart2.nakshatra.varna);

    // Bride's varna should be equal or lower than groom's
    if (varna1Index <= varna2Index) {
      return 1;
    }
    return 0;
  }

  /**
   * Calculate Vashya (Dominance) compatibility
   */
  calculateVashyaScore(chart1, chart2) {
    const vashyaGroups = {
      'Chatushpada': ['Aries', 'Taurus', 'Leo', 'Sagittarius', 'Capricorn'],
      'Jalchar': ['Cancer', 'Scorpio', 'Pisces'],
      'Dwipada': ['Gemini', 'Virgo', 'Libra', 'Aquarius']
    };

    let group1, group2;
    for (const [group, signs] of Object.entries(vashyaGroups)) {
      if (signs.includes(chart1.rashi.name)) group1 = group;
      if (signs.includes(chart2.rashi.name)) group2 = group;
    }

    // Same group gets full points
    if (group1 === group2) return 2;

    // Compatible groups
    const compatibility = {
      'Chatushpada': ['Jalchar'],
      'Jalchar': ['Chatushpada'],
      'Dwipada': ['Chatushpada']
    };

    if (compatibility[group1] && compatibility[group1].includes(group2)) {
      return 1;
    }

    return 0;
  }

  /**
   * Calculate Yoni (Sexual) compatibility
   */
  calculateYoniScore(chart1, chart2) {
    const yoni1 = chart1.nakshatra.yoni;
    const yoni2 = chart2.nakshatra.yoni;

    if (yoni1 === yoni2) return 4; // Same yoni - perfect match

    const compatibility = this.YONI_COMPATIBILITY[yoni1];
    if (compatibility.friend.includes(yoni2)) return 4;
    if (compatibility.neutral.includes(yoni2)) return 2;
    if (compatibility.enemy.includes(yoni2)) return 0;

    return 2; // Default neutral
  }

  /**
   * Calculate Gana (Temperament) compatibility
   */
  calculateGanaScore(chart1, chart2) {
    const gana1 = chart1.nakshatra.gana;
    const gana2 = chart2.nakshatra.gana;

    if (gana1 === gana2) return 6; // Same gana

    const compatibility = {
      'Deva': { 'Manushya': 5, 'Rakshasa': 0 },
      'Manushya': { 'Deva': 1, 'Rakshasa': 0 },
      'Rakshasa': { 'Deva': 0, 'Manushya': 0 }
    };

    return compatibility[gana1]?.[gana2] || 0;
  }

  /**
   * Calculate Graha Maitri (Planetary Friendship) compatibility
   */
  calculateGrahaMaitriScore(chart1, chart2) {
    const lord1 = chart1.nakshatra.lord;
    const lord2 = chart2.nakshatra.lord;

    if (lord1 === lord2) return 5; // Same lord

    const friendship = this.PLANETARY_FRIENDSHIP[lord1];
    if (friendship.friends.includes(lord2)) return 5;
    if (friendship.neutral.includes(lord2)) return 3;
    if (friendship.enemies.includes(lord2)) return 0;

    return 3; // Default neutral
  }

  /**
   * Calculate Bhakoot (Love and Affection) compatibility
   */
  calculateBhakootScore(chart1, chart2) {
    const rashi1Index = chart1.rashi.index;
    const rashi2Index = chart2.rashi.index;

    const diff = Math.abs(rashi1Index - rashi2Index);

    // Incompatible positions (6-8, 2-12 relationships)
    if (diff === 5 || diff === 7 || diff === 1 || diff === 11) {
      return 0;
    }

    return 7;
  }

  /**
   * Calculate Nadi (Health and Progeny) compatibility
   */
  calculateNadiScore(chart1, chart2) {
    const nadiGroups = {
      'Aadi': [0, 3, 6, 9, 12, 15, 18, 21, 24], // Ashwini, Rohini, Punarvasu, etc.
      'Madhya': [1, 4, 7, 10, 13, 16, 19, 22, 25], // Bharani, Mrigashira, Pushya, etc.
      'Antya': [2, 5, 8, 11, 14, 17, 20, 23, 26] // Krittika, Ardra, Ashlesha, etc.
    };

    let nadi1, nadi2;
    for (const [nadi, indices] of Object.entries(nadiGroups)) {
      if (indices.includes(chart1.nakshatra.index)) nadi1 = nadi;
      if (indices.includes(chart2.nakshatra.index)) nadi2 = nadi;
    }

    // Same nadi is incompatible (except for same nakshatra)
    if (nadi1 === nadi2 && chart1.nakshatra.index !== chart2.nakshatra.index) {
      return 0;
    }

    return 8;
  }

  /**
   * Calculate complete Ashtakoot Guna matching
   */
  calculateAshtakootGuna(chart1, chart2) {
    const scores = {
      varna: this.calculateVarnaScore(chart1, chart2),
      vashya: this.calculateVashyaScore(chart1, chart2),
      tara: this.calculateTaraScore(chart1, chart2),
      yoni: this.calculateYoniScore(chart1, chart2),
      graha_maitri: this.calculateGrahaMaitriScore(chart1, chart2),
      gana: this.calculateGanaScore(chart1, chart2),
      bhakoot: this.calculateBhakootScore(chart1, chart2),
      nadi: this.calculateNadiScore(chart1, chart2)
    };

    const totalScore = Object.values(scores).reduce((sum, score) => sum + score, 0);

    return {
      scores,
      totalScore,
      maxScore: 36,
      percentage: Math.round((totalScore / 36) * 100)
    };
  }

  /**
   * Get compatibility level based on total score
   */
  getCompatibilityLevel(totalScore) {
    if (totalScore >= 28) return 'Excellent';
    if (totalScore >= 24) return 'Very Good';
    if (totalScore >= 18) return 'Good';
    if (totalScore >= 12) return 'Average';
    return 'Poor';
  }

  /**
   * Generate detailed compatibility analysis
   */
  generateCompatibilityAnalysis(scores, totalScore) {
    const analysis = [];

    // Analyze each Guna
    if (scores.varna === 1) {
      analysis.push("✅ Varna: Excellent caste compatibility ensuring social harmony");
    } else {
      analysis.push("⚠️ Varna: Caste compatibility needs consideration");
    }

    if (scores.vashya >= 1) {
      analysis.push("✅ Vashya: Good dominance compatibility for mutual respect");
    } else {
      analysis.push("❌ Vashya: Dominance issues may arise in relationship");
    }

    if (scores.tara === 3) {
      analysis.push("✅ Tara: Excellent birth star compatibility for prosperity");
    } else {
      analysis.push("⚠️ Tara: Birth star compatibility requires attention");
    }

    if (scores.yoni >= 3) {
      analysis.push("✅ Yoni: Good sexual and physical compatibility");
    } else {
      analysis.push("❌ Yoni: Physical compatibility challenges expected");
    }

    if (scores.graha_maitri >= 4) {
      analysis.push("✅ Graha Maitri: Strong planetary friendship for mental harmony");
    } else {
      analysis.push("⚠️ Graha Maitri: Mental compatibility needs work");
    }

    if (scores.gana >= 5) {
      analysis.push("✅ Gana: Excellent temperament compatibility");
    } else {
      analysis.push("❌ Gana: Temperament differences may cause conflicts");
    }

    if (scores.bhakoot === 7) {
      analysis.push("✅ Bhakoot: Perfect love and affection compatibility");
    } else {
      analysis.push("❌ Bhakoot: Love and affection challenges - requires remedies");
    }

    if (scores.nadi === 8) {
      analysis.push("✅ Nadi: Excellent health and progeny compatibility");
    } else {
      analysis.push("❌ Nadi: Health and progeny issues - requires immediate remedies");
    }

    return analysis;
  }
}

module.exports = VedicAstrologyService;
