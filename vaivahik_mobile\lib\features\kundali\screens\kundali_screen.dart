import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:share_plus/share_plus.dart';
import '../../../app/theme.dart';
import '../../../core/widgets/enhanced_ui_components.dart';
import '../../../core/api/api_client.dart';
import '../models/kundali_models.dart';
import '../services/kundali_service.dart';
import '../../../core/widgets/feature_access_widget.dart';
import '../../../core/services/feature_access_service.dart';

/// 🔮 COMPREHENSIVE KUNDALI SYSTEM - World-Class Astrology Features
/// Features: Birth Chart Generation, Planetary Positions, Doshas, Premium Reports
/// Reuses existing website logic and API for complete feature parity

final kundaliProvider = FutureProvider<KundaliData?>((ref) async {
  final service = KundaliService(ApiClient());
  return await service.getMyKundali();
});

final kundaliGenerationProvider = StateNotifierProvider<KundaliGenerationNotifier, KundaliGenerationState>((ref) {
  return KundaliGenerationNotifier();
});

class KundaliGenerationNotifier extends StateNotifier<KundaliGenerationState> {
  KundaliGenerationNotifier() : super(const KundaliGenerationState());

  Future<void> generateKundali({
    required String name,
    required DateTime birthDate,
    required String birthTime,
    required String birthPlace,
    required double latitude,
    required double longitude,
    String? timezone,
  }) async {
    state = state.copyWith(isGenerating: true, error: null);

    try {
      final service = KundaliService(ApiClient());
      final kundali = await service.generateKundali(
        name: name,
        birthDate: birthDate,
        birthTime: birthTime,
        birthPlace: birthPlace,
        latitude: latitude,
        longitude: longitude,
        timezone: timezone ?? 'Asia/Kolkata',
      );

      state = state.copyWith(
        isGenerating: false,
        kundali: kundali,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

class KundaliGenerationState {
  final bool isGenerating;
  final KundaliData? kundali;
  final String? error;

  const KundaliGenerationState({
    this.isGenerating = false,
    this.kundali,
    this.error,
  });

  KundaliGenerationState copyWith({
    bool? isGenerating,
    KundaliData? kundali,
    String? error,
  }) {
    return KundaliGenerationState(
      isGenerating: isGenerating ?? this.isGenerating,
      kundali: kundali ?? this.kundali,
      error: error,
    );
  }
}

class KundaliScreen extends ConsumerStatefulWidget {
  const KundaliScreen({super.key});

  @override
  ConsumerState<KundaliScreen> createState() => _KundaliScreenState();
}

class _KundaliScreenState extends ConsumerState<KundaliScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final kundaliAsync = ref.watch(kundaliProvider);
    final generationState = ref.watch(kundaliGenerationProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildKundaliTabs(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBirthChartTab(kundaliAsync),
                _buildPlanetaryPositionsTab(kundaliAsync),
                _buildDoshasTab(kundaliAsync),
                _buildReportsTab(kundaliAsync),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(kundaliAsync, generationState),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text(
        'Kundali System',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      actions: [
        FeatureAccessWidget(
          feature: FeatureType.kundaliMatching,
          child: IconButton(
            onPressed: () => _shareKundali(),
            icon: const Icon(Icons.share),
            tooltip: 'Share Kundali',
          ),
        ),
        IconButton(
          onPressed: () => _showKundaliSettings(),
          icon: const Icon(Icons.settings),
          tooltip: 'Kundali Settings',
        ),
      ],
    );
  }

  Widget _buildKundaliTabs() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppShadows.cardShadow,
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: AppColors.primary,
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
        isScrollable: true,
        tabs: const [
          Tab(
            icon: Icon(Icons.auto_awesome, size: 20),
            text: 'Birth Chart',
          ),
          Tab(
            icon: Icon(Icons.public, size: 20),
            text: 'Planets',
          ),
          Tab(
            icon: Icon(Icons.warning, size: 20),
            text: 'Doshas',
          ),
          Tab(
            icon: Icon(Icons.description, size: 20),
            text: 'Reports',
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.2);
  }

  Widget _buildBirthChartTab(AsyncValue<KundaliData?> kundaliAsync) {
    return kundaliAsync.when(
      data: (kundali) => kundali != null
          ? _buildBirthChartContent(kundali)
          : _buildGenerateKundaliPrompt(),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildBirthChartContent(KundaliData kundali) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicInfoCard(kundali),
          const SizedBox(height: 16),
          _buildBirthChartVisualization(kundali),
          const SizedBox(height: 16),
          _buildAscendantCard(kundali),
          const SizedBox(height: 16),
          _buildNakshatraCard(kundali),
        ],
      ),
    );
  }

  Widget _buildBasicInfoCard(KundaliData kundali) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.person, color: AppColors.primary),
              const SizedBox(width: 8),
              const Text(
                'Birth Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (kundali.isPremium == true)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Premium',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDetailRow('Date of Birth', _formatDate(kundali.birthDate)),
          _buildDetailRow('Time of Birth', kundali.birthTime),
          _buildDetailRow('Place of Birth', kundali.birthPlace),
          if (kundali.nakshatra != null)
            _buildDetailRow('Nakshatra', kundali.nakshatra!),
          if (kundali.rashi != null)
            _buildDetailRow('Rashi', kundali.rashi!),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.2);
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Text(': '),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBirthChartVisualization(KundaliData kundali) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.auto_awesome, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Birth Chart',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            height: 300,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.auto_awesome,
                    size: 64,
                    color: AppColors.primary,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Birth Chart Visualization',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Interactive chart coming soon',
                    style: TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms).scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildAscendantCard(KundaliData kundali) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.trending_up, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Ascendant & Moon Sign',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (kundali.ascendant != null)
            _buildDetailRow('Ascendant', kundali.ascendant!),
          if (kundali.rashi != null)
            _buildDetailRow('Moon Sign', kundali.rashi!),
          if (kundali.moonLongitude != null)
            _buildDetailRow('Moon Longitude', '${kundali.moonLongitude!.toStringAsFixed(2)}°'),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms).slideX(begin: 0.2);
  }

  Widget _buildNakshatraCard(KundaliData kundali) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.star, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Nakshatra Details',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (kundali.nakshatra != null)
            _buildDetailRow('Nakshatra', kundali.nakshatra!),
          if (kundali.rashi != null)
            _buildDetailRow('Rashi', kundali.rashi!),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Text(
              'Nakshatra characteristics and predictions will be displayed here based on your birth details.',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 1200.ms).slideY(begin: 0.2);
  }

  Widget _buildPlanetaryPositionsTab(AsyncValue<KundaliData?> kundaliAsync) {
    return kundaliAsync.when(
      data: (kundali) => kundali != null
          ? _buildPlanetaryPositionsContent(kundali)
          : _buildGenerateKundaliPrompt(),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildPlanetaryPositionsContent(KundaliData kundali) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          EnhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.public, color: AppColors.primary),
                    SizedBox(width: 8),
                    Text(
                      'Planetary Positions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (kundali.planetaryPositions.isNotEmpty)
                  ...kundali.planetaryPositions.entries.map((entry) =>
                    _buildPlanetRow(entry.key, entry.value.toString())
                  )
                else
                  const Text(
                    'Planetary positions will be calculated and displayed here.',
                    style: TextStyle(color: Colors.grey),
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          EnhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.home, color: AppColors.primary),
                    SizedBox(width: 8),
                    Text(
                      'Houses',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (kundali.houses.isNotEmpty)
                  ...kundali.houses.entries.map((entry) =>
                    _buildHouseRow(entry.key, entry.value.toString())
                  )
                else
                  const Text(
                    'House positions will be calculated and displayed here.',
                    style: TextStyle(color: Colors.grey),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanetRow(String planet, String position) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppColors.primary.withAlpha(26),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                planet.substring(0, 2).toUpperCase(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  planet,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  position,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHouseRow(String house, String details) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.blue.withAlpha(26),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Text(
                house.replaceAll('house', '').trim(),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  house,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  details,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDoshasTab(AsyncValue<KundaliData?> kundaliAsync) {
    return kundaliAsync.when(
      data: (kundali) => kundali != null
          ? _buildDoshasContent(kundali)
          : _buildGenerateKundaliPrompt(),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildDoshasContent(KundaliData kundali) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMangalDoshaCard(kundali),
          const SizedBox(height: 16),
          _buildKalSarpaDoshaCard(kundali),
          const SizedBox(height: 16),
          _buildPitruDoshaCard(kundali),
          const SizedBox(height: 16),
          _buildOtherDoshasCard(kundali),
        ],
      ),
    );
  }

  Widget _buildMangalDoshaCard(KundaliData kundali) {
    final hasMangalDosha = kundali.mangalDosha != null && kundali.mangalDosha!.toLowerCase() != 'none';

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                hasMangalDosha ? Icons.warning : Icons.check_circle,
                color: hasMangalDosha ? Colors.orange : Colors.green,
              ),
              const SizedBox(width: 8),
              const Text(
                'Mangal Dosha',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: hasMangalDosha ? Colors.orange.withAlpha(26) : Colors.green.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  hasMangalDosha ? 'Present' : 'Not Present',
                  style: TextStyle(
                    color: hasMangalDosha ? Colors.orange : Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            kundali.mangalDosha ?? 'No Mangal Dosha detected',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
          if (hasMangalDosha) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'Remedies and detailed analysis available in premium reports.',
                style: TextStyle(
                  color: Colors.orange,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.2);
  }

  Widget _buildKalSarpaDoshaCard(KundaliData kundali) {
    final hasKalSarpaDosha = kundali.kalSarpaDosha != null && kundali.kalSarpaDosha!.toLowerCase() != 'none';

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                hasKalSarpaDosha ? Icons.warning : Icons.check_circle,
                color: hasKalSarpaDosha ? Colors.red : Colors.green,
              ),
              const SizedBox(width: 8),
              const Text(
                'Kal Sarpa Dosha',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: hasKalSarpaDosha ? Colors.red.withAlpha(26) : Colors.green.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  hasKalSarpaDosha ? 'Present' : 'Not Present',
                  style: TextStyle(
                    color: hasKalSarpaDosha ? Colors.red : Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            kundali.kalSarpaDosha ?? 'No Kal Sarpa Dosha detected',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms).slideX(begin: 0.2);
  }

  Widget _buildPitruDoshaCard(KundaliData kundali) {
    final hasPitruDosha = kundali.pitruDosha != null && kundali.pitruDosha!.toLowerCase() != 'none';

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                hasPitruDosha ? Icons.warning : Icons.check_circle,
                color: hasPitruDosha ? Colors.purple : Colors.green,
              ),
              const SizedBox(width: 8),
              const Text(
                'Pitru Dosha',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: hasPitruDosha ? Colors.purple.withAlpha(26) : Colors.green.withAlpha(26),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  hasPitruDosha ? 'Present' : 'Not Present',
                  style: TextStyle(
                    color: hasPitruDosha ? Colors.purple : Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            kundali.pitruDosha ?? 'No Pitru Dosha detected',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms).slideY(begin: 0.2);
  }

  Widget _buildOtherDoshasCard(KundaliData kundali) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.list, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Other Doshas',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (kundali.doshas.isNotEmpty)
            ...kundali.doshas.map((dosha) => Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: AppColors.primary, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      dosha,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ))
          else
            const Text(
              'No other doshas detected in your birth chart.',
              style: TextStyle(color: Colors.grey),
            ),
        ],
      ),
    ).animate().fadeIn(duration: 1200.ms).scale(begin: const Offset(0.9, 0.9));
  }

  Widget _buildReportsTab(AsyncValue<KundaliData?> kundaliAsync) {
    return kundaliAsync.when(
      data: (kundali) => kundali != null
          ? _buildReportsContent(kundali)
          : _buildGenerateKundaliPrompt(),
      loading: () => _buildLoadingWidget(),
      error: (error, stack) => _buildErrorWidget(error.toString()),
    );
  }

  Widget _buildReportsContent(KundaliData kundali) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBasicReportCard(),
          const SizedBox(height: 16),
          _buildPremiumReportsCard(),
          const SizedBox(height: 16),
          _buildCompatibilityReportCard(),
        ],
      ),
    );
  }

  Widget _buildBasicReportCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.description, color: AppColors.primary),
              SizedBox(width: 8),
              Text(
                'Basic Kundali Report',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Get a comprehensive basic report of your birth chart including planetary positions, houses, and basic predictions.',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: EnhancedButton(
                  text: 'Generate Report',
                  onPressed: () => _generateBasicReport(),
                  type: ButtonType.primary,
                ),
              ),
              const SizedBox(width: 12),
              EnhancedButton(
                text: 'Share',
                onPressed: () => _shareKundali(),
                type: ButtonType.secondary,
                icon: const Icon(Icons.share, size: 16),
              ),
            ],
          ),
        ],
      ),
    ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.2);
  }

  Widget _buildPremiumReportsCard() {
    return FeatureAccessWidget(
      feature: FeatureType.kundaliMatching,
      child: EnhancedCard(
        isPremium: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber),
                const SizedBox(width: 8),
                const Text(
                  'Premium Astrology Reports',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Premium',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'Unlock detailed astrology reports with remedies, predictions, and personalized guidance from expert astrologers.',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildFeatureChip('Detailed Predictions'),
                _buildFeatureChip('Remedies & Solutions'),
                _buildFeatureChip('Yearly Forecast'),
                _buildFeatureChip('Career Guidance'),
                _buildFeatureChip('Health Analysis'),
                _buildFeatureChip('Relationship Insights'),
              ],
            ),
            const SizedBox(height: 16),
            EnhancedButton(
              text: 'Upgrade to Premium',
              onPressed: () => _showPremiumUpgrade(),
              type: ButtonType.primary,
              isFullWidth: true,
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 800.ms).slideX(begin: 0.2);
  }

  Widget _buildFeatureChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.amber.withAlpha(26),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.amber.withAlpha(51)),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.amber,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildCompatibilityReportCard() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.favorite, color: Colors.red),
              SizedBox(width: 8),
              Text(
                'Compatibility Matching',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Text(
            'Check compatibility with potential partners using comprehensive Kundali matching based on Ashtakoot Guna system.',
            style: TextStyle(color: Colors.grey),
          ),
          const SizedBox(height: 16),
          EnhancedButton(
            text: 'Find Compatible Matches',
            onPressed: () => _navigateToMatching(),
            type: ButtonType.secondary,
            isFullWidth: true,
            icon: const Icon(Icons.search, size: 16),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 1000.ms).slideY(begin: 0.2);
  }

  Widget _buildGenerateKundaliPrompt() {
    return Center(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: AppGradients.primaryGradient,
                borderRadius: BorderRadius.circular(60),
              ),
              child: const Icon(
                Icons.auto_awesome,
                size: 60,
                color: Colors.white,
              ),
            ).animate().scale(duration: 800.ms),
            const SizedBox(height: 24),
            const Text(
              'Generate Your Kundali',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppColors.primary,
              ),
            ).animate().fadeIn(delay: 200.ms),
            const SizedBox(height: 12),
            Text(
              'Create your personalized birth chart with detailed planetary positions, doshas, and astrological insights.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ).animate().fadeIn(delay: 400.ms),
            const SizedBox(height: 32),
            EnhancedButton(
              text: 'Generate Kundali',
              onPressed: () => _showKundaliGenerationDialog(),
              type: ButtonType.primary,
              size: ButtonSize.large,
              icon: const Icon(Icons.auto_awesome, size: 20),
            ).animate().fadeIn(delay: 600.ms).slideY(begin: 0.2),
            const SizedBox(height: 16),
            TextButton(
              onPressed: () => _showKundaliInfo(),
              child: const Text('Learn more about Kundali'),
            ).animate().fadeIn(delay: 800.ms),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text(
            'Loading Kundali...',
            style: TextStyle(
              fontSize: 16,
              color: AppColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading Kundali',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(kundaliProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(AsyncValue<KundaliData?> kundaliAsync, KundaliGenerationState generationState) {
    if (generationState.isGenerating) {
      return const FloatingActionButton(
        onPressed: null,
        backgroundColor: Colors.grey,
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 2,
        ),
      );
    }

    return kundaliAsync.when(
      data: (kundali) => kundali != null
          ? FloatingActionButton.extended(
              onPressed: () => _showQuickActions(),
              backgroundColor: AppColors.primary,
              icon: const Icon(Icons.more_horiz),
              label: const Text('Actions'),
            )
          : FloatingActionButton.extended(
              onPressed: () => _showKundaliGenerationDialog(),
              backgroundColor: AppColors.primary,
              icon: const Icon(Icons.auto_awesome),
              label: const Text('Generate'),
            ),
      loading: () => const FloatingActionButton(
        onPressed: null,
        backgroundColor: Colors.grey,
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 2,
        ),
      ),
      error: (_, __) => FloatingActionButton.extended(
        onPressed: () => _showKundaliGenerationDialog(),
        backgroundColor: AppColors.primary,
        icon: const Icon(Icons.auto_awesome),
        label: const Text('Generate'),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  // Action methods
  void _shareKundali() async {
    try {
      await Share.share(
        'Check out my Kundali from Vaivahik - The Premier Maratha Matrimony Platform',
        subject: 'My Kundali',
      );
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sharing: $e')),
        );
      }
    }
  }

  void _showKundaliSettings() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'Kundali Settings',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.language, color: AppColors.primary),
              title: const Text('Language'),
              subtitle: const Text('Hindi, English, Marathi'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.notifications, color: AppColors.primary),
              title: const Text('Notifications'),
              subtitle: const Text('Kundali updates & reports'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.privacy_tip, color: AppColors.primary),
              title: const Text('Privacy'),
              subtitle: const Text('Who can see your Kundali'),
              onTap: () => Navigator.pop(context),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showQuickActions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(20),
              child: Text(
                'Quick Actions',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.download, color: AppColors.primary),
              title: const Text('Download PDF'),
              onTap: () {
                Navigator.pop(context);
                _generateBasicReport();
              },
            ),
            ListTile(
              leading: const Icon(Icons.share, color: AppColors.primary),
              title: const Text('Share Kundali'),
              onTap: () {
                Navigator.pop(context);
                _shareKundali();
              },
            ),
            ListTile(
              leading: const Icon(Icons.favorite, color: Colors.red),
              title: const Text('Find Matches'),
              onTap: () {
                Navigator.pop(context);
                _navigateToMatching();
              },
            ),
            ListTile(
              leading: const Icon(Icons.star, color: Colors.amber),
              title: const Text('Premium Reports'),
              onTap: () {
                Navigator.pop(context);
                _showPremiumUpgrade();
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showKundaliGenerationDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Generate Kundali'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('To generate your Kundali, we need your complete birth details:'),
            SizedBox(height: 12),
            Text('• Date of Birth'),
            Text('• Time of Birth'),
            Text('• Place of Birth'),
            SizedBox(height: 12),
            Text('Please ensure your profile has these details filled.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to profile edit
            },
            child: const Text('Update Profile'),
          ),
        ],
      ),
    );
  }

  void _showKundaliInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Kundali'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'What is Kundali?',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text(
                'Kundali (also known as birth chart or horoscope) is a detailed astrological chart that maps the positions of celestial bodies at the time of your birth.',
              ),
              SizedBox(height: 16),
              Text(
                'What does it include?',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• Planetary positions'),
              Text('• Houses and their significance'),
              Text('• Doshas (astrological flaws)'),
              Text('• Nakshatra and Rashi'),
              Text('• Compatibility analysis'),
              Text('• Predictions and remedies'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _generateBasicReport() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Generating Kundali report...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _showPremiumUpgrade() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.star, color: Colors.amber),
            SizedBox(width: 8),
            Text('Premium Features'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Unlock premium astrology features:'),
            SizedBox(height: 12),
            Text('✨ Detailed predictions'),
            Text('✨ Remedies and solutions'),
            Text('✨ Yearly forecast'),
            Text('✨ Expert consultations'),
            Text('✨ Priority matching'),
            SizedBox(height: 12),
            Text('Starting from ₹299/month'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Navigate to premium upgrade
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
            ),
            child: const Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  void _navigateToMatching() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Navigating to compatibility matching...'),
        backgroundColor: AppColors.primary,
      ),
    );
  }
}
