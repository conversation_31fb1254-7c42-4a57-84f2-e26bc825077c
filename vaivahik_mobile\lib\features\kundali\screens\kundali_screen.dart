import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class KundaliScreen extends ConsumerWidget {
  const KundaliScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kundali'),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // Share kundali via system share
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Opening share options...')),
              );
              // This would use share_plus package
            },
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.auto_awesome, size: 80, color: Colors.orange),
            SizedBox(height: 16),
            Text(
              'Kundali Screen',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            Sized<PERSON><PERSON>(height: 8),
            Text('Your astrological chart will be displayed here...'),
          ],
        ),
      ),
    );
  }
}
