import '../../../core/api/api_client.dart';
import '../models/match_model.dart';

class MatchingService {
  final ApiClient _apiClient;

  MatchingService(this._apiClient);

  // Get match suggestions based on user preferences
  Future<List<MatchModel>> getMatchSuggestions({
    int page = 1,
    int limit = 10,
    MatchFilters? filters,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page,
        'limit': limit,
        if (filters != null) ...filters.toJson(),
      };

      final response = await _apiClient.get('/matches', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> matchesData = response['matches'] ?? response['data'] ?? [];
        return matchesData.map((match) => MatchModel.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch matches');
      }
    } catch (e) {
      throw Exception('Error fetching matches: $e');
    }
  }

  // Get daily recommendations
  Future<List<MatchModel>> getDailyRecommendations() async {
    try {
      final response = await _apiClient.get('/matches/daily-recommendations');

      if (response['success'] == true) {
        final List<dynamic> matchesData = response['recommendations'] ?? response['data'] ?? [];
        return matchesData.map((match) => MatchModel.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch daily recommendations');
      }
    } catch (e) {
      throw Exception('Error fetching daily recommendations: $e');
    }
  }

  // Get premium matches
  Future<List<MatchModel>> getPremiumMatches({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get('/matches/premium', queryParams: {
        'page': page,
        'limit': limit,
      });

      if (response['success'] == true) {
        final List<dynamic> matchesData = response['matches'] ?? response['data'] ?? [];
        return matchesData.map((match) => MatchModel.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch premium matches');
      }
    } catch (e) {
      throw Exception('Error fetching premium matches: $e');
    }
  }

  // Get recently viewed profiles
  Future<List<MatchModel>> getRecentlyViewed() async {
    try {
      final response = await _apiClient.get('/matches/recently-viewed');

      if (response['success'] == true) {
        final List<dynamic> matchesData = response['profiles'] ?? response['data'] ?? [];
        return matchesData.map((match) => MatchModel.fromJson(match)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch recently viewed');
      }
    } catch (e) {
      throw Exception('Error fetching recently viewed: $e');
    }
  }

  // Browse profiles (TIER 1 feature)
  Future<List<MatchModel>> browseProfiles({
    int page = 1,
    int limit = 10,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page,
        'limit': limit,
        if (filters != null) ...filters,
      };

      final response = await _apiClient.get('/users/browse', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> profilesData = response['profiles'] ?? response['data'] ?? [];
        return profilesData.map((profile) => MatchModel.fromJson(profile)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to browse profiles');
      }
    } catch (e) {
      throw Exception('Error browsing profiles: $e');
    }
  }

  // Search profiles with filters
  Future<List<MatchModel>> searchProfiles({
    int page = 1,
    int limit = 10,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final Map<String, dynamic> queryParams = {
        'page': page,
        'limit': limit,
        if (filters != null) ...filters,
      };

      final response = await _apiClient.get('/users/search', queryParams: queryParams);

      if (response['success'] == true) {
        final List<dynamic> profilesData = response['profiles'] ?? response['data'] ?? [];
        return profilesData.map((profile) => MatchModel.fromJson(profile)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to search profiles');
      }
    } catch (e) {
      throw Exception('Error searching profiles: $e');
    }
  }

  // Get profile by ID
  Future<MatchModel> getProfileById(String profileId) async {
    try {
      final response = await _apiClient.get('/users/view-profile/$profileId');

      if (response['success'] == true) {
        return MatchModel.fromJson(response['profile'] ?? response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch profile');
      }
    } catch (e) {
      throw Exception('Error fetching profile: $e');
    }
  }

  // Send interest to a profile
  Future<bool> sendInterest(String profileId, {String? message}) async {
    try {
      final response = await _apiClient.post('/matches/$profileId/interest', {
        if (message != null) 'message': message,
      });

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to send interest');
      }
    } catch (e) {
      throw Exception('Error sending interest: $e');
    }
  }

  // Accept interest
  Future<bool> acceptInterest(String interestId) async {
    try {
      final response = await _apiClient.post('/interests/$interestId/accept', {});

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to accept interest');
      }
    } catch (e) {
      throw Exception('Error accepting interest: $e');
    }
  }

  // Decline interest
  Future<bool> declineInterest(String interestId) async {
    try {
      final response = await _apiClient.post('/interests/$interestId/decline', {});

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to decline interest');
      }
    } catch (e) {
      throw Exception('Error declining interest: $e');
    }
  }

  // Get received interests
  Future<List<InterestModel>> getReceivedInterests({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get('/interests/received', queryParams: {
        'page': page,
        'limit': limit,
      });

      if (response['success'] == true) {
        final List<dynamic> interestsData = response['interests'] ?? response['data'] ?? [];
        return interestsData.map((interest) => InterestModel.fromJson(interest)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch received interests');
      }
    } catch (e) {
      throw Exception('Error fetching received interests: $e');
    }
  }

  // Get sent interests
  Future<List<InterestModel>> getSentInterests({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get('/interests/sent', queryParams: {
        'page': page,
        'limit': limit,
      });

      if (response['success'] == true) {
        final List<dynamic> interestsData = response['interests'] ?? response['data'] ?? [];
        return interestsData.map((interest) => InterestModel.fromJson(interest)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch sent interests');
      }
    } catch (e) {
      throw Exception('Error fetching sent interests: $e');
    }
  }

  // Add to shortlist
  Future<bool> addToShortlist(String profileId, {String? note}) async {
    try {
      final response = await _apiClient.post('/user/shortlist', {
        'profileId': profileId,
        if (note != null) 'note': note,
      });

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to add to shortlist');
      }
    } catch (e) {
      throw Exception('Error adding to shortlist: $e');
    }
  }

  // Remove from shortlist
  Future<bool> removeFromShortlist(String profileId) async {
    try {
      final response = await _apiClient.delete('/user/shortlist/$profileId');

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to remove from shortlist');
      }
    } catch (e) {
      throw Exception('Error removing from shortlist: $e');
    }
  }

  // Get shortlisted profiles
  Future<List<ShortlistModel>> getShortlistedProfiles({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      final response = await _apiClient.get('/user/shortlist', queryParams: {
        'page': page,
        'limit': limit,
      });

      if (response['success'] == true) {
        final List<dynamic> shortlistData = response['shortlist'] ?? response['data'] ?? [];
        return shortlistData.map((item) => ShortlistModel.fromJson(item)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch shortlisted profiles');
      }
    } catch (e) {
      throw Exception('Error fetching shortlisted profiles: $e');
    }
  }

  // Like a profile
  Future<bool> likeProfile(String profileId) async {
    try {
      final response = await _apiClient.post('/matches/$profileId/like', {});

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to like profile');
      }
    } catch (e) {
      throw Exception('Error liking profile: $e');
    }
  }

  // Pass a profile
  Future<bool> passProfile(String profileId) async {
    try {
      final response = await _apiClient.post('/matches/$profileId/pass', {});

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to pass profile');
      }
    } catch (e) {
      throw Exception('Error passing profile: $e');
    }
  }

  // Block a profile
  Future<bool> blockProfile(String profileId, {String? reason}) async {
    try {
      final response = await _apiClient.post('/matches/$profileId/block', {
        if (reason != null) 'reason': reason,
      });

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to block profile');
      }
    } catch (e) {
      throw Exception('Error blocking profile: $e');
    }
  }

  // Report a profile
  Future<bool> reportProfile(String profileId, String reason, {String? details}) async {
    try {
      final response = await _apiClient.post('/matches/$profileId/report', {
        'reason': reason,
        if (details != null) 'details': details,
      });

      if (response['success'] == true) {
        return true;
      } else {
        throw Exception(response['message'] ?? 'Failed to report profile');
      }
    } catch (e) {
      throw Exception('Error reporting profile: $e');
    }
  }

  // Get match statistics
  Future<Map<String, dynamic>> getMatchStatistics() async {
    try {
      final response = await _apiClient.get('/matches/statistics');

      if (response['success'] == true) {
        return response['statistics'] ?? response['data'] ?? {};
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch statistics');
      }
    } catch (e) {
      throw Exception('Error fetching statistics: $e');
    }
  }
}
