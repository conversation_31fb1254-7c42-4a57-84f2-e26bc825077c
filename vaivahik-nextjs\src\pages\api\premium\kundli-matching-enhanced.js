import { PrismaClient } from '@prisma/client';
import jwt from 'jsonwebtoken';

const prisma = new PrismaClient();

// Import the comprehensive kundali service
const ComprehensiveKundaliService = require('../../../../../../../vaivahik-backend/src/services/comprehensiveKundali.service');
const kundaliService = new ComprehensiveKundaliService();

export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method not allowed' });
  }

  try {
    // Check if kundali matching is enabled via admin panel
    const isEnabled = await kundaliService.isKundaliMatchingEnabled();
    if (!isEnabled) {
      return res.status(403).json({
        success: false,
        message: 'Kundali matching feature is currently disabled'
      });
    }

    // Check if it's free promotion period
    const isFreePromotion = await kundaliService.isKundaliMatchingFree();

    // Verify authentication
    const token = req.headers.authorization?.replace('Bearer ', '');
    if (!token) {
      return res.status(401).json({ success: false, message: 'Authentication required' });
    }

    let decoded;
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET);
    } catch (error) {
      return res.status(401).json({ success: false, message: 'Invalid token' });
    }

    // Check if user has premium access (unless free promotion)
    const currentUser = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: { profile: true, subscription: true }
    });

    if (!currentUser) {
      return res.status(404).json({ success: false, message: 'User not found' });
    }

    // Check premium access (unless it's a free promotion)
    const isPremiumRequired = !isFreePromotion && 
      (!currentUser.subscription || currentUser.subscription.status !== 'ACTIVE');

    if (isPremiumRequired) {
      return res.status(403).json({
        success: false,
        message: 'Premium subscription required for kundli matching',
        upgradeRequired: true,
        promotionAvailable: isFreePromotion
      });
    }

    const { user1, user2, options = {} } = req.body;

    // Validate required data
    if (!user1 || !user2) {
      return res.status(400).json({ 
        success: false, 
        message: 'Both user profiles are required' 
      });
    }

    // Validate birth data using comprehensive service
    const user1Validation = kundaliService.validateBirthData(user1);
    const user2Validation = kundaliService.validateBirthData(user2);

    if (!user1Validation.isValid || !user2Validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'Invalid birth data',
        errors: {
          user1: user1Validation.errors,
          user2: user2Validation.errors
        }
      });
    }

    // Prepare comprehensive user data
    const user1Data = {
      id: currentUser.id,
      name: currentUser.profile?.firstName + ' ' + (currentUser.profile?.lastName || ''),
      birthDate: user1.birthDate,
      birthTime: user1.birthTime,
      birthPlace: user1.birthPlace,
      timezone: user1.timezone || 'IST'
    };

    const user2Data = {
      id: user2.id,
      name: user2.name || 'User 2',
      birthDate: user2.birthDate,
      birthTime: user2.birthTime,
      birthPlace: user2.birthPlace,
      timezone: user2.timezone || 'IST'
    };

    // Enhanced options
    const enhancedOptions = {
      includeCharts: options.includeCharts || false,
      includeRemedies: options.includeRemedies || true,
      includeAuspiciousDates: options.includeAuspiciousDates || false,
      includeMLScore: options.includeMLScore || true,
      includeDetailedAnalysis: options.includeDetailedAnalysis || true
    };

    // Generate comprehensive kundali match using new service
    const kundliMatch = await kundaliService.generateCompleteKundaliMatch(
      user1Data, 
      user2Data, 
      enhancedOptions
    );

    // Log the kundli matching activity
    await prisma.userActivity.create({
      data: {
        userId: decoded.userId,
        activityType: 'KUNDLI_MATCH',
        details: JSON.stringify({
          targetUserId: user2Data.id,
          score: kundliMatch.overallCompatibility.score,
          compatibility: kundliMatch.overallCompatibility.level
        })
      }
    });

    res.status(200).json({
      success: true,
      data: kundliMatch,
      message: 'Kundali matching completed successfully',
      isFreePromotion,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in comprehensive kundli matching:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
}
