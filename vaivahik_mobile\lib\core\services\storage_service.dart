import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService {
  static late SharedPreferences _prefs;
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Secure Storage Methods (for sensitive data like tokens)
  static Future<void> setSecureString(String key, String value) async {
    await _secureStorage.write(key: key, value: value);
  }

  static Future<String?> getSecureString(String key) async {
    return await _secureStorage.read(key: key);
  }

  static Future<void> deleteSecureString(String key) async {
    await _secureStorage.delete(key: key);
  }

  static Future<void> clearSecureStorage() async {
    await _secureStorage.deleteAll();
  }

  // Regular Storage Methods (for non-sensitive data)
  static Future<void> setString(String key, String value) async {
    await _prefs.setString(key, value);
  }

  static String? getString(String key) {
    return _prefs.getString(key);
  }

  static Future<void> setInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  static int? getInt(String key) {
    return _prefs.getInt(key);
  }

  static Future<void> setBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  static bool? getBool(String key) {
    return _prefs.getBool(key);
  }

  static Future<void> setDouble(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  static double? getDouble(String key) {
    return _prefs.getDouble(key);
  }

  static Future<void> setStringList(String key, List<String> value) async {
    await _prefs.setStringList(key, value);
  }

  static List<String>? getStringList(String key) {
    return _prefs.getStringList(key);
  }

  static Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  static Future<void> clear() async {
    await _prefs.clear();
  }

  static bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  // Storage Keys Constants
  static const String keyAuthToken = 'auth_token';
  static const String keyRefreshToken = 'refresh_token';
  static const String keyUserId = 'user_id';
  static const String keyUserEmail = 'user_email';
  static const String keyUserPhone = 'user_phone';
  static const String keyUserData = 'user_data';
  static const String keyIsLoggedIn = 'is_logged_in';
  static const String keyOnboardingCompleted = 'onboarding_completed';
  static const String keyThemeMode = 'theme_mode';
  static const String keyLanguage = 'language';
  static const String keyNotificationsEnabled = 'notifications_enabled';
  static const String keyLocationPermission = 'location_permission';
  static const String keyBiometricEnabled = 'biometric_enabled';
  static const String keyLastSyncTime = 'last_sync_time';
  static const String keyOfflineData = 'offline_data';
  static const String keySearchHistory = 'search_history';
  static const String keyRecentMatches = 'recent_matches';
  static const String keyFavoriteProfiles = 'favorite_profiles';
  static const String keyPrivacySettings = 'privacy_settings';
  static const String keyNotificationSettings = 'notification_settings';
  static const String keyAppSettings = 'app_settings';
  static const String keyUserPreferences = 'user_preferences';
  static const String keyAnalyticsData = 'analytics_data';
  static const String keyCacheData = 'cache_data';
  static const String keyFcmToken = 'fcm_token';
  static const String keyDeviceId = 'device_id';
  static const String keyAppVersion = 'app_version';
  static const String keyLastUpdateCheck = 'last_update_check';
}

class NotificationService {
  static Future<void> init() async {
    try {
      // Initialize local notifications
      await _initializeLocalNotifications();

      // Request notification permissions
      await requestPermissions();

      print('NotificationService initialized successfully');
    } catch (e) {
      print('Error initializing NotificationService: $e');
    }
  }

  static Future<void> _initializeLocalNotifications() async {
    // Initialize local notification plugin
    // This would be implemented when adding firebase_messaging package
    print('Local notifications initialized');
  }

  static Future<void> requestPermissions() async {
    try {
      // Request notification permissions from the system
      // This would use firebase_messaging or local notification permissions
      print('Notification permissions requested');
    } catch (e) {
      print('Error requesting notification permissions: $e');
    }
  }

  static Future<String?> getFcmToken() async {
    try {
      // Get FCM token for push notifications
      // This would return the actual FCM token when firebase_messaging is implemented
      print('FCM token requested');
      return 'mock_fcm_token_${DateTime.now().millisecondsSinceEpoch}';
    } catch (e) {
      print('Error getting FCM token: $e');
      return null;
    }
  }

  static Future<void> subscribeToTopic(String topic) async {
    try {
      // Subscribe to FCM topic for targeted notifications
      print('Subscribed to topic: $topic');
    } catch (e) {
      print('Error subscribing to topic $topic: $e');
    }
  }

  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      // Unsubscribe from FCM topic
      print('Unsubscribed from topic: $topic');
    } catch (e) {
      print('Error unsubscribing from topic $topic: $e');
    }
  }

  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      // Show immediate local notification
      print('Showing notification: $title - $body');
      // This would use flutter_local_notifications plugin when implemented
    } catch (e) {
      print('Error showing local notification: $e');
    }
  }

  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    try {
      // Schedule notification for future delivery
      print('Scheduling notification $id for ${scheduledTime.toString()}: $title');
      // This would use flutter_local_notifications plugin when implemented
    } catch (e) {
      print('Error scheduling notification: $e');
    }
  }

  static Future<void> cancelNotification(int id) async {
    try {
      // Cancel specific notification by ID
      print('Cancelling notification with ID: $id');
      // This would use flutter_local_notifications plugin when implemented
    } catch (e) {
      print('Error cancelling notification $id: $e');
    }
  }

  static Future<void> cancelAllNotifications() async {
    try {
      // Cancel all pending notifications
      print('Cancelling all notifications');
      // This would use flutter_local_notifications plugin when implemented
    } catch (e) {
      print('Error cancelling all notifications: $e');
    }
  }
}
