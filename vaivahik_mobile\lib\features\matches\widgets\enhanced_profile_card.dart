import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/services/matching_service.dart';
import '../../profile/models/profile_model.dart';

class EnhancedProfileCard extends ConsumerStatefulWidget {
  final MatchResult matchResult;
  final VoidCallback? onTap;
  final VoidCallback? onLike;
  final VoidCallback? onDislike;
  final VoidCallback? onSuperLike;
  final VoidCallback? onShortlist;
  final VoidCallback? onBlock;
  final bool showActions;
  final bool showCompatibility;
  final bool showOnlineStatus;

  const EnhancedProfileCard({
    super.key,
    required this.matchResult,
    this.onTap,
    this.onLike,
    this.onDislike,
    this.onSuperLike,
    this.onShortlist,
    this.onBlock,
    this.showActions = true,
    this.showCompatibility = true,
    this.showOnlineStatus = true,
  });

  @override
  ConsumerState<EnhancedProfileCard> createState() => _EnhancedProfileCardState();
}

class _EnhancedProfileCardState extends ConsumerState<EnhancedProfileCard>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isLiked = false;
  bool _isShortlisted = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _isLiked = widget.matchResult.isLiked;
    _isShortlisted = widget.matchResult.isShortlisted;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final profile = widget.matchResult.profile;
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: (_) => _animationController.forward(),
            onTapUp: (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            onTap: widget.onTap,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  height: 400,
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [Colors.white, Color(0xFFF8F9FA)],
                    ),
                  ),
                  child: Column(
                    children: [
                      // Profile Image Section
                      Expanded(
                        flex: 3,
                        child: _buildProfileImageSection(profile),
                      ),
                      // Profile Info Section
                      Expanded(
                        flex: 2,
                        child: _buildProfileInfoSection(profile),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileImageSection(ProfileModel profile) {
    return Stack(
      children: [
        // Main Profile Image
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            image: profile.hasProfilePhoto
                ? DecorationImage(
                    image: NetworkImage(profile.profilePhoto!),
                    fit: BoxFit.cover,
                  )
                : null,
            color: profile.hasProfilePhoto ? null : AppTheme.surfaceColor,
          ),
          child: !profile.hasProfilePhoto
              ? const Center(
                  child: Icon(
                    Icons.person,
                    size: 80,
                    color: AppTheme.textSecondary,
                  ),
                )
              : null,
        ),
        
        // Gradient Overlay
        Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withValues(alpha: 0.3),
              ],
            ),
          ),
        ),

        // Top Row - Online Status & Premium Badge
        Positioned(
          top: 16,
          left: 16,
          right: 16,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Online Status
              if (widget.showOnlineStatus) _buildOnlineStatus(),
              // Premium Badge
              if (profile.isPremium) _buildPremiumBadge(),
            ],
          ),
        ),

        // Bottom Row - Profile Details
        Positioned(
          bottom: 16,
          left: 16,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Name and Age
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${profile.displayName}, ${profile.age ?? 'N/A'}',
                      style: const TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (profile.isVerified)
                    const Icon(
                      Icons.verified,
                      color: Colors.blue,
                      size: 20,
                    ),
                ],
              ),
              const SizedBox(height: 4),
              // Location
              Text(
                profile.locationDisplay,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),

        // Photo Count Indicator
        if (profile.hasPhotos)
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.photo_library,
                    color: Colors.white,
                    size: 14,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${profile.photoCount}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildOnlineStatus() {
    final isOnline = widget.matchResult.isOnline;
    final lastSeen = widget.matchResult.lastSeen;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isOnline ? Colors.green : Colors.grey.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: const BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            isOnline ? 'Online' : _getLastSeenText(lastSeen),
            style: const TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: Colors.white,
            size: 14,
          ),
          SizedBox(width: 4),
          Text(
            'Premium',
            style: TextStyle(
              fontSize: 12,
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  String _getLastSeenText(DateTime? lastSeen) {
    if (lastSeen == null) return 'Offline';

    final now = DateTime.now();
    final difference = now.difference(lastSeen);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Widget _buildProfileInfoSection(ProfileModel profile) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Compatibility Score & Match Category
          if (widget.showCompatibility) _buildCompatibilityRow(),
          const SizedBox(height: 12),

          // Profile Details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Education & Occupation
                _buildInfoRow(
                  Icons.school,
                  profile.education ?? 'Education not specified',
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  Icons.work,
                  profile.occupation ?? 'Occupation not specified',
                ),
                const SizedBox(height: 8),
                _buildInfoRow(
                  Icons.height,
                  profile.heightDisplay,
                ),
              ],
            ),
          ),

          // Action Buttons
          if (widget.showActions) _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildCompatibilityRow() {
    final score = widget.matchResult.compatibilityScore;
    final category = widget.matchResult.matchCategory;
    final usingML = widget.matchResult.usingML;

    return Row(
      children: [
        // Compatibility Score
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: _getScoreGradient(score),
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.favorite,
                color: Colors.white,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${score.toInt()}% Match',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: 8),

        // AI Badge
        if (usingML)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
            ),
            child: const Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.psychology,
                  color: AppTheme.primaryColor,
                  size: 12,
                ),
                SizedBox(width: 4),
                Text(
                  'AI',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

        const Spacer(),

        // Match Category
        if (category != null)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              category,
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.textSecondary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondary,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Dislike Button
        _buildActionButton(
          icon: Icons.close,
          color: Colors.grey,
          onTap: _handleDislike,
          isLoading: _isLoading,
        ),

        // Shortlist Button
        _buildActionButton(
          icon: _isShortlisted ? Icons.bookmark : Icons.bookmark_border,
          color: _isShortlisted ? AppTheme.accentColor : Colors.grey,
          onTap: _handleShortlist,
          isLoading: _isLoading,
        ),

        // Like Button
        _buildActionButton(
          icon: _isLiked ? Icons.favorite : Icons.favorite_border,
          color: _isLiked ? Colors.red : Colors.grey,
          onTap: _handleLike,
          isLoading: _isLoading,
        ),

        // Super Like Button
        _buildActionButton(
          icon: Icons.star,
          color: AppTheme.primaryColor,
          onTap: _handleSuperLike,
          isLoading: _isLoading,
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required bool isLoading,
  }) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          shape: BoxShape.circle,
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: isLoading
            ? const Center(
                child: SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
              )
            : Icon(
                icon,
                color: color,
                size: 24,
              ),
      ),
    );
  }

  List<Color> _getScoreGradient(double score) {
    if (score >= 80) {
      return [Colors.green, Colors.lightGreen];
    } else if (score >= 60) {
      return [Colors.orange, Colors.amber];
    } else {
      return [Colors.red, Colors.pink];
    }
  }

  void _handleLike() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final matchingService = ref.read(matchingServiceProvider);
      final success = await matchingService.likeProfile(
        widget.matchResult.profile.id,
        reason: 'profile_card_like',
      );

      if (success) {
        setState(() {
          _isLiked = !_isLiked;
        });
        widget.onLike?.call();
      }
    } catch (e) {
      // Show error snackbar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleDislike() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final matchingService = ref.read(matchingServiceProvider);
      final success = await matchingService.dislikeProfile(
        widget.matchResult.profile.id,
        reason: 'profile_card_dislike',
      );

      if (success) {
        widget.onDislike?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleSuperLike() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final matchingService = ref.read(matchingServiceProvider);
      final success = await matchingService.superLikeProfile(
        widget.matchResult.profile.id,
        message: 'Super liked from profile card',
      );

      if (success) {
        widget.onSuperLike?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _handleShortlist() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final matchingService = ref.read(matchingServiceProvider);
      final success = await matchingService.shortlistProfile(
        widget.matchResult.profile.id,
        remove: _isShortlisted,
      );

      if (success) {
        setState(() {
          _isShortlisted = !_isShortlisted;
        });
        widget.onShortlist?.call();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
